# Tier 3: Community Sources Scraper for PowerShell Content
# Scrapes community blogs and articles from reputable PowerShell experts
param(
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "./ScrapedData/tier3_community_$(Get-Date -Format 'yyyyMMdd_HHmmss').json",
    [Parameter(Mandatory = $false)]
    [int]$MaxArticlesPerSource = 20
)

Write-Host "Tier 3: Community Sources Scraper" -ForegroundColor Cyan
Write-Host "Targeting expert blogs and community articles" -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Gray

# Community sources configuration
$communitySources = @(
    @{
        name = "Adam the Automator"
        base_url = "https://adamtheautomator.com"
        rss_feed = "https://adamtheautomator.com/feed/"
        search_url = "https://adamtheautomator.com/?s=powershell"
        credibility = 0.9
        type = "blog"
    },
    @{
        name = "PowerShell Explained"
        base_url = "https://powershellexplained.com"
        rss_feed = "https://powershellexplained.com/feed.xml"
        search_url = "https://powershellexplained.com"
        credibility = 0.95
        type = "blog"
    },
    @{
        name = "Petri.com PowerShell"
        base_url = "https://petri.com"
        search_url = "https://petri.com/search?q=powershell"
        credibility = 0.85
        type = "article_site"
    },
    @{
        name = "4sysops PowerShell"
        base_url = "https://4sysops.com"
        search_url = "https://4sysops.com/?s=powershell"
        credibility = 0.8
        type = "article_site"
    },
    @{
        name = "PowerShell.org"
        base_url = "https://powershell.org"
        search_url = "https://powershell.org"
        credibility = 0.9
        type = "community"
    }
)

function Get-RSSFeedArticles {
    param(
        [string]$FeedUrl,
        [string]$SourceName,
        [int]$MaxArticles = 20
    )
    
    Write-Host "  Fetching RSS feed: $FeedUrl" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $FeedUrl -ErrorAction Stop
        $articles = @()
        
        # Handle different RSS formats
        $items = if ($response.rss.channel.item) { 
            $response.rss.channel.item 
        } elseif ($response.feed.entry) { 
            $response.feed.entry 
        } else { 
            $response.channel.item 
        }
        
        $count = 0
        foreach ($item in $items) {
            if ($count -ge $MaxArticles) { break }
            
            # Filter for PowerShell-related content
            $title = if ($item.title) { $item.title } else { $item.title.'#text' }
            $description = if ($item.description) { $item.description } else { $item.summary }
            
            if ($title -match "powershell|ps1|cmdlet|automation|active.directory" -or 
                $description -match "powershell|ps1|cmdlet|automation|active.directory") {
                
                $link = if ($item.link) { $item.link } else { $item.link.href }
                $pubDate = if ($item.pubDate) { $item.pubDate } else { $item.published }
                
                $articles += @{
                    title = $title
                    url = $link
                    description = $description
                    published_date = $pubDate
                    source = $SourceName
                }
                $count++
            }
        }
        
        Write-Host "    Found $($articles.Count) PowerShell-related articles" -ForegroundColor Green
        return $articles
    }
    catch {
        Write-Host "    Failed to fetch RSS feed: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

function Get-ArticleContent {
    param(
        [string]$Url,
        [string]$Title
    )
    
    try {
        Write-Host "      Fetching: $Title" -ForegroundColor Gray
        
        $response = Invoke-WebRequest -Uri $Url -ErrorAction Stop -TimeoutSec 30
        $html = $response.Content
        
        # Extract main content using common patterns
        $content = ""
        
        # Try different content selectors
        $contentPatterns = @(
            '<article[^>]*>(.*?)</article>',
            '<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>',
            '<div[^>]*class="[^"]*post[^"]*"[^>]*>(.*?)</div>',
            '<div[^>]*class="[^"]*entry[^"]*"[^>]*>(.*?)</div>',
            '<main[^>]*>(.*?)</main>'
        )
        
        foreach ($pattern in $contentPatterns) {
            $matches = [regex]::Matches($html, $pattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            if ($matches.Count -gt 0) {
                $content = $matches[0].Groups[1].Value
                break
            }
        }
        
        # If no specific content found, extract from body
        if (-not $content) {
            $bodyMatch = [regex]::Match($html, '<body[^>]*>(.*?)</body>', [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            if ($bodyMatch.Success) {
                $content = $bodyMatch.Groups[1].Value
            }
        }
        
        # Clean HTML content
        $cleanContent = Clean-HtmlContent -HtmlContent $content
        
        # Validate content quality
        if ($cleanContent.Length -gt 500 -and $cleanContent -match "powershell|cmdlet|Get-|Set-|New-") {
            return $cleanContent
        }
        
        return $null
    }
    catch {
        Write-Host "      Failed to fetch article: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Clean-HtmlContent {
    param([string]$HtmlContent)
    
    if (-not $HtmlContent) { return "" }
    
    # Remove script and style tags
    $content = $HtmlContent -replace '<script[^>]*>.*?</script>', '' -replace '<style[^>]*>.*?</style>', ''
    
    # Remove HTML comments
    $content = $content -replace '<!--.*?-->', ''
    
    # Convert common HTML entities
    $content = $content -replace '&nbsp;', ' ' -replace '&amp;', '&' -replace '&lt;', '<' -replace '&gt;', '>' -replace '&quot;', '"'
    
    # Preserve code blocks but clean other HTML
    $codeBlocks = @()
    $codePattern = '<(?:pre|code)[^>]*>(.*?)</(?:pre|code)>'
    $matches = [regex]::Matches($content, $codePattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    for ($i = 0; $i -lt $matches.Count; $i++) {
        $placeholder = "CODEBLOCK_PLACEHOLDER_$i"
        $codeBlocks += $matches[$i].Groups[1].Value
        $content = $content -replace [regex]::Escape($matches[$i].Value), $placeholder
    }
    
    # Remove remaining HTML tags
    $content = $content -replace '<[^>]+>', ''
    
    # Restore code blocks
    for ($i = 0; $i -lt $codeBlocks.Count; $i++) {
        $placeholder = "CODEBLOCK_PLACEHOLDER_$i"
        $content = $content -replace $placeholder, "`n```powershell`n$($codeBlocks[$i])`n```"
    }
    
    # Clean up whitespace
    $content = $content -replace '\s+', ' ' -replace '\n\s*\n', "`n`n"
    $content = $content.Trim()
    
    return $content
}

function Extract-PowerShellCmdlets {
    param([string]$Content)
    
    $cmdletPattern = '\b(Get-|Set-|New-|Remove-|Add-|Update-|Start-|Stop-|Enable-|Disable-|Test-|Import-|Export-|Connect-|Disconnect-)[A-Za-z][A-Za-z0-9]*\b'
    $matches = [regex]::Matches($Content, $cmdletPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    $cmdlets = $matches | ForEach-Object { $_.Value } | Sort-Object -Unique
    return $cmdlets
}

function Extract-CodeBlocks {
    param([string]$Content)
    
    $codeBlocks = @()
    
    # PowerShell code blocks in markdown
    $markdownPattern = '```(?:powershell|ps1)?\s*\n(.*?)\n```'
    $matches = [regex]::Matches($Content, $markdownPattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    foreach ($match in $matches) {
        $code = $match.Groups[1].Value.Trim()
        if ($code.Length -gt 20) {
            $codeBlocks += $code
        }
    }
    
    # Also look for inline code patterns
    $inlinePattern = '\$[A-Za-z][A-Za-z0-9]*|Get-\w+|Set-\w+|New-\w+'
    $inlineMatches = [regex]::Matches($Content, $inlinePattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    if ($inlineMatches.Count -gt 5) {
        # Extract paragraphs with multiple PowerShell commands
        $paragraphs = $Content -split '\n\n'
        foreach ($paragraph in $paragraphs) {
            if (($paragraph -split '\n').Count -le 10 -and 
                ([regex]::Matches($paragraph, $inlinePattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)).Count -ge 3) {
                $codeBlocks += $paragraph.Trim()
            }
        }
    }
    
    return $codeBlocks
}

# Main scraping logic
try {
    $scrapedData = @{
        source = "community"
        tier = 3
        description = "PowerShell articles and tutorials from community experts and blogs"
        scraped_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        total_sources = $communitySources.Count
        entries = @()
        statistics = @{
            sources_processed = 0
            articles_processed = 0
            rss_feeds_processed = 0
            total_cmdlets = 0
            avg_content_length = 0
        }
    }
    
    $allEntries = @()
    $totalContentLength = 0
    $processedArticles = 0
    
    foreach ($source in $communitySources) {
        Write-Host "Processing source: $($source.name)" -ForegroundColor Yellow
        
        $articles = @()
        
        # Try RSS feed first if available
        if ($source.rss_feed) {
            $articles = Get-RSSFeedArticles -FeedUrl $source.rss_feed -SourceName $source.name -MaxArticles $MaxArticlesPerSource
            $scrapedData.statistics.rss_feeds_processed++
        }
        
        # If no articles from RSS or no RSS feed, try other methods
        if ($articles.Count -eq 0) {
            Write-Host "  No RSS articles found, trying alternative methods..." -ForegroundColor Gray
            # Could implement web scraping of search results here
            # For now, skip to avoid being too aggressive
        }
        
        # Process each article
        foreach ($article in $articles) {
            $content = Get-ArticleContent -Url $article.url -Title $article.title
            
            if ($content) {
                $cmdlets = Extract-PowerShellCmdlets -Content $content
                $codeBlocks = Extract-CodeBlocks -Content $content
                
                $entry = @{
                    id = "community_$($source.name.Replace(' ', '_').ToLower())_$($article.url.GetHashCode().ToString().Replace('-', 'n'))"
                    title = $article.title
                    content = $content
                    source = @{
                        url = $article.url
                        type = "community"
                        site_name = $source.name
                        credibility = $source.credibility
                    }
                    cmdlets = $cmdlets
                    code_examples = $codeBlocks
                    tags = @("community", "powershell", $source.type, $source.name.Replace(' ', '-').ToLower())
                    metadata = @{
                        published_date = $article.published_date
                        description = $article.description
                        source_type = $source.type
                    }
                    last_updated = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
                }
                
                $allEntries += $entry
                $processedArticles++
                $totalContentLength += $content.Length
            }
            
            Start-Sleep -Milliseconds 1000  # Be respectful with rate limiting
        }
        
        $scrapedData.statistics.sources_processed++
        Write-Host "  Processed $($articles.Count) articles from $($source.name)" -ForegroundColor Green
        
        Start-Sleep -Milliseconds 2000  # Rate limiting between sources
    }
    
    # Finalize statistics
    $scrapedData.entries = $allEntries
    $scrapedData.statistics.articles_processed = $processedArticles
    $scrapedData.statistics.avg_content_length = if ($processedArticles -gt 0) { [math]::Round($totalContentLength / $processedArticles) } else { 0 }
    
    $allCmdlets = $allEntries | ForEach-Object { $_.cmdlets } | Sort-Object -Unique
    $scrapedData.statistics.total_cmdlets = $allCmdlets.Count
    
    # Save the scraped data
    $scrapedData | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "`nTier 3 Community scraping completed!" -ForegroundColor Green
    Write-Host "=" * 60 -ForegroundColor Gray
    Write-Host "Statistics:" -ForegroundColor Cyan
    Write-Host "  Sources processed: $($scrapedData.statistics.sources_processed)" -ForegroundColor White
    Write-Host "  Articles processed: $($scrapedData.statistics.articles_processed)" -ForegroundColor White
    Write-Host "  RSS feeds processed: $($scrapedData.statistics.rss_feeds_processed)" -ForegroundColor White
    Write-Host "  Unique cmdlets: $($scrapedData.statistics.total_cmdlets)" -ForegroundColor White
    Write-Host "  Average content length: $($scrapedData.statistics.avg_content_length) characters" -ForegroundColor White
    Write-Host "  Output saved to: $OutputPath" -ForegroundColor Green
}
catch {
    Write-Host "Community scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Full error: $($_.Exception)" -ForegroundColor Red
    exit 1
}
