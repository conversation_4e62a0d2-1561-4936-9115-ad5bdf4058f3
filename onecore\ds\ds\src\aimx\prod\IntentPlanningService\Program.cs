using IntentPlanningService.Configuration;
using IntentPlanningService.Services;
using Microsoft.Extensions.AI;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Microsoft.SemanticKernel.Connectors.Onnx;
using System.Text.Json;

namespace IntentPlanningService;

public class Program
{
    public static async Task Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        // Configure logging
        builder.Logging.ClearProviders();
        builder.Logging.AddConsole();
        builder.Logging.AddDebug();

        // Load configuration
        var configuration = LoadConfiguration(builder.Configuration);
        builder.Services.AddSingleton(configuration);

        // Configure services
        ConfigureServices(builder.Services, configuration);

        // Configure Semantic Kernel and AI services
        await ConfigureSemanticKernelAsync(builder.Services, configuration);

        // Add controllers and API services
        builder.Services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.WriteIndented = true;
            });

        // Add API documentation
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
            {
                Title = "Intent Planning Service API",
                Version = "v1",
                Description = "LLM-Orchestrated Expert System - Intent Understanding and Workflow Planning Engine"
            });
        });

        // Add CORS if enabled
        if (configuration.Service.EnableCors)
        {
            builder.Services.AddCors(options =>
            {
                options.AddDefaultPolicy(policy =>
                {
                    policy.AllowAnyOrigin()
                          .AllowAnyMethod()
                          .AllowAnyHeader();
                });
            });
        }

        // Add health checks
        builder.Services.AddHealthChecks();

        // Configure HTTP clients
        ConfigureHttpClients(builder.Services, configuration);

        var app = builder.Build();

        // Configure the HTTP request pipeline
        if (app.Environment.IsDevelopment() || configuration.Service.EnableSwagger)
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Intent Planning Service API v1");
                c.RoutePrefix = string.Empty; // Serve Swagger UI at root
            });
        }

        if (configuration.Service.EnableCors)
        {
            app.UseCors();
        }

        app.UseRouting();
        app.MapControllers();
        app.MapHealthChecks("/health");

        // Log startup information
        var logger = app.Services.GetRequiredService<ILogger<Program>>();
        logger.LogInformation("Intent Planning Service starting on {Host}:{Port}", 
            configuration.Service.Host, configuration.Service.Port);

        // Start the application
        await app.RunAsync($"http://{configuration.Service.Host}:{configuration.Service.Port}");
    }

    /// <summary>
    /// Load configuration from various sources
    /// </summary>
    private static IntentPlanningConfiguration LoadConfiguration(IConfiguration configuration)
    {
        var config = new IntentPlanningConfiguration();
        
        // Bind configuration from appsettings.json and environment variables
        configuration.Bind(config);
        
        // Override with environment-specific settings if needed
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
        
        if (environment == "Development")
        {
            // Development-specific overrides
            config.Service.EnableSwagger = true;
            config.Slm.EnableCaching = true;
        }
        
        return config;
    }

    /// <summary>
    /// Configure dependency injection services
    /// </summary>
    private static void ConfigureServices(IServiceCollection services, IntentPlanningConfiguration configuration)
    {
        // Register core services
        services.AddScoped<IntentAnalysisService>();
        services.AddScoped<WorkflowPlanningService>();
        services.AddScoped<ToolDiscoveryService>();
        services.AddScoped<RiskAssessmentService>();

        // Add memory cache for caching
        services.AddMemoryCache();

        // Add logging
        services.AddLogging();
    }

    /// <summary>
    /// Configure Semantic Kernel and AI services
    /// </summary>
    private static Task ConfigureSemanticKernelAsync(IServiceCollection services, IntentPlanningConfiguration configuration)
    {
        // Configure Semantic Kernel
        var kernelBuilder = Kernel.CreateBuilder();

        // Add ONNX embedding service for SLM
        if (File.Exists(configuration.Slm.ModelPath))
        {
            // TODO: Fix ONNX extension method
            // kernelBuilder.AddOnnxTextEmbeddingGeneration(
            //     modelId: "slm-embedding",
            //     modelPath: configuration.Slm.ModelPath);
        }
        else
        {
            // Fallback to a mock embedding service for development
            services.AddSingleton<IEmbeddingGenerator<string, Embedding<float>>>(provider =>
                new MockEmbeddingGenerator());
        }

        // Add chat completion service (can be OpenAI or local model)
        var openAiApiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY");
        if (!string.IsNullOrEmpty(openAiApiKey))
        {
            kernelBuilder.AddOpenAIChatCompletion(
                modelId: "gpt-3.5-turbo",
                apiKey: openAiApiKey);
        }
        else
        {
            // Use a mock chat service for development
            services.AddSingleton<Microsoft.SemanticKernel.ChatCompletion.IChatCompletionService>(provider =>
                new MockChatCompletionService());
        }

        var kernel = kernelBuilder.Build();
        services.AddSingleton(kernel);

        // Register embedding service separately for direct injection
        if (!File.Exists(configuration.Slm.ModelPath))
        {
            services.AddSingleton<IEmbeddingGenerator<string, Embedding<float>>>(provider =>
                new MockEmbeddingGenerator());
        }
        
        return Task.CompletedTask;
    }

    /// <summary>
    /// Configure HTTP clients for external services
    /// </summary>
    private static void ConfigureHttpClients(IServiceCollection services, IntentPlanningConfiguration configuration)
    {
        // HTTP client for Tool Discovery Service
        services.AddHttpClient<ToolDiscoveryService>(client =>
        {
            client.BaseAddress = new Uri(configuration.ExternalServices.ToolManager.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(configuration.ExternalServices.ToolManager.TimeoutSeconds);
            
            // Add any required headers
            foreach (var header in configuration.ExternalServices.ToolManager.Headers)
            {
                client.DefaultRequestHeaders.Add(header.Key, header.Value);
            }
        });

        // HTTP client for RAG Service
        services.AddHttpClient("RagService", client =>
        {
            client.BaseAddress = new Uri(configuration.ExternalServices.NetRagService.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(configuration.ExternalServices.NetRagService.TimeoutSeconds);
        });

        // HTTP client for Workflow Engine
        services.AddHttpClient("WorkflowEngine", client =>
        {
            client.BaseAddress = new Uri(configuration.ExternalServices.WorkflowEngine.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(configuration.ExternalServices.WorkflowEngine.TimeoutSeconds);
        });

        // HTTP client for AIMX Server
        services.AddHttpClient("AimxServer", client =>
        {
            client.BaseAddress = new Uri(configuration.ExternalServices.AimxServer.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(configuration.ExternalServices.AimxServer.TimeoutSeconds);
        });
    }
}

/// <summary>
/// Mock embedding generator for development/testing
/// </summary>
public class MockEmbeddingGenerator : IEmbeddingGenerator<string, Embedding<float>>
{
    public EmbeddingGeneratorMetadata Metadata => new("mock-embedding");

    public Task<GeneratedEmbeddings<Embedding<float>>> GenerateAsync(
        IEnumerable<string> values, 
        EmbeddingGenerationOptions? options = null, 
        CancellationToken cancellationToken = default)
    {
        var embeddings = values.Select(value => 
        {
            // Generate a simple mock embedding based on string hash
            var hash = value.GetHashCode();
            var vector = new float[384]; // Standard embedding size
            
            for (int i = 0; i < vector.Length; i++)
            {
                vector[i] = (float)Math.Sin(hash + i) * 0.1f;
            }
            
            return new Embedding<float>(vector);
        }).ToList();

        return Task.FromResult(new GeneratedEmbeddings<Embedding<float>>(embeddings));
    }

    public void Dispose() { }

    public object? GetService(Type serviceType, object? serviceKey = null)
    {
        return null;
    }
}

/// <summary>
/// Mock chat completion service for development/testing
/// </summary>
public class MockChatCompletionService : Microsoft.SemanticKernel.ChatCompletion.IChatCompletionService
{
    public IReadOnlyDictionary<string, object?> Attributes => new Dictionary<string, object?>();

    public Task<IReadOnlyList<Microsoft.SemanticKernel.ChatMessageContent>> GetChatMessageContentsAsync(
        Microsoft.SemanticKernel.ChatCompletion.ChatHistory chatHistory,
        Microsoft.SemanticKernel.PromptExecutionSettings? executionSettings = null,
        Kernel? kernel = null,
        CancellationToken cancellationToken = default)
    {
        // Simple mock response based on the last user message
        var lastMessage = chatHistory.LastOrDefault()?.Content ?? "";
        
        var response = GenerateMockResponse(lastMessage);
        
        var messageContent = new Microsoft.SemanticKernel.ChatMessageContent(Microsoft.SemanticKernel.ChatCompletion.AuthorRole.Assistant, response);
        return Task.FromResult<IReadOnlyList<Microsoft.SemanticKernel.ChatMessageContent>>(new List<Microsoft.SemanticKernel.ChatMessageContent> { messageContent });
    }

    public IAsyncEnumerable<Microsoft.SemanticKernel.StreamingChatMessageContent> GetStreamingChatMessageContentsAsync(
        Microsoft.SemanticKernel.ChatCompletion.ChatHistory chatHistory,
        Microsoft.SemanticKernel.PromptExecutionSettings? executionSettings = null,
        Kernel? kernel = null,
        CancellationToken cancellationToken = default)
    {
        var response = GenerateMockResponse(chatHistory.LastOrDefault()?.Content ?? "");
        return GetStreamingResponseAsync(response, cancellationToken);
    }

    private async IAsyncEnumerable<Microsoft.SemanticKernel.StreamingChatMessageContent> GetStreamingResponseAsync(
        string response, 
        [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        await Task.Delay(10, cancellationToken); // Small delay to make it truly async
        yield return new Microsoft.SemanticKernel.StreamingChatMessageContent(Microsoft.SemanticKernel.ChatCompletion.AuthorRole.Assistant, response);
    }

    private string GenerateMockResponse(string input)
    {
        var inputLower = input.ToLowerInvariant();
        
        // Simple rule-based mock responses
        if (inputLower.Contains("user") && inputLower.Contains("create"))
        {
            return "user_management";
        }
        else if (inputLower.Contains("group"))
        {
            return "group_management";
        }
        else if (inputLower.Contains("computer"))
        {
            return "computer_management";
        }
        else if (inputLower.Contains("security") || inputLower.Contains("permission"))
        {
            return "security_operations";
        }
        else if (inputLower.Contains("yes") || inputLower.Contains("it"))
        {
            return "YES";
        }
        else if (inputLower.Contains("json"))
        {
            return @"{
                ""primaryObjective"": ""Mock objective based on input"",
                ""successCriteria"": ""Operation completed successfully"",
                ""constraints"": [""Mock constraint""],
                ""urgencyLevel"": ""normal""
            }";
        }
        
        return "system_administration";
    }
}
