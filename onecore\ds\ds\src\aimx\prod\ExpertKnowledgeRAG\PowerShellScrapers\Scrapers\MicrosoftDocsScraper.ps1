# Microsoft Docs PowerShell AD Module Scraper
# Scrapes official Microsoft documentation for PowerShell Active Directory cmdlets

#Requires -Version 5.1

param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "../ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [switch]$FullScrape = $false,
    
    [Parameter(Mandatory = $false)]
    [string[]]$SpecificCmdlets = @()
)

Import-Module -Name (Join-Path $PSScriptRoot "../Core/ScrapingFramework.psm1") -Force

function Get-CmdletDomain {
    [CmdletBinding()]
    param([string]$CmdletName)

    switch -Regex ($CmdletName) {
        ".*User.*" { return "user_management" }
        ".*Group.*" { return "group_management" }
        ".*Computer.*" { return "computer_management" }
        ".*OrganizationalUnit.*|.*OU.*" { return "ou_management" }
        ".*Account.*" { return "account_management" }
        ".*Password.*" { return "password_management" }
        ".*Permission.*|.*ACL.*" { return "security_management" }
        default { return "general_ad" }
    }
}

function Get-CmdletOperation {
    [CmdletBinding()]
    param([string]$CmdletName)

    switch -Regex ($CmdletName) {
        "^Get-.*" { return "read" }
        "^Set-.*" { return "update" }
        "^New-.*" { return "create" }
        "^Remove-.*" { return "delete" }
        "^Add-.*" { return "add" }
        "^Enable-.*" { return "enable" }
        "^Disable-.*" { return "disable" }
        "^Unlock-.*" { return "unlock" }
        "^Reset-.*" { return "reset" }
        "^Move-.*" { return "move" }
        "^Search-.*" { return "search" }
        default { return "other" }
    }
}

function Get-ADCmdletList {
    [CmdletBinding()]
    param()
    
    Write-ScrapingLog -Message "Fetching Active Directory cmdlet list from Microsoft Docs" -Source "MicrosoftDocsScraper"
    
    $baseUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/"
    
    try {
        $response = Invoke-WebRequestWithRetry -Uri $baseUrl
        $content = $response.Content
        
        # Extract cmdlet links using regex - updated pattern for learn.microsoft.com
        $cmdletPattern = '>([A-Za-z]+-AD[A-Za-z]+)<'
        $matches = [regex]::Matches($content, $cmdletPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        
        $cmdlets = @()
        foreach ($match in $matches) {
            $cmdletName = $match.Groups[1].Value

            # Construct URL for the cmdlet documentation
            $cmdletUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($cmdletName.ToLower())"

            $cmdlets += @{
                Name = $cmdletName
                Url = $cmdletUrl
                Domain = Get-CmdletDomain -CmdletName $cmdletName
                Operation = Get-CmdletOperation -CmdletName $cmdletName
            }
        }
        
        Write-ScrapingLog -Message "Found $($cmdlets.Count) Active Directory cmdlets" -Source "MicrosoftDocsScraper"
        return $cmdlets
    }
    catch {
        Write-ScrapingLog -Message "Failed to fetch cmdlet list: $($_.Exception.Message)" -Level "Error" -Source "MicrosoftDocsScraper"
        throw
    }
}



function Scrape-CmdletDocumentation {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$Cmdlet
    )
    
    Write-ScrapingLog -Message "Scraping documentation for: $($Cmdlet.Name)" -Source "MicrosoftDocsScraper"
    
    try {
        $response = Invoke-WebRequestWithRetry -Uri $Cmdlet.Url
        $content = $response.Content
        
        # Parse HTML content
        $parsedContent = Parse-CmdletDocumentation -Content $content -CmdletName $Cmdlet.Name
        
        if ($parsedContent) {
            # Create knowledge pattern
            $pattern = New-KnowledgePattern -Title "Microsoft Docs: $($Cmdlet.Name)" -Content $parsedContent.FullContent -SourceUrl $Cmdlet.Url -SourceType "microsoft_docs" -Domain $Cmdlet.Domain -Operation $Cmdlet.Operation -Author "Microsoft" -CredibilityScore 0.95
            
            # Enhance with parsed metadata
            $pattern.Abstract = $parsedContent.Synopsis
            $pattern.RequiredParameters = ($parsedContent.Parameters | Where-Object { $_.Required -eq $true } | ConvertTo-Json -Compress)
            $pattern.BestPractices = $parsedContent.Notes
            $pattern.CodeTemplate = ($parsedContent.Examples -join "`n`n")
            
            return $pattern
        }
        
        return $null
    }
    catch {
        Write-ScrapingLog -Message "Failed to scrape $($Cmdlet.Name): $($_.Exception.Message)" -Level "Error" -Source "MicrosoftDocsScraper"
        return $null
    }
}

function Parse-CmdletDocumentation {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Content,
        
        [Parameter(Mandatory = $true)]
        [string]$CmdletName
    )
    
    try {
        # Extract synopsis
        $synopsisPattern = '<h2[^>]*>Synopsis</h2>\s*<p[^>]*>(.*?)</p>'
        $synopsisMatch = [regex]::Match($Content, $synopsisPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Singleline)
        $synopsis = if ($synopsisMatch.Success) { $synopsisMatch.Groups[1].Value -replace '<[^>]+>', '' } else { "" }
        
        # Extract description
        $descriptionPattern = '<h2[^>]*>Description</h2>\s*<p[^>]*>(.*?)</p>'
        $descriptionMatch = [regex]::Match($Content, $descriptionPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Singleline)
        $description = if ($descriptionMatch.Success) { $descriptionMatch.Groups[1].Value -replace '<[^>]+>', '' } else { "" }
        
        # Extract parameters
        $parameters = Extract-Parameters -Content $Content
        
        # Extract examples
        $examples = Extract-Examples -Content $Content
        
        # Extract notes
        $notes = Extract-Notes -Content $Content
        
        # Combine all content
        $fullContent = @"
# $CmdletName

## Synopsis
$synopsis

## Description
$description

## Parameters
$($parameters | ConvertTo-Json -Depth 3)

## Examples
$($examples -join "`n`n")

## Notes
$($notes -join "`n")
"@
        
        return @{
            Synopsis = $synopsis
            Description = $description
            Parameters = $parameters
            Examples = $examples
            Notes = $notes
            FullContent = $fullContent
        }
    }
    catch {
        Write-ScrapingLog -Message "Failed to parse documentation for $CmdletName : $($_.Exception.Message)" -Level "Warning" -Source "MicrosoftDocsScraper"
        return $null
    }
}

function Extract-Parameters {
    [CmdletBinding()]
    param([string]$Content)
    
    $parameters = @()
    
    # Pattern to match parameter tables
    $parameterPattern = '<tr[^>]*>.*?<td[^>]*><strong>([^<]+)</strong></td>.*?<td[^>]*>([^<]*)</td>.*?<td[^>]*>([^<]*)</td>.*?</tr>'
    $matches = [regex]::Matches($Content, $parameterPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Singleline)
    
    foreach ($match in $matches) {
        $paramName = $match.Groups[1].Value.Trim()
        $paramType = $match.Groups[2].Value.Trim()
        $paramRequired = $match.Groups[3].Value.Trim() -eq "True"
        
        if ($paramName -and $paramName -ne "Parameter") {
            $parameters += @{
                Name = $paramName
                Type = $paramType
                Required = $paramRequired
            }
        }
    }
    
    return $parameters
}

function Extract-Examples {
    [CmdletBinding()]
    param([string]$Content)
    
    $examples = @()
    
    # Pattern to match example sections
    $examplePattern = '<h3[^>]*>Example \d+[^<]*</h3>(.*?)(?=<h3|<h2|$)'
    $matches = [regex]::Matches($Content, $examplePattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Singleline)
    
    foreach ($match in $matches) {
        $exampleContent = $match.Groups[1].Value
        
        # Extract code blocks from example
        $codeBlocks = Extract-CodeBlocks -Content $exampleContent
        
        if ($codeBlocks.Count -gt 0) {
            $examples += $codeBlocks
        }
    }
    
    return $examples
}

function Extract-Notes {
    [CmdletBinding()]
    param([string]$Content)
    
    $notes = @()
    
    # Pattern to match notes section
    $notesPattern = '<h2[^>]*>Notes</h2>(.*?)(?=<h2|$)'
    $notesMatch = [regex]::Match($Content, $notesPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Singleline)
    
    if ($notesMatch.Success) {
        $notesContent = $notesMatch.Groups[1].Value
        
        # Extract list items
        $listPattern = '<li[^>]*>(.*?)</li>'
        $listMatches = [regex]::Matches($notesContent, $listPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Singleline)
        
        foreach ($listMatch in $listMatches) {
            $note = $listMatch.Groups[1].Value -replace '<[^>]+>', ''
            if ($note.Trim()) {
                $notes += $note.Trim()
            }
        }
    }
    
    return $notes
}

# Main execution
function Start-MicrosoftDocsScraping {
    [CmdletBinding()]
    param()
    
    Write-ScrapingLog -Message "Starting Microsoft Docs scraping process" -Source "MicrosoftDocsScraper"
    
    try {
        # Initialize framework
        if (-not (Initialize-ScrapingFramework -ConfigPath $ConfigPath)) {
            throw "Failed to initialize scraping framework"
        }
        
        # Get cmdlet list
        $cmdlets = Get-ADCmdletList
        
        # Filter cmdlets if specific ones requested
        if ($SpecificCmdlets.Count -gt 0) {
            Write-ScrapingLog -Message "Specific cmdlets requested: $($SpecificCmdlets -join ', ')" -Source "MicrosoftDocsScraper"
            Write-ScrapingLog -Message "Total cmdlets before filtering: $($cmdlets.Count)" -Source "MicrosoftDocsScraper"

            # Debug: Show first few cmdlet names
            $firstFew = $cmdlets | Select-Object -First 5 | ForEach-Object { $_.Name }
            Write-ScrapingLog -Message "First few cmdlets: $($firstFew -join ', ')" -Source "MicrosoftDocsScraper"

            $cmdlets = $cmdlets | Where-Object { $_.Name -in $SpecificCmdlets }
            Write-ScrapingLog -Message "Filtering to $($cmdlets.Count) specific cmdlets" -Source "MicrosoftDocsScraper"

            # Debug: Show what was found
            if ($cmdlets.Count -gt 0) {
                $foundNames = $cmdlets | ForEach-Object { $_.Name }
                Write-ScrapingLog -Message "Found cmdlets: $($foundNames -join ', ')" -Source "MicrosoftDocsScraper"
            } else {
                Write-ScrapingLog -Message "No matching cmdlets found!" -Level "Warning" -Source "MicrosoftDocsScraper"
            }
        }
        
        # Scrape each cmdlet
        $patterns = @()
        $totalCmdlets = $cmdlets.Count
        $currentIndex = 0
        
        foreach ($cmdlet in $cmdlets) {
            $currentIndex++
            Write-Progress -Activity "Scraping Microsoft Docs" -Status "Processing $($cmdlet.Name)" -PercentComplete (($currentIndex / $totalCmdlets) * 100)
            
            $pattern = Scrape-CmdletDocumentation -Cmdlet $cmdlet
            if ($pattern) {
                $patterns += $pattern
            }
            
            # Rate limiting
            Start-Sleep -Milliseconds 1000
        }
        
        Write-Progress -Activity "Scraping Microsoft Docs" -Completed
        
        # Save results
        if ($patterns.Count -gt 0) {
            $outputFile = Save-ScrapingResults -Patterns $patterns -SourceType "microsoft_docs"
            Write-ScrapingLog -Message "Successfully scraped $($patterns.Count) patterns from Microsoft Docs" -Source "MicrosoftDocsScraper"
            Write-ScrapingLog -Message "Results saved to: $outputFile" -Source "MicrosoftDocsScraper"
        } else {
            Write-ScrapingLog -Message "No patterns extracted from Microsoft Docs" -Level "Warning" -Source "MicrosoftDocsScraper"
        }
        
        return $patterns
    }
    catch {
        Write-ScrapingLog -Message "Microsoft Docs scraping failed: $($_.Exception.Message)" -Level "Error" -Source "MicrosoftDocsScraper"
        throw
    }
}

# Execute if run directly
if ($MyInvocation.InvocationName -ne '.') {
    Start-MicrosoftDocsScraping
}
