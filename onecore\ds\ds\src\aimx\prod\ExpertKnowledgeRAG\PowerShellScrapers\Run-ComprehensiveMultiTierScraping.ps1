# Comprehensive Multi-Tier PowerShell Knowledge Scraping Orchestrator
# Implements the complete tiered data collection strategy
param(
    [Parameter(Mandatory = $false)]
    [string]$OutputDirectory = "./ScrapedData",
    [Parameter(Mandatory = $false)]
    [string[]]$Tiers = @("1", "2", "3"),
    [Parameter(Mandatory = $false)]
    [int]$MaxItemsPerTier = 100,
    [Parameter(Mandatory = $false)]
    [string]$GitHubToken = $env:GITHUB_TOKEN,
    [Parameter(Mandatory = $false)]
    [switch]$ConsolidateResults = $true
)

Write-Host "Comprehensive Multi-Tier PowerShell Knowledge Scraping" -ForegroundColor Cyan
Write-Host "Implementing complete tiered data collection strategy" -ForegroundColor Gray
Write-Host "=" * 80 -ForegroundColor Cyan

# Ensure output directory exists
if (-not (Test-Path $OutputDirectory)) {
    New-Item -ItemType Directory -Path $OutputDirectory -Force | Out-Null
}

$timestamp = Get-Date -Format 'yyyyMMdd_HHmmss'
$consolidatedOutputPath = Join-Path $OutputDirectory "consolidated_multitier_$timestamp.json"

# Tier execution configuration
$tierConfig = @{
    "1" = @{
        name = "Microsoft Official Documentation"
        scripts = @("Tier1-EnhancedMicrosoftScraper.ps1", "Clean-MicrosoftDocsScraper.ps1")
        description = "High-quality structured baseline from Microsoft Learn and official docs"
        priority = 1
        expected_credibility = 0.95
    }
    "2" = @{
        name = "Community & GitHub Sources"
        scripts = @("Enhanced-GitHubAPIScaper.ps1", "Tier2-GitHubScraper.ps1", "Clean-StackOverflowScraper.ps1")
        description = "Real-world Q&A, scripts, and examples from GitHub and StackOverflow"
        priority = 2
        expected_credibility = 0.80
    }
    "3" = @{
        name = "Expert Blogs & Community Articles"
        scripts = @("Tier3-CommunityScraper.ps1")
        description = "Community experts, MVPs, and specialized PowerShell blogs"
        priority = 3
        expected_credibility = 0.85
    }
}

function Write-TierHeader {
    param([string]$TierNumber, [hashtable]$TierInfo)
    
    Write-Host "`n" + "=" * 80 -ForegroundColor Green
    Write-Host "TIER $TierNumber: $($TierInfo.name)" -ForegroundColor Green
    Write-Host "Description: $($TierInfo.description)" -ForegroundColor Gray
    Write-Host "Expected Credibility: $($TierInfo.expected_credibility)" -ForegroundColor Gray
    Write-Host "=" * 80 -ForegroundColor Green
}

function Invoke-TierScraping {
    param(
        [string]$TierNumber,
        [hashtable]$TierInfo,
        [int]$MaxItems
    )
    
    $tierResults = @()
    $tierStartTime = Get-Date
    
    foreach ($scriptName in $TierInfo.scripts) {
        $scriptPath = Join-Path $PSScriptRoot $scriptName
        
        if (Test-Path $scriptPath) {
            Write-Host "`nExecuting: $scriptName" -ForegroundColor Yellow
            
            try {
                $outputPath = Join-Path $OutputDirectory "tier${TierNumber}_$(($scriptName -replace '\.ps1$', '').ToLower())_$timestamp.json"
                
                # Execute the scraper script with appropriate parameters
                $params = @{
                    OutputPath = $outputPath
                }
                
                # Add tier-specific parameters
                switch ($scriptName) {
                    "Enhanced-GitHubAPIScaper.ps1" {
                        if ($GitHubToken) { $params.GitHubToken = $GitHubToken }
                        $params.MaxReposPerOrg = [math]::Floor($MaxItems / 10)
                        $params.MaxFilesPerRepo = 10
                    }
                    "Tier2-GitHubScraper.ps1" {
                        if ($GitHubToken) { $params.GitHubToken = $GitHubToken }
                        $params.MaxRepositories = [math]::Floor($MaxItems / 5)
                        $params.MaxFilesPerRepo = 15
                    }
                    "Clean-StackOverflowScraper.ps1" {
                        $params.MaxQuestions = [math]::Floor($MaxItems / 3)
                    }
                    "Tier3-CommunityScraper.ps1" {
                        $params.MaxArticlesPerSource = [math]::Floor($MaxItems / 6)
                    }
                    default {
                        $params.MaxItems = $MaxItems
                    }
                }
                
                # Execute the script
                & $scriptPath @params
                
                # Check if output file was created and load results
                if (Test-Path $outputPath) {
                    $scriptResults = Get-Content $outputPath -Raw | ConvertFrom-Json
                    $tierResults += @{
                        script = $scriptName
                        output_path = $outputPath
                        results = $scriptResults
                        execution_time = (Get-Date) - $tierStartTime
                    }
                    Write-Host "  ✅ $scriptName completed successfully" -ForegroundColor Green
                } else {
                    Write-Host "  ❌ $scriptName did not produce output file" -ForegroundColor Red
                }
            }
            catch {
                Write-Host "  ❌ $scriptName failed: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "     Full error: $($_.Exception)" -ForegroundColor DarkRed
            }
        } else {
            Write-Host "  ⚠️  Script not found: $scriptPath" -ForegroundColor Yellow
        }
        
        # Rate limiting between scripts
        Start-Sleep -Seconds 2
    }
    
    return $tierResults
}

function Consolidate-TierResults {
    param([array]$AllTierResults)
    
    Write-Host "`n" + "=" * 80 -ForegroundColor Magenta
    Write-Host "CONSOLIDATING MULTI-TIER RESULTS" -ForegroundColor Magenta
    Write-Host "=" * 80 -ForegroundColor Magenta
    
    $consolidatedData = @{
        source = "comprehensive_multitier_scraping"
        description = "Consolidated PowerShell knowledge from all tiers of sources"
        scraped_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        tier_strategy = @{
            tier1 = "Microsoft Official Documentation - High-quality structured baseline"
            tier2 = "Community & GitHub - Real-world Q&A and script examples"
            tier3 = "Expert Blogs - Community experts and specialized tutorials"
        }
        entries = @()
        statistics = @{
            total_entries = 0
            tier1_entries = 0
            tier2_entries = 0
            tier3_entries = 0
            total_sources = 0
            avg_credibility = 0
            quality_distribution = @{}
            processing_summary = @{}
        }
        tier_results = $AllTierResults
    }
    
    $allEntries = @()
    $processingStats = @{}
    
    # Process results from each tier
    foreach ($tierResult in $AllTierResults) {
        $tierNumber = $tierResult.tier
        $tierName = $tierConfig[$tierNumber].name
        
        Write-Host "Processing Tier $tierNumber results..." -ForegroundColor Yellow
        
        $tierEntries = 0
        $tierSources = 0
        
        foreach ($scriptResult in $tierResult.results) {
            if ($scriptResult.results -and $scriptResult.results.entries) {
                $entries = $scriptResult.results.entries
                
                # Add tier information to each entry
                foreach ($entry in $entries) {
                    $entry.tier = [int]$tierNumber
                    $entry.tier_name = $tierName
                    $entry.source_script = $scriptResult.script
                    
                    # Ensure credibility is set
                    if (-not $entry.source.credibility) {
                        $entry.source.credibility = $tierConfig[$tierNumber].expected_credibility
                    }
                    
                    $allEntries += $entry
                    $tierEntries++
                }
                
                $tierSources++
            }
        }
        
        # Update tier statistics
        switch ($tierNumber) {
            "1" { $consolidatedData.statistics.tier1_entries = $tierEntries }
            "2" { $consolidatedData.statistics.tier2_entries = $tierEntries }
            "3" { $consolidatedData.statistics.tier3_entries = $tierEntries }
        }
        
        $processingStats["tier$tierNumber"] = @{
            entries = $tierEntries
            sources = $tierSources
            scripts_executed = $tierResult.results.Count
        }
        
        Write-Host "  Tier $tierNumber : $tierEntries entries from $tierSources sources" -ForegroundColor Green
    }
    
    # Calculate overall statistics
    $consolidatedData.entries = $allEntries
    $consolidatedData.statistics.total_entries = $allEntries.Count
    $consolidatedData.statistics.total_sources = ($processingStats.Values | ForEach-Object { $_.sources } | Measure-Object -Sum).Sum
    $consolidatedData.statistics.processing_summary = $processingStats
    
    if ($allEntries.Count -gt 0) {
        # Calculate average credibility
        $avgCredibility = ($allEntries | ForEach-Object { $_.source.credibility } | Measure-Object -Average).Average
        $consolidatedData.statistics.avg_credibility = [math]::Round($avgCredibility, 3)
        
        # Quality distribution
        $highQuality = ($allEntries | Where-Object { $_.source.credibility -ge 0.9 }).Count
        $mediumQuality = ($allEntries | Where-Object { $_.source.credibility -ge 0.7 -and $_.source.credibility -lt 0.9 }).Count
        $lowerQuality = ($allEntries | Where-Object { $_.source.credibility -lt 0.7 }).Count
        
        $consolidatedData.statistics.quality_distribution = @{
            high_credibility = @{ count = $highQuality; percentage = [math]::Round(($highQuality / $allEntries.Count) * 100, 1) }
            medium_credibility = @{ count = $mediumQuality; percentage = [math]::Round(($mediumQuality / $allEntries.Count) * 100, 1) }
            lower_credibility = @{ count = $lowerQuality; percentage = [math]::Round(($lowerQuality / $allEntries.Count) * 100, 1) }
        }
        
        # Tier distribution
        $consolidatedData.statistics.tier_distribution = @{
            tier1_percentage = [math]::Round(($consolidatedData.statistics.tier1_entries / $allEntries.Count) * 100, 1)
            tier2_percentage = [math]::Round(($consolidatedData.statistics.tier2_entries / $allEntries.Count) * 100, 1)
            tier3_percentage = [math]::Round(($consolidatedData.statistics.tier3_entries / $allEntries.Count) * 100, 1)
        }
    }
    
    # Save consolidated results
    $consolidatedData | ConvertTo-Json -Depth 15 | Out-File -FilePath $consolidatedOutputPath -Encoding UTF8
    
    return $consolidatedData
}

# Main execution logic
try {
    $overallStartTime = Get-Date
    $allTierResults = @()

    Write-Host "Starting comprehensive multi-tier PowerShell knowledge scraping..." -ForegroundColor Cyan
    Write-Host "Target tiers: $($Tiers -join ', ')" -ForegroundColor Gray
    Write-Host "Max items per tier: $MaxItemsPerTier" -ForegroundColor Gray
    Write-Host "Output directory: $OutputDirectory" -ForegroundColor Gray

    if ($GitHubToken) {
        Write-Host "GitHub token provided for enhanced API access" -ForegroundColor Green
    } else {
        Write-Host "No GitHub token - using anonymous access (limited)" -ForegroundColor Yellow
    }

    # Execute each requested tier
    foreach ($tier in $Tiers) {
        if ($tierConfig.ContainsKey($tier)) {
            Write-TierHeader -TierNumber $tier -TierInfo $tierConfig[$tier]

            $tierResults = Invoke-TierScraping -TierNumber $tier -TierInfo $tierConfig[$tier] -MaxItems $MaxItemsPerTier

            $allTierResults += @{
                tier = $tier
                results = $tierResults
                execution_time = (Get-Date) - $overallStartTime
            }

            Write-Host "`nTier $tier completed with $($tierResults.Count) script executions" -ForegroundColor Green
        } else {
            Write-Host "Unknown tier: $tier" -ForegroundColor Red
        }
    }

    # Consolidate results if requested
    if ($ConsolidateResults) {
        $consolidatedData = Consolidate-TierResults -AllTierResults $allTierResults

        # Display comprehensive results
        Write-Host "`n" + "=" * 80 -ForegroundColor Cyan
        Write-Host "COMPREHENSIVE MULTI-TIER SCRAPING COMPLETED!" -ForegroundColor Cyan
        Write-Host "=" * 80 -ForegroundColor Cyan

        $totalTime = (Get-Date) - $overallStartTime

        Write-Host "`nOverall Statistics:" -ForegroundColor Yellow
        Write-Host "  Total processing time: $([math]::Round($totalTime.TotalMinutes, 2)) minutes" -ForegroundColor White
        Write-Host "  Total entries collected: $($consolidatedData.statistics.total_entries)" -ForegroundColor White
        Write-Host "  Total sources processed: $($consolidatedData.statistics.total_sources)" -ForegroundColor White
        Write-Host "  Average credibility score: $($consolidatedData.statistics.avg_credibility)" -ForegroundColor White

        Write-Host "`nTier Distribution:" -ForegroundColor Yellow
        Write-Host "  Tier 1 (Microsoft Official): $($consolidatedData.statistics.tier1_entries) entries ($($consolidatedData.statistics.tier_distribution.tier1_percentage)%)" -ForegroundColor White
        Write-Host "  Tier 2 (Community/GitHub): $($consolidatedData.statistics.tier2_entries) entries ($($consolidatedData.statistics.tier_distribution.tier2_percentage)%)" -ForegroundColor White
        Write-Host "  Tier 3 (Expert Blogs): $($consolidatedData.statistics.tier3_entries) entries ($($consolidatedData.statistics.tier_distribution.tier3_percentage)%)" -ForegroundColor White

        Write-Host "`nQuality Distribution:" -ForegroundColor Yellow
        Write-Host "  High credibility (≥0.9): $($consolidatedData.statistics.quality_distribution.high_credibility.count) entries ($($consolidatedData.statistics.quality_distribution.high_credibility.percentage)%)" -ForegroundColor White
        Write-Host "  Medium credibility (0.7-0.9): $($consolidatedData.statistics.quality_distribution.medium_credibility.count) entries ($($consolidatedData.statistics.quality_distribution.medium_credibility.percentage)%)" -ForegroundColor White
        Write-Host "  Lower credibility (<0.7): $($consolidatedData.statistics.quality_distribution.lower_credibility.count) entries ($($consolidatedData.statistics.quality_distribution.lower_credibility.percentage)%)" -ForegroundColor White

        Write-Host "`nData Strategy Impact:" -ForegroundColor Yellow
        Write-Host "  ✅ Tier 1 provides high-quality structured baseline from Microsoft Learn" -ForegroundColor Green
        Write-Host "  ✅ Tier 2 adds real-world Q&A and script examples from community" -ForegroundColor Green
        Write-Host "  ✅ Tier 3 includes expert insights and specialized tutorials" -ForegroundColor Green
        Write-Host "  ✅ This combination dramatically improves conversational query understanding" -ForegroundColor Green

        Write-Host "`nOutput Files:" -ForegroundColor Yellow
        Write-Host "  Consolidated dataset: $consolidatedOutputPath" -ForegroundColor Green

        # List individual tier output files
        foreach ($tierResult in $allTierResults) {
            Write-Host "  Tier $($tierResult.tier) files:" -ForegroundColor Gray
            foreach ($scriptResult in $tierResult.results) {
                if ($scriptResult.output_path -and (Test-Path $scriptResult.output_path)) {
                    Write-Host "    - $($scriptResult.output_path)" -ForegroundColor DarkGray
                }
            }
        }

        Write-Host "`nNext Steps:" -ForegroundColor Yellow
        Write-Host "  1. Review the consolidated dataset for quality and completeness" -ForegroundColor Gray
        Write-Host "  2. Use the data for RAG model training or knowledge base creation" -ForegroundColor Gray
        Write-Host "  3. Consider running additional targeted scraping for specific topics" -ForegroundColor Gray
        Write-Host "  4. Set up automated periodic updates to keep the knowledge current" -ForegroundColor Gray

        Write-Host "`n🎉 Multi-tier PowerShell knowledge scraping strategy successfully implemented!" -ForegroundColor Green
    } else {
        Write-Host "`nTier scraping completed without consolidation" -ForegroundColor Yellow
        Write-Host "Individual tier results saved to: $OutputDirectory" -ForegroundColor Gray
    }
}
catch {
    Write-Host "`nComprehensive multi-tier scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Full error: $($_.Exception)" -ForegroundColor Red
    exit 1
}
