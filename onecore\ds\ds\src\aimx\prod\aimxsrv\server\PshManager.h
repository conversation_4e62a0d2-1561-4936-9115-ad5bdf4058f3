/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    PshManager.h

Abstract:

    PowerShell manager for execution and command search via NetRag service.

--*/

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include "nlohmann/json.hpp"

// PowerShell execution result
struct POWERSHELL_EXECUTION_RESULT
{
    bool success;
    nlohmann::json result;
    std::wstring errorMessage;
};

// PowerShell command search result with full context
struct POWERSHELL_COMMAND_SEARCH_RESULT
{
    std::wstring commandName;
    std::wstring fullText;
    std::wstring parameterNames;
    std::wstring id;
    int parameterCount;
    int exampleCount;
    double score;
    nlohmann::json metadata;
};

class PshManager
{
public:
    // Execute PowerShell tool via HTTP to NetRag service
    static HRESULT ExecuteTool(
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _Out_ POWERSHELL_EXECUTION_RESULT& result
        );

    // Search for PowerShell commands with full context using semantic similarity
    static HRESULT SearchPowerShellCommands(
        _In_ const std::wstring& query,
        _In_ int topK,
        _Out_ std::vector<POWERSHELL_COMMAND_SEARCH_RESULT>& results
        );

private:
    // Build PowerShell command from tool name and parameters
    static HRESULT BuildPowerShellCommand(
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _Out_ std::wstring& command
        );

    // Parse NetRag HTTP response
    static HRESULT ParseNetRagResponse(
        _In_ const nlohmann::json& httpResponse,
        _Out_ POWERSHELL_EXECUTION_RESULT& result
        );

    // Execute PowerShell command via HTTP to NetRag service
    static HRESULT ExecutePowerShellViaHttp(
        _In_ const std::wstring& command,
        _In_ int timeoutSeconds,
        _Out_ nlohmann::json& response
        );

    // Send HTTP GET request to NetRag service
    static HRESULT SendHttpGetRequest(
        _In_ const std::wstring& endpoint,
        _Out_ nlohmann::json& response
        );

    // Send HTTP POST request to NetRag service
    static HRESULT SendHttpPostRequest(
        _In_ const std::wstring& endpoint,
        _In_ const nlohmann::json& requestBody,
        _Out_ nlohmann::json& response
        );
};
