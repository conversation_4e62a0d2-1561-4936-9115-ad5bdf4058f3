# Quick test script to verify NetRag service integration
param(
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "Testing NetRag Service Integration" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "=" * 50

# Test 1: Health Check
Write-Host "`n1. Testing Health Check..." -ForegroundColor Cyan
try {
    $healthResponse = Invoke-RestMethod -Uri "$BaseUrl/api/powershellcommand/health" -Method GET -TimeoutSec 5
    Write-Host "[PASS] Health Check" -ForegroundColor Green
    Write-Host "Status: $($healthResponse.status)" -ForegroundColor White
    Write-Host "Vector Count: $($healthResponse.vector_count)" -ForegroundColor White
}
catch {
    Write-Host "[FAIL] Health Check: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure NetRag service is running on $BaseUrl" -ForegroundColor Yellow
    return
}

# Test 2: Get Command Names
Write-Host "`n2. Testing Command Names Endpoint..." -ForegroundColor Cyan
try {
    $namesResponse = Invoke-RestMethod -Uri "$BaseUrl/api/powershellcommand/names" -Method GET -TimeoutSec 10
    Write-Host "[PASS] Command Names Retrieved" -ForegroundColor Green
    Write-Host "Total Commands: $($namesResponse.Count)" -ForegroundColor White
    Write-Host "First 5 Commands:" -ForegroundColor White
    $namesResponse | Select-Object -First 5 | ForEach-Object { Write-Host "  - $_" -ForegroundColor Gray }
}
catch {
    Write-Host "[FAIL] Command Names: $($_.Exception.Message)" -ForegroundColor Red
    return
}

# Test 3: Search for a specific command
Write-Host "`n3. Testing Search Endpoint..." -ForegroundColor Cyan
$testQuery = "Get-ADUser"
try {
    $searchUrl = "$BaseUrl/api/powershellcommand/search?query=$([System.Web.HttpUtility]::UrlEncode($testQuery))&limit=1"
    $searchResponse = Invoke-RestMethod -Uri $searchUrl -Method GET -TimeoutSec 10
    
    Write-Host "[PASS] Search Completed" -ForegroundColor Green
    Write-Host "Query: '$testQuery'" -ForegroundColor White
    Write-Host "Results Found: $($searchResponse.Count)" -ForegroundColor White
    
    if ($searchResponse.Count -gt 0) {
        $result = $searchResponse[0]
        Write-Host "Command: $($result.commandName)" -ForegroundColor White
        Write-Host "Score: $([math]::Round($result.score, 3))" -ForegroundColor White
        Write-Host "Parameters: $($result.parameterCount)" -ForegroundColor White
        Write-Host "ID: $($result.id)" -ForegroundColor White
    }
}
catch {
    Write-Host "[FAIL] Search: $($_.Exception.Message)" -ForegroundColor Red
    return
}

Write-Host "`n" + "=" * 50
Write-Host "NetRag Service Integration Test Completed!" -ForegroundColor Green
Write-Host "All tests passed - NetRag service is accessible and working" -ForegroundColor Green
