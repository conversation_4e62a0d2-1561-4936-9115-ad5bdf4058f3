<#
.SYNOPSIS
    Quick test script for MCP Tools RAG Service
    
.DESCRIPTION
    A simplified test script that performs basic validation of the MCP RAG Service
    
.PARAMETER BaseUrl
    Base URL of the service (default: http://localhost:5000)
#>

[CmdletBinding()]
param(
    [string]$BaseUrl = "http://localhost:5000"
)

$ErrorActionPreference = "Continue"

function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Method,
        [string]$Endpoint,
        [object]$Body = $null
    )
    
    Write-Host "`n[TEST] Testing: $Name" -ForegroundColor Yellow
    
    try {
        $uri = "$BaseUrl$Endpoint"
        $headers = @{"Content-Type" = "application/json"}
        
        $params = @{
            Uri = $uri
            Method = $Method
            Headers = $headers
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json -Depth 5)
        }
        
        $response = Invoke-RestMethod @params
        Write-Host "[SUCCESS]" -ForegroundColor Green
        
        # Show relevant response data
        if ($response.status) {
            Write-Host "   Status: $($response.status)" -ForegroundColor Cyan
        }
        if ($response.message) {
            Write-Host "   Message: $($response.message)" -ForegroundColor Cyan
        }
        if ($response.tools -and $response.tools.Count -gt 0) {
            Write-Host "   Found $($response.tools.Count) tools" -ForegroundColor Cyan
        }
        if ($response.totalTools) {
            Write-Host "   Total tools: $($response.totalTools)" -ForegroundColor Cyan
        }
        
        return $true
    }
    catch {
        Write-Host "[FAILED]: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

Write-Host "Quick Test for MCP Tools RAG Service" -ForegroundColor Magenta
Write-Host "Service URL: $BaseUrl" -ForegroundColor Cyan

# Test 1: Health Check
$healthOk = Test-Endpoint -Name "Health Check" -Method "GET" -Endpoint "/api/mcptools/health"

if (-not $healthOk) {
    Write-Host "`n[ERROR] Service is not responding. Make sure it's running at $BaseUrl" -ForegroundColor Red
    exit 1
}

# Test 2: Register a test server
$serverData = @{
    Id = "12345678-1234-1234-1234-123456789abc"
    Name = "Quick Test Server"
    Description = "Test server for validation"
    Version = "1.0.0"
}

Test-Endpoint -Name "Server Registration" -Method "POST" -Endpoint "/api/mcpservers/register" -Body $serverData

# Test 3: Register a test tool
$toolData = @{
    Id = "12345678-1234-1234-1234-123456789abc_87654321-4321-4321-4321-cba987654321"
    Name = "Test-Tool"
    Description = "A test tool for finding user information in the directory"
    Category = "Testing"
    Examples = @("Find user by username", "Get user details")
    Tags = @("test", "user", "directory")
}

Test-Endpoint -Name "Tool Registration" -Method "POST" -Endpoint "/api/mcptools/register" -Body $toolData

# Test 4: Search for tools
$searchData = @{
    Query = "find user information"
    TopK = 5
}

Test-Endpoint -Name "Tool Search" -Method "POST" -Endpoint "/api/mcptools/search" -Body $searchData

# Test 5: Get statistics
Test-Endpoint -Name "Tool Statistics" -Method "GET" -Endpoint "/api/mcptools/statistics"

Write-Host "`nQuick test completed!" -ForegroundColor Magenta
Write-Host "If all tests passed, your MCP RAG Service is working correctly." -ForegroundColor Green
