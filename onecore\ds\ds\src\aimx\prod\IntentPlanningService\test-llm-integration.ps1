# Quick test script to verify LLM service integration
param(
    [string]$BaseUrl = "http://************:5273"
)

Write-Host "Testing LLM Service Integration" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "=" * 50

# Test LLM service
Write-Host "`n1. Testing LLM Service..." -ForegroundColor Cyan
try {
    $requestBody = @{
        model = "Phi-4-mini-instruct-cuda-gpu"
        messages = @(
            @{ role = "system"; content = "You are a helpful assistant." }
            @{ role = "user"; content = "Is this an IT administration request? 'Create a new user account for <PERSON>'. Respond with only YES or NO." }
        )
        max_tokens = 4096
        temperature = 0.7
        top_k = 40
        top_p = 0.9
        stream = $false
    } | ConvertTo-Json -Depth 10

    $response = Invoke-RestMethod -Uri "$BaseUrl/v1/chat/completions" -Method POST -Body $requestBody -ContentType "application/json" -TimeoutSec 10
    
    Write-Host "[PASS] LLM Service Responded" -ForegroundColor Green
    $content = $response.choices[0].message.content
    Write-Host "Response: $content" -ForegroundColor White
}
catch [System.Net.WebException] {
    Write-Host "[FAIL] LLM Service Connection Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "The LLM service at $BaseUrl may not be accessible" -ForegroundColor Yellow
}
catch {
    Write-Host "[FAIL] LLM Service Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error Type: $($_.Exception.GetType().Name)" -ForegroundColor Yellow
}

Write-Host "`n" + "=" * 50
Write-Host "LLM Service Integration Test Completed!" -ForegroundColor Green
