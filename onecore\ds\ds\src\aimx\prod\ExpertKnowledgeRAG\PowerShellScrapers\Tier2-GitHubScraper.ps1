# Tier 2: GitHub Repository Scraper for PowerShell Content
# Scrapes PowerShell scripts, modules, and documentation from highly-starred GitHub repositories
param(
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "./ScrapedData/tier2_github_$(Get-Date -Format 'yyyyMMdd_HHmmss').json",
    [Parameter(Mandatory = $false)]
    [int]$MaxRepos = 50,
    [Parameter(Mandatory = $false)]
    [int]$MaxFilesPerRepo = 20
)

Write-Host "Tier 2: GitHub Repository Scraper" -ForegroundColor Cyan
Write-Host "Targeting PowerShell scripts, modules, and documentation" -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Gray

# GitHub API configuration
$githubApiBase = "https://api.github.com"
$headers = @{
    "Accept" = "application/vnd.github.v3+json"
    "User-Agent" = "PowerShell-Knowledge-Scraper"
}

# Target repositories and organizations
$targetOrgs = @(
    "microsoft", "powershell", "azure", "microsoftdocs"
)

$targetTopics = @(
    "powershell", "powershell-script", "automation", "active-directory", 
    "powershell-module", "windows-administration", "system-administration"
)

function Get-PowerShellRepositories {
    param([int]$MaxResults = 50)
    
    Write-Host "Searching for PowerShell repositories..." -ForegroundColor Yellow
    
    $allRepos = @()
    
    # Search by language and topics
    foreach ($topic in $targetTopics) {
        try {
            $searchUrl = "$githubApiBase/search/repositories?q=language:powershell+topic:$topic&sort=stars&order=desc&per_page=20"
            Write-Host "  Searching topic: $topic" -ForegroundColor Gray
            
            $response = Invoke-RestMethod -Uri $searchUrl -Headers $headers -ErrorAction Stop
            
            foreach ($repo in $response.items) {
                if ($repo.stargazers_count -ge 10 -and $repo.size -gt 0) {
                    $allRepos += @{
                        name = $repo.name
                        full_name = $repo.full_name
                        description = $repo.description
                        stars = $repo.stargazers_count
                        url = $repo.html_url
                        api_url = $repo.url
                        topics = $repo.topics
                        language = $repo.language
                        size = $repo.size
                        updated_at = $repo.updated_at
                    }
                }
            }
            
            Start-Sleep -Milliseconds 500  # Rate limiting
        }
        catch {
            Write-Host "  Failed to search topic $topic`: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Search in target organizations
    foreach ($org in $targetOrgs) {
        try {
            $orgUrl = "$githubApiBase/orgs/$org/repos?type=public&sort=stars&per_page=30"
            Write-Host "  Searching organization: $org" -ForegroundColor Gray
            
            $response = Invoke-RestMethod -Uri $orgUrl -Headers $headers -ErrorAction Stop
            
            foreach ($repo in $response) {
                if ($repo.language -eq "PowerShell" -or 
                    ($repo.topics -and ($repo.topics | Where-Object { $_ -in $targetTopics })) -or
                    $repo.name -match "powershell|ps1|automation") {
                    
                    $allRepos += @{
                        name = $repo.name
                        full_name = $repo.full_name
                        description = $repo.description
                        stars = $repo.stargazers_count
                        url = $repo.html_url
                        api_url = $repo.url
                        topics = $repo.topics
                        language = $repo.language
                        size = $repo.size
                        updated_at = $repo.updated_at
                    }
                }
            }
            
            Start-Sleep -Milliseconds 500  # Rate limiting
        }
        catch {
            Write-Host "  Failed to search org $org`: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Remove duplicates and sort by stars
    $uniqueRepos = $allRepos | Sort-Object full_name -Unique | Sort-Object stars -Descending
    $topRepos = $uniqueRepos | Select-Object -First $MaxResults
    
    Write-Host "Found $($uniqueRepos.Count) unique repositories, selecting top $($topRepos.Count)" -ForegroundColor Green
    return $topRepos
}

function Get-PowerShellFiles {
    param(
        [string]$RepoFullName,
        [int]$MaxFiles = 20
    )
    
    try {
        # Search for PowerShell files in the repository
        $searchUrl = "$githubApiBase/search/code?q=repo:$RepoFullName+extension:ps1+OR+extension:psm1+OR+filename:README.md&per_page=$MaxFiles"
        $response = Invoke-RestMethod -Uri $searchUrl -Headers $headers -ErrorAction Stop
        
        $files = @()
        foreach ($file in $response.items) {
            if ($file.name -match '\.(ps1|psm1)$' -or $file.name -eq "README.md") {
                $files += @{
                    name = $file.name
                    path = $file.path
                    download_url = $file.download_url
                    html_url = $file.html_url
                    type = if ($file.name -match '\.ps1$') { "script" } 
                           elseif ($file.name -match '\.psm1$') { "module" } 
                           else { "documentation" }
                }
            }
        }
        
        return $files
    }
    catch {
        Write-Host "    Failed to get files: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

function Get-FileContent {
    param(
        [string]$DownloadUrl,
        [string]$FileName
    )
    
    try {
        $content = Invoke-RestMethod -Uri $DownloadUrl -Headers $headers -ErrorAction Stop
        
        # Clean and validate content
        if ($content -and $content.Length -gt 50) {
            return $content
        }
        return $null
    }
    catch {
        Write-Host "      Failed to download $FileName`: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Extract-PowerShellCmdlets {
    param([string]$Content)
    
    $cmdletPattern = '\b(Get-|Set-|New-|Remove-|Add-|Update-|Start-|Stop-|Enable-|Disable-|Test-|Import-|Export-|Connect-|Disconnect-)[A-Za-z][A-Za-z0-9]*\b'
    $matches = [regex]::Matches($Content, $cmdletPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    $cmdlets = $matches | ForEach-Object { $_.Value } | Sort-Object -Unique
    return $cmdlets
}

function Extract-CodeBlocks {
    param([string]$Content)
    
    $codeBlocks = @()
    
    # PowerShell code blocks in markdown
    $markdownPattern = '```(?:powershell|ps1)?\s*\n(.*?)\n```'
    $matches = [regex]::Matches($Content, $markdownPattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    foreach ($match in $matches) {
        $code = $match.Groups[1].Value.Trim()
        if ($code.Length -gt 20) {
            $codeBlocks += $code
        }
    }
    
    # For .ps1/.psm1 files, treat entire content as code if it's valid PowerShell
    if ($Content -match '\$|Get-|Set-|New-|function|param\(' -and $Content.Length -gt 50) {
        $codeBlocks += $Content
    }
    
    return $codeBlocks
}

# Main scraping logic
try {
    $scrapedData = @{
        source = "github"
        tier = 2
        description = "PowerShell scripts, modules, and documentation from GitHub repositories"
        scraped_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        total_repositories = 0
        total_files = 0
        entries = @()
        statistics = @{
            repositories_processed = 0
            files_processed = 0
            scripts_found = 0
            modules_found = 0
            documentation_found = 0
            total_cmdlets = 0
            avg_content_length = 0
        }
    }
    
    # Get target repositories
    $repositories = Get-PowerShellRepositories -MaxResults $MaxRepos
    $scrapedData.total_repositories = $repositories.Count
    
    $allEntries = @()
    $processedFiles = 0
    $totalContentLength = 0
    
    foreach ($repo in $repositories) {
        Write-Host "Processing repository: $($repo.full_name) ($($repo.stars) stars)" -ForegroundColor Yellow
        
        # Get PowerShell files from the repository
        $files = Get-PowerShellFiles -RepoFullName $repo.full_name -MaxFiles $MaxFilesPerRepo
        
        if ($files.Count -eq 0) {
            Write-Host "  No PowerShell files found" -ForegroundColor Gray
            continue
        }
        
        Write-Host "  Found $($files.Count) PowerShell files" -ForegroundColor Green
        
        foreach ($file in $files) {
            Write-Host "    Processing: $($file.name)" -ForegroundColor Gray
            
            $content = Get-FileContent -DownloadUrl $file.download_url -FileName $file.name
            
            if ($content) {
                $cmdlets = Extract-PowerShellCmdlets -Content $content
                $codeBlocks = Extract-CodeBlocks -Content $content
                
                $entry = @{
                    id = "github_$($repo.full_name.Replace('/', '_'))_$($file.name.Replace('.', '_'))"
                    title = "$($file.name) - $($repo.name)"
                    content = $content
                    source = @{
                        url = $file.html_url
                        type = "github"
                        repository = $repo.full_name
                        file_type = $file.type
                        credibility = [math]::Min(1.0, [math]::Log10($repo.stars + 1) / 4)
                    }
                    cmdlets = $cmdlets
                    code_examples = $codeBlocks
                    tags = @("github", "powershell", $file.type) + $repo.topics
                    metadata = @{
                        repository_stars = $repo.stars
                        repository_description = $repo.description
                        file_path = $file.path
                        repository_updated = $repo.updated_at
                    }
                    last_updated = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
                }
                
                $allEntries += $entry
                $processedFiles++
                $totalContentLength += $content.Length
                
                # Update statistics
                switch ($file.type) {
                    "script" { $scrapedData.statistics.scripts_found++ }
                    "module" { $scrapedData.statistics.modules_found++ }
                    "documentation" { $scrapedData.statistics.documentation_found++ }
                }
            }
            
            Start-Sleep -Milliseconds 200  # Rate limiting
        }
        
        $scrapedData.statistics.repositories_processed++
        Start-Sleep -Milliseconds 1000  # Rate limiting between repos
    }
    
    # Finalize statistics
    $scrapedData.entries = $allEntries
    $scrapedData.total_files = $processedFiles
    $scrapedData.statistics.files_processed = $processedFiles
    $scrapedData.statistics.avg_content_length = if ($processedFiles -gt 0) { [math]::Round($totalContentLength / $processedFiles) } else { 0 }
    
    $allCmdlets = $allEntries | ForEach-Object { $_.cmdlets } | Sort-Object -Unique
    $scrapedData.statistics.total_cmdlets = $allCmdlets.Count
    
    # Save the scraped data
    $scrapedData | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "`nTier 2 GitHub scraping completed!" -ForegroundColor Green
    Write-Host "=" * 60 -ForegroundColor Gray
    Write-Host "Statistics:" -ForegroundColor Cyan
    Write-Host "  Repositories processed: $($scrapedData.statistics.repositories_processed)" -ForegroundColor White
    Write-Host "  Files processed: $($scrapedData.statistics.files_processed)" -ForegroundColor White
    Write-Host "  Scripts found: $($scrapedData.statistics.scripts_found)" -ForegroundColor White
    Write-Host "  Modules found: $($scrapedData.statistics.modules_found)" -ForegroundColor White
    Write-Host "  Documentation found: $($scrapedData.statistics.documentation_found)" -ForegroundColor White
    Write-Host "  Unique cmdlets: $($scrapedData.statistics.total_cmdlets)" -ForegroundColor White
    Write-Host "  Average content length: $($scrapedData.statistics.avg_content_length) characters" -ForegroundColor White
    Write-Host "  Output saved to: $OutputPath" -ForegroundColor Green
}
catch {
    Write-Host "GitHub scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Full error: $($_.Exception)" -ForegroundColor Red
    exit 1
}
