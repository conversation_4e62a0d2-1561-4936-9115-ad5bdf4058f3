namespace NetRagService;

/// <summary>
/// Configuration for the RAG service - all values read from Windows Registry
/// Registry Location: HKLM\SYSTEM\CurrentControlSet\Services\AIMXSrv\Parameters
/// </summary>
public class RagConfiguration
{
    // Foundry Local Configuration
    public string FoundryLocalBaseUrl { get; set; } = "http://localhost:5273/v1";
    public string FoundryLocalModelName { get; set; } = "Phi-3.5-mini-instruct-cuda-gpu";
    public string FoundryLocalServiceId { get; set; } = "phi-3-mini";

    // Embedding Configuration - use paths relative to executable location
    public string EmbeddingModelPath { get; set; } = GetExecutableRelativePath("model.onnx");
    public string EmbeddingVocabPath { get; set; } = GetExecutableRelativePath("vocab.txt");
    public int EmbeddingVectorSize { get; set; } = 768;

    // Document Ingestion Configuration
    public int DocumentChunkSize { get; set; } = 300;
    public int DocumentChunkOverlap { get; set; } = 60;
    public int DocumentSearchLimit { get; set; } = 5;

    // Vector Store Configuration
    public int VectorStoreInitialCapacity { get; set; } = 10000;
    public bool VectorStoreEnableStatistics { get; set; } = true;
    public string VectorStorePersistenceFilePath { get; set; } = GetExecutableRelativePath("database.bin");

    // MCP Service Configuration
    public string McpServiceHost { get; set; } = "localhost";
    public int McpServicePort { get; set; } = 5000;
    public int McpServiceDefaultTopK { get; set; } = 10;
    public int McpServiceMaxTopK { get; set; } = 100;
    public float McpServiceDefaultMinScore { get; set; } = 0.0f;
    public bool McpServiceEnableCors { get; set; } = true;

    // Backend Service Configuration
    public string BackendServiceUrl { get; set; } = "http://localhost:5000";
    public int BackendServiceTimeout { get; set; } = 30;



    // Helper properties for backward compatibility with existing code
    public FoundryLocalConfig FoundryLocal => new()
    {
        BaseUrl = FoundryLocalBaseUrl,
        ModelName = FoundryLocalModelName,
        ServiceId = FoundryLocalServiceId
    };

    public EmbeddingConfig Embedding => new()
    {
        ModelPath = EmbeddingModelPath,
        VocabPath = EmbeddingVocabPath,
        VectorSize = EmbeddingVectorSize
    };

    public DocumentIngestionConfig DocumentIngestion => new()
    {
        ChunkSize = DocumentChunkSize,
        ChunkOverlap = DocumentChunkOverlap,
        SearchLimit = DocumentSearchLimit
    };

    public InMemoryVectorStoreConfig InMemoryVectorStore => new()
    {
        InitialCapacity = VectorStoreInitialCapacity,
        EnableStatistics = VectorStoreEnableStatistics,
        PersistenceFilePath = VectorStorePersistenceFilePath
    };

    public McpServiceConfig McpService => new()
    {
        Host = McpServiceHost,
        Port = McpServicePort,
        DefaultTopK = McpServiceDefaultTopK,
        MaxTopK = McpServiceMaxTopK,
        DefaultMinScore = McpServiceDefaultMinScore,
        EnableCors = McpServiceEnableCors,
        BackendServiceUrl = BackendServiceUrl,
        BackendServiceTimeout = BackendServiceTimeout
    };



    /// <summary>
    /// Helper method to get file paths relative to the executable location
    /// </summary>
    /// <param name="fileName">The file name to resolve</param>
    /// <returns>Full path relative to the executable directory</returns>
    private static string GetExecutableRelativePath(string fileName)
    {
        // Get the directory where the executable is located
        var executablePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
        var executableDirectory = Path.GetDirectoryName(executablePath);

        if (string.IsNullOrEmpty(executableDirectory))
        {
            // Fallback to current directory if we can't determine executable location
            return Path.Combine(".", fileName);
        }

        return Path.Combine(executableDirectory, fileName);
    }
}

public class FoundryLocalConfig
{
    public string BaseUrl { get; set; } = "http://localhost:5273/v1";
    public string ModelName { get; set; } = "qwen2.5-0.5b-instruct-generic-gpu";
    public string ServiceId { get; set; } = "qwen2.5-0.5b";
}

public class InMemoryVectorStoreConfig
{
    public int InitialCapacity { get; set; } = 10000;
    public bool EnableStatistics { get; set; } = true;
    public string PersistenceFilePath { get; set; } = "database.bin";
}

public class EmbeddingConfig
{
    public string ModelPath { get; set; } = @"C:\ProgramData\Microsoft\aimx\Models\jina\model.onnx";
    public string VocabPath { get; set; } = @"C:\ProgramData\Microsoft\aimx\Models\jina\vocab.txt";
    public int VectorSize { get; set; } = 768;
}

public class DocumentIngestionConfig
{
    public int ChunkSize { get; set; } = 300;
    public int ChunkOverlap { get; set; } = 60;
    public int SearchLimit { get; set; } = 5;
}

public class McpServiceConfig
{
    public string Host { get; set; } = "localhost";
    public int Port { get; set; } = 5000;
    public int DefaultTopK { get; set; } = 10;
    public int MaxTopK { get; set; } = 100;
    public float DefaultMinScore { get; set; } = 0.0f;
    public bool EnableCors { get; set; } = true;

    // Registry-configurable properties (automatically bound from registry keys)
    public string BackendServiceUrl { get; set; } = "http://localhost:5000";
    public int BackendServiceTimeout { get; set; } = 30;
}


