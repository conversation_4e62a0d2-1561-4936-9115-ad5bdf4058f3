# Debug script to check what RAG is actually returning for each step
param(
    [string]$BaseUrl = "http://localhost:5000"
)

Write-Host "=== DEBUGGING RAG SEARCH RESULTS ===" -ForegroundColor Red
Write-Host "Checking what RAG actually returns for user creation steps" -ForegroundColor Yellow

$testSteps = @(
    "Create new user account for <PERSON>",
    "Add <PERSON> to Sales group", 
    "Set initial password for <PERSON>",
    "Confirm <PERSON>'s account creation",
    "Verify <PERSON>'s group membership"
)

foreach ($step in $testSteps) {
    Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
    Write-Host "STEP: $step" -ForegroundColor Cyan
    Write-Host "=" * 60
    
    try {
        # Test TOP 1 result (current implementation)
        Write-Host "`nTOP 1 Result:" -ForegroundColor Yellow
        $encodedQuery = [System.Uri]::EscapeDataString($step)
        $searchUrl = "$BaseUrl/api/powershellcommand/search?query=$encodedQuery&limit=1"
        Write-Host "URL: $searchUrl" -ForegroundColor Gray
        
        $top1Response = Invoke-RestMethod -Uri $searchUrl -Method GET
        if ($top1Response -and $top1Response.Count -gt 0) {
            $result = $top1Response[0]
            Write-Host "  Command: $($result.commandName)" -ForegroundColor White
            Write-Host "  Score: $([math]::Round($result.score, 4))" -ForegroundColor Gray
        } else {
            Write-Host "  No results found" -ForegroundColor Red
        }
        
        # Test TOP 3 results (proposed new implementation)
        Write-Host "`nTOP 3 Results:" -ForegroundColor Yellow
        $encodedQuery3 = [System.Uri]::EscapeDataString($step)
        $searchUrl3 = "$BaseUrl/api/powershellcommand/search?query=$encodedQuery3&limit=3"
        Write-Host "URL: $searchUrl3" -ForegroundColor Gray
        
        $top3Response = Invoke-RestMethod -Uri $searchUrl3 -Method GET
        if ($top3Response -and $top3Response.Count -gt 0) {
            for ($i = 0; $i -lt $top3Response.Count; $i++) {
                $result = $top3Response[$i]
                Write-Host "  $($i + 1). $($result.commandName) (Score: $([math]::Round($result.score, 4)))" -ForegroundColor White
            }
        } else {
            Write-Host "  No results found" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "  ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 200
}

Write-Host "`n" + "=" * 60 -ForegroundColor Green
Write-Host "ANALYSIS" -ForegroundColor Green
Write-Host "=" * 60
Write-Host "Now we can see what RAG is actually returning vs what the LLM is generating!" -ForegroundColor Yellow
Write-Host "The correct flow should be:" -ForegroundColor White
Write-Host "  1. Get TOP 3 results from RAG for each step" -ForegroundColor White
Write-Host "  2. Ask LLM to pick the best command from the 3 options" -ForegroundColor White
Write-Host "  3. Fetch full command details from JSON dataset" -ForegroundColor White
Write-Host "  4. Ask LLM to generate final command with proper parameters" -ForegroundColor White
