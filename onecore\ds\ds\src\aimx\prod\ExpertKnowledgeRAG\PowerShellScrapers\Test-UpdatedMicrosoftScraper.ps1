# Test the updated Microsoft Docs scraper
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing Updated Microsoft Docs Scraper" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    Write-Host "✅ Core module imported" -ForegroundColor Green

    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    if ($result) {
        Write-Host "✅ Framework initialized" -ForegroundColor Green
    } else {
        Write-Host "❌ Framework initialization failed" -ForegroundColor Red
        exit 1
    }

    # Test the Get-ADCmdletList function from the scraper
    Write-Host "`nTesting cmdlet list extraction..." -ForegroundColor Yellow
    
    # Load the scraper functions
    . (Join-Path $PSScriptRoot "Scrapers/MicrosoftDocsScraper.ps1")
    
    # Test cmdlet list extraction
    $cmdlets = Get-ADCmdletList
    
    Write-Host "✅ Found $($cmdlets.Count) cmdlets" -ForegroundColor Green
    
    # Show first 10 cmdlets
    Write-Host "`nFirst 10 cmdlets:" -ForegroundColor Yellow
    $count = 0
    foreach ($cmdlet in $cmdlets) {
        if ($count -ge 10) { break }
        Write-Host "  $($cmdlet.Name) -> $($cmdlet.Domain)/$($cmdlet.Operation)" -ForegroundColor Gray
        Write-Host "    URL: $($cmdlet.Url)" -ForegroundColor DarkGray
        $count++
    }
    
    if ($cmdlets.Count -gt 10) {
        Write-Host "  ... and $($cmdlets.Count - 10) more" -ForegroundColor Gray
    }

    # Test scraping a specific cmdlet
    if ($cmdlets.Count -gt 0) {
        Write-Host "`nTesting cmdlet documentation scraping..." -ForegroundColor Yellow
        $testCmdlet = $cmdlets[0]
        Write-Host "  Testing: $($testCmdlet.Name)" -ForegroundColor Gray
        Write-Host "  URL: $($testCmdlet.Url)" -ForegroundColor Gray
        
        try {
            $pattern = Scrape-CmdletDocumentation -Cmdlet $testCmdlet
            if ($pattern) {
                Write-Host "✅ Cmdlet documentation scraped successfully" -ForegroundColor Green
                Write-Host "  Pattern ID: $($pattern.Id)" -ForegroundColor Gray
                Write-Host "  Title: $($pattern.Title)" -ForegroundColor Gray
                Write-Host "  Content length: $($pattern.Content.Length) characters" -ForegroundColor Gray
                Write-Host "  Credibility: $($pattern.CredibilityScore)" -ForegroundColor Gray
                
                # Check if code examples were extracted
                if ($pattern.CodeTemplate -and $pattern.CodeTemplate.Length -gt 0) {
                    Write-Host "  ✅ Code examples extracted" -ForegroundColor Green
                } else {
                    Write-Host "  ⚠️  No code examples found" -ForegroundColor Yellow
                }
            } else {
                Write-Host "❌ Failed to scrape cmdlet documentation" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "❌ Error scraping cmdlet: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # Test with specific cmdlets
    Write-Host "`nTesting with specific cmdlets..." -ForegroundColor Yellow
    $specificCmdlets = @("Get-ADUser", "Set-ADUser", "New-ADUser")
    
    foreach ($cmdletName in $specificCmdlets) {
        $foundCmdlet = $cmdlets | Where-Object { $_.Name -eq $cmdletName }
        if ($foundCmdlet) {
            Write-Host "  ✅ Found: $cmdletName" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Missing: $cmdletName" -ForegroundColor Red
        }
    }

    Write-Host "`n✅ Updated Microsoft Docs scraper test completed!" -ForegroundColor Green
}
catch {
    Write-Host "`n❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    exit 1
}
