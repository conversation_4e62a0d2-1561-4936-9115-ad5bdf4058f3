# Enhanced Microsoft Docs Scraper - Captures FULL offline content
# Scrapes complete cmdlet documentation, parameters, examples, syntax
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxCmdlets = 50,
    
    [Parameter(Mandatory = $false)]
    [string[]]$SpecificCmdlets = @()
)

# Helper function to extract full cmdlet documentation
function Extract-FullCmdletDocumentation {
    param(
        [string]$Content,
        [string]$CmdletName,
        [string]$Url
    )

    $result = @{
        FullContent = $Content
        Synopsis = ""
        Description = ""
        Parameters = @()
        Examples = @()
        Syntax = @()
        Notes = ""
        RelatedLinks = @()
        PrimaryExample = ""
        RequiredParameters = @()
        BestPractices = @()
        CommonMistakes = @()
    }

    try {
        # Extract synopsis
        $synopsisMatch = [regex]::Match($Content, '<h2[^>]*>Synopsis</h2>\s*<p[^>]*>([^<]+)</p>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($synopsisMatch.Success) {
            $result.Synopsis = $synopsisMatch.Groups[1].Value.Trim()
        }

        # Extract description
        $descMatch = [regex]::Match($Content, '<h2[^>]*>Description</h2>\s*<p[^>]*>([^<]+)</p>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($descMatch.Success) {
            $result.Description = $descMatch.Groups[1].Value.Trim()
        }

        # Extract all code examples
        $codePattern = '<pre[^>]*><code[^>]*>([^<]+)</code></pre>'
        $codeMatches = [regex]::Matches($Content, $codePattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Singleline)

        foreach ($match in $codeMatches) {
            $codeText = [System.Web.HttpUtility]::HtmlDecode($match.Groups[1].Value).Trim()
            if ($codeText -and $codeText.Length -gt 5) {
                $result.Examples += $codeText
            }
        }

        # Set primary example
        if ($result.Examples.Count -gt 0) {
            $result.PrimaryExample = $result.Examples[0]
        }

        # Extract parameter information
        $paramPattern = '<h3[^>]*>-([^<]+)</h3>'
        $paramMatches = [regex]::Matches($Content, $paramPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)

        foreach ($match in $paramMatches) {
            $paramName = $match.Groups[1].Value.Trim()
            if ($paramName -and $paramName.Length -gt 0) {
                $result.Parameters += $paramName
            }
        }

        # Extract syntax information
        $syntaxPattern = '<h2[^>]*>Syntax</h2>.*?<pre[^>]*><code[^>]*>([^<]+)</code></pre>'
        $syntaxMatch = [regex]::Match($Content, $syntaxPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Singleline)
        if ($syntaxMatch.Success) {
            $syntaxText = [System.Web.HttpUtility]::HtmlDecode($syntaxMatch.Groups[1].Value).Trim()
            $result.Syntax += $syntaxText
        }

        return $result
    }
    catch {
        Write-Host "Error extracting documentation for $CmdletName : $($_.Exception.Message)" -ForegroundColor Red
        return $result
    }
}

# Helper function to determine cmdlet domain
function Get-CmdletDomain {
    param([string]$CmdletName)

    if ($CmdletName -like "*User*") { return "user_management" }
    elseif ($CmdletName -like "*Group*") { return "group_management" }
    elseif ($CmdletName -like "*Computer*") { return "computer_management" }
    elseif ($CmdletName -like "*OU*" -or $CmdletName -like "*OrganizationalUnit*") { return "organizational_unit" }
    elseif ($CmdletName -like "*Domain*") { return "domain_management" }
    elseif ($CmdletName -like "*Forest*") { return "forest_management" }
    elseif ($CmdletName -like "*Policy*" -or $CmdletName -like "*GPO*") { return "policy_management" }
    else { return "general_ad" }
}

# Helper function to determine cmdlet operation
function Get-CmdletOperation {
    param([string]$CmdletName)

    if ($CmdletName -like "Get-*" -or $CmdletName -like "Search-*") { return "read" }
    elseif ($CmdletName -like "Set-*" -or $CmdletName -like "Enable-*" -or $CmdletName -like "Disable-*") { return "update" }
    elseif ($CmdletName -like "New-*" -or $CmdletName -like "Add-*") { return "create" }
    elseif ($CmdletName -like "Remove-*" -or $CmdletName -like "Clear-*") { return "delete" }
    elseif ($CmdletName -like "Move-*" -or $CmdletName -like "Restore-*") { return "modify" }
    else { return "other" }
}

Write-Host "Enhanced Microsoft Docs Scraper - Full Offline Content" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    if (-not $result) {
        throw "Failed to initialize scraping framework"
    }
    
    Write-Host "Framework initialized successfully" -ForegroundColor Green
    
    # Get all AD cmdlets
    Write-Host "`nDiscovering PowerShell AD cmdlets..." -ForegroundColor Yellow
    
    $baseUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/"
    $response = Invoke-WebRequestWithRetry -Uri $baseUrl
    $content = $response.Content
    
    # Extract cmdlet names using the working pattern from our previous tests
    $cmdletPattern = '>([A-Za-z]+-AD[A-Za-z]+)<'
    $matches = [regex]::Matches($content, $cmdletPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)

    $cmdletUrls = @()
    foreach ($match in $matches) {
        $cmdletName = $match.Groups[1].Value
        $fullUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($cmdletName.ToLower())"
        $cmdletUrls += @{
            Name = $cmdletName
            Url = $fullUrl
        }
    }
    
    # Remove duplicates and filter
    $uniqueCmdlets = $cmdletUrls | Sort-Object Name -Unique
    
    if ($SpecificCmdlets.Count -gt 0) {
        $uniqueCmdlets = $uniqueCmdlets | Where-Object { $_.Name -in $SpecificCmdlets }
        Write-Host "Filtered to $($uniqueCmdlets.Count) specific cmdlets" -ForegroundColor Gray
    } else {
        $uniqueCmdlets = $uniqueCmdlets | Select-Object -First $MaxCmdlets
        Write-Host "Limited to first $($uniqueCmdlets.Count) cmdlets" -ForegroundColor Gray
    }
    
    Write-Host "Found $($uniqueCmdlets.Count) cmdlets to process" -ForegroundColor Green
    
    # Process each cmdlet with FULL content extraction
    $allPatterns = @()
    $count = 0
    
    foreach ($cmdletInfo in $uniqueCmdlets) {
        $count++
        $cmdletName = $cmdletInfo.Name
        $cmdletUrl = $cmdletInfo.Url
        
        Write-Progress -Activity "Enhanced Microsoft Docs Scraping" -Status "Processing $cmdletName ($count/$($uniqueCmdlets.Count))" -PercentComplete (($count / $uniqueCmdlets.Count) * 100)
        Write-Host "`nProcessing: $cmdletName" -ForegroundColor White
        
        try {
            # Get full page content
            $pageResponse = Invoke-WebRequestWithRetry -Uri $cmdletUrl -MaxRetries 2
            $pageContent = $pageResponse.Content
            
            # Extract comprehensive information
            $cmdletData = Extract-FullCmdletDocumentation -Content $pageContent -CmdletName $cmdletName -Url $cmdletUrl
            
            if ($cmdletData) {
                # Determine domain and operation
                $domain = Get-CmdletDomain -CmdletName $cmdletName
                $operation = Get-CmdletOperation -CmdletName $cmdletName
                
                # Create comprehensive knowledge pattern
                $pattern = New-KnowledgePattern -Title "Microsoft Docs: $cmdletName" -Content $cmdletData.FullContent -SourceUrl $cmdletUrl -SourceType "microsoft_docs" -Domain $domain -Operation $operation -Author "Microsoft" -CredibilityScore 0.95
                
                if ($pattern) {
                    # Add comprehensive data
                    $pattern.Synopsis = $cmdletData.Synopsis
                    $pattern.Description = $cmdletData.Description
                    $pattern.Parameters = $cmdletData.Parameters
                    $pattern.Examples = $cmdletData.Examples
                    $pattern.Syntax = $cmdletData.Syntax
                    $pattern.Notes = $cmdletData.Notes
                    $pattern.RelatedLinks = $cmdletData.RelatedLinks
                    $pattern.CodeTemplate = $cmdletData.PrimaryExample
                    $pattern.RequiredParameters = $cmdletData.RequiredParameters
                    $pattern.BestPractices = $cmdletData.BestPractices
                    $pattern.CommonMistakes = $cmdletData.CommonMistakes
                    
                    $allPatterns += $pattern
                    Write-Host "  Created comprehensive pattern: $($pattern.Id)" -ForegroundColor Green
                    Write-Host "    Synopsis: $($cmdletData.Synopsis.Length) chars" -ForegroundColor Gray
                    Write-Host "    Parameters: $($cmdletData.Parameters.Count) items" -ForegroundColor Gray
                    Write-Host "    Examples: $($cmdletData.Examples.Count) items" -ForegroundColor Gray
                    Write-Host "    Full content: $($cmdletData.FullContent.Length) chars" -ForegroundColor Gray
                }
            }
        }
        catch {
            Write-Host "  Failed to process $cmdletName : $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Rate limiting
        Start-Sleep -Milliseconds 800
    }
    
    Write-Progress -Activity "Enhanced Microsoft Docs Scraping" -Completed
    
    # Save comprehensive results
    if ($allPatterns.Count -gt 0) {
        $outputFile = Save-ScrapingResults -Patterns $allPatterns -SourceType "enhanced_microsoft_docs"
        Write-Host "`nSUCCESS: Enhanced Microsoft Docs scraping completed!" -ForegroundColor Green
        Write-Host "Patterns created: $($allPatterns.Count)" -ForegroundColor Cyan
        Write-Host "Output file: $outputFile" -ForegroundColor Cyan
        
        # Calculate total content size
        $totalContentSize = ($allPatterns | ForEach-Object { $_.Content.Length + $_.Synopsis.Length + $_.Description.Length }) | Measure-Object -Sum
        Write-Host "Total offline content: $([Math]::Round($totalContentSize.Sum / 1024, 2)) KB" -ForegroundColor Cyan
        
        # Show sample
        if ($allPatterns.Count -gt 0) {
            $sample = $allPatterns[0]
            Write-Host "`nSample pattern structure:" -ForegroundColor Yellow
            Write-Host "  Title: $($sample.Title)" -ForegroundColor Gray
            Write-Host "  Synopsis: $($sample.Synopsis.Substring(0, [Math]::Min(100, $sample.Synopsis.Length)))..." -ForegroundColor Gray
            Write-Host "  Parameters: $($sample.Parameters.Count) documented" -ForegroundColor Gray
            Write-Host "  Examples: $($sample.Examples.Count) code examples" -ForegroundColor Gray
        }
    } else {
        Write-Host "No patterns created" -ForegroundColor Red
    }
}
catch {
    Write-Host "`nEnhanced Microsoft Docs scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    exit 1
}


