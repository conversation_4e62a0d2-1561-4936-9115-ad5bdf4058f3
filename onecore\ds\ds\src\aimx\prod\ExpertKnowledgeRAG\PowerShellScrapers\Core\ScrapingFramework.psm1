# Expert Knowledge RAG - PowerShell Scraping Framework
# Core module for web scraping and content extraction

#Requires -Version 5.1

# Import required modules
Import-Module -Name @('Microsoft.PowerShell.Utility', 'Microsoft.PowerShell.Management') -Force

# Global configuration
$Script:Config = $null
$Script:LogPath = $null
$Script:OutputPath = $null

function Initialize-ScrapingFramework {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$ConfigPath,
        
        [Parameter(Mandatory = $false)]
        [string]$LogLevel = "Information"
    )
    
    try {
        # Load configuration
        if (-not (Test-Path $ConfigPath)) {
            throw "Configuration file not found: $ConfigPath"
        }
        
        $Script:Config = Get-Content $ConfigPath | ConvertFrom-Json
        
        # Setup directories
        $Script:OutputPath = $Script:Config.ScrapingConfiguration.OutputDirectory
        $Script:LogPath = $Script:Config.ScrapingConfiguration.LogDirectory
        
        if (-not (Test-Path $Script:OutputPath)) {
            New-Item -Path $Script:OutputPath -ItemType Directory -Force | Out-Null
        }
        
        if (-not (Test-Path $Script:LogPath)) {
            New-Item -Path $Script:LogPath -ItemType Directory -Force | Out-Null
        }
        
        Write-ScrapingLog -Message "Scraping framework initialized successfully" -Level "Information"
        return $true
    }
    catch {
        Write-Error "Failed to initialize scraping framework: $($_.Exception.Message)"
        return $false
    }
}

function Write-ScrapingLog {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [ValidateSet("Information", "Warning", "Error", "Debug")]
        [string]$Level = "Information",
        
        [Parameter(Mandatory = $false)]
        [string]$Source = "ScrapingFramework"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] [$Source] $Message"
    
    # Write to console
    switch ($Level) {
        "Information" { Write-Host $logEntry -ForegroundColor Green }
        "Warning" { Write-Warning $logEntry }
        "Error" { Write-Error $logEntry }
        "Debug" { Write-Verbose $logEntry }
    }
    
    # Write to log file
    if ($Script:LogPath) {
        $logFile = Join-Path $Script:LogPath "scraping_$(Get-Date -Format 'yyyyMMdd').log"
        Add-Content -Path $logFile -Value $logEntry
    }
}

function Invoke-WebRequestWithRetry {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Uri,
        
        [Parameter(Mandatory = $false)]
        [int]$MaxRetries = 3,
        
        [Parameter(Mandatory = $false)]
        [int]$DelaySeconds = 1,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$Headers = @{},
        
        [Parameter(Mandatory = $false)]
        [int]$TimeoutSec = 30
    )
    
    $attempt = 0
    $defaultHeaders = @{
        'User-Agent' = $Script:Config.ScrapingConfiguration.UserAgent
    }

    # Merge headers (custom headers override defaults)
    $allHeaders = $defaultHeaders.Clone()
    foreach ($key in $Headers.Keys) {
        $allHeaders[$key] = $Headers[$key]
    }
    
    while ($attempt -lt $MaxRetries) {
        try {
            $attempt++
            Write-ScrapingLog -Message "Attempting to fetch: $Uri (Attempt $attempt/$MaxRetries)" -Level "Debug"
            
            $response = Invoke-WebRequest -Uri $Uri -Headers $allHeaders -TimeoutSec $TimeoutSec -UseBasicParsing
            
            Write-ScrapingLog -Message "Successfully fetched: $Uri" -Level "Debug"
            return $response
        }
        catch {
            Write-ScrapingLog -Message "Attempt $attempt failed for $Uri : $($_.Exception.Message)" -Level "Warning"
            
            if ($attempt -eq $MaxRetries) {
                Write-ScrapingLog -Message "All attempts failed for: $Uri" -Level "Error"
                throw
            }
            
            Start-Sleep -Seconds ($DelaySeconds * $attempt)
        }
    }
}

function Extract-CodeBlocks {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Content,
        
        [Parameter(Mandatory = $false)]
        [string[]]$Patterns = @()
    )
    
    if ($Patterns.Count -eq 0) {
        $Patterns = $Script:Config.PatternExtraction.CodeBlockPatterns
    }
    
    $codeBlocks = @()
    
    foreach ($pattern in $Patterns) {
        try {
            $matches = [regex]::Matches($Content, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Multiline)
            
            foreach ($match in $matches) {
                if ($match.Groups.Count -gt 1) {
                    $code = $match.Groups[1].Value.Trim()
                    if ($code.Length -gt $Script:Config.QualityFilters.MinimumCodeLength) {
                        $codeBlocks += $code
                    }
                }
            }
        }
        catch {
            Write-ScrapingLog -Message "Error extracting code with pattern '$pattern': $($_.Exception.Message)" -Level "Warning"
        }
    }
    
    return $codeBlocks | Select-Object -Unique
}

function Extract-PowerShellCommands {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Content
    )
    
    $commands = @()
    $cmdletPatterns = $Script:Config.PatternExtraction.PowerShellCmdlets
    
    foreach ($pattern in $cmdletPatterns) {
        try {
            $matches = [regex]::Matches($Content, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            
            foreach ($match in $matches) {
                $command = $match.Value.Trim()
                if ($command.Length -gt 5) {  # Minimum command length
                    $commands += $command
                }
            }
        }
        catch {
            Write-ScrapingLog -Message "Error extracting commands with pattern '$pattern': $($_.Exception.Message)" -Level "Warning"
        }
    }
    
    return $commands | Select-Object -Unique
}

function Test-ContentQuality {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Content,
        
        [Parameter(Mandatory = $true)]
        [string]$Title,
        
        [Parameter(Mandatory = $false)]
        [string[]]$RequiredKeywords = @()
    )
    
    if ($RequiredKeywords.Count -eq 0) {
        $RequiredKeywords = $Script:Config.QualityFilters.RequiredKeywords
    }
    
    $qualityScore = 0.0
    $maxScore = 100.0
    
    # Check for required keywords
    $keywordScore = 0
    foreach ($keyword in $RequiredKeywords) {
        if ($Content -match [regex]::Escape($keyword)) {
            $keywordScore += 20
        }
        if ($Title -match [regex]::Escape($keyword)) {
            $keywordScore += 10
        }
    }
    $qualityScore += [Math]::Min($keywordScore, 40)
    
    # Check for code examples
    $codeBlocks = Extract-CodeBlocks -Content $Content
    if ($codeBlocks.Count -gt 0) {
        $qualityScore += 30
    }
    
    # Check content length
    if ($Content.Length -gt 500) {
        $qualityScore += 20
    } elseif ($Content.Length -gt 200) {
        $qualityScore += 10
    }
    
    # Check for exclude patterns
    $excludePatterns = $Script:Config.QualityFilters.ExcludePatterns
    foreach ($pattern in $excludePatterns) {
        if ($Content -match [regex]::Escape($pattern)) {
            $qualityScore -= 20
        }
    }
    
    # Normalize score
    $normalizedScore = [Math]::Max(0, [Math]::Min($qualityScore / $maxScore, 1.0))
    
    return @{
        Score = $normalizedScore
        HasCodeExamples = $codeBlocks.Count -gt 0
        CodeBlockCount = $codeBlocks.Count
        ContentLength = $Content.Length
        PassesMinimumThreshold = $normalizedScore -ge $Script:Config.QualityFilters.MinimumCredibilityScore
    }
}

function New-KnowledgePattern {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Title,
        
        [Parameter(Mandatory = $true)]
        [string]$Content,
        
        [Parameter(Mandatory = $true)]
        [string]$SourceUrl,
        
        [Parameter(Mandatory = $true)]
        [string]$SourceType,
        
        [Parameter(Mandatory = $false)]
        [string]$Domain = "",
        
        [Parameter(Mandatory = $false)]
        [string]$Operation = "",
        
        [Parameter(Mandatory = $false)]
        [string]$Author = "",
        
        [Parameter(Mandatory = $false)]
        [double]$CredibilityScore = 0.5
    )
    
    # Extract patterns and metadata
    $codeBlocks = Extract-CodeBlocks -Content $Content
    $commands = Extract-PowerShellCommands -Content $Content
    $qualityCheck = Test-ContentQuality -Content $Content -Title $Title
    
    # Generate pattern ID
    $patternId = "pattern_$($Domain)_$([System.Guid]::NewGuid().ToString('N').Substring(0,8))"
    
    # Determine pattern type based on content
    $patternType = "workflow"
    if ($commands.Count -eq 1) {
        $patternType = "cmdlet_usage"
    } elseif ($Content -match "best practice|recommended") {
        $patternType = "best_practice"
    } elseif ($Content -match "mistake|error|wrong") {
        $patternType = "common_mistake"
    }
    
    # Create knowledge pattern object
    $pattern = @{
        Id = $patternId
        Domain = $Domain
        Operation = $Operation
        PatternType = $patternType
        Title = $Title
        Abstract = ($Content.Substring(0, [Math]::Min(500, $Content.Length)) + "...")
        Content = $Content
        CodeTemplate = ($codeBlocks -join "`n`n")
        RequiredParameters = ($commands | ConvertTo-Json -Compress)
        BestPractices = @()
        CommonMistakes = @()
        PerformanceTips = @()
        Sources = @(@{
            Id = [System.Guid]::NewGuid().ToString()
            SourceType = $SourceType
            Url = $SourceUrl
            Author = $Author
            Title = $Title
            CredibilityScore = $CredibilityScore
            PublishedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            ScrapedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        })
        Tags = @("powershell", "active-directory", $Domain, $Operation) | Where-Object { $_ -ne "" }
        CredibilityScore = [Math]::Max($CredibilityScore, $qualityCheck.Score)
        RelevanceScore = $qualityCheck.Score
        UsageCount = 0
        CreatedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        UpdatedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        CreatedBy = "PowerShellScraper"
    }
    
    return $pattern
}

function Save-ScrapingResults {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [array]$Patterns,
        
        [Parameter(Mandatory = $true)]
        [string]$SourceType,
        
        [Parameter(Mandatory = $false)]
        [string]$BatchId = ""
    )
    
    if ($BatchId -eq "") {
        $BatchId = Get-Date -Format "yyyyMMdd_HHmmss"
    }
    
    $fileName = "scraped_${SourceType}_${BatchId}.json"
    $filePath = Join-Path $Script:OutputPath $fileName
    
    try {
        $results = @{
            BatchId = $BatchId
            SourceType = $SourceType
            ScrapedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            TotalPatterns = $Patterns.Count
            Patterns = $Patterns
        }
        
        $results | ConvertTo-Json -Depth 10 | Out-File -FilePath $filePath -Encoding UTF8
        
        Write-ScrapingLog -Message "Saved $($Patterns.Count) patterns to: $filePath" -Level "Information"
        return $filePath
    }
    catch {
        Write-ScrapingLog -Message "Failed to save results: $($_.Exception.Message)" -Level "Error"
        throw
    }
}

# Export functions
Export-ModuleMember -Function @(
    'Initialize-ScrapingFramework',
    'Write-ScrapingLog',
    'Invoke-WebRequestWithRetry',
    'Extract-CodeBlocks',
    'Extract-PowerShellCommands',
    'Test-ContentQuality',
    'New-KnowledgePattern',
    'Save-ScrapingResults'
)
