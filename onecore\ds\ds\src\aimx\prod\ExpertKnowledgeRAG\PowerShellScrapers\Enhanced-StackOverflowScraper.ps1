# Enhanced Stack Overflow Scraper - Captures FULL offline Q&A content
# Scrapes complete questions, all answers, code blocks, and comments
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxQuestions = 20,
    
    [Parameter(Mandatory = $false)]
    [int]$MinScore = 5
)

# Helper function to get full question details with answers
function Get-FullQuestionDetails {
    param(
        [int]$QuestionId,
        [string]$BaseUrl
    )
    
    try {
        # Get question details with answers using simpler filter
        $detailsUrl = "${BaseUrl}questions/${QuestionId}?order=desc&sort=votes&site=stackoverflow&filter=withbody"
        $detailsResponse = Invoke-WebRequestWithRetry -Uri $detailsUrl -MaxRetries 2
        $detailsData = $detailsResponse.Content | ConvertFrom-Json

        if ($detailsData.items.Count -gt 0) {
            $question = $detailsData.items[0]

            # Get answers for this question
            $answersUrl = "${BaseUrl}questions/${QuestionId}/answers?order=desc&sort=votes&site=stackoverflow&filter=withbody"
            $answersResponse = Invoke-WebRequestWithRetry -Uri $answersUrl -MaxRetries 2
            $answersData = $answersResponse.Content | ConvertFrom-Json
            
            $result = @{
                Question = $question
                Answers = $answersData.items
                FullContent = ""
                AllCodeBlocks = @()
                BestAnswer = $null
                TotalScore = $question.score
            }
            
            # Build comprehensive content
            $fullContent = "# Question: $($question.title)`n`n"
            $fullContent += "**Score:** $($question.score) | **Views:** $($question.view_count) | **Tags:** $($question.tags -join ', ')`n`n"
            $fullContent += "## Question Body`n`n"

            # Check if question body exists
            if ($question.body -and $question.body.Length -gt 0) {
                $fullContent += $question.body + "`n`n"
            } else {
                $fullContent += "Question body not available`n`n"
            }
            
            # Extract code from question
            $questionCode = Extract-CodeBlocks -Content $question.body
            $result.AllCodeBlocks += $questionCode
            
            # Add all answers
            if ($answersData.items.Count -gt 0) {
                $fullContent += "## Answers ($($answersData.items.Count) total)`n`n"
                
                $answerIndex = 1
                foreach ($answer in $answersData.items) {
                    $fullContent += "### Answer $answerIndex (Score: $($answer.score))"
                    if ($answer.is_accepted) {
                        $fullContent += " ✅ ACCEPTED"
                        $result.BestAnswer = $answer
                    }
                    $fullContent += "`n`n"
                    $fullContent += $answer.body + "`n`n"
                    
                    # Extract code from answer
                    $answerCode = Extract-CodeBlocks -Content $answer.body
                    $result.AllCodeBlocks += $answerCode
                    
                    # Update total score
                    $result.TotalScore += $answer.score
                    
                    $answerIndex++
                }
            }
            
            # If no accepted answer, use highest scored answer as best
            if (-not $result.BestAnswer -and $answersData.items.Count -gt 0) {
                $result.BestAnswer = $answersData.items[0]  # Already sorted by score
            }
            
            $result.FullContent = $fullContent
            return $result
        }
        
        return $null
    }
    catch {
        Write-Host "Failed to get details for question $QuestionId : $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

Write-Host "Enhanced Stack Overflow Scraper - Full Offline Q&A Content" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    if (-not $result) {
        throw "Failed to initialize scraping framework"
    }
    
    Write-Host "Framework initialized successfully" -ForegroundColor Green
    
    # Get Stack Overflow questions
    Write-Host "`nFetching PowerShell AD questions from Stack Overflow..." -ForegroundColor Yellow
    
    $baseUrl = "https://api.stackexchange.com/2.3/"
    $tags = "powershell;active-directory"
    $questionsUrl = "${baseUrl}questions?order=desc&sort=votes&tagged=${tags}&site=stackoverflow&pagesize=${MaxQuestions}&filter=withbody"
    
    $questionsResponse = Invoke-WebRequestWithRetry -Uri $questionsUrl
    $questionsData = $questionsResponse.Content | ConvertFrom-Json
    
    Write-Host "Found $($questionsData.items.Count) questions, quota remaining: $($questionsData.quota_remaining)" -ForegroundColor Green
    
    # Filter by minimum score
    $filteredQuestions = $questionsData.items | Where-Object { $_.score -ge $MinScore }
    Write-Host "Filtered to $($filteredQuestions.Count) questions with score >= $MinScore" -ForegroundColor Gray
    
    # Process each question with FULL content extraction
    $allPatterns = @()
    $count = 0
    
    foreach ($question in $filteredQuestions) {
        $count++
        Write-Progress -Activity "Enhanced Stack Overflow Scraping" -Status "Processing question $count/$($filteredQuestions.Count)" -PercentComplete (($count / $filteredQuestions.Count) * 100)
        Write-Host "`nProcessing: $($question.title)" -ForegroundColor White
        Write-Host "  Score: $($question.score), Views: $($question.view_count)" -ForegroundColor Gray
        
        try {
            # Get full question details with all answers
            $fullDetails = Get-FullQuestionDetails -QuestionId $question.question_id -BaseUrl $baseUrl
            
            if ($fullDetails) {
                # Create comprehensive knowledge pattern
                $pattern = New-KnowledgePattern -Title "Stack Overflow Q&A: $($question.title)" -Content $fullDetails.FullContent -SourceUrl $question.link -SourceType "stackoverflow_qa" -Domain "general_ad" -Operation "read" -Author "Stack Overflow Community" -CredibilityScore 0.85
                
                if ($pattern) {
                    # Add comprehensive Q&A data
                    $pattern.QuestionScore = $question.score
                    $pattern.ViewCount = $question.view_count
                    $pattern.AnswerCount = $fullDetails.Answers.Count
                    $pattern.TotalScore = $fullDetails.TotalScore
                    $pattern.HasAcceptedAnswer = ($fullDetails.BestAnswer -and $fullDetails.BestAnswer.is_accepted)
                    $pattern.AllCodeBlocks = $fullDetails.AllCodeBlocks
                    $pattern.Tags = $question.tags
                    
                    # Set primary code template from best answer or question
                    if ($fullDetails.AllCodeBlocks.Count -gt 0) {
                        # Prefer code from accepted answer, then highest scored answer, then question
                        if ($fullDetails.BestAnswer) {
                            $bestAnswerCode = Extract-CodeBlocks -Content $fullDetails.BestAnswer.body
                            if ($bestAnswerCode.Count -gt 0) {
                                $pattern.CodeTemplate = $bestAnswerCode[0]
                            } else {
                                $pattern.CodeTemplate = $fullDetails.AllCodeBlocks[0]
                            }
                        } else {
                            $pattern.CodeTemplate = $fullDetails.AllCodeBlocks[0]
                        }
                    }
                    
                    # Calculate relevance based on total engagement
                    $pattern.RelevanceScore = [Math]::Min(1.0, ($fullDetails.TotalScore / 200.0) + ($question.view_count / 1000000.0))
                    
                    # Extract best practices and common mistakes from answers
                    if ($fullDetails.BestAnswer) {
                        $answerText = $fullDetails.BestAnswer.body
                        if ($answerText -like "*best practice*" -or $answerText -like "*recommended*" -or $answerText -like "*should*") {
                            $pattern.BestPractices += "From accepted answer: Use recommended approaches mentioned in the solution"
                        }
                        if ($answerText -like "*don't*" -or $answerText -like "*avoid*" -or $answerText -like "*mistake*" -or $answerText -like "*wrong*") {
                            $pattern.CommonMistakes += "From accepted answer: Avoid approaches mentioned as problematic"
                        }
                    }
                    
                    $allPatterns += $pattern
                    Write-Host "  Created comprehensive Q&A pattern: $($pattern.Id)" -ForegroundColor Green
                    Write-Host "    Question score: $($question.score), Answers: $($fullDetails.Answers.Count)" -ForegroundColor Gray
                    Write-Host "    Code blocks: $($fullDetails.AllCodeBlocks.Count), Full content: $($fullDetails.FullContent.Length) chars" -ForegroundColor Gray
                    Write-Host "    Has accepted answer: $($pattern.HasAcceptedAnswer)" -ForegroundColor Gray
                }
            }
        }
        catch {
            Write-Host "  Failed to process question: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Rate limiting - Stack Overflow API is strict
        Start-Sleep -Seconds 2
    }
    
    Write-Progress -Activity "Enhanced Stack Overflow Scraping" -Completed
    
    # Save comprehensive results
    if ($allPatterns.Count -gt 0) {
        $outputFile = Save-ScrapingResults -Patterns $allPatterns -SourceType "enhanced_stackoverflow"
        Write-Host "`nSUCCESS: Enhanced Stack Overflow scraping completed!" -ForegroundColor Green
        Write-Host "Patterns created: $($allPatterns.Count)" -ForegroundColor Cyan
        Write-Host "Output file: $outputFile" -ForegroundColor Cyan
        
        # Calculate total content size
        $totalContentSize = ($allPatterns | ForEach-Object { $_.Content.Length }) | Measure-Object -Sum
        $totalCodeBlocks = ($allPatterns | ForEach-Object { $_.AllCodeBlocks.Count }) | Measure-Object -Sum
        $totalAnswers = ($allPatterns | ForEach-Object { $_.AnswerCount }) | Measure-Object -Sum
        
        Write-Host "Total offline content: $([Math]::Round($totalContentSize.Sum / 1024, 2)) KB" -ForegroundColor Cyan
        Write-Host "Total code blocks: $($totalCodeBlocks.Sum)" -ForegroundColor Cyan
        Write-Host "Total answers: $($totalAnswers.Sum)" -ForegroundColor Cyan
        
        # Show sample
        if ($allPatterns.Count -gt 0) {
            $sample = $allPatterns[0]
            Write-Host "`nSample Q&A pattern structure:" -ForegroundColor Yellow
            Write-Host "  Title: $($sample.Title)" -ForegroundColor Gray
            Write-Host "  Question score: $($sample.QuestionScore), Views: $($sample.ViewCount)" -ForegroundColor Gray
            Write-Host "  Answers: $($sample.AnswerCount), Code blocks: $($sample.AllCodeBlocks.Count)" -ForegroundColor Gray
            Write-Host "  Has accepted answer: $($sample.HasAcceptedAnswer)" -ForegroundColor Gray
            Write-Host "  Content length: $($sample.Content.Length) characters" -ForegroundColor Gray
        }
    } else {
        Write-Host "No patterns created" -ForegroundColor Red
    }
}
catch {
    Write-Host "`nEnhanced Stack Overflow scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    exit 1
}
