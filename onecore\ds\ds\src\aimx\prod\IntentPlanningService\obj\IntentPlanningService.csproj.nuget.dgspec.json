{"format": 1, "restore": {"E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\IntentPlanningService\\IntentPlanningService.csproj": {}}, "projects": {"E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\IntentPlanningService\\IntentPlanningService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\IntentPlanningService\\IntentPlanningService.csproj", "projectName": "IntentPlanningService", "projectPath": "E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\IntentPlanningService\\IntentPlanningService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\IntentPlanningService\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\netRag\\NetRagService\\NetRagService.csproj": {"projectPath": "E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\netRag\\NetRagService\\NetRagService.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.EventLog": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.60.0, )"}, "Microsoft.SemanticKernel.Connectors.Onnx": {"target": "Package", "version": "[1.60.0-alpha, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}, "System.Numerics.Tensors": {"target": "Package", "version": "[9.0.6, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "c:\\program files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\netRag\\NetRagService\\NetRagService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\netRag\\NetRagService\\NetRagService.csproj", "projectName": "NetRagService", "projectPath": "E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\netRag\\NetRagService\\NetRagService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\netRag\\NetRagService\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.EventLog": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.PowerShell.SDK": {"target": "Package", "version": "[7.4.6, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.60.0, )"}, "Microsoft.SemanticKernel.Connectors.Onnx": {"target": "Package", "version": "[1.60.0-alpha, )"}, "System.Numerics.Tensors": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "c:\\program files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}