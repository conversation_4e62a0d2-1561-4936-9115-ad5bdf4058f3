<#
.SYNOPSIS
    Populates the MCP RAG Service with sample data
    
.DESCRIPTION
    This script creates sample MCP servers and tools to demonstrate the service functionality.
    It simulates various types of tools that might be found in different MCP servers.
    
.PARAMETER BaseUrl
    Base URL of the MCP RAG Service (default: http://localhost:5000)
    
.PARAMETER ServerCount
    Number of sample servers to create (default: 3)
    
.EXAMPLE
    .\Populate-SampleData.ps1
    
.EXAMPLE
    .\Populate-SampleData.ps1 -BaseUrl "http://localhost:8080" -ServerCount 5
#>

[CmdletBinding()]
param(
    [string]$BaseUrl = "http://localhost:5000",
    [int]$ServerCount = 3
)

$ErrorActionPreference = "Stop"

# Sample server configurations
$SampleServers = @(
    @{
        Name = "Active Directory MCP Server"
        Description = "Provides tools for managing Active Directory users, groups, and computers"
        Categories = @("User Management", "Group Management", "Computer Management")
        ToolPrefix = "AD"
    },
    @{
        Name = "File System MCP Server"
        Description = "Tools for file and directory operations"
        Categories = @("File Operations", "Directory Management", "Permissions")
        ToolPrefix = "FS"
    },
    @{
        Name = "Network MCP Server"
        Description = "Network administration and monitoring tools"
        Categories = @("Network Monitoring", "DNS Management", "Firewall")
        ToolPrefix = "NET"
    },
    @{
        Name = "Database MCP Server"
        Description = "Database administration and query tools"
        Categories = @("Database Admin", "Query Operations", "Backup")
        ToolPrefix = "DB"
    },
    @{
        Name = "Cloud Services MCP Server"
        Description = "Cloud platform management tools"
        Categories = @("VM Management", "Storage", "Networking")
        ToolPrefix = "CLOUD"
    }
)

# Sample tool templates
$ToolTemplates = @{
    "User Management" = @(
        @{ Name = "Get-User"; Description = "Retrieves user account information"; Tags = @("user", "lookup", "identity") },
        @{ Name = "New-User"; Description = "Creates a new user account"; Tags = @("user", "create", "account") },
        @{ Name = "Set-UserPassword"; Description = "Sets or resets a user's password"; Tags = @("password", "reset", "security") },
        @{ Name = "Disable-User"; Description = "Disables a user account"; Tags = @("user", "disable", "security") },
        @{ Name = "Enable-User"; Description = "Enables a disabled user account"; Tags = @("user", "enable", "activate") }
    )
    "Group Management" = @(
        @{ Name = "Get-Group"; Description = "Retrieves group information and membership"; Tags = @("group", "lookup", "membership") },
        @{ Name = "New-Group"; Description = "Creates a new security or distribution group"; Tags = @("group", "create", "security") },
        @{ Name = "Add-GroupMember"; Description = "Adds a user to a group"; Tags = @("group", "member", "add") },
        @{ Name = "Remove-GroupMember"; Description = "Removes a user from a group"; Tags = @("group", "member", "remove") }
    )
    "File Operations" = @(
        @{ Name = "Get-FileInfo"; Description = "Gets detailed information about files"; Tags = @("file", "info", "metadata") },
        @{ Name = "Copy-File"; Description = "Copies files with advanced options"; Tags = @("file", "copy", "transfer") },
        @{ Name = "Move-File"; Description = "Moves or renames files"; Tags = @("file", "move", "rename") },
        @{ Name = "Set-FilePermissions"; Description = "Sets file access permissions"; Tags = @("file", "permissions", "security") }
    )
    "Network Monitoring" = @(
        @{ Name = "Test-Connection"; Description = "Tests network connectivity to hosts"; Tags = @("network", "ping", "connectivity") },
        @{ Name = "Get-NetworkAdapter"; Description = "Gets network adapter information"; Tags = @("network", "adapter", "interface") },
        @{ Name = "Monitor-Bandwidth"; Description = "Monitors network bandwidth usage"; Tags = @("network", "bandwidth", "monitoring") }
    )
    "Database Admin" = @(
        @{ Name = "Get-DatabaseInfo"; Description = "Gets database server information"; Tags = @("database", "info", "server") },
        @{ Name = "Backup-Database"; Description = "Creates a database backup"; Tags = @("database", "backup", "maintenance") },
        @{ Name = "Execute-Query"; Description = "Executes SQL queries safely"; Tags = @("database", "query", "sql") }
    )
}

function Invoke-ApiCall {
    param(
        [string]$Method,
        [string]$Endpoint,
        [object]$Body = $null
    )
    
    $uri = "$BaseUrl$Endpoint"
    $headers = @{"Content-Type" = "application/json"}
    
    try {
        $params = @{
            Uri = $uri
            Method = $Method
            Headers = $headers
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json -Depth 10)
        }
        
        $response = Invoke-RestMethod @params
        return @{ Success = $true; Data = $response }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function New-SampleServer {
    param([hashtable]$ServerConfig)
    
    $serverId = [Guid]::NewGuid().ToString()
    
    $serverData = @{
        Id = $serverId
        Name = $ServerConfig.Name
        Description = $ServerConfig.Description
        Version = "1.0.0"
        Metadata = @{
            categories = $ServerConfig.Categories
            toolPrefix = $ServerConfig.ToolPrefix
            sampleData = $true
        }
    }
    
    Write-Host "Creating server: $($ServerConfig.Name)" -ForegroundColor Green
    
    $result = Invoke-ApiCall -Method "POST" -Endpoint "/api/mcpservers/register" -Body $serverData
    
    if ($result.Success) {
        Write-Host "  [SUCCESS] Server created with ID: $serverId" -ForegroundColor Cyan
        return $serverId
    } else {
        Write-Host "  [FAILED] Failed to create server: $($result.Error)" -ForegroundColor Red
        return $null
    }
}

function New-SampleTools {
    param(
        [string]$ServerId,
        [hashtable]$ServerConfig
    )
    
    $toolsCreated = 0
    
    foreach ($category in $ServerConfig.Categories) {
        if ($ToolTemplates.ContainsKey($category)) {
            $templates = $ToolTemplates[$category]
            
            foreach ($template in $templates) {
                $toolId = "$ServerId" + "_" + [Guid]::NewGuid().ToString()
                
                $toolData = @{
                    Id = $toolId
                    Name = "$($ServerConfig.ToolPrefix)-$($template.Name)"
                    Description = $template.Description
                    Category = $category
                    InputSchema = '{"type":"object","properties":{"target":{"type":"string","description":"Target for the operation"}}}'
                    Examples = @("Example usage of $($template.Name)")
                    Tags = $template.Tags + @($ServerConfig.ToolPrefix.ToLower())
                    Version = "1.0.0"
                    Metadata = @{
                        serverName = $ServerConfig.Name
                        category = $category
                        sampleData = $true
                    }
                }
                
                $result = Invoke-ApiCall -Method "POST" -Endpoint "/api/mcptools/register" -Body $toolData
                
                if ($result.Success) {
                    Write-Host "    [SUCCESS] Created tool: $($toolData.Name)" -ForegroundColor Gray
                    $toolsCreated++
                } else {
                    Write-Host "    [FAILED] Failed to create tool: $($toolData.Name)" -ForegroundColor Red
                }
            }
        }
    }
    
    Write-Host "  Created $toolsCreated tools for server" -ForegroundColor Cyan
    return $toolsCreated
}

# Main execution
Write-Host "Populating MCP RAG Service with sample data" -ForegroundColor Magenta
Write-Host "Service URL: $BaseUrl" -ForegroundColor Cyan
Write-Host "Creating $ServerCount servers with tools..." -ForegroundColor Cyan

# Test service health first
Write-Host "`nTesting service health..." -ForegroundColor Yellow
$healthResult = Invoke-ApiCall -Method "GET" -Endpoint "/api/mcptools/health"

if (-not $healthResult.Success) {
    Write-Host "[ERROR] Service is not responding at $BaseUrl" -ForegroundColor Red
    Write-Host "Please ensure the service is running before populating data." -ForegroundColor Red
    exit 1
}

Write-Host "[SUCCESS] Service is healthy, proceeding with data population..." -ForegroundColor Green

$totalServers = 0
$totalTools = 0

# Create servers and tools
for ($i = 0; $i -lt [Math]::Min($ServerCount, $SampleServers.Count); $i++) {
    $serverConfig = $SampleServers[$i]
    
    $serverId = New-SampleServer -ServerConfig $serverConfig
    
    if ($serverId) {
        $totalServers++
        $toolsCreated = New-SampleTools -ServerId $serverId -ServerConfig $serverConfig
        $totalTools += $toolsCreated
    }
    
    Write-Host ""
}

# Show final statistics
Write-Host "Population Summary:" -ForegroundColor Magenta
Write-Host "  Servers created: $totalServers" -ForegroundColor Green
Write-Host "  Tools created: $totalTools" -ForegroundColor Green

# Get service statistics
Write-Host "`nGetting service statistics..." -ForegroundColor Yellow
$statsResult = Invoke-ApiCall -Method "GET" -Endpoint "/api/mcptools/statistics"

if ($statsResult.Success) {
    Write-Host "Service Statistics:" -ForegroundColor Magenta
    Write-Host "  Total tools in service: $($statsResult.Data.totalTools)" -ForegroundColor Cyan
    Write-Host "  Cached tools: $($statsResult.Data.cachedTools)" -ForegroundColor Cyan
}

Write-Host "`nSample data population completed!" -ForegroundColor Green
Write-Host "You can now test the service with queries like:" -ForegroundColor Yellow
Write-Host "  - 'find user information'" -ForegroundColor Gray
Write-Host "  - 'create new group'" -ForegroundColor Gray
Write-Host "  - 'backup database'" -ForegroundColor Gray
Write-Host "  - 'monitor network'" -ForegroundColor Gray
