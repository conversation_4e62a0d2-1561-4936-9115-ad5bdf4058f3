# Simple Stack Overflow API test without framework
Write-Host "Simple Stack Overflow API Test" -ForegroundColor Cyan

try {
    # Test different URL formats
    $testUrls = @(
        "https://api.stackexchange.com/2.3/questions?order=desc&sort=votes&tagged=powershell&site=stackoverflow&pagesize=2",
        "https://api.stackexchange.com/2.3/questions?order=desc&sort=votes&tagged=active-directory&site=stackoverflow&pagesize=2",
        "https://api.stackexchange.com/2.3/questions?order=desc&sort=votes&tagged=powershell%3Bactive-directory&site=stackoverflow&pagesize=2"
    )
    
    foreach ($url in $testUrls) {
        Write-Host "`nTesting URL: $url" -ForegroundColor Yellow
        
        try {
            $response = Invoke-RestMethod -Uri $url -Method Get
            Write-Host "Success! Found $($response.items.Count) questions" -ForegroundColor Green
            Write-Host "Quota remaining: $($response.quota_remaining)" -ForegroundColor Gray
            
            if ($response.items.Count -gt 0) {
                $firstQuestion = $response.items[0]
                Write-Host "First question: $($firstQuestion.title)" -ForegroundColor Cyan
                Write-Host "Score: $($firstQuestion.score), Views: $($firstQuestion.view_count)" -ForegroundColor Gray
                break  # Found working URL
            }
        }
        catch {
            Write-Host "Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Test with working URL and body content
    Write-Host "`nTesting with body content..." -ForegroundColor Yellow
    $bodyUrl = "https://api.stackexchange.com/2.3/questions?order=desc&sort=votes&tagged=powershell&site=stackoverflow&pagesize=2&filter=withbody"
    
    try {
        $bodyResponse = Invoke-RestMethod -Uri $bodyUrl -Method Get
        Write-Host "Success with body! Found $($bodyResponse.items.Count) questions" -ForegroundColor Green
        
        if ($bodyResponse.items.Count -gt 0) {
            $question = $bodyResponse.items[0]
            Write-Host "Question: $($question.title)" -ForegroundColor Cyan
            Write-Host "Body length: $($question.body.Length)" -ForegroundColor Gray
            Write-Host "Body preview: $($question.body.Substring(0, [Math]::Min(200, $question.body.Length)))..." -ForegroundColor Gray
            
            # Test getting answers
            Write-Host "`nTesting answers..." -ForegroundColor Yellow
            $answersUrl = "https://api.stackexchange.com/2.3/questions/$($question.question_id)/answers?order=desc&sort=votes&site=stackoverflow&filter=withbody"
            
            $answersResponse = Invoke-RestMethod -Uri $answersUrl -Method Get
            Write-Host "Found $($answersResponse.items.Count) answers" -ForegroundColor Green
            
            if ($answersResponse.items.Count -gt 0) {
                $answer = $answersResponse.items[0]
                Write-Host "First answer score: $($answer.score)" -ForegroundColor Gray
                Write-Host "Answer body length: $($answer.body.Length)" -ForegroundColor Gray
                Write-Host "Answer preview: $($answer.body.Substring(0, [Math]::Min(200, $answer.body.Length)))..." -ForegroundColor Gray
            }
        }
    }
    catch {
        Write-Host "Failed with body: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`nSimple Stack Overflow test completed!" -ForegroundColor Green
}
catch {
    Write-Host "`nSimple Stack Overflow test failed: $($_.Exception.Message)" -ForegroundColor Red
}
