# Test the filtering logic specifically
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing Filtering Logic" -ForegroundColor Cyan

# Simulate the cmdlet list and filtering logic
$mockCmdlets = @(
    @{ Name = "Get-ADUser"; Domain = "user_management"; Operation = "read" },
    @{ Name = "Get-ADGroup"; Domain = "group_management"; Operation = "read" },
    @{ Name = "Get-ADComputer"; Domain = "computer_management"; Operation = "read" },
    @{ Name = "Set-ADUser"; Domain = "user_management"; Operation = "update" }
)

Write-Host "`nMock cmdlet list:" -ForegroundColor Yellow
foreach ($cmdlet in $mockCmdlets) {
    Write-Host "  $($cmdlet.Name)" -ForegroundColor Gray
}

# Test different filtering scenarios
$testCases = @(
    @{ Name = "Exact match"; SpecificCmdlets = @("Get-ADUser", "Get-ADGroup") },
    @{ Name = "Case variation"; SpecificCmdlets = @("get-aduser", "GET-ADGROUP") },
    @{ Name = "Single cmdlet"; SpecificCmdlets = @("Get-ADUser") },
    @{ Name = "Non-existent"; SpecificCmdlets = @("Get-ADNonExistent") },
    @{ Name = "Mixed"; SpecificCmdlets = @("Get-ADUser", "Get-ADNonExistent") }
)

foreach ($testCase in $testCases) {
    Write-Host "`nTest case: $($testCase.Name)" -ForegroundColor Yellow
    $specificCmdlets = $testCase.SpecificCmdlets
    
    Write-Host "  Specific cmdlets: $($specificCmdlets -join ', ')" -ForegroundColor Gray
    
    # Test the filtering logic
    if ($specificCmdlets.Count -gt 0) {
        $filtered = $mockCmdlets | Where-Object { $_.Name -in $specificCmdlets }
        Write-Host "  Filtered count: $($filtered.Count)" -ForegroundColor Green
        
        foreach ($cmdlet in $filtered) {
            Write-Host "    Found: $($cmdlet.Name)" -ForegroundColor Cyan
        }
        
        if ($filtered.Count -eq 0) {
            Write-Host "    No matches found!" -ForegroundColor Red
            
            # Check case-insensitive matching
            $caseInsensitive = $mockCmdlets | Where-Object { $specificCmdlets -contains $_.Name }
            Write-Host "    Case-insensitive matches: $($caseInsensitive.Count)" -ForegroundColor Yellow
            
            # Check individual matches
            foreach ($specific in $specificCmdlets) {
                $exactMatch = $mockCmdlets | Where-Object { $_.Name -eq $specific }
                $likeMatch = $mockCmdlets | Where-Object { $_.Name -like $specific }
                Write-Host "      '$specific' -> exact: $($exactMatch.Count), like: $($likeMatch.Count)" -ForegroundColor DarkGray
            }
        }
    } else {
        Write-Host "  No specific cmdlets specified" -ForegroundColor Gray
    }
}

# Test the actual parameter parsing
Write-Host "`nTesting parameter parsing..." -ForegroundColor Yellow

# Simulate how PowerShell might parse the parameters
$testParams = @(
    "Get-ADUser",
    "Get-ADGroup", 
    "Get-ADObject"
)

Write-Host "  Test parameters: $($testParams -join ', ')" -ForegroundColor Gray
Write-Host "  Parameter count: $($testParams.Count)" -ForegroundColor Gray
Write-Host "  Parameter type: $($testParams.GetType().Name)" -ForegroundColor Gray

# Test the -in operator
$testResult = $mockCmdlets | Where-Object { $_.Name -in $testParams }
Write-Host "  Filtered result count: $($testResult.Count)" -ForegroundColor Green

foreach ($result in $testResult) {
    Write-Host "    Match: $($result.Name)" -ForegroundColor Cyan
}

Write-Host "`n✅ Filtering logic test completed!" -ForegroundColor Green
