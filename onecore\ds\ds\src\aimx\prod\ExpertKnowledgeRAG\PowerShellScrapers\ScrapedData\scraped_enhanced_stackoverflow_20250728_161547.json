﻿{
    "ScrapedAt":  "2025-07-28T16:15:47Z",
    "BatchId":  "20250728_161547",
    "Patterns":  [
                     {
                         "AllCodeBlocks":  [
                                               "Management_Install.ps1",
                                               "Set-ExecutionPolicy -ExecutionPolicy Unrestricted",
                                               "Get-ExecutionPolicy",
                                               "Unrestricted",
                                               "C:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\\Management_Install.ps1",
                                               "get-help about_signing",
                                               ".\\Management_Install.ps1",
                                               "Set-ExecutionPolicy RemoteSigned",
                                               "Set-ExecutionPolicy Restricted",
                                               "Access to the registry key\n\u0027HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\ShellIds\\Microsoft.PowerShell\u0027 is denied. \nTo change the execution policy for the default (LocalMachine) scope, \n  start Windows PowerShell with the \u0026quot;Run as administrator\u0026quot; option. \nTo change the execution policy for the current user, \n  run \u0026quot;Set-ExecutionPolicy -Scope CurrentUser\u0026quot;.",
                                               "Set-ExecutionPolicy RemoteSigned -Scope CurrentUser",
                                               "-ExecutionPolicy Bypass",
                                               "powershell -ExecutionPolicy Bypass -File script.ps1",
                                               "C:\\Windows\\SysWOW64\\cmd.exe",
                                               "powershell Set-ExecutionPolicy RemoteSigned",
                                               "C:\\Windows\\system32\\cmd.exe",
                                               "echo %PROCESSOR_ARCHITECTURE%",
                                               "[Environment]::Is64BitProcess",
                                               "Get-ExecutionPolicy",
                                               "Set-ExecutionPolicy",
                                               "RemoteSigned",
                                               "Unrestricted",
                                               "LocalMachine",
                                               "CurrentUser",
                                               "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser",
                                               "%UserProfile%\\My Documents\\WindowsPowerShell\\Microsoft.PowerShellISE_profile.ps1",
                                               "ExecutionPolicy",
                                               "Get-ExecutionPolicy",
                                               "Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted -Force\n\nSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Bypass -Force",
                                               "Unrestricted",
                                               "Set-ExecutionPolicy Unrestricted",
                                               "CurrentUser",
                                               "RemoteSigned",
                                               "Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;",
                                               "Unrestricted",
                                               "Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;Unrestricted\u0026quot;",
                                               "Help Get-ExecutionPolicy -Full\nHelp Set-ExecutionPolicy -Full",
                                               "Set-ExecutionPolicy RemoteSigned",
                                               "Set-ExecutionPolicy RemoteSigned",
                                               "set-ExecutionPolicy RemoteSigned -Scope CurrentUser",
                                               "Get-ExecutionPolicy",
                                               "ExecutionPolicy",
                                               "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser",
                                               "Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted",
                                               "Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted",
                                               "powershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;",
                                               "powershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;AllSigned\u0026quot;",
                                               "powershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;",
                                               "*.Format.ps1xml",
                                               "*.Types.ps1xml",
                                               "Get-ChildItem",
                                               "-ExecutionPolicy",
                                               "powershell.exe",
                                               "RemoteSigned",
                                               "LocalMachine",
                                               "Set-ExecutionPolicy",
                                               "Unrestricted",
                                               "Set-ExecutionPolicy -Scope CurrentUser RemoteSigned -Force",
                                               "-ExecutionPolicy \u0026lt;policy\u0026gt;",
                                               "Set-ExecutionPolicy -Scope Process ...",
                                               "pwsh.exe -ExecutionPolicy RemoteSigned -File someScript.ps1",
                                               "Get-ExecutionPolicy",
                                               "MachinePolicy",
                                               "CurrentUser",
                                               "powershell Get-Content .\\test.ps1 | Invoke-Expression",
                                               "Set-ExecutionPolicy",
                                               "Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned",
                                               "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\",
                                               "Set-ExecutionPolicy -Scope CurrentUser Unrestricted",
                                               "Set-ExecutionPolicy RemoteSigned -Scope Process",
                                               "Set-ExecutionPolicy RemoteSigned",
                                               "powershell Set-ExecutionPolicy -Scope CurrentUser\n\ncmdlet Set-ExecutionPolicy at command pipeline position 1\nSupply values for the following parameters:\nExecutionPolicy: `RemoteSigned`",
                                               "Get-ExecutionPolicy",
                                               "Set-ExecutionPolicy Unrestricted"
                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:15:41Z",
                         "ViewCount":  5174855,
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_general_ad_846bd5db",
                         "QuestionScore":  2987,
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T16:15:41Z",
                         "TotalScore":  9106,
                         "AnswerCount":  30,
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow Q\u0026A: PowerShell says \u0026quot;execution of scripts is disabled on this system.\u0026quot;",
                         "CodeTemplate":  "Set-ExecutionPolicy RemoteSigned",
                         "CommonMistakes":  [
                                                "Contains warnings about common mistakes and errors"
                                            ],
                         "HasAcceptedAnswer":  true,
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "windows-server-2008-r2"
                                  ],
                         "Abstract":  "# Question: PowerShell says \u0026quot;execution of scripts is disabled on this system.\u0026quot;\n\n**Score:** 2987 | **Views:** 5174855 | **Tags:** powershell, windows-server-2008-r2\n\n**URL:** https://stackoverflow.com/questions/4037939/powershell-says-execution-of-scripts-is-disabled-on-this-system\n\n## Question Body\n\n\u003cp\u003eI am trying to run a \u003ccode\u003ecmd\u003c/code\u003e file that calls a PowerShell script from \u003ccode\u003ecmd.exe\u003c/code\u003e, but I am getting this error:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003e\u003ccode\u003eManagement_Install.ps1\u003c/code\u003e ...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow_qa",
                                             "CredibilityScore":  0.85,
                                             "PublishedAt":  "2025-07-28T16:15:41Z",
                                             "Url":  "https://stackoverflow.com/questions/4037939/powershell-says-execution-of-scripts-is-disabled-on-this-system",
                                             "ScrapedAt":  "2025-07-28T16:15:41Z",
                                             "Id":  "bc0a90ce-87ec-4260-ab1d-31c8c39a770b",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow Q\u0026A: PowerShell says \u0026quot;execution of scripts is disabled on this system.\u0026quot;"
                                         }
                                     ],
                         "Content":  "# Question: PowerShell says \u0026quot;execution of scripts is disabled on this system.\u0026quot;\n\n**Score:** 2987 | **Views:** 5174855 | **Tags:** powershell, windows-server-2008-r2\n\n**URL:** https://stackoverflow.com/questions/4037939/powershell-says-execution-of-scripts-is-disabled-on-this-system\n\n## Question Body\n\n\u003cp\u003eI am trying to run a \u003ccode\u003ecmd\u003c/code\u003e file that calls a PowerShell script from \u003ccode\u003ecmd.exe\u003c/code\u003e, but I am getting this error:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003e\u003ccode\u003eManagement_Install.ps1\u003c/code\u003e cannot be loaded because the execution of scripts is disabled on this system.\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eI ran this command:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -ExecutionPolicy Unrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eWhen I run \u003ccode\u003eGet-ExecutionPolicy\u003c/code\u003e from PowerShell, it returns \u003ccode\u003eUnrestricted\u003c/code\u003e.\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eGet-ExecutionPolicy\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eOutput:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eUnrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\u003chr /\u003e\n\u003cblockquote\u003e\n\u003cp\u003ecd \u0026quot;C:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\u0026quot;\npowershell .\\Management_Install.ps1 1\u003c/p\u003e\n\u003cp\u003eWARNING: Running x86 PowerShell...\u003c/p\u003e\n\u003cp\u003eFile \u003ccode\u003eC:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\\Management_Install.ps1\u003c/code\u003e cannot be loaded because the execution of scripts is disabled on this system. Please see \u0026quot;\u003ccode\u003eget-help about_signing\u003c/code\u003e\u0026quot; for more details.\u003c/p\u003e\n\u003cp\u003eAt line:1 char:25\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003e.\\Management_Install.ps1\u003c/code\u003e \u0026lt;\u0026lt;\u0026lt;\u0026lt;  1\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003eCategoryInfo          : NotSpecified: (:) [], PSSecurityException\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eFullyQualifiedErrorId : RuntimeException\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eC:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\u0026gt; PAUSE\u003c/p\u003e\n\u003cp\u003ePress any key to continue . . .\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003chr /\u003e\n\u003cp\u003eThe system is \u003ca href=\"https://en.wikipedia.org/wiki/Windows_Server_2008\" rel=\"noreferrer\"\u003eWindows Server 2008\u003c/a\u003e R2.\u003c/p\u003e\n\u003cp\u003eWhat am I doing wrong?\u003c/p\u003e\n\n\n## Answers (30 total)\n\n### Answer 1 (Score: 3631) âœ… ACCEPTED ANSWER\n\n\u003cp\u003eIf you\u0027re using \u003ca href=\"https://en.wikipedia.org/wiki/Windows_Server_2008\" rel=\"noreferrer\"\u003eWindows Server 2008\u003c/a\u003e R2 then there is an \u003cem\u003ex64\u003c/em\u003e and \u003cem\u003ex86\u003c/em\u003e version of PowerShell both of which have to have their execution policies set. Did you set the execution policy on both hosts?\u003c/p\u003e\n\u003cp\u003eAs an \u003cem\u003eAdministrator\u003c/em\u003e, you can set the execution policy by typing this into your PowerShell window:\u003c/p\u003e\n\u003cpre class=\"lang-None prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy RemoteSigned\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eFor more information, see \u003cem\u003e\u003ca href=\"https://learn.microsoft.com/powershell/module/microsoft.powershell.security/set-executionpolicy\" rel=\"noreferrer\"\u003eUsing the Set-ExecutionPolicy Cmdlet\u003c/a\u003e\u003c/em\u003e.\u003c/p\u003e\n\u003cp\u003eWhen you are done, you can set the policy back to its default value with:\u003c/p\u003e\n\u003cpre class=\"lang-None prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy Restricted\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou may see an error:\u003c/p\u003e\n\u003cpre class=\"lang-None prettyprint-override\"\u003e\u003ccode\u003eAccess to the registry key\n\u0027HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\ShellIds\\Microsoft.PowerShell\u0027 is denied. \nTo change the execution policy for the default (LocalMachine) scope, \n  start Windows PowerShell with the \u0026quot;Run as administrator\u0026quot; option. \nTo change the execution policy for the current user, \n  run \u0026quot;Set-ExecutionPolicy -Scope CurrentUser\u0026quot;.\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eSo you may need to run the command like this (as seen in comments):\u003c/p\u003e\n\u003cpre class=\"lang-None prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy RemoteSigned -Scope CurrentUser\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 2 (Score: 1186)\n\n\u003cp\u003eYou can bypass this policy for a single file by adding \u003ccode\u003e-ExecutionPolicy Bypass\u003c/code\u003e when running PowerShell\u003c/p\u003e\n\u003cpre class=\"lang-None prettyprint-override\"\u003e\u003ccode\u003epowershell -ExecutionPolicy Bypass -File script.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 3 (Score: 412)\n\n\u003cp\u003eI had a similar issue and noted that the default \u003ccode\u003ecmd\u003c/code\u003e on \u003ca href=\"https://en.wikipedia.org/wiki/Windows_Server_2012\" rel=\"noreferrer\"\u003eWindows Server 2012\u003c/a\u003e, was running the x64 one.\u003c/p\u003e\n\u003cp\u003eFor \u003cstrong\u003eWindows 11\u003c/strong\u003e, \u003cstrong\u003eWindows 10\u003c/strong\u003e, \u003cstrong\u003eWindows 7\u003c/strong\u003e, \u003cstrong\u003eWindows 8\u003c/strong\u003e, \u003cstrong\u003eWindows Server 2008 R2\u003c/strong\u003e or \u003cstrong\u003eWindows Server 2012\u003c/strong\u003e, run the following commands as \u003cstrong\u003eAdministrator\u003c/strong\u003e:\u003c/p\u003e\n\u003cp\u003e\u003cem\u003ex86\u003c/em\u003e (32 bit)\u003cbr/\u003e\nOpen \u003ccode\u003eC:\\Windows\\SysWOW64\\cmd.exe\u003c/code\u003e\u003cbr/\u003e\nRun the command \u003ccode\u003epowershell Set-ExecutionPolicy RemoteSigned\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003e\u003cem\u003ex64\u003c/em\u003e (64 bit)\u003cbr/\u003e\nOpen \u003ccode\u003eC:\\Windows\\system32\\cmd.exe\u003c/code\u003e\u003cbr/\u003e\nRun the command \u003ccode\u003epowershell Set-ExecutionPolicy RemoteSigned\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eYou can check mode using\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eIn CMD: \u003ccode\u003eecho %PROCESSOR_ARCHITECTURE%\u003c/code\u003e\u003c/li\u003e\n\u003cli\u003eIn Powershell: \u003ccode\u003e[Environment]::Is64BitProcess\u003c/code\u003e\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003e\u003cstrong\u003eReferences:\u003c/strong\u003e\u003cbr/\u003e\n\u003ca href=\"https://technet.microsoft.com/library/hh847748.aspx\" rel=\"noreferrer\"\u003eMSDN - Windows PowerShell execution policies\u003c/a\u003e\u003cbr/\u003e\n\u003ca href=\"http://www.samlogic.net/articles/32-64-bit-windows-folder-x86-syswow64.htm\" rel=\"noreferrer\"\u003eWindows - 32bit vs 64bit directory explanation\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 4 (Score: 280)\n\n\u003cp\u003eMost of the existing answers explain the \u003cem\u003eHow\u003c/em\u003e, but very few explain the \u003cem\u003eWhy\u003c/em\u003e. And before you go around executing code from strangers on the Internet, especially code that disables security measures, you should understand exactly what you\u0027re doing. So here\u0027s a little more detail on this problem.\u003c/p\u003e\n\u003cp\u003eFrom the TechNet \u003ca href=\"http://technet.microsoft.com/library/hh847748.aspx\" rel=\"noreferrer\"\u003eAbout Execution Policies Page\u003c/a\u003e:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003eWindows PowerShell execution policies let you determine the conditions under which Windows PowerShell loads configuration files and runs scripts.\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eThe benefits of which, as enumerated by \u003ca href=\"http://www.darkoperator.com/blog/2013/3/5/powershell-basics-execution-policy-part-1.html\" rel=\"noreferrer\"\u003ePowerShell Basics - Execution Policy and Code Signing\u003c/a\u003e, are:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cstrong\u003eControl of Execution\u003c/strong\u003e - Control the level of trust for executing scripts.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003eCommand Highjack\u003c/strong\u003e - Prevent injection of commands in my path.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003eIdentity\u003c/strong\u003e - Is the script created and signed by a developer I trust and/or a signed with a certificate from a Certificate Authority I trust.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003eIntegrity\u003c/strong\u003e - Scripts cannot be modified by malware or malicious user.\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eTo check your current execution policy, you can run \u003ca href=\"http://technet.microsoft.com/library/hh849821.aspx\" rel=\"noreferrer\"\u003e\u003ccode\u003eGet-ExecutionPolicy\u003c/code\u003e\u003c/a\u003e. But you\u0027re probably here because you want to change it.\u003c/p\u003e\n\u003cp\u003eTo do so you\u0027ll run the \u003ca href=\"http://technet.microsoft.com/library/hh849812.aspx\" rel=\"noreferrer\"\u003e\u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e\u003c/a\u003e cmdlet.\u003c/p\u003e\n\u003cp\u003eYou\u0027ll have two major decisions to make when updating the execution policy.\u003c/p\u003e\n\u003ch3\u003eExecution Policy Type:\u003c/h3\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eRestricted\u003c/code\u003e\u003c/strong\u003e\u003csup\u003e†\u003c/sup\u003e - No Script either local, remote or downloaded can be executed on the system.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eAllSigned\u003c/code\u003e\u003c/strong\u003e - All script that are ran require to be digitally signed.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eRemoteSigned\u003c/code\u003e\u003c/strong\u003e - All remote scripts (UNC) or downloaded need to be signed.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eUnrestricted\u003c/code\u003e\u003c/strong\u003e - No signature for any type of script is required.\u003c/li\u003e\n\u003c/ul\u003e\n\u003ch3\u003eScope of new Change\u003c/h3\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eLocalMachine\u003c/code\u003e\u003c/strong\u003e\u003csup\u003e†\u003c/sup\u003e - The execution policy affects all users of the computer.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eCurrentUser\u003c/code\u003e\u003c/strong\u003e - The execution policy affects only the current user.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eProcess\u003c/code\u003e\u003c/strong\u003e - The execution policy affects only the current Windows PowerShell process.\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003e\u003csup\u003e† = Default\u003c/sup\u003e\u003c/p\u003e\n\u003cp\u003e\u003cem\u003eFor example\u003c/em\u003e: if you wanted to change the policy to RemoteSigned for just the CurrentUser, you\u0027d run the following command:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cstrong\u003eNote\u003c/strong\u003e: In order to change the Execution policy, you must be running \u003cstrong\u003ePowerShell As Administrator\u003c/strong\u003e.\nIf you are in regular mode and try to change the execution policy, you\u0027ll get the following error:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003eAccess to the registry key \u0027HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\ShellIds\\Microsoft.PowerShell\u0027 is denied. To change the execution policy for the default (LocalMachine) scope, start Windows PowerShell with the \u0026quot;Run as administrator\u0026quot; option.\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eIf you want to tighten up the internal restrictions on your own scripts that have not been downloaded from the Internet (or at least don\u0027t contain the UNC metadata), you can force the policy to only run signed scripts.  To sign your own scripts, you can follow the instructions on Scott Hanselman\u0027s article on \u003ca href=\"http://www.hanselman.com/blog/SigningPowerShellScripts.aspx\" rel=\"noreferrer\"\u003eSigning PowerShell Scripts\u003c/a\u003e.\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003eNote\u003c/strong\u003e: Most people are likely to get this error whenever they open PowerShell because the first thing PowerShell tries to do when it launches is execute your user profile script that sets up your environment however you like it.\u003c/p\u003e\n\u003cp\u003eThe file is typically located in:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e%UserProfile%\\My Documents\\WindowsPowerShell\\Microsoft.PowerShellISE_profile.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou can find the exact location by running the PowerShell variable\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e$profile\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eIf there\u0027s nothing that you care about in the profile, and don\u0027t want to fuss with your security settings, you can just delete it and PowerShell won\u0027t find anything that it cannot execute.\u003c/p\u003e\n\n\n### Answer 5 (Score: 80)\n\n\u003cp\u003eWe can get the status of current \u003ccode\u003eExecutionPolicy\u003c/code\u003e by the command below:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eGet-ExecutionPolicy\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eBy default it is \u003cstrong\u003eRestricted\u003c/strong\u003e. To allow the execution of PowerShell scripts we need to set this ExecutionPolicy either as \u003cstrong\u003eUnrestricted\u003c/strong\u003e or \u003cstrong\u003eBypass\u003c/strong\u003e.\u003c/p\u003e\n\u003cp\u003eWe can set the policy for Current User as \u003ccode\u003eBypass\u003c/code\u003e by using any of the below PowerShell commands:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted -Force\n\nSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Bypass -Force\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cstrong\u003eUnrestricted\u003c/strong\u003e policy loads all configuration files and runs all scripts. If you run an unsigned script that was downloaded from the Internet, you are prompted for permission before it runs.\u003c/p\u003e\n\u003cp\u003eWhereas in \u003cstrong\u003eBypass\u003c/strong\u003e policy, nothing is blocked and there are no warnings or prompts during script execution. Bypass \u003ccode\u003eExecutionPolicy\u003c/code\u003e is more relaxed than \u003ccode\u003eUnrestricted\u003c/code\u003e.\u003c/p\u003e\n\n\n### Answer 6 (Score: 68)\n\n\u003cp\u003eAlso running this command before the script also solves the issue:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy Unrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 7 (Score: 59)\n\n\u003cp\u003eIf you are in an environment where you are not an administrator, you can set the Execution Policy just for you (\u003ccode\u003eCurrentUser\u003c/code\u003e), and it will not require administrator.\u003c/p\u003e\n\u003cp\u003eYou can set it to \u003ccode\u003eRemoteSigned\u003c/code\u003e:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eor \u003ccode\u003eUnrestricted\u003c/code\u003e:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;Unrestricted\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou can read all about Getting and Setting Execution policy in the help entries:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eHelp Get-ExecutionPolicy -Full\nHelp Set-ExecutionPolicy -Full\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 8 (Score: 58)\n\n\u003cp\u003eIn Windows 7:\u003c/p\u003e\n\u003cp\u003eGo to Start Menu and search for \u0026quot;Windows PowerShell ISE\u0026quot;.\u003c/p\u003e\n\u003cp\u003eRight click the x86 version and choose \u0026quot;Run as administrator\u0026quot;.\u003c/p\u003e\n\u003cp\u003eIn the top part, paste \u003ccode\u003eSet-ExecutionPolicy RemoteSigned\u003c/code\u003e; run the script. Choose \u0026quot;Yes\u0026quot;.\u003c/p\u003e\n\u003cp\u003eRepeat these steps for the 64-bit version of Powershell ISE too (the non x86 version).\u003c/p\u003e\n\n\n### Answer 9 (Score: 43)\n\n\u003cp\u003eRemoteSigned: all scripts you created yourself will be run, and all scripts downloaded from the Internet will need to be signed by a trusted publisher.\u003c/p\u003e\n\u003cp\u003eOK, change the policy by simply typing:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy RemoteSigned\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 10 (Score: 36)\n\n\u003cp\u003eFirst, you need to open the PowerShell window and run this command.\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eset-ExecutionPolicy RemoteSigned -Scope CurrentUser\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eThen it will ask you to confirm. Type \u003ckbd\u003eY\u003c/kbd\u003e and press \u003ckbd\u003eEnter\u003c/kbd\u003e.\u003c/p\u003e\n\u003cp\u003eWhen you run this command, you can see that your system has set all policies for the current user as remotely. It will take a few seconds to complete this process.\u003c/p\u003e\n\u003cp\u003eThe image will be shown like below:\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/XyFUJ.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/XyFUJ.png\" alt=\"Enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003cp\u003eTo check if the execution policy has set. Type:\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eGet-ExecutionPolicy\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eIf it was set, the output would be like this:\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/0wTAV.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/0wTAV.png\" alt=\"Enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 11 (Score: 33)\n\n\u003cp\u003eI\u0027m using \u003cstrong\u003eWindows 10\u003c/strong\u003e and was unable to run any command. The only command that gave me some clues was this: \u003c/p\u003e\n\n\u003cp\u003e[x64]\u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003col\u003e\n  \u003cli\u003eOpen C:\\Windows\\SysWOW64\\cmd.exe \u003cem\u003e[as administrator]\u003c/em\u003e\u003c/li\u003e\n  \u003cli\u003eRun the command\u003e \u003cstrong\u003epowershell Set-ExecutionPolicy Unrestricted\u003c/strong\u003e\u003c/li\u003e\n  \u003c/ol\u003e\n\u003c/blockquote\u003e\n\n\u003cp\u003eBut this didn\u0027t work. It was limited. Probably new security policies for Windows10. I had this error: \u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003cp\u003eSet-ExecutionPolicy: Windows PowerShell updated your execution policy successfully, but the setting is overridden by a policy defined at a more specific scope. Due to the override, your shell will retain its current effective execution policy of...\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\u003cp\u003eSo I found another way (\u003cstrong\u003esolution\u003c/strong\u003e):\u003c/p\u003e\n\n\u003col\u003e\n\u003cli\u003eOpen Run Command/Console (\u003ckbd\u003eWin\u003c/kbd\u003e + \u003ckbd\u003eR\u003c/kbd\u003e) \u003c/li\u003e\n\u003cli\u003eType: \u003cstrong\u003egpedit.msc\u003c/strong\u003e (\u003ca href=\"http://en.wikipedia.org/wiki/Group_Policy\" rel=\"noreferrer\"\u003eGroup Policy\u003c/a\u003e Editor)\u003c/li\u003e\n\u003cli\u003eBrowse to \u003cem\u003eLocal Computer Policy\u003c/em\u003e -\u003e \u003cem\u003eComputer Configuration\u003c/em\u003e -\u003e \u003cem\u003eAdministrative Templates\u003c/em\u003e -\u003e \u003cem\u003eWindows Components\u003c/em\u003e -\u003e \u003cem\u003eWindows Powershell\u003c/em\u003e. \u003c/li\u003e\n\u003cli\u003eEnable \"\u003cstrong\u003eTurn on Script Execution\u003c/strong\u003e\"\u003c/li\u003e\n\u003cli\u003eSet the policy as needed. I set mine to \"\u003cstrong\u003eAllow all scripts\u003c/strong\u003e\".\u003c/li\u003e\n\u003c/ol\u003e\n\n\u003cp\u003eNow open PowerShell and enjoy ;)\u003c/p\u003e\n\n\n### Answer 12 (Score: 30)\n\n\u003cp\u003eOpen a Windows PowerShell command window and run the below query to change \u003ccode\u003eExecutionPolicy\u003c/code\u003e:\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003e\u003ccode\u003eSet-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser\u003c/code\u003e\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003eIf it asks for confirming changes, press \u003ckbd\u003eY\u003c/kbd\u003e and hit \u003ckbd\u003eEnter\u003c/kbd\u003e.\u003c/p\u003e\n\n\n### Answer 13 (Score: 25)\n\n\u003cpre\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis worked for me\u003c/p\u003e\n\n\n### Answer 14 (Score: 22)\n\n\u003cp\u003eYou should run this command:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 15 (Score: 19)\n\n\u003cp\u003e\u003ckbd\u003eWin\u003c/kbd\u003e + \u003ckbd\u003eR\u003c/kbd\u003e and type copy paste command and press \u003ckbd\u003eOK\u003c/kbd\u003e:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eAnd execute your script.\u003c/p\u003e\n\u003cp\u003eThen revert changes like:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;AllSigned\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 16 (Score: 14)\n\n\u003cp\u003eOpen the command prompt in Windows.\nIf the problem is only with PowerShell, use the following command:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 17 (Score: 14)\n\n\n\u003cp\u003eThere\u0027s great information in the existing answers, but let me attempt a \u003cstrong\u003esystematic overview\u003c/strong\u003e:\u003c/p\u003e\n\u003ch3\u003eContext\u003c/h3\u003e\n\u003cp\u003ePowerShell\u0027s effective \u003cstrong\u003e\u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Execution_Policies\" rel=\"nofollow noreferrer\"\u003eexecution policy\u003c/a\u003e applies\u003c/strong\u003e:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003eto \u003cstrong\u003ePowerShell code stored in \u003cem\u003efiles\u003c/em\u003e\u003c/strong\u003e, which means:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eregular script files (\u003ccode\u003e*.ps1\u003c/code\u003e)\u003c/li\u003e\n\u003cli\u003escript \u003cem\u003emodule\u003c/em\u003e files (\u003ccode\u003e*.psm1\u003c/code\u003e) (modules implemented in PowerShell)\u003c/li\u003e\n\u003cli\u003emodules that are bundled with \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Format.ps1xml\" rel=\"nofollow noreferrer\"\u003eformatting\u003c/a\u003e and \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Types.ps1xml\" rel=\"nofollow noreferrer\"\u003etype-extension\u003c/a\u003e files (\u003ccode\u003e*.Format.ps1xml\u003c/code\u003e and \u003ccode\u003e*.Types.ps1xml\u003c/code\u003e) - even if those files happen \u003cem\u003enot\u003c/em\u003e to contain embedded PowerShell script blocks.\u003c/li\u003e\n\u003cli\u003eIt does \u003cem\u003enot\u003c/em\u003e apply to:\n\u003cul\u003e\n\u003cli\u003ecalls to (binary) \u003cem\u003ecmdlets\u003c/em\u003e (e.g., \u003ccode\u003eGet-ChildItem\u003c/code\u003e), \u003cem\u003eexcept\u003c/em\u003e for third-party binary cmdlets that come with modules that encompass formatting and type-extension files, as discussed above.\u003c/li\u003e\n\u003cli\u003ecommands submitted interactively or passed to the PowerShell \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_powershell_exe\" rel=\"nofollow noreferrer\"\u003eCLI\u003c/a\u003e via the\u003cbr /\u003e\n\u003ccode\u003e-Command\u003c/code\u003e parameter (unless these commands directly or indirectly call script files as defined above).\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003cstrong\u003eon Windows only\u003c/strong\u003e (that is, on \u003cem\u003eUnix-like\u003c/em\u003e platforms (Linux, macOS) execution policies do not apply and \u003cem\u003eno\u003c/em\u003e restrictions are placed on executing PowerShell code)\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eIn \u003cstrong\u003e\u003cem\u003eworkstation\u003c/em\u003e editions of Windows\u003c/strong\u003e, \u003cstrong\u003escript-file execution is \u003cem\u003edisabled\u003c/em\u003e by default\u003c/strong\u003e (policy \u003cstrong\u003e\u003ccode\u003eRestricted\u003c/code\u003e\u003c/strong\u003e), requiring either a \u003cem\u003epersistent\u003c/em\u003e modification of the policy to enable it, or a \u003cem\u003ecurrent-process-only\u003c/em\u003e modification such as via the \u003ccode\u003e-ExecutionPolicy\u003c/code\u003e parameter when calling the PowerShell \u003cem\u003eCLI\u003c/em\u003e, \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_powershell_exe\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003epowershell.exe\u003c/code\u003e\u003c/a\u003e (\u003cem\u003eWindows PowerShell\u003c/em\u003e edition) / \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_pwsh\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003epwsh.exe\u003c/code\u003e\u003c/a\u003e (\u003cem\u003ePowerShell (Core) 7\u003c/em\u003e edition).\u003cbr /\u003e\nIn \u003cstrong\u003erecent \u003cem\u003eserver\u003c/em\u003e editions of Windows\u003c/strong\u003e, the \u003cstrong\u003edefault policy is \u003ccode\u003eRemoteSigned\u003c/code\u003e\u003c/strong\u003e, meaning that while locally stored scripts (including on file shares) may be executed, downloaded-from-the-web scripts only execute if they\u0027re \u003cem\u003esigned\u003c/em\u003e.\u003c/p\u003e\n\u003cp\u003eExecution \u003cstrong\u003epolicies are maintained \u003cem\u003eseparately\u003c/em\u003e\u003c/strong\u003e:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003efor the two \u003cstrong\u003ePowerShell \u003cem\u003eeditions\u003c/em\u003e\u003c/strong\u003e:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003ethe legacy, \u003cem\u003eWindows-only\u003c/em\u003e, ships-with-Windows \u003cstrong\u003e\u003cem\u003eWindows PowerShell\u003c/em\u003e\u003c/strong\u003e edition (whose latest and last version is v5.1.x)\u003c/li\u003e\n\u003cli\u003ethe modern, \u003cem\u003ecross-platform\u003c/em\u003e, install-on-demand \u003cstrong\u003e\u003ca href=\"https://github.com/PowerShell/PowerShell/blob/master/README.md\" rel=\"nofollow noreferrer\"\u003e\u003cem\u003ePowerShell (Core) 7\u003c/em\u003e\u003c/a\u003e\u003c/strong\u003e edition.\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003efor the \u003cstrong\u003e\u003cem\u003e32-bit and 64-bit\u003c/em\u003e versions of \u003cem\u003eWindows PowerShell\u003c/em\u003e\u003c/strong\u003e (both of which are preinstalled)\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eNote: If you were to install 32-bit and 64-bit versions of \u003cem\u003ePowerShell (Core)\u003c/em\u003e side by side (which would be unusual), only the \u003ccode\u003eLocalMachine\u003c/code\u003e scope would be bitness-specific.\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eFor a given edition / bitness combination of PowerShell, the \u003cstrong\u003eexecution policies can be set in \u003cem\u003emultiple scopes\u003c/em\u003e\u003c/strong\u003e, but \u003cstrong\u003ethere\u0027s only ever \u003cem\u003eone effective\u003c/em\u003e policy\u003c/strong\u003e, based on precedence rules - see below.\u003c/p\u003e\n\u003chr /\u003e\n\u003ch3\u003eDetails\u003c/h3\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003e\u003cstrong\u003eIn PowerShell \u003cem\u003eon Windows\u003c/em\u003e, script-file execution is \u003cem\u003edisabled\u003c/em\u003e by default\u003c/strong\u003e in \u003cstrong\u003e\u003cem\u003eworkstation\u003c/em\u003e editions\u003c/strong\u003e of Windows (on Unix, execution policies do not apply); that is, the default execution policy in workstation editions of Windows is \u003cstrong\u003e\u003ccode\u003eRestricted\u003c/code\u003e\u003c/strong\u003e, whereas in \u003cstrong\u003e\u003cem\u003eserver\u003c/em\u003e editions\u003c/strong\u003e, it is \u003cstrong\u003e\u003ccode\u003eRemoteSigned\u003c/code\u003e\u003c/strong\u003e; see the conceptual \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Execution_Policies\" rel=\"nofollow noreferrer\"\u003eabout_Execution_Policies\u003c/a\u003e help topic for a description of all available policies.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eTo \u003cstrong\u003eset a (local) policy that permits script execution\u003c/strong\u003e, use \u003cstrong\u003e\u003ca href=\"https://learn.microsoft.com/powershell/module/microsoft.powershell.security/set-executionpolicy\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e\u003c/a\u003e\u003c/strong\u003e with a policy of \u003ccode\u003eAllSigned\u003c/code\u003e, \u003ccode\u003eRemoteSigned\u003c/code\u003e, \u003ccode\u003eUnrestricted\u003c/code\u003e, or \u003ccode\u003eBypass\u003c/code\u003e, in descending order of security. There are \u003cstrong\u003ethree scopes\u003c/strong\u003e that \u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e can target, using the \u003cstrong\u003e\u003ccode\u003e-Scope\u003c/code\u003e parameter\u003c/strong\u003e (see below); \u003cstrong\u003echanging the \u003ccode\u003eLocalMachine\u003c/code\u003e scope\u0027s policy requires \u003cem\u003eelevation\u003c/em\u003e (running as admin)\u003c/strong\u003e.\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003e\u003cstrong\u003eA frequently used policy that provides a \u003cem\u003ebalance between security and convenience\u003c/em\u003e is \u003ccode\u003eRemoteSigned\u003c/code\u003e\u003c/strong\u003e, which allows \u003cem\u003elocal\u003c/em\u003e scripts - including from \u003cem\u003enetwork shares\u003c/em\u003e - to execute \u003cem\u003ewithout containing a \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Signing\" rel=\"nofollow noreferrer\"\u003esignature\u003c/a\u003e\u003c/em\u003e, while requiring scripts downloaded \u003cem\u003efrom the internet\u003c/em\u003e to be signed (assuming that the downloading mechanism marks such as scripts as internet-originated, which web browsers do by default). For instance, \u003cstrong\u003eto set the current user\u0027s execution policy to \u003ccode\u003eRemoteSigned\u003c/code\u003e\u003c/strong\u003e, run the following (\u003ccode\u003e-Force\u003c/code\u003e bypasses the confirmation prompt that is shown by default):\u003c/p\u003e\n\u003cpre class=\"lang-bash prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser RemoteSigned -Force\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eTo \u003cem\u003eunset\u003c/em\u003e a previously set policy in a given scope, use the \u003ccode\u003eUndefined\u003c/code\u003e policy.\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eThe \u003cstrong\u003ePowerShell \u003cem\u003eCLI\u003c/em\u003e\u003c/strong\u003e (\u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_powershell_exe\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003epowershell.exe\u003c/code\u003e\u003c/a\u003e for \u003cem\u003eWindows PowerShell\u003c/em\u003e, \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Pwsh\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003epwsh.exe\u003c/code\u003e\u003c/a\u003e for \u003cem\u003ePowerShell (Core) 7\u003c/em\u003e) accepts a process-specific \u003ccode\u003e-ExecutionPolicy \u0026lt;policy\u0026gt;\u003c/code\u003e argument too, which is often used for \u003cstrong\u003e\u003cem\u003ead-hoc\u003c/em\u003e policy overrides\u003c/strong\u003e (only for the process being created, the equivalent of \u003ccode\u003eSet-ExecutionPolicy -Scope Process ...\u003c/code\u003e); e.g.:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epwsh.exe -ExecutionPolicy RemoteSigned -File someScript.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003cstrong\u003eImportant\u003c/strong\u003e:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003eExecution policies can also be set via \u003cstrong\u003e\u003ca href=\"https://en.wikipedia.org/wiki/Group_Policy\" rel=\"nofollow noreferrer\"\u003eGroup Policy Objects (GPOs)\u003c/a\u003e\u003c/strong\u003e, in which case they \u003cstrong\u003ecan \u003cem\u003enot\u003c/em\u003e be changed or overridden with \u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e or via the CLI\u0027s \u003ccode\u003e-ExecutionPolicy\u003c/code\u003e parameter\u003c/strong\u003e: see \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Group_Policy_Settings\" rel=\"nofollow noreferrer\"\u003eabout_Group_Policy_Settings\u003c/a\u003e\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eExecution policies can be set in \u003cstrong\u003evarious scopes\u003c/strong\u003e, and which one is in effect is determined by their \u003cstrong\u003eprecedence\u003c/strong\u003e (run \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/Microsoft.PowerShell.Security/Get-ExecutionPolicy\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003eGet-ExecutionPolicy\u003c/code\u003e\u003c/a\u003e\u003ccode\u003e -List\u003c/code\u003e to see all scopes and their respective policies), in \u003cstrong\u003edescending order\u003c/strong\u003e:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003ccode\u003eMachinePolicy\u003c/code\u003e (via GPO; cannot be overridden locally)\u003csup\u003e[1]\u003c/sup\u003e\u003c/li\u003e\n\u003cli\u003e\u003ccode\u003eUserPolicy\u003c/code\u003e (via GPO; cannot be overridden locally)\u003csup\u003e[1]\u003c/sup\u003e\u003c/li\u003e\n\u003cli\u003e\u003ccode\u003eProcess\u003c/code\u003e (current process only; typically set ad-hoc via the CLI)\u003c/li\u003e\n\u003cli\u003e\u003ccode\u003eCurrentUser\u003c/code\u003e (as set by \u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e)\u003c/li\u003e\n\u003cli\u003e\u003ccode\u003eLocalMachine\u003c/code\u003e (as set by \u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e, with admin rights)\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003chr /\u003e\n\u003cp\u003e\u003csup\u003e[1] This applies to \u003cem\u003edomain\u003c/em\u003e-wide GPOs. \u003cem\u003eLocal\u003c/em\u003e GPOs \u003cem\u003ecan\u003c/em\u003e  be modified locally, namely via \u003ccode\u003egpedit.msc\u003c/code\u003e or directly via the registry, which in the case of the \u003cem\u003emachine\u003c/em\u003e policy requires administrative privileges.\u003c/sup\u003e\u003c/p\u003e\n\n\n### Answer 18 (Score: 11)\n\n\u003cp\u003eSetting the execution policy is environment-specific. If you are trying to execute a script from the running x86 \u003ca href=\"https://technet.microsoft.com/en-us/library/dd315244.aspx\" rel=\"noreferrer\"\u003eISE\u003c/a\u003e you have to use the x86 PowerShell to set the execution policy. Likewise, if you are running the 64-bit ISE you have to set the policy with the 64-bit PowerShell.\u003c/p\u003e\n\n\n### Answer 19 (Score: 11)\n\n\u003col\u003e\n\u003cli\u003eOpen Run Command/Console ( Win + R )\nType: \u003cstrong\u003egpedit. msc\u003c/strong\u003e (Group Policy Editor)\u003c/li\u003e\n\u003cli\u003eBrowse to \u003cstrong\u003eLocal Computer Policy\u003c/strong\u003e -\u003e \u003cstrong\u003eComputer Configuration\u003c/strong\u003e -\u003e \u003cstrong\u003eAdministrative Templates\u003c/strong\u003e -\u003e \u003cstrong\u003eWindows Components\u003c/strong\u003e -\u003e \u003cstrong\u003eWindows Powershell.\u003c/strong\u003e\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003eEnable\u003c/strong\u003e \"\u003cstrong\u003eTurn on Script Execution\u003c/strong\u003e\"\nSet the policy as needed. \u003cstrong\u003eI set mine to \"Allow all scripts\"\u003c/strong\u003e.\u003c/li\u003e\n\u003c/ol\u003e\n\n\u003cp\u003eNow run the run command what ever you are using.. Trust this the app will runs.. Enjoy :)\u003c/p\u003e\n\n\n### Answer 20 (Score: 10)\n\n\u003cp\u003eYou can also bypass this by using the following command:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell Get-Content .\\test.ps1 | Invoke-Expression\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou can also read this article by Scott Sutherland that explains 15 different ways to bypass the PowerShell \u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e if you don\u0027t have administrator privileges:\u003c/p\u003e\n\u003cp\u003e\u003cem\u003e\u003ca href=\"https://blog.netspi.com/15-ways-to-bypass-the-powershell-execution-policy/\" rel=\"nofollow noreferrer\"\u003e15 Ways to Bypass the PowerShell Execution Policy\u003c/a\u003e\u003c/em\u003e\u003c/p\u003e\n\n\n### Answer 21 (Score: 10)\n\n\u003cp\u003eyou may try this and select \"All\" Option\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 22 (Score: 10)\n\n\u003cp\u003eI have also faced a similar issue. Try this.\u003c/p\u003e\n\u003cp\u003eAs I\u0027m using Windows, I followed the steps as given below.\nOpen a command prompt as an administrator and then go to this path:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eC:\\Users\\<USER>\\AppData\\Roaming\\npm\\\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eLook for the file ng.ps1 in this folder (directory)\nand then delete it (\u003cem\u003edel ng.ps1\u003c/em\u003e).\u003c/p\u003e\n\u003cp\u003eYou can also clear npm cache after this though it should work without this step as well.\u003c/p\u003e\n\n\n### Answer 23 (Score: 9)\n\n\u003cp\u003eIf you have Git installed, just use \u003ca href=\"https://superuser.com/questions/1053633/what-is-git-bash-for-windows-anyway\"\u003eGit Bash\u003c/a\u003e.\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/59WhL.png\" rel=\"nofollow noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/59WhL.png\" alt=\"Enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 24 (Score: 8)\n\n\u003cp\u003eFor Windows 11...\u003c/p\u003e\n\u003cp\u003eIt is indeed very easy. Just open the settings application.\nNavigate to \u003cem\u003ePrivacy and Security\u003c/em\u003e:\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/q76IF.png\" rel=\"nofollow noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/q76IF.png\" alt=\"Privacy and security image\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003cp\u003eClick on \u003cem\u003eFor Developers\u003c/em\u003e and scroll to the bottom and find the PowerShell option under which check the checkbox stating \u0026quot;Change the execution policy ... remote scripts\u0026quot;.\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/Snz6j.png\" rel=\"nofollow noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/Snz6j.png\" alt=\"Developer options image\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 25 (Score: 8)\n\n\u003cp\u003eIn VS code just run this command:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser Unrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 26 (Score: 7)\n\n\u003cp\u003eIn the PowerShell \u003ca href=\"https://technet.microsoft.com/en-us/library/dd315244.aspx\" rel=\"nofollow noreferrer\"\u003eISE\u003c/a\u003e editor I found running the following line first allowed scripts.\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy RemoteSigned -Scope Process\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 27 (Score: 7)\n\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy RemoteSigned\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eExecuting this command in administrator mode in PowerShell will solve the problem.\u003c/p\u003e\n\n\n### Answer 28 (Score: 7)\n\n\u003cp\u003eIn Window 10:\u003c/p\u003e\n\u003cp\u003eIf you are not administrator, you can use this:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell Set-ExecutionPolicy -Scope CurrentUser\n\ncmdlet Set-ExecutionPolicy at command pipeline position 1\nSupply values for the following parameters:\nExecutionPolicy: `RemoteSigned`\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eIt solved my problem like a charm!\u003c/p\u003e\n\n\n### Answer 29 (Score: 6)\n\n\u003cp\u003eIn powershell\u003c/p\u003e\n\u003cp\u003eTo check the current execution policy, use the following command:\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eGet-ExecutionPolicy\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eTo change the execution policy to Unrestricted, which allows running any script without digital signatures, use the following command:\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eSet-ExecutionPolicy Unrestricted\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eThis solution worked for me, but be careful of the security risks involved.\u003c/p\u003e\n\n\n### Answer 30 (Score: 5)\n\n\u003col\u003e\n\u003cli\u003eOpen PowerShell as Administrator and run \u003cstrong\u003eSet-ExecutionPolicy -Scope CurrentUser\u003c/strong\u003e\u003c/li\u003e\n\u003cli\u003eProvide \u003cstrong\u003eRemoteSigned\u003c/strong\u003e and press Enter\u003c/li\u003e\n\u003cli\u003eRun \u003cstrong\u003eSet-ExecutionPolicy -Scope CurrentUser\u003c/strong\u003e\u003c/li\u003e\n\u003cli\u003eProvide \u003cstrong\u003eUnrestricted\u003c/strong\u003e and press Enter\u003c/li\u003e\n\u003c/ol\u003e\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "$PSVersionTable.PSVersion",
                                               "$Host.Version",
                                               "(Get-Host).Version",
                                               "PS C:\\\u0026gt; $PSVersionTable.PSVersion\n\nMajor  Minor  Build  Revision\n-----  -----  -----  --------\n4      0      -1     -1",
                                               "$PSVersionTable",
                                               "get-host\n\nName             : ConsoleHost\nVersion          : 2.0\nInstanceId       : d730016e-2875-4b57-9cd6-d32c8b71e18a\nUI               : System.Management.Automation.Internal.Host.InternalHostUserInterface\nCurrentCulture   : en-GB\nCurrentUICulture : en-US\nPrivateData      : Microsoft.PowerShell.ConsoleHost+ConsoleColorProxy\nIsRunspacePushed : False\nRunspace         : System.Management.Automation.Runspaces.LocalRunspace\n\n$PSVersionTable\n\nName                           Value\n----                           -----\nCLRVersion                     2.0.50727.4200\nBuildVersion                   6.0.6002.18111\nPSVersion                      2.0\nWSManStackVersion              2.0\nPSCompatibleVersions           {1.0, 2.0}\nSerializationVersion           *******\nPSRemotingProtocolVersion      2.1",
                                               "$psversiontable",
                                               "1 \u0026gt;  $psversiontable\n\nName                           Value                                           \n----                           -----                                           \nCLRVersion                     2.0.50727.4927                                  \nBuildVersion                   6.1.7600.16385                                  \nPSVersion                      2.0                                             \nWSManStackVersion              2.0                                             \nPSCompatibleVersions           {1.0, 2.0}                                      \nSerializationVersion           *******                                         \nPSRemotingProtocolVersion      2.1",
                                               "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\PowerShell\\1\\Install",
                                               "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\3",
                                               "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\PowerShellEngine\\PowerShellVersion",
                                               "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\3\\PowerShellEngine\\PowerShellVersion",
                                               "$isV2 = test-path variable:\\psversiontable",
                                               "function Get-PSVersion {\n    if (test-path variable:psversiontable) {$psversiontable.psversion} else {[version]\"*******\"}\n}",
                                               "powershell -Command \"$PSVersionTable.PSVersion\"",
                                               "powershell -command \"(Get-Variable PSVersionTable -ValueOnly).PSVersion\"",
                                               "Get-Host | Select-Object Version",
                                               "Version\n-------\n3.0",
                                               "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1",
                                               "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\PowerShellEngine",
                                               "HKLM\\Software\\Microsoft\\PowerShellCore\\InstalledVersions\\\u0026lt;GUID\u0026gt;",
                                               "\u0026lt;GUID\u0026gt;",
                                               "SemanticVersion",
                                               "Path                                                                                           Name              Type Data\n----                                                                                           ----              ---- ----\nHKLM:\\SOFTWARE\\Microsoft\\PowerShellCore\\InstalledVersions\\31ab5147-9a97-4452-8443-d9709f0516e1 SemanticVersion String 7.1.3",
                                               "Get-ItemPropertyValue",
                                               "PS\u0026gt; Get-ItemPropertyValue -Path \u0026quot;HKLM:\\SOFTWARE\\Microsoft\\PowerShellCore\\InstalledVersions\\*\u0026quot; -Name \u0026quot;SemanticVersion\u0026quot;\n\n7.1.3",
                                               "C:\\Users\\<USER>\u0026gt;powershell\n\nWindows PowerShell\nCopyright (C) 2009 Microsoft Corporation. All rights reserved.\n\nPS C:\\Users\\<USER>\u0026gt;",
                                               "$PSVersionTable.PSVersion",
                                               "PS C:\\Users\\<USER>\u0026gt; $PSVersionTable.PSVersion\n\nMajor  Minor  Build  Revision\n-----  -----  -----  --------\n2      0      -1     -1\n\nPS C:\\Users\\<USER>\u0026gt;",
                                               "$host.version",
                                               "$psversiontable.psversion",
                                               "Get-Variable",
                                               "Get-Variable | where {$_.Name -Like \u0027*version*\u0027} | %{$_[0].Value}",
                                               "$psVersion = $PSVersionTable.PSVersion\nIf ($psVersion)\n{\n    #PowerShell Version Mapping\n    $psVersionMappings = @()\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.1.14393.0\u0027;FriendlyName=\u0027Windows PowerShell 5.1 Preview\u0027;ApplicableOS=\u0027Windows 10 Anniversary Update\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.1.14300.1000\u0027;FriendlyName=\u0027Windows PowerShell 5.1 Preview\u0027;ApplicableOS=\u0027Windows Server 2016 Technical Preview 5\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.494\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1607\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.122\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1603\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.117\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM 1602\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.63\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1602\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.51\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM 1512\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10514.6\u0027;FriendlyName=\u0027Windows PowerShell 5 Production Preview 1508\u0027;ApplicableOS=\u0027Windows Server 2012 R2\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10018.0\u0027;FriendlyName=\u0027Windows PowerShell 5 Preview 1502\u0027;ApplicableOS=\u0027Windows Server 2012 R2\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.9883.0\u0027;FriendlyName=\u0027Windows PowerShell 5 Preview November 2014\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows 8.1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00274.0\u0027;FriendlyName=\u0027Windows PowerShell 4 RTM\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00273.0\u0027;FriendlyName=\u0027Windows PowerShell 3 RTM\u0027;ApplicableOS=\u0027Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00272.0\u0027;FriendlyName=\u0027Windows PowerShell 2 RTM\u0027;ApplicableOS=\u0027Windows Server 2008 R2 SP1 and Windows 7\u0027}\n    foreach ($psVersionMapping in $psVersionMappings)\n    {\n        If ($psVersion -ge $psVersionMapping.Name) {\n            @{CurrentVersion=$psVersion;FriendlyName=$psVersionMapping.FriendlyName;ApplicableOS=$psVersionMapping.ApplicableOS}\n            Break\n        }\n    }\n}\nElse{\n    @{CurrentVersion=\u00271.0\u0027;FriendlyName=\u0027Windows PowerShell 1 RTM\u0027;ApplicableOS=\u0027Windows Server 2008, Windows Server 2003, Windows Vista, Windows XP\u0027}\n}",
                                               "HKLM\\Software\\Microsoft\\PowerShell\\1 Install ( = 1 )",
                                               "HKLM\\Software\\Microsoft\\PowerShell\\1 PID (=89393-100-0001260-00301) -- For RC2\nHKLM\\Software\\Microsoft\\PowerShell\\1 PID (=89393-100-0001260-04309) -- For RTM",
                                               "@echo off\necho Checking powershell version...\ndel \"%temp%\\PSVers.txt\" 2\u0026gt;nul\npowershell -command \"[string]$PSVersionTable.PSVersion.Major +\u0027.\u0027+ [string]$PSVersionTable.PSVersion.Minor | Out-File ([string](cat env:\\temp) + \u0027\\PSVers.txt\u0027)\" 2\u0026gt;nul\nif errorlevel 1 (\n echo Powershell is not installed. Please install it from download.Microsoft.com; thanks.\n) else (\n echo You have installed Powershell version:\n type \"%temp%\\PSVers.txt\"\n del \"%temp%\\PSVers.txt\" 2\u0026gt;nul\n)\ntimeout 15",
                                               "if ($PSVersionTable.PSVersion.Major -eq 5) {\n    #Execute code available in PowerShell 5, like Compress\n    Write-Host \"You are running PowerShell version 5\"\n}\nelse {\n    #Use a different process\n    Write-Host \"This is version $PSVersionTable.PSVersion.Major\"\n}",
                                               "$PSVersionTable.PSVersion.Major",
                                               "powershell \"exit $PSVersionTable.PSVersion.Major\"\nif %errorlevel% GEQ 5 (\n    echo Do some fancy stuff that only powershell v5 or higher supports\n) else (\n    echo Functionality not support by current powershell version.\n)",
                                               "powershell \"exit $PSVersionTable.PSVersion.Major\"\nif %errorlevel% GEQ 5 (\n    rem Unzip archive automatically\n    powershell Expand-Archive Compressed.zip\n) else (\n    rem Make the user unzip, because lazy\n    echo Please unzip Compressed.zip prior to continuing...\n    pause\n)",
                                               "version 7.1.0",
                                               "$PSVersionTable | Select-Object PSVersion",
                                               "PSVersion\n---------\n7.1.0",
                                               "version 5.1",
                                               "$PSVersionTable.PSVersion",
                                               "Major  Minor  Build  Revision\n-----  -----  -----  --------\n5      1      18362  1171",
                                               "pwsh --version",
                                               "PowerShell 7.2.5",
                                               "$psver = $PSVersionTable.PSVersion.Major",
                                               "$psver = (Get-Host).Version.Major",
                                               "$PSVer = (Get-Host).Version.Major\n$sortSwitch = \u0026quot;-Descending\u0026quot;\nif ($PSVer -gt 5) {$sortSwitch = \u0026quot;-r\u0026quot;}\n$pathSep = [IO.Path]::DirectorySeparatorChar\n\n$pattern = \u0026quot;???? something.zip\u0026quot;\n$files = (Get-ChildItem $pattern -Name | sort $sortSwitch)\nforeach ($file in $files) {\n    # Do stuff\n}",
                                               "reg query \u0026quot;HKLM\\SOFTWARE\\Microsoft\\PowerShell\\1\u0026quot; /v Install \u0026gt;nul 2\u0026gt;\u0026amp;1\nif %ERRORLEVEL% EQU 0 (\n  :: Default to PowerShell 5 if both are installed\n  set PSEXE=powershell\n) else (\n  set PSEXE=pwsh\n)\necho Using %PSEXE%\n%PSEXE% -ExecutionPolicy bypass -command \u0026quot;\u0026amp; { ... ; exit $LASTEXITCODE }\u0026quot;",
                                               "$PSVersionTable",
                                               "$version = if (Test-Path Variable:PSVersionTable)\n{\n    [version]$PSVersionTable.PSVersion\n}\nelse\n{\n    [version]\u0026quot;1.0\u0026quot;\n}",
                                               "Get-Host | select {$_.Version}",
                                               "@echo off\nfor /f \"tokens=2 delims=:\" %%a in (\u0027powershell -Command Get-Host ^| findstr /c:Version\u0027) do (echo %%a)",
                                               "5.1.18362.752"
                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:15:43Z",
                         "ViewCount":  3218400,
                         "PatternType":  "best_practice",
                         "Id":  "pattern_general_ad_df4acea7",
                         "QuestionScore":  2908,
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T16:15:43Z",
                         "TotalScore":  7659,
                         "AnswerCount":  24,
                         "BestPractices":  [
                                               "Contains recommended practices and approaches"
                                           ],
                         "Title":  "Stack Overflow Q\u0026A: Determine installed PowerShell version",
                         "CodeTemplate":  "$PSVersionTable.PSVersion",
                         "CommonMistakes":  [
                                                "Contains warnings about common mistakes and errors"
                                            ],
                         "HasAcceptedAnswer":  true,
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "version"
                                  ],
                         "Abstract":  "# Question: Determine installed PowerShell version\n\n**Score:** 2908 | **Views:** 3218400 | **Tags:** powershell, version\n\n**URL:** https://stackoverflow.com/questions/1825585/determine-installed-powershell-version\n\n## Question Body\n\n\u003cp\u003eHow can I determine what version of PowerShell is installed on a computer, and indeed if it is installed at all?\u003c/p\u003e\n\n\n## Answers (24 total)\n\n### Answer 1 (Score: 3864) âœ… ACCEPTED ANSWER\n\n\u003cp\u003eUse \u003ccode\u003e$PSVersionTable.PSVersion\u003c/code\u003e to determine the engine vers...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow_qa",
                                             "CredibilityScore":  0.85,
                                             "PublishedAt":  "2025-07-28T16:15:43Z",
                                             "Url":  "https://stackoverflow.com/questions/1825585/determine-installed-powershell-version",
                                             "ScrapedAt":  "2025-07-28T16:15:43Z",
                                             "Id":  "70dcafbc-3246-4e7e-8efc-40f694100a45",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow Q\u0026A: Determine installed PowerShell version"
                                         }
                                     ],
                         "Content":  "# Question: Determine installed PowerShell version\n\n**Score:** 2908 | **Views:** 3218400 | **Tags:** powershell, version\n\n**URL:** https://stackoverflow.com/questions/1825585/determine-installed-powershell-version\n\n## Question Body\n\n\u003cp\u003eHow can I determine what version of PowerShell is installed on a computer, and indeed if it is installed at all?\u003c/p\u003e\n\n\n## Answers (24 total)\n\n### Answer 1 (Score: 3864) âœ… ACCEPTED ANSWER\n\n\u003cp\u003eUse \u003ccode\u003e$PSVersionTable.PSVersion\u003c/code\u003e to determine the engine version. If the variable does not exist, it is safe to assume the engine is version \u003ccode\u003e1.0\u003c/code\u003e.\u003c/p\u003e\n\n\u003cp\u003eNote that \u003ccode\u003e$Host.Version\u003c/code\u003e and \u003ccode\u003e(Get-Host).Version\u003c/code\u003e are not reliable - they reflect\nthe version of the host only, not the engine. PowerGUI,\nPowerShellPLUS, etc. are all hosting applications, and\nthey will set the host\u0027s version to reflect their product\nversion\u0026nbsp;\u0026mdash; which is entirely correct, but not what you\u0027re looking for.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ePS C:\\\u0026gt; $PSVersionTable.PSVersion\n\nMajor  Minor  Build  Revision\n-----  -----  -----  --------\n4      0      -1     -1\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 2 (Score: 459)\n\n\u003cp\u003eI would use either \u003cstrong\u003e\u003ca href=\"https://technet.microsoft.com/en-us/library/hh849946.aspx\" rel=\"noreferrer\"\u003eGet-Host\u003c/a\u003e\u003c/strong\u003e or \u003cstrong\u003e$PSVersionTable\u003c/strong\u003e.  As Andy Schneider points out, \u003ccode\u003e$PSVersionTable\u003c/code\u003e doesn\u0027t work in version 1; it was introduced in version 2.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eget-host\n\nName             : ConsoleHost\nVersion          : 2.0\nInstanceId       : d730016e-2875-4b57-9cd6-d32c8b71e18a\nUI               : System.Management.Automation.Internal.Host.InternalHostUserInterface\nCurrentCulture   : en-GB\nCurrentUICulture : en-US\nPrivateData      : Microsoft.PowerShell.ConsoleHost+ConsoleColorProxy\nIsRunspacePushed : False\nRunspace         : System.Management.Automation.Runspaces.LocalRunspace\n\n$PSVersionTable\n\nName                           Value\n----                           -----\nCLRVersion                     2.0.50727.4200\nBuildVersion                   6.0.6002.18111\nPSVersion                      2.0\nWSManStackVersion              2.0\nPSCompatibleVersions           {1.0, 2.0}\nSerializationVersion           *******\nPSRemotingProtocolVersion      2.1\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 3 (Score: 106)\n\n\u003cp\u003eYou can look at the built in variable, \u003ccode\u003e$psversiontable\u003c/code\u003e. If it doesn\u0027t exist, you have V1. If it does exist, it will give you all the info you need.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e1 \u0026gt;  $psversiontable\n\nName                           Value                                           \n----                           -----                                           \nCLRVersion                     2.0.50727.4927                                  \nBuildVersion                   6.1.7600.16385                                  \nPSVersion                      2.0                                             \nWSManStackVersion              2.0                                             \nPSCompatibleVersions           {1.0, 2.0}                                      \nSerializationVersion           *******                                         \nPSRemotingProtocolVersion      2.1    \n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 4 (Score: 100)\n\n\u003cp\u003eTo determine if PowerShell is installed, you can check the registry for the existence of \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKEY_LOCAL_MACHINE\\Software\\Microsoft\\PowerShell\\1\\Install\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eand\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\3\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eand, if it exists, whether the value is 1 (for installed), as detailed in the blog post \u003cem\u003e\u003ca href=\"http://myitforum.com/cs2/blogs/yli628/archive/2007/08/16/check-if-powershell-installed-and-version.aspx\" rel=\"noreferrer\"\u003eCheck if PowerShell installed and version\u003c/a\u003e\u003c/em\u003e.\u003c/p\u003e\n\n\u003cp\u003eTo determine the version of PowerShell that is installed, you can check the registry keys \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\PowerShellEngine\\PowerShellVersion\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eand\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\3\\PowerShellEngine\\PowerShellVersion\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eTo determine the version of PowerShell that is installed from a .ps1 script, you can use the following one-liner, as detailed on PowerShell.com in \u003cem\u003e\u003ca href=\"http://powershell.com/cs/blogs/tips/archive/2009/09/11/which-powershell-version-am-i-running.aspx\" rel=\"noreferrer\"\u003eWhich PowerShell Version Am I Running\u003c/a\u003e\u003c/em\u003e.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e$isV2 = test-path variable:\\psversiontable\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eThe same site also gives a function to return the version:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003efunction Get-PSVersion {\n    if (test-path variable:psversiontable) {$psversiontable.psversion} else {[version]\"*******\"}\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 5 (Score: 61)\n\n\u003cp\u003eYou can directly check the version with one line only by invoking PowerShell \u003cem\u003eexternally\u003c/em\u003e, such as from Command Prompt\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003epowershell -Command \"$PSVersionTable.PSVersion\"\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eAccording to \u003ca href=\"https://stackoverflow.com/users/9226723/psaul\"\u003e@psaul\u003c/a\u003e you \u003cstrong\u003ecan\u003c/strong\u003e actually have one command that is agnostic from where it came (CMD, PowerShell or Pwsh). Thank you for that.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003epowershell -command \"(Get-Variable PSVersionTable -ValueOnly).PSVersion\"\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eI\u0027ve tested and it worked flawlessly on both CMD and PowerShell.\u003c/p\u003e\n\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/WN2dY.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/WN2dY.png\" alt=\"Image\"\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 6 (Score: 49)\n\n\u003cp\u003eYou can verify that Windows PowerShell version installed by completing the following check:\u003c/p\u003e\n\n\u003col\u003e\n\u003cli\u003eClick Start, click All Programs, click Accessories, click Windows PowerShell, and then click Windows PowerShell.\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eIn the Windows PowerShell console, type the following command at the command prompt and then press ENTER:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-Host | Select-Object Version\n\u003c/code\u003e\u003c/pre\u003e\u003c/li\u003e\n\u003c/ol\u003e\n\n\u003cp\u003eYou will see output that looks like this:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eVersion\n-------\n3.0\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003e\u003ca href=\"http://www.myerrorsandmysolutions.com/how-to-verify-the-windows-powershell-version-installed/\" rel=\"noreferrer\"\u003ehttp://www.myerrorsandmysolutions.com/how-to-verify-the-windows-powershell-version-installed/\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 7 (Score: 24)\n\n\u003cp\u003e\u003ca href=\"https://blogs.msdn.com/b/powershell/archive/2009/06/25/detection-logic-poweshell-installation.aspx?Redirected=true\"\u003eMicrosoft\u0027s recommended forward compatible method for checking if PowerShell is installed and determining the installed version\u003c/a\u003e is to look at two specific registry keys. I\u0027ve reproduced the details here in case the link breaks.\u003c/p\u003e\n\n\u003cp\u003eAccording to the linked page:\u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003cp\u003eDepending on any other registry key(s), or version of PowerShell.exe or the location of PowerShell.exe is not guaranteed to work in the long term.\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\u003cp\u003eTo check if \u003cstrong\u003eany version\u003c/strong\u003e of PowerShell is installed, check for the following value in the registry:\u003c/p\u003e\n\n\u003cul\u003e\n\u003cli\u003eKey Location: \u003ccode\u003eHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\u003c/code\u003e\u003c/li\u003e\n\u003cli\u003eValue Name: Install\u003c/li\u003e\n\u003cli\u003eValue Type: REG_DWORD\u003c/li\u003e\n\u003cli\u003eValue Data: 0x00000001 (1\u003c/li\u003e\n\u003c/ul\u003e\n\n\u003cp\u003eTo check \u003cstrong\u003ewhether version 1.0 or 2.0\u003c/strong\u003e of PowerShell is installed, check for the following value in the registry:\u003c/p\u003e\n\n\u003cul\u003e\n\u003cli\u003eKey Location: \u003ccode\u003eHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\PowerShellEngine\u003c/code\u003e\u003c/li\u003e\n\u003cli\u003eValue Name: PowerShellVersion\u003c/li\u003e\n\u003cli\u003eValue Type: REG_SZ\u003c/li\u003e\n\u003cli\u003eValue Data: \u0026lt;1.0 | 2.0\u003e\u003c/li\u003e\n\u003c/ul\u003e\n\n\n### Answer 8 (Score: 17)\n\n\u003ch1\u003ePowerShell 7\u003c/h1\u003e\n\u003cp\u003eThe accepted answer is only appropriate if one version of PowerShell is installed on a computer. With the advent of PowerShell 7, this scenario becomes increasingly unlikely.\u003c/p\u003e\n\u003cp\u003eMicrosoft\u0027s \u003ca href=\"https://learn.microsoft.com/en-us/powershell/scripting/install/installing-powershell-core-on-windows#registry-keys-created-during-installation\" rel=\"noreferrer\"\u003edocumentation\u003c/a\u003e states that additional registry keys are created when PowerShell 7 is installed:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003eBeginning in PowerShell 7.1, the [installer] package creates registry keys\nthat store the installation location and version of PowerShell. These\nvalues are located in\n\u003ccode\u003eHKLM\\Software\\Microsoft\\PowerShellCore\\InstalledVersions\\\u0026lt;GUID\u0026gt;\u003c/code\u003e. The\nvalue of \u003ccode\u003e\u0026lt;GUID\u0026gt;\u003c/code\u003e is unique for each build type (release or preview),\nmajor version, and architecture.\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eExploring the registry in the aforementioned location reveals the following registry value: \u003ccode\u003eSemanticVersion\u003c/code\u003e. This value contains the information we seek.\u003c/p\u003e\n\u003cp\u003eOn my computer it appears like the following:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003ePath                                                                                           Name              Type Data\n----                                                                                           ----              ---- ----\nHKLM:\\SOFTWARE\\Microsoft\\PowerShellCore\\InstalledVersions\\31ab5147-9a97-4452-8443-d9709f0516e1 SemanticVersion String 7.1.3\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/uRpmX.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/uRpmX.png\" alt=\"Image displaying the specified key in the Windows Registry Editor\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003cp\u003eAs you can see, the version of PowerShell 7 installed on my computer is 7.1.3. If PowerShell 7 is \u003cstrong\u003enot\u003c/strong\u003e installed on the target computer, the key in its entirety should not exist.\u003c/p\u003e\n\u003cp\u003eAs mentioned in the Microsoft documentation, the registry path will be slightly different dependent on installed PowerShell version.\u003c/p\u003e\n\u003cp\u003ePart of the key path changing could pose a challenge in some scenarios, but for those interested in a command line-based solution, PowerShell itself can handle this problem easily.\u003c/p\u003e\n\u003cp\u003eThe PowerShell cmdlet used to query the data in this registry value is the \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.management/get-itempropertyvalue\" rel=\"noreferrer\"\u003e\u003ccode\u003eGet-ItemPropertyValue\u003c/code\u003e cmdlet\u003c/a\u003e. Observe its use and output as follows (note the asterisk \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_wildcards\" rel=\"noreferrer\"\u003ewildcard\u003c/a\u003e character used in place of the part of the key path that is likely to change):\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003ePS\u0026gt; Get-ItemPropertyValue -Path \u0026quot;HKLM:\\SOFTWARE\\Microsoft\\PowerShellCore\\InstalledVersions\\*\u0026quot; -Name \u0026quot;SemanticVersion\u0026quot;\n\n7.1.3\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eJust a simple one-liner.\u003c/p\u003e\n\n\n### Answer 9 (Score: 11)\n\n\u003cp\u003eI found the easiest way to check if installed was to:\u003c/p\u003e\n\n\u003cul\u003e\n\u003cli\u003erun a command prompt (Start, Run, \u003ccode\u003ecmd\u003c/code\u003e, then OK)\u003c/li\u003e\n\u003cli\u003etype \u003ccode\u003epowershell\u003c/code\u003e then hit return.  You should then get the PowerShell \u003ccode\u003ePS\u003c/code\u003e prompt:\u003c/li\u003e\n\u003c/ul\u003e\n\n\u003cp\u003e\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eC:\\Users\\<USER>\u0026gt;powershell\n\nWindows PowerShell\nCopyright (C) 2009 Microsoft Corporation. All rights reserved.\n\nPS C:\\Users\\<USER>\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eYou can then check the version from the PowerShell prompt by typing \u003ccode\u003e$PSVersionTable.PSVersion\u003c/code\u003e:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ePS C:\\Users\\<USER>\u0026gt; $PSVersionTable.PSVersion\n\nMajor  Minor  Build  Revision\n-----  -----  -----  --------\n2      0      -1     -1\n\nPS C:\\Users\\<USER>\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eType \u003ccode\u003eexit\u003c/code\u003e if you want to go back to the command prompt (\u003ccode\u003eexit\u003c/code\u003e again if you want to also close the command prompt).\u003c/p\u003e\n\n\u003cp\u003eTo run scripts, see \u003ca href=\"http://ss64.com/ps/syntax-run.html\" rel=\"noreferrer\"\u003ehttp://ss64.com/ps/syntax-run.html\u003c/a\u003e.\u003c/p\u003e\n\n\n### Answer 10 (Score: 9)\n\n\u003cp\u003e\u003ccode\u003e$host.version\u003c/code\u003e is just plain wrong/unreliable. This gives you the version of the hosting executable (powershell.exe, powergui.exe, powershell_ise.exe, powershellplus.exe etc) and \u003cstrong\u003enot\u003c/strong\u003e the version of the engine itself.\u003c/p\u003e\n\n\u003cp\u003eThe engine version is contained in \u003ccode\u003e$psversiontable.psversion\u003c/code\u003e. For PowerShell 1.0, this variable does not exist, so obviously if this variable is not available it is entirely safe to assume the engine is 1.0, obviously.\u003c/p\u003e\n\n\n### Answer 11 (Score: 9)\n\n\u003cp\u003eThe easiest way to forget this page and never return to it is to learn the \u003ccode\u003eGet-Variable\u003c/code\u003e: \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-Variable | where {$_.Name -Like \u0027*version*\u0027} | %{$_[0].Value}\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eThere is no need to remember every variable. Just \u003ccode\u003eGet-Variable\u003c/code\u003e is enough (and \"There should be something about version\"). \u003c/p\u003e\n\n\n### Answer 12 (Score: 8)\n\n\u003cp\u003eUse:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e$psVersion = $PSVersionTable.PSVersion\nIf ($psVersion)\n{\n    #PowerShell Version Mapping\n    $psVersionMappings = @()\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.1.14393.0\u0027;FriendlyName=\u0027Windows PowerShell 5.1 Preview\u0027;ApplicableOS=\u0027Windows 10 Anniversary Update\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.1.14300.1000\u0027;FriendlyName=\u0027Windows PowerShell 5.1 Preview\u0027;ApplicableOS=\u0027Windows Server 2016 Technical Preview 5\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.494\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1607\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.122\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1603\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.117\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM 1602\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.63\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1602\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.51\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM 1512\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10514.6\u0027;FriendlyName=\u0027Windows PowerShell 5 Production Preview 1508\u0027;ApplicableOS=\u0027Windows Server 2012 R2\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10018.0\u0027;FriendlyName=\u0027Windows PowerShell 5 Preview 1502\u0027;ApplicableOS=\u0027Windows Server 2012 R2\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.9883.0\u0027;FriendlyName=\u0027Windows PowerShell 5 Preview November 2014\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows 8.1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00274.0\u0027;FriendlyName=\u0027Windows PowerShell 4 RTM\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00273.0\u0027;FriendlyName=\u0027Windows PowerShell 3 RTM\u0027;ApplicableOS=\u0027Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00272.0\u0027;FriendlyName=\u0027Windows PowerShell 2 RTM\u0027;ApplicableOS=\u0027Windows Server 2008 R2 SP1 and Windows 7\u0027}\n    foreach ($psVersionMapping in $psVersionMappings)\n    {\n        If ($psVersion -ge $psVersionMapping.Name) {\n            @{CurrentVersion=$psVersion;FriendlyName=$psVersionMapping.FriendlyName;ApplicableOS=$psVersionMapping.ApplicableOS}\n            Break\n        }\n    }\n}\nElse{\n    @{CurrentVersion=\u00271.0\u0027;FriendlyName=\u0027Windows PowerShell 1 RTM\u0027;ApplicableOS=\u0027Windows Server 2008, Windows Server 2003, Windows Vista, Windows XP\u0027}\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eYou can download the detailed script from \u003cem\u003e\u003ca href=\"https://gallery.technet.microsoft.com/How-to-determine-installed-32de832c\" rel=\"noreferrer\"\u003eHow to determine installed PowerShell version\u003c/a\u003e\u003c/em\u003e.\u003c/p\u003e\n\n\n### Answer 13 (Score: 7)\n\n\u003cp\u003eTo check if PowerShell is installed use:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKLM\\Software\\Microsoft\\PowerShell\\1 Install ( = 1 )\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eTo check if RC2 or RTM is installed use:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKLM\\Software\\Microsoft\\PowerShell\\1 PID (=89393-100-0001260-00301) -- For RC2\nHKLM\\Software\\Microsoft\\PowerShell\\1 PID (=89393-100-0001260-04309) -- For RTM\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eSource: \u003ca href=\"http://www.myitforum.com/articles/40/view.asp?id=10618\" rel=\"nofollow noreferrer\"\u003ethis website\u003c/a\u003e.\u003c/p\u003e\n\n\n### Answer 14 (Score: 7)\n\n\u003cp\u003eSince the most helpful answer didn\u0027t address the \u003cem\u003eif exists\u003c/em\u003e portion, I thought I\u0027d give one take on it via a quick-and-dirty solution. It relies on PowerShell being in the path \u003ca href=\"http://en.wikipedia.org/wiki/Environment_variable\" rel=\"noreferrer\"\u003eenvironment variable\u003c/a\u003e which is likely what you want. (Hat tip to the top answer as I didn\u0027t know that.) Paste this into a \u003cem\u003etext\u003c/em\u003e file and name it\u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003cp\u003eTest Powershell Version.cmd\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\u003cp\u003eor similar.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e@echo off\necho Checking powershell version...\ndel \"%temp%\\PSVers.txt\" 2\u0026gt;nul\npowershell -command \"[string]$PSVersionTable.PSVersion.Major +\u0027.\u0027+ [string]$PSVersionTable.PSVersion.Minor | Out-File ([string](cat env:\\temp) + \u0027\\PSVers.txt\u0027)\" 2\u0026gt;nul\nif errorlevel 1 (\n echo Powershell is not installed. Please install it from download.Microsoft.com; thanks.\n) else (\n echo You have installed Powershell version:\n type \"%temp%\\PSVers.txt\"\n del \"%temp%\\PSVers.txt\" 2\u0026gt;nul\n)\ntimeout 15\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 15 (Score: 6)\n\n\u003cp\u003eI needed to check the version of PowerShell and then run the appropriate code. Some of our servers run v5, and others v4. This means that some functions, like compress, may or may not be available.\u003c/p\u003e\n\n\u003cp\u003eThis is my solution: \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eif ($PSVersionTable.PSVersion.Major -eq 5) {\n    #Execute code available in PowerShell 5, like Compress\n    Write-Host \"You are running PowerShell version 5\"\n}\nelse {\n    #Use a different process\n    Write-Host \"This is version $PSVersionTable.PSVersion.Major\"\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 16 (Score: 5)\n\n\u003cp\u003eThe below  cmdlet will return the PowerShell version.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e$PSVersionTable.PSVersion.Major\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 17 (Score: 4)\n\n\u003cp\u003eThis is the top search result for \"Batch file get powershell version\", so I\u0027d like to provide a basic example of how to do conditional flow in a batch file depending on the powershell version\u003c/p\u003e\n\n\u003cp\u003e\u003cstrong\u003eGeneric example\u003c/strong\u003e\u003c/p\u003e\n\n\n\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell \"exit $PSVersionTable.PSVersion.Major\"\nif %errorlevel% GEQ 5 (\n    echo Do some fancy stuff that only powershell v5 or higher supports\n) else (\n    echo Functionality not support by current powershell version.\n)\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003e\u003cstrong\u003eReal world example\u003c/strong\u003e\u003c/p\u003e\n\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell \"exit $PSVersionTable.PSVersion.Major\"\nif %errorlevel% GEQ 5 (\n    rem Unzip archive automatically\n    powershell Expand-Archive Compressed.zip\n) else (\n    rem Make the user unzip, because lazy\n    echo Please unzip Compressed.zip prior to continuing...\n    pause\n)\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 18 (Score: 4)\n\n\u003cp\u003eI tried this on \u003ccode\u003eversion 7.1.0\u003c/code\u003e and it worked:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$PSVersionTable | Select-Object PSVersion\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cem\u003e\u003cstrong\u003eOutput\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePSVersion\n---------\n7.1.0\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eIt doesn\u0027t work on \u003ccode\u003eversion 5.1\u003c/code\u003e though, so rather go for this on versions below 7:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$PSVersionTable.PSVersion\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cem\u003e\u003cstrong\u003eOutput\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eMajor  Minor  Build  Revision\n-----  -----  -----  --------\n5      1      18362  1171\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cem\u003e\u003cstrong\u003eEDIT\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cp\u003eAs of PowerShell 7.2.5, you can now do:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003epwsh -v\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eOr\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003epwsh --version\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cem\u003e\u003cstrong\u003eOutput\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePowerShell 7.2.5\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 19 (Score: 3)\n\n\u003cp\u003eI used the following, which works on PS 7 and PS 5:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$psver = $PSVersionTable.PSVersion.Major\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eor:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$psver = (Get-Host).Version.Major\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThen I can use logic depending on which version is running.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$PSVer = (Get-Host).Version.Major\n$sortSwitch = \u0026quot;-Descending\u0026quot;\nif ($PSVer -gt 5) {$sortSwitch = \u0026quot;-r\u0026quot;}\n$pathSep = [IO.Path]::DirectorySeparatorChar\n\n$pattern = \u0026quot;???? something.zip\u0026quot;\n$files = (Get-ChildItem $pattern -Name | sort $sortSwitch)\nforeach ($file in $files) {\n    # Do stuff\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 20 (Score: 1)\n\n\u003cp\u003eVery old question but still relevant, it\u0027s just that the nature of the problem is different now in 2023.  Finding the version is easy, but first we have to launch the right executable.  For that, we\u0027re basically back to looking in the registry.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ereg query \u0026quot;HKLM\\SOFTWARE\\Microsoft\\PowerShell\\1\u0026quot; /v Install \u0026gt;nul 2\u0026gt;\u0026amp;1\nif %ERRORLEVEL% EQU 0 (\n  :: Default to PowerShell 5 if both are installed\n  set PSEXE=powershell\n) else (\n  set PSEXE=pwsh\n)\necho Using %PSEXE%\n%PSEXE% -ExecutionPolicy bypass -command \u0026quot;\u0026amp; { ... ; exit $LASTEXITCODE }\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThere are other hints you can get by inspecting environment variables, but I think testing the registry for \u0027Windows\u0027 PowerShell is the safest.\u003c/p\u003e\n\n\n### Answer 21 (Score: 1)\n\n\u003cp\u003ePowerShell versions 2-7 contain a variable named \u003ccode\u003e$PSVersionTable\u003c/code\u003e. It seems horrible, but you have to test for version 1:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$version = if (Test-Path Variable:PSVersionTable)\n{\n    [version]$PSVersionTable.PSVersion\n}\nelse\n{\n    [version]\u0026quot;1.0\u0026quot;\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 22 (Score: 0)\n\n\u003cp\u003eYou can also call the \"host\" command from the PowerShell commandline. It should give you the value of the \u003ccode\u003e$host\u003c/code\u003e variable.\u003c/p\u003e\n\n\n### Answer 23 (Score: -2)\n\n\u003cp\u003eExtending the answer with a select operator:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-Host | select {$_.Version}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 24 (Score: -2)\n\n\u003cp\u003eI have made a small batch script that can determine PowerShell version:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e@echo off\nfor /f \"tokens=2 delims=:\" %%a in (\u0027powershell -Command Get-Host ^| findstr /c:Version\u0027) do (echo %%a)\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eThis simply extracts the version of PowerShell using \u003ccode\u003eGet-Host\u003c/code\u003e and searches the string \u003ccode\u003eVersion\u003c/code\u003e\u003c/p\u003e\n\n\u003cp\u003eWhen the line with the version is found, it uses the \u003ccode\u003efor\u003c/code\u003e command to extract the version. In this case we are saying that the delimiter is a colon and search next the first colon, resulting in my case \u003ccode\u003e5.1.18362.752\u003c/code\u003e.\u003c/p\u003e\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "\u0026lt;PORT\u0026gt;",
                                               "netstat -ano | findstr :\u0026lt;PORT\u0026gt;",
                                               "\u0026lt;PORT\u0026gt;",
                                               "taskkill /PID \u0026lt;PID\u0026gt; /F",
                                               "npx kill-port 8080",
                                               "npx kill-port 8080",
                                               "netstat -ano|findstr \u0026quot;PID :8080\u0026quot;",
                                               "taskkill /PID 18264 /f",
                                               "netstat -aon | findstr 3000",
                                               "taskkill /f /pid 1234",
                                               "sudo lsof -t -i:3000",
                                               "sudo kill -9 1234",
                                               "netstat -ano | findstr :yourPortNumber",
                                               "tskill typeyourPIDhere",
                                               "netstat -ano | findstr :8080",
                                               "taskkill /PID typeyourPIDhere /F",
                                               "netstat -ano | findstr :8080",
                                               "stop-process 82932",
                                               "kill $(lsof -t -i :PORT_NUMBER)",
                                               "for /f \"tokens=5\" %a in (\u0027netstat -aon ^| find \":8080\" ^| find \"LISTENING\"\u0027) do taskkill /f /pid %a",
                                               "for /f \"tokens=5\" %%a in (\u0027netstat -aon ^| find \":8080\" ^| find \"LISTENING\"\u0027) do taskkill /f /pid %%a",
                                               "npx kill-port 8080",
                                               "npx: installed 3 in 13.796s\nProcess on port 8080 killed",
                                               "netstat -ano|findstr \u0026quot;PID :8888\u0026quot;",
                                               "taskkill /pid 8912 /f",
                                               "SUCCESS: The process with PID 8860 has been terminated.",
                                               "Get-Process -Id (Get-NetTCPConnection -LocalPort \u0026quot;8080\u0026quot;).OwningProcess | Stop-Process",
                                               "1. netstat -ano | findstr :2181\n       TCP    0.0.0.0:2181           0.0.0.0:0              LISTENING       8876\n       TCP    [::]:2181              [::]:0                 LISTENING       8876\n\n     2.taskkill //PID 8876 //F\n       SUCCESS: The process with PID 8876 has been terminated.",
                                               "set /P port=\u0026quot;Enter port : \u0026quot;\necho showing process running with port %port%\n\nnetstat -ano|findstr \u0026quot;PID :%port%\u0026quot;\n\nset /P pid=\u0026quot;Enter PID to kill : \u0026quot;\n\ntaskkill /pid %pid% /f\n\nset /P exit=\u0026quot;Press any key to exit...\u0026quot;",
                                               "netstat -ano | findstr :\u0026lt;8080\u0026gt;",
                                               "taskkill /PID \u0026lt;Enter the copied PID Number\u0026gt; /F",
                                               "netstat -ano | findstr :PORT",
                                               "PS C:\\Users\\<USER>\u0026gt; netstat -ano | findstr :4445\n  TCP    0.0.0.0:4445           0.0.0.0:0              LISTENING       7368\n  TCP    [::]:4445              [::]:0                 LISTENING       7368\nPS C:\\Users\\<USER>\u0026gt; kill 7368\nPS C:\\Users\\<USER>\u0026gt; netstat -ano | findstr :4445\nPS C:\\Users\\<USER>\u0026gt;",
                                               "function killport([parameter(mandatory)] [string] $uport){\n    if($upid = (Get-NetTCPConnection -LocalPort $uport -ErrorAction Ignore).OwningProcess){kill $upid}\n}",
                                               "killport 8080",
                                               "kill $(Get-NetTCPConnection -LocalPort 8761 -ErrorAction Ignore).OwningProcess",
                                               "@ECHO OFF                                                                              \nFOR /F \"tokens=5\" %%T IN (\u0027netstat -a -n -o ^| findstr \"9797\" \u0027) DO (\nSET /A ProcessId=%%T) \u0026amp;GOTO SkipLine                                                   \n:SkipLine                                                                              \necho ProcessId to kill = %ProcessId%\ntaskkill /f /pid %ProcessId%\nPAUSE",
                                               "from psutil import process_iter\nfrom signal import SIGTERM # or SIGKILL\n\nfor proc in process_iter():\n    for conns in proc.connections(kind=\u0027inet\u0027):\n        if conns.laddr.port == 8080:\n            proc.send_signal(SIGTERM) # or SIGKILL\n            continue",
                                               "netstat -vanp tcp | grep 8888",
                                               "tcp4     0      0    127.0.0.1.8888   *.*    LISTEN      131072 131072  76061    0\ntcp46    0      0    *.8888           *.*    LISTEN      131072 131072  50523    0",
                                               "sudo kill -9 76061 50523",
                                               "netstat -ano | findstr :PORT\nkill PI",
                                               "tskill `netstat -ano | grep LISTENING | findstr :8080 | sed -r \u0027s/(\\s+[^\\s]+){4}(.*)/\\1/\u0027`",
                                               "function killport() {\n        tskill `netstat -ano | findstr LISTENING | findstr :$1 | sed -r \u0027s/^(\\s+[^\\s]+){4}(\\d*)$/\\1/\u0027`\n}",
                                               "killport 8080",
                                               "node killPort.js 8080",
                                               "killPort.js",
                                               "const { exec } = require(\u0027child_process\u0027);\nconst fs = require(`fs`);\n\nconst port = process.argv.length \u0026gt; 2 ? process.argv[2] : ``;\nif (!port || isNaN(port)) console.log(`port is required as an argument and has to be a number`);\nelse {\n    exec(`netstat -ano | findstr :${port}`, (err, stdout, stderr) =\u0026gt; {\n        if (!stdout) console.log(`nobody listens on port ${port}`);\n        else {\n            const res = stdout.split(`\\n`).map(s =\u0026gt; s.trim());\n            const pid = res.map(s =\u0026gt; s.split(` `).pop()).filter(s =\u0026gt; s).pop();\n            console.log(`Listener of ${port} is found, its pid is ${pid}, killing it...`);\n            exec(`taskkill /PID ${pid} /F`, (err, stdout, stderr) =\u0026gt; {\n                if (!stdout) console.log(`we tried to kill it, but not sure about the result, please run me again`);\n                else console.log(stdout);\n            })\n        }\n    });\n}",
                                               "PIDS=$(cmd.exe /c netstat -ano | cmd.exe /c findstr :$1 | awk \u0027{print $5}\u0027)\nfor pid in $PIDS\ndo\n    cmd.exe /c taskkill /PID $pid /F\ndone"
                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:15:44Z",
                         "ViewCount":  3942248,
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_general_ad_d31b1df4",
                         "QuestionScore":  1383,
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T16:15:44Z",
                         "TotalScore":  6011,
                         "AnswerCount":  28,
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow Q\u0026A: How can I close some specific port on Linux?",
                         "CodeTemplate":  "netstat -ano | findstr :\u0026lt;PORT\u0026gt;",
                         "CommonMistakes":  [
                                                "Contains warnings about common mistakes and errors"
                                            ],
                         "HasAcceptedAnswer":  true,
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "linux",
                                      "windows",
                                      "powershell",
                                      "ubuntu"
                                  ],
                         "Abstract":  "# Question: How can I close some specific port on Linux?\n\n**Score:** 1383 | **Views:** 3942248 | **Tags:** linux, windows, powershell, ubuntu\n\n**URL:** https://stackoverflow.com/questions/39632667/how-can-i-close-some-specific-port-on-linux\n\n## Question Body\n\n\u003cp\u003eHow can I remove the port that some application/process has used it but didn\u0027t close the port?\u003c/p\u003e\n\u003cp\u003eFor example: when I run ssh on my ubuntu and tunnel the ssh connection  on some port \u003ccode\u003e\u0026lt;PORT\u0026gt;\u003c/code\u003e on my system if my netwo...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow_qa",
                                             "CredibilityScore":  0.85,
                                             "PublishedAt":  "2025-07-28T16:15:44Z",
                                             "Url":  "https://stackoverflow.com/questions/39632667/how-can-i-close-some-specific-port-on-linux",
                                             "ScrapedAt":  "2025-07-28T16:15:44Z",
                                             "Id":  "a326faee-0882-4883-8206-ccc7419b00d8",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow Q\u0026A: How can I close some specific port on Linux?"
                                         }
                                     ],
                         "Content":  "# Question: How can I close some specific port on Linux?\n\n**Score:** 1383 | **Views:** 3942248 | **Tags:** linux, windows, powershell, ubuntu\n\n**URL:** https://stackoverflow.com/questions/39632667/how-can-i-close-some-specific-port-on-linux\n\n## Question Body\n\n\u003cp\u003eHow can I remove the port that some application/process has used it but didn\u0027t close the port?\u003c/p\u003e\n\u003cp\u003eFor example: when I run ssh on my ubuntu and tunnel the ssh connection  on some port \u003ccode\u003e\u0026lt;PORT\u0026gt;\u003c/code\u003e on my system if my network interrupts and disconnect the port is still open but the ssh connection is lost, How to close the \u003ccode\u003e\u0026lt;PORT\u0026gt;\u003c/code\u003e?\u003c/p\u003e\n\n\n## Answers (28 total)\n\n### Answer 1 (Score: 2862) âœ… ACCEPTED ANSWER\n\n\u003cp\u003e\u003cstrong\u003eStep 1:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003eOpen up cmd.exe (note: you \u003cem\u003emay\u003c/em\u003e need to run it as an administrator, but this isn\u0027t always necessary), then run the below command:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003e\u003ccode\u003enetstat -ano | findstr :\u0026lt;PORT\u0026gt;\u003c/code\u003e\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003e(Replace \u003ccode\u003e\u0026lt;PORT\u0026gt;\u003c/code\u003e with the port number you want, but keep the colon)\u003c/p\u003e\n\u003cp\u003e\u003cimg src=\"https://i.sstatic.net/lEpCZ.png\" alt=\"\" /\u003e\u003c/p\u003e\n\u003cp\u003eThe area circled in red shows the PID (process identifier). Locate the PID of the process that\u0027s using the port you want.\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003eStep 2:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003eNext, run the following command:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003e\u003ccode\u003etaskkill /PID \u0026lt;PID\u0026gt; /F\u003c/code\u003e\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003e(No colon this time)\u003c/p\u003e\n\u003cp\u003e\u003cimg src=\"https://i.sstatic.net/8k64x.png\" alt=\"\" /\u003e\u003c/p\u003e\n\u003cp\u003eLastly, you can check whether the operation succeeded or not by re-running the command in \u0026quot;Step 1\u0026quot;. If it was successful you shouldn\u0027t see any more search results for that port number.\u003c/p\u003e\n\n\n### Answer 2 (Score: 607)\n\n\u003cp\u003eI know that is really old question, but found pretty easy to remember, fast command to kill apps that are using port.\u003c/p\u003e\n\u003cp\u003eRequirements: npm@5.2.0^ version\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enpx kill-port 8080\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou can also read more about kill-port here: \u003ca href=\"https://www.npmjs.com/package/kill-port\" rel=\"noreferrer\"\u003ehttps://www.npmjs.com/package/kill-port\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 3 (Score: 229)\n\n\u003cp\u003eThere are two ways to kill the processes\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003eOption 01 - Simplest and easiest\u003c/strong\u003e\n\u003cbr\u003e\u003cbr\u003e\n\u003cem\u003eRequirement : npm@5.2.0^ version\u003c/em\u003e\n\u003cbr\u003e\n\u003cbr\u003e\nOpen the Command prompt as Administrator and give the following command with the port (Here the port is 8080)\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enpx kill-port 8080\n\u003c/code\u003e\u003c/pre\u003e\n\u003chr /\u003e\n\u003cp\u003e\u003cstrong\u003eOption 02 - Most commonly used\u003c/strong\u003e\n\u003cbr\u003e\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eStep 01\u003cbr\u003e\u003cbr\u003e\nOpen Windows command prompt as Administrator\u003c/li\u003e\n\u003cli\u003eStep 02\u003cbr\u003e\u003cbr\u003e\nFind the PID of the port you want to kill with the below command: Here port is 8080\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -ano|findstr \u0026quot;PID :8080\u0026quot;\n\n\u003c/code\u003e\u003c/pre\u003e\n\u003cblockquote\u003e\n\u003cp\u003eTCP 0.0.0.0:8080 0.0.0.0:0 LISTENING 18264\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cul\u003e\n\u003cli\u003eStep 03\u003cbr\u003e\u003cbr\u003e\nKill the PID you received above with the below command (In my case PID is 18264)\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003etaskkill /PID 18264 /f\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 4 (Score: 219)\n\n\u003cp\u003e\u003cstrong\u003eWith Windows 10/11 default tools:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003e✔ Step one:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003e\u003cem\u003eOpen Windows PowerShell \u003cstrong\u003eas Administrator\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003e✔ Step two:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003e\u003cem\u003eFind the ProcessID for the port you need to kill (e.g. 3000)\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -aon | findstr 3000\n\u003c/code\u003e\u003c/pre\u003e\n\u003cblockquote\u003e\n\u003cp\u003eTCP    0.0.0.0:3000  \u003cstrong\u003eLISTEN   1234\u003c/strong\u003e\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003e\u003cstrong\u003e✔ Step three:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003e\u003cem\u003eKill the zombie process:\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etaskkill /f /pid 1234\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003ewhere \u0026quot;\u003cstrong\u003e1234\u003c/strong\u003e\u0026quot; is your ProcessID (aka PID)\u003c/p\u003e\n\u003cp\u003e*\u003cem\u003e\u003cstrong\u003eExtra tip if you use Windows Subsystem for Linux (Ubuntu WSL):\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003e✔ Step one:\u003c/strong\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003esudo lsof -t -i:3000\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cstrong\u003e✔ Step two:\u003c/strong\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003esudo kill -9 1234\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 5 (Score: 191)\n\n\u003cp\u003e\u003cem\u003eStep 1 (same is in accepted \u003ca href=\"https://stackoverflow.com/a/39633428/5292302\"\u003eanswer\u003c/a\u003e written by \u003ca href=\"https://stackoverflow.com/users/3626371/kavinduwije\"\u003eKavinduWije\u003c/a\u003e):\u003c/em\u003e\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -ano | findstr :yourPortNumber\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/blockquote\u003e\n\u003cp\u003e\u003cem\u003eChange in Step 2 to:\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etskill typeyourPIDhere \n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cem\u003eNote\u003c/em\u003e: \u003ccode\u003etaskkill\u003c/code\u003e is not working in some git bash terminal\u003c/p\u003e\n\n\n### Answer 6 (Score: 116)\n\n\u003cp\u003e\u003cstrong\u003eIf you are using GitBash\u003c/strong\u003e\u003c/p\u003e\n\n\u003cp\u003e\u003cem\u003eStep one:\u003c/em\u003e\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003enetstat -ano | findstr :8080\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003e\u003cem\u003eStep two:\u003c/em\u003e \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003etaskkill /PID typeyourPIDhere /F \n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003e(\u003ccode\u003e/F\u003c/code\u003e forcefully terminates the process)\u003c/p\u003e\n\n\n### Answer 7 (Score: 91)\n\n\u003cp\u003eThe simplest solution — the only one I can ever remember:\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003eIn Windows Powershell\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003eSay we want to stop a process on port \u003cstrong\u003e8080\u003c/strong\u003e\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003eGet the process:\u003c/li\u003e\n\u003c/ol\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -ano | findstr :8080\n\u003c/code\u003e\u003c/pre\u003e\n\u003col start=\"2\"\u003e\n\u003cli\u003eStop the process\u003c/li\u003e\n\u003c/ol\u003e\n\u003cpre\u003e\u003ccode\u003estop-process 82932\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 8 (Score: 63)\n\n\u003cp\u003eIf you already know the port number, it will probably suffice to send a software termination signal to the process (SIGTERM):\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ekill $(lsof -t -i :PORT_NUMBER)\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 9 (Score: 46)\n\n\u003cp\u003eFor use in command line:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003efor /f \"tokens=5\" %a in (\u0027netstat -aon ^| find \":8080\" ^| find \"LISTENING\"\u0027) do taskkill /f /pid %a\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eFor use in bat-file:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003efor /f \"tokens=5\" %%a in (\u0027netstat -aon ^| find \":8080\" ^| find \"LISTENING\"\u0027) do taskkill /f /pid %%a\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 10 (Score: 43)\n\n\u003cp\u003eSimple CMD is working me. Easy to remember\u003c/p\u003e\n\u003cp\u003efind the port number which you want kill and run the below cmd\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enpx kill-port 8080\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eAfter complete the Port get stopped and getting this message\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enpx: installed 3 in 13.796s\nProcess on port 8080 killed\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 11 (Score: 34)\n\n\u003cp\u003eIn \u003cstrong\u003eWindows PowerShell\u003c/strong\u003e version 1 or later to stop a process on port 3000 type:\u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003cp\u003eStop-Process (,(netstat -ano | findstr :3000).split() | foreach {$\u003cem\u003e[$\u003c/em\u003e.length-1]}) -Force\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\u003chr\u003e\n\n\u003cp\u003eAs suggested by @morganpdx here`s a more PowerShell-ish, better version:\u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003cp\u003eStop-Process -Id (Get-NetTCPConnection -LocalPort 3000).OwningProcess -Force\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\n### Answer 12 (Score: 23)\n\n\u003cp\u003eOpen command prompt and issue below command\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -ano|findstr \u0026quot;PID :8888\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eOutput will show the process id occupying the port\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/pLq08.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/pLq08.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003cp\u003eIssue below command to kill the PID\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etaskkill /pid 8912 /f\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou will receive the output as below\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eSUCCESS: The process with PID 8860 has been terminated.\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 13 (Score: 20)\n\n\u003cp\u003eIf you can use PowerShell on Windows you just need :\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eGet-Process -Id (Get-NetTCPConnection -LocalPort \u0026quot;8080\u0026quot;).OwningProcess | Stop-Process\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 14 (Score: 17)\n\n\u003cp\u003eFor Windows users, you can use the \u003ca href=\"https://www.nirsoft.net/utils/cports.html#DownloadLinks\" rel=\"noreferrer\"\u003eCurrPorts\u003c/a\u003e tool to kill ports under usage easily:\u003c/p\u003e\n\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/wSqrm.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/wSqrm.png\" alt=\"Enter image description here\"\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 15 (Score: 11)\n\n\u003cp\u003eI was running zookeeper on Windows and wasn\u0027t able to stop \u003ca href=\"https://en.wikipedia.org/wiki/Apache_ZooKeeper\" rel=\"nofollow noreferrer\"\u003eZooKeeper\u003c/a\u003e running at 2181 port using zookeeper-stop.sh, so tried this double slash \"//\" method to taskkill. It worked\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e     1. netstat -ano | findstr :2181\n       TCP    0.0.0.0:2181           0.0.0.0:0              LISTENING       8876\n       TCP    [::]:2181              [::]:0                 LISTENING       8876\n\n     2.taskkill //PID 8876 //F\n       SUCCESS: The process with PID 8876 has been terminated.\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 16 (Score: 11)\n\n\u003ch2\u003eLet\u0027s Automate!\u003c/h2\u003e\n\u003cp\u003eIf you fall into this issue much often like me, make an .bat file and run it to end process.\u003c/p\u003e\n\u003ch2\u003ecreate a bat file \u0026quot;killport.bat\u0026quot;\u003c/h2\u003e\n\u003cpre\u003e\u003ccode\u003eset /P port=\u0026quot;Enter port : \u0026quot;\necho showing process running with port %port%\n\nnetstat -ano|findstr \u0026quot;PID :%port%\u0026quot;\n\nset /P pid=\u0026quot;Enter PID to kill : \u0026quot;\n\ntaskkill /pid %pid% /f\n\nset /P exit=\u0026quot;Press any key to exit...\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eRun this file by double clicking and\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003eEnter port number (then it will list process with PID)\u003c/li\u003e\n\u003cli\u003eEnter PID to kill\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eDone\u003c/p\u003e\n\u003ch1\u003e(Optional)\u003c/h1\u003e\n\u003cp\u003eSet to path environment so that you can access this file from anywhere.\u003c/p\u003e\n\u003cp\u003eMost probably you will know how to add an new path to env. But here\u0027s how if you don\u0027t\u003c/p\u003e\n\u003ch2\u003eStep 1 of 4\u003c/h2\u003e\n\u003cp\u003eSearch ENV on start menu\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/Bry76.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/Bry76.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003ch2\u003eStep 2 of 4\u003c/h2\u003e\n\u003cp\u003eSelect Environment Variables\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/AxVDy.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/AxVDy.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003ch2\u003eStep 3 of 4\u003c/h2\u003e\n\u003cp\u003eSelect \u0027path\u0027 and click Edit button\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/3c0sS.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/3c0sS.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003ch2\u003eStep 4 of 4\u003c/h2\u003e\n\u003cp\u003eClick \u0027New\u0027 add the path where .bat file is stored. Since I saved it on \u0027../Documents/bats\u0027 folder I am adding this path. Your path will depend on where you save this file.\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/wSdzm.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/wSdzm.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003ch3\u003eOpen CMD and test. Remember you filename will the word to run this file. Since I saved the .bat file as \u0027killport.bat\u0027 =\u0026gt; \u0027killport\u0027 is the word to run it.\u003c/h3\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/zaLxh.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/zaLxh.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\nif do enjoy!\nelse share how you done it here\u003c/p\u003e\n\n\n### Answer 17 (Score: 11)\n\n\u003cp\u003eRun cmd as administrator. Then type this code in there.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -ano | findstr :\u0026lt;8080\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThen you can see the PID run on your port. Then copy that PID number. ( PID is a unique number that helps identify a hardware product or a registered software product.)  And type below code line and press enter.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etaskkill /PID \u0026lt;Enter the copied PID Number\u0026gt; /F\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 18 (Score: 9)\n\n\u003cp\u003eIf you\u0027re using \u003ca href=\"https://devblogs.microsoft.com/commandline/windows-terminal-1-0/\" rel=\"nofollow noreferrer\"\u003eWindows Terminal\u003c/a\u003e then the killing process might be little less tedious.\nI\u0027ve been using windows terminal and \u003ccode\u003ekill PID\u003c/code\u003e works fine for me to kill processes on the port as the new Windows Terminal supports certain bash commands. For example: \u003ccode\u003ekill 13300\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eSo, the complete process will look like this-\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eOpen Windows Terminal\u003c/li\u003e\n\u003cli\u003eType the following command to show processes running on the port you\u0027re looking to kill processes.\n\u003ccode\u003enetstat -ano | findstr :PORT\u003c/code\u003e\u003c/li\u003e\n\u003cli\u003eType following to kill the process.\n\u003ccode\u003ekill PID\u003c/code\u003e\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eFor Example:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePS C:\\Users\\<USER>\u0026gt; netstat -ano | findstr :4445\n  TCP    0.0.0.0:4445           0.0.0.0:0              LISTENING       7368\n  TCP    [::]:4445              [::]:0                 LISTENING       7368\nPS C:\\Users\\<USER>\u0026gt; kill 7368\nPS C:\\Users\\<USER>\u0026gt; netstat -ano | findstr :4445\nPS C:\\Users\\<USER>\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eSee when I typed the first command to list processes on the port it returned empty. That means all processes are killed now.\u003c/p\u003e\n\u003cp\u003eUpdate: \u003ccode\u003ekill\u003c/code\u003e is an alias for \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.management/stop-process?view=powershell-7.1\" rel=\"nofollow noreferrer\"\u003eStop-Process\u003c/a\u003e. Thanks, @FSCKur for letting us know.\u003c/p\u003e\n\n\n### Answer 19 (Score: 8)\n\n\u003cp\u003eIf you use powershell 7+ this worked for me. Just add this function in your $PROFILE file.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003efunction killport([parameter(mandatory)] [string] $uport){\n    if($upid = (Get-NetTCPConnection -LocalPort $uport -ErrorAction Ignore).OwningProcess){kill $upid}\n}\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003ethen simply use \u003ccode\u003ekillport 8080\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eor if you prefer just the command you can try this:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ekill $(Get-NetTCPConnection -LocalPort 8761 -ErrorAction Ignore).OwningProcess\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 20 (Score: 6)\n\n\u003cp\u003eYou can do by run a bat file:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e@ECHO OFF                                                                              \nFOR /F \"tokens=5\" %%T IN (\u0027netstat -a -n -o ^| findstr \"9797\" \u0027) DO (\nSET /A ProcessId=%%T) \u0026amp;GOTO SkipLine                                                   \n:SkipLine                                                                              \necho ProcessId to kill = %ProcessId%\ntaskkill /f /pid %ProcessId%\nPAUSE\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 21 (Score: 4)\n\n\u003cp\u003eIn case you want to do it using Python: check \u003cem\u003e\u003ca href=\"https://stackoverflow.com/questions/20691258/is-possible-in-python-kill-process-which-is-running-on-specific-port-for-exampl\"\u003eIs it possible in python to kill process that is listening on specific port, for example 8080?\u003c/a\u003e\u003c/em\u003e\u003c/p\u003e\n\n\u003cp\u003eThe answer from \u003ca href=\"https://stackoverflow.com/users/905256/smunk\"\u003eSmunk\u003c/a\u003e works nicely. I repeat his code here:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003efrom psutil import process_iter\nfrom signal import SIGTERM # or SIGKILL\n\nfor proc in process_iter():\n    for conns in proc.connections(kind=\u0027inet\u0027):\n        if conns.laddr.port == 8080:\n            proc.send_signal(SIGTERM) # or SIGKILL\n            continue\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 22 (Score: 3)\n\n\u003cp\u003ethe first step\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -vanp tcp | grep 8888\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eexample\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etcp4     0      0    127.0.0.1.8888   *.*    LISTEN      131072 131072  76061    0\ntcp46    0      0    *.8888           *.*    LISTEN      131072 131072  50523    0\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003ethe second step: find your PIDs and kill them\u003c/p\u003e\n\u003cp\u003ein my case\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003esudo kill -9 76061 50523\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 23 (Score: 3)\n\n\u003cpre\u003e\u003ccode\u003enetstat -ano | findstr :PORT\nkill PI\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 24 (Score: 2)\n\n\u003cp\u003eOne line solution using \u003cstrong\u003eGitBash\u003c/strong\u003e:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e tskill `netstat -ano | grep LISTENING | findstr :8080 | sed -r \u0027s/(\\s+[^\\s]+){4}(.*)/\\1/\u0027`\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eReplace \u003cstrong\u003e8080\u003c/strong\u003e with the port your server is listening to.\u003c/p\u003e\n\n\u003cp\u003eIf you need to use it often, try adding to your \u003ccode\u003e~/.bashrc\u003c/code\u003e the function:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003efunction killport() {\n        tskill `netstat -ano | findstr LISTENING | findstr :$1 | sed -r \u0027s/^(\\s+[^\\s]+){4}(\\d*)$/\\1/\u0027`\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eand simply run \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ekillport 8080\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 25 (Score: 1)\n\n\u003cp\u003eI wrote a tiny node js script for this. Just run it like this:\n\u003ccode\u003enode killPort.js 8080\u003c/code\u003e or whatever port you need to kill. Save the following to a \u003ccode\u003ekillPort.js\u003c/code\u003e file:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003econst { exec } = require(\u0027child_process\u0027);\nconst fs = require(`fs`);\n\nconst port = process.argv.length \u0026gt; 2 ? process.argv[2] : ``;\nif (!port || isNaN(port)) console.log(`port is required as an argument and has to be a number`);\nelse {\n    exec(`netstat -ano | findstr :${port}`, (err, stdout, stderr) =\u0026gt; {\n        if (!stdout) console.log(`nobody listens on port ${port}`);\n        else {\n            const res = stdout.split(`\\n`).map(s =\u0026gt; s.trim());\n            const pid = res.map(s =\u0026gt; s.split(` `).pop()).filter(s =\u0026gt; s).pop();\n            console.log(`Listener of ${port} is found, its pid is ${pid}, killing it...`);\n            exec(`taskkill /PID ${pid} /F`, (err, stdout, stderr) =\u0026gt; {\n                if (!stdout) console.log(`we tried to kill it, but not sure about the result, please run me again`);\n                else console.log(stdout);\n            })\n        }\n    });\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 26 (Score: 1)\n\n\u003cp\u003eI am using GitBash and I error like below when ran\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003etaskkill //PID XXXX\u003c/p\u003e\n\u003cp\u003eERROR: The process with PID 7420 could not be terminated. Reason: This\nprocess can only be terminated forcefully (with /F option).\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eSo used //F like below and worked fine\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003etaskkill //F //PID  XXXX\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\n### Answer 27 (Score: 0)\n\n\u003cp\u003eHere is a script to do it in WSL2\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePIDS=$(cmd.exe /c netstat -ano | cmd.exe /c findstr :$1 | awk \u0027{print $5}\u0027)\nfor pid in $PIDS\ndo\n    cmd.exe /c taskkill /PID $pid /F\ndone\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 28 (Score: -3)\n\n\u003cp\u003eWe can avoid this by simple restarting IIS, using the below command:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eIISRESET\n\u003c/code\u003e\u003c/pre\u003e\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "# This is a comment in PowerShell",
                                               "\u0026lt;# #\u0026gt;",
                                               "#REQUIRES -Version 2.0\n\n\u0026lt;#\n.SYNOPSIS\n    A brief description of the function or script. This keyword can be used\n    only once in each topic.\n.DESCRIPTION\n    A detailed description of the function or script. This keyword can be\n    used only once in each topic.\n.NOTES\n    File Name      : xxxx.ps1\n    Author         : J.P. Blanc (<EMAIL>)\n    Prerequisite   : PowerShell V2 over Vista and upper.\n    Copyright 2011 - Jean Paul Blanc/Silogix\n.LINK\n    Script posted over:\n    http://silogix.fr\n.EXAMPLE\n    Example 1\n.EXAMPLE\n    Example 2\n#\u0026gt;\nFunction blabla\n{}",
                                               "# This is a comment in PowerShell",
                                               "# Comment Here",
                                               "\u0026lt;# \n  Multi \n  Line \n#\u0026gt;",
                                               "Get-Content -Path \u0026lt;# configuration file #\u0026gt; C:\\config.ini",
                                               "Space + TAB",
                                               "# Single line comment in PowerShell\n\n\u0026lt;#\n--------------------------------------\nMulti-line comment in PowerShell V2+\n--------------------------------------\n#\u0026gt;",
                                               "# Get all Windows Service processes \u0026lt;-- one line comment, it starts with \u0027#\u0027\nGet-Process -Name *host*\n\nGet-Process -Name *host* ## You could put as many ### as you want, it does not matter\n\nGet-Process -Name *host* # | Stop-Service # Everything from the first # until end of the line is treated as comment\n\nStop-Service -DisplayName Windows*Update # -WhatIf # You can use it to comment out cmdlet switches",
                                               "\u0026lt;#\nEveryting between \u0027\u0026lt; #\u0027 and \u0027# \u0026gt;\u0027 is\ntreated as a comment. A typical use case is for help, see below.\n\n# You could also have a single line comment inside the multi line comment block.\n# Or two... :)\n\n#\u0026gt;\n\n\u0026lt;#\n.SYNOPSIS\n    A brief description of the function or script.\n    This keyword can be used only once in each topic.\n\n.DESCRIPTION\n    A detailed description of the function or script.\n    This keyword can be used only once in each topic.\n\n.NOTES\n    Some additional notes. This keyword can be used only once in each topic.\n    This keyword can be used only once in each topic.\n\n.LINK\n    A link used when Get-Help with a switch -OnLine is used.\n    This keyword can be used only once in each topic.\n\n.EXAMPLE\n    Example 1\n    You can use this keyword as many as you want.\n\n.EXAMPLE\n    Example 2\n    You can use this keyword as many as you want.\n#\u0026gt;",
                                               "\u0026lt;#\nNope, these are not allowed in PowerShell.\n\n\u0026lt;# This will break your first multiline comment block... #\u0026gt;\n...and this will throw a syntax error.\n#\u0026gt;",
                                               "\u0026lt;#\nThe multi line comment opening/close\ncan be also used to comment some nested code\nor as an explanation for multi chained operations..\n#\u0026gt;\nGet-Service | \u0026lt;# Step explanation #\u0026gt;\nWhere-Object { $_.Status -eq [ServiceProcess.ServiceControllerStatus]::Stopped } |\n\u0026lt;# Format-Table -Property DisplayName, Status -AutoSize |#\u0026gt;\nOut-File -FilePath Services.txt -Encoding Unicode",
                                               "# Some well written script\nexit\nWriting something after exit is possible but not recommended.\nIt isn\u0027t a comment.\nEspecially in Visual Studio Code, these words baffle PSScriptAnalyzer.\nYou could actively break your session in VS Code.",
                                               "# Comment here",
                                               "#requires -runasadmin",
                                               "(Some basic code) # Use \"#\" after a line and use:\n\n \u0026lt;#\n    for more lines\n    ...\n    ...\n    ...\n    ..\n    .\n #\u0026gt;",
                                               "....\nexit \n\nHi\nHello\nWe are comments\nAnd not executed"
                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:15:45Z",
                         "ViewCount":  1052890,
                         "PatternType":  "best_practice",
                         "Id":  "pattern_general_ad_b7eabce7",
                         "QuestionScore":  1115,
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T16:15:45Z",
                         "TotalScore":  2883,
                         "AnswerCount":  10,
                         "BestPractices":  [
                                               "Contains recommended practices and approaches"
                                           ],
                         "Title":  "Stack Overflow Q\u0026A: How do you comment out code in PowerShell?",
                         "CodeTemplate":  "# This is a comment in PowerShell",
                         "CommonMistakes":  [
                                                "Contains warnings about common mistakes and errors"
                                            ],
                         "HasAcceptedAnswer":  true,
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "syntax",
                                      "powershell-2.0",
                                      "comments"
                                  ],
                         "Abstract":  "# Question: How do you comment out code in PowerShell?\n\n**Score:** 1115 | **Views:** 1052890 | **Tags:** powershell, syntax, powershell-2.0, comments\n\n**URL:** https://stackoverflow.com/questions/7342597/how-do-you-comment-out-code-in-powershell\n\n## Question Body\n\n\u003cp\u003eHow do you comment out code in \u003cstrong\u003ePowerShell\u003c/strong\u003e (1.0 or 2.0)?\u003c/p\u003e\n\n\n## Answers (10 total)\n\n### Answer 1 (Score: 1469) âœ… ACCEPTED ANSWER\n\n\u003cp\u003eIn PowerShell V1 there\u0027s only \u003ccode\u003e#\u003c/code\u003e to make the text after it a commen...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow_qa",
                                             "CredibilityScore":  0.85,
                                             "PublishedAt":  "2025-07-28T16:15:45Z",
                                             "Url":  "https://stackoverflow.com/questions/7342597/how-do-you-comment-out-code-in-powershell",
                                             "ScrapedAt":  "2025-07-28T16:15:45Z",
                                             "Id":  "7d9fc768-cf74-48ce-be3d-ff160265ff4d",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow Q\u0026A: How do you comment out code in PowerShell?"
                                         }
                                     ],
                         "Content":  "# Question: How do you comment out code in PowerShell?\n\n**Score:** 1115 | **Views:** 1052890 | **Tags:** powershell, syntax, powershell-2.0, comments\n\n**URL:** https://stackoverflow.com/questions/7342597/how-do-you-comment-out-code-in-powershell\n\n## Question Body\n\n\u003cp\u003eHow do you comment out code in \u003cstrong\u003ePowerShell\u003c/strong\u003e (1.0 or 2.0)?\u003c/p\u003e\n\n\n## Answers (10 total)\n\n### Answer 1 (Score: 1469) âœ… ACCEPTED ANSWER\n\n\u003cp\u003eIn PowerShell V1 there\u0027s only \u003ccode\u003e#\u003c/code\u003e to make the text after it a comment.\u003c/p\u003e\n\u003cpre class=\"lang-powershell prettyprint-override\"\u003e\u003ccode\u003e# This is a comment in PowerShell\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eIn PowerShell V2 \u003ccode\u003e\u0026lt;# #\u0026gt;\u003c/code\u003e can be used for block comments and more specifically for help comments.\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e#REQUIRES -Version 2.0\n\n\u0026lt;#\n.SYNOPSIS\n    A brief description of the function or script. This keyword can be used\n    only once in each topic.\n.DESCRIPTION\n    A detailed description of the function or script. This keyword can be\n    used only once in each topic.\n.NOTES\n    File Name      : xxxx.ps1\n    Author         : J.P. Blanc (<EMAIL>)\n    Prerequisite   : PowerShell V2 over Vista and upper.\n    Copyright 2011 - Jean Paul Blanc/Silogix\n.LINK\n    Script posted over:\n    http://silogix.fr\n.EXAMPLE\n    Example 1\n.EXAMPLE\n    Example 2\n#\u0026gt;\nFunction blabla\n{}\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eFor more explanation about \u003ccode\u003e.SYNOPSIS\u003c/code\u003e and \u003ccode\u003e.*\u003c/code\u003e see \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_comment_based_help?view=powershell-7.1\" rel=\"noreferrer\"\u003eabout_Comment_Based_Help\u003c/a\u003e.\u003c/p\u003e\n\u003cp\u003eRemark: These function comments are used by the \u003ccode\u003eGet-Help\u003c/code\u003e CmdLet and can be put before the keyword \u003ccode\u003eFunction\u003c/code\u003e, or inside the \u003ccode\u003e{}\u003c/code\u003e before or after the code itself.\u003c/p\u003e\n\n\n### Answer 2 (Score: 119)\n\n\u003cp\u003eYou use the hash mark like this:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e# This is a comment in PowerShell\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eWikipedia has a good page for keeping track of how to do comments in several popular languages:\u003c/p\u003e\n\u003cp\u003e\u003cem\u003e\u003ca href=\"http://en.wikipedia.org/wiki/Comparison_of_programming_languages_(syntax)#Comments\" rel=\"noreferrer\"\u003eComments\u003c/a\u003e\u003c/em\u003e\u003c/p\u003e\n\n\n### Answer 3 (Score: 65)\n\n\u003cp\u003eSingle line comments start with a \u003ca href=\"https://en.wikipedia.org/wiki/Number_sign\"\u003ehash symbol\u003c/a\u003e, everything to the right of the \u003ccode\u003e#\u003c/code\u003e will be ignored:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e# Comment Here\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eIn PowerShell 2.0 and above multi-line block comments can be used:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e\u0026lt;# \n  Multi \n  Line \n#\u0026gt; \n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eYou could use block comments to embed comment text within a command:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-Content -Path \u0026lt;# configuration file #\u0026gt; C:\\config.ini\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003e\u003cstrong\u003eNote:\u003c/strong\u003e Because PowerShell supports \u003ca href=\"https://en.wikipedia.org/wiki/Command-line_completion\"\u003eTab Completion\u003c/a\u003e you need to be careful about copying and pasting \u003ccode\u003eSpace + TAB\u003c/code\u003e before comments.\u003c/p\u003e\n\n\n### Answer 4 (Score: 42)\n\n\u003cp\u003eIt\u0027s the \u003ccode\u003e#\u003c/code\u003e.\u003c/p\u003e\n\u003cp\u003eSee \u003cem\u003e\u003ca href=\"http://www.neolisk.com/techblog/powershell-specialcharactersandtokens\" rel=\"nofollow noreferrer\"\u003ePowerShell - Special Characters And Tokens\u003c/a\u003e\u003c/em\u003e for special characters.\u003c/p\u003e\n\n\n### Answer 5 (Score: 24)\n\n\u003cp\u003eHere\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e# Single line comment in PowerShell\n\n\u0026lt;#\n--------------------------------------\nMulti-line comment in PowerShell V2+\n--------------------------------------\n#\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 6 (Score: 24)\n\n\u003cp\u003eWithin PowerShell ISE you can hit \u003ckbd\u003eCtrl\u003c/kbd\u003e+\u003ckbd\u003eJ\u003c/kbd\u003e to open the \u003cem\u003eStart Snipping\u003c/em\u003e  menu and select \u003cem\u003eComment block\u003c/em\u003e:\u003c/p\u003e\n\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/caJt2.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/caJt2.png\" alt=\"enter image description here\"\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 7 (Score: 10)\n\n\u003cp\u003eI\u0027m a little bit late to this party but seems that nobody actually wrote all use cases. So...\u003c/p\u003e\n\u003cp\u003eOnly supported version of PowerShell these days (\u003cem\u003efall of 2020 and beyond\u003c/em\u003e) are:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eWindows PowerShell 5.1.x\u003c/li\u003e\n\u003cli\u003ePowerShell 7.0.x.\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eYou don\u0027t want to or you shouldn\u0027t work with different versions of PowerShell.\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003eBoth versions\u003c/strong\u003e (\u003cem\u003eor any another version which you could come around WPS 3.0-5.0, PS Core 6.x.x on some outdated stations\u003c/em\u003e) \u003cstrong\u003eshare the same comment functionality.\u003c/strong\u003e\u003c/p\u003e\n\u003ch2\u003eOne line comments\u003c/h2\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e# Get all Windows Service processes \u0026lt;-- one line comment, it starts with \u0027#\u0027\nGet-Process -Name *host*\n\nGet-Process -Name *host* ## You could put as many ### as you want, it does not matter\n\nGet-Process -Name *host* # | Stop-Service # Everything from the first # until end of the line is treated as comment\n\nStop-Service -DisplayName Windows*Update # -WhatIf # You can use it to comment out cmdlet switches\n\u003c/code\u003e\u003c/pre\u003e\n\u003ch2\u003eMulti line comments\u003c/h2\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e\u0026lt;#\nEveryting between \u0027\u0026lt; #\u0027 and \u0027# \u0026gt;\u0027 is\ntreated as a comment. A typical use case is for help, see below.\n\n# You could also have a single line comment inside the multi line comment block.\n# Or two... :)\n\n#\u0026gt;\n\n\u0026lt;#\n.SYNOPSIS\n    A brief description of the function or script.\n    This keyword can be used only once in each topic.\n\n.DESCRIPTION\n    A detailed description of the function or script.\n    This keyword can be used only once in each topic.\n\n.NOTES\n    Some additional notes. This keyword can be used only once in each topic.\n    This keyword can be used only once in each topic.\n\n.LINK\n    A link used when Get-Help with a switch -OnLine is used.\n    This keyword can be used only once in each topic.\n\n.EXAMPLE\n    Example 1\n    You can use this keyword as many as you want.\n\n.EXAMPLE\n    Example 2\n    You can use this keyword as many as you want.\n#\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\u003ch2\u003eNested multi line comments\u003c/h2\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e\u0026lt;#\nNope, these are not allowed in PowerShell.\n\n\u0026lt;# This will break your first multiline comment block... #\u0026gt;\n...and this will throw a syntax error.\n#\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\u003ch2\u003eIn code nested multi line comments\u003c/h2\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e\u0026lt;#\nThe multi line comment opening/close\ncan be also used to comment some nested code\nor as an explanation for multi chained operations..\n#\u0026gt;\nGet-Service | \u0026lt;# Step explanation #\u0026gt;\nWhere-Object { $_.Status -eq [ServiceProcess.ServiceControllerStatus]::Stopped } |\n\u0026lt;# Format-Table -Property DisplayName, Status -AutoSize |#\u0026gt;\nOut-File -FilePath Services.txt -Encoding Unicode\n\u003c/code\u003e\u003c/pre\u003e\n\u003ch2\u003eEdge case scenario\u003c/h2\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e# Some well written script\nexit\nWriting something after exit is possible but not recommended.\nIt isn\u0027t a comment.\nEspecially in Visual Studio Code, these words baffle PSScriptAnalyzer.\nYou could actively break your session in VS Code.\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 8 (Score: 10)\n\n\u003cp\u003eUse a hashtag followed by a white space(!) for this:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e # Comment here\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eDo not forget the white space here! Otherwise it can interfere with internal commands.\u003c/p\u003e\n\u003cp\u003eE.g., this is \u003cem\u003enot\u003c/em\u003e a comment:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e#requires -runasadmin\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 9 (Score: 3)\n\n\u003cp\u003eYou can make:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e (Some basic code) # Use \"#\" after a line and use:\n\n \u0026lt;#\n    for more lines\n    ...\n    ...\n    ...\n    ..\n    .\n #\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 10 (Score: 2)\n\n\u003cp\u003eThere is a special way of inserting comments add the end of script:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e....\nexit \n\nHi\nHello\nWe are comments\nAnd not executed \n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eAnything after \u003ccode\u003eexit\u003c/code\u003e is not executed, and behave quite like comments.\u003c/p\u003e\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "powershell.exe \u0027C:\\my_path\\yada_yada\\run_import_script.ps1\u0027",
                                               "PS\u0026gt; cd C:\\my_path\\yada_yada\\ (enter)",
                                               "PS\u0026gt; .\\run_import_script.ps1 (enter)",
                                               "powershell -noexit \u0026quot;\u0026amp; \u0026quot;\u0026quot;C:\\my_path\\yada_yada\\run_import_script.ps1\u0026quot;\u0026quot;\u0026quot; (enter)",
                                               "Powershell.exe -File C:\\my_path\\yada_yada\\run_import_script.ps1",
                                               "powershell [-noexit] -executionpolicy bypass -File \u0026lt;Filename\u0026gt;",
                                               "powershell -executionpolicy bypass -File .\\Test.ps1",
                                               "powershell.exe -noexit \"\u0026amp; \u0027c:\\Data\\ScheduledScripts\\ShutdownVM.ps1\u0027\"",
                                               "powershell -command - \u0026lt; c:\\mypath\\myscript.ps1",
                                               "set-executionpolicy unrestricted",
                                               "set-ExecutionPolicy default",
                                               "./myscript.ps1",
                                               "@echo off\ncolor 1F\necho.\n\nC:\\Windows\\system32\\WindowsPowerShell\\v1.0\\powershell.exe -ExecutionPolicy Bypass -File \"PrepareEnvironment.ps1\"\n\n:EOF\necho Waiting seconds\ntimeout /t 10 /nobreak \u0026gt; NUL",
                                               "Program/Script",
                                               "Powershell.exe",
                                               "-File \"C:\\xxx.ps1\"",
                                               "$\u0026gt; . c:\\program file\\prog.ps1",
                                               "$\u0026gt; add or entry_func or main",
                                               "C:\\my_path\\yada_yada\\run_import_script.ps1",
                                               "powershell.exe \u0026quot;\u0026amp; \u0027C:\\my_path\\yada_yada\\run_import_script.ps1\u0027\u0026quot;",
                                               "powershell.exe .\\test.ps1\npowershell.exe -File .\\test.ps1\npowershell.exe -C \u0026quot;\u0026amp; \u0027.\\test.ps1\u0027\u0026quot;",
                                               "\u0026quot;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe\u0026quot; \u0026quot;-Command\u0026quot; \u0026quot;if((Get-ExecutionPolicy ) -ne \u0027AllSigned\u0027) { Set-ExecutionPolicy -Scope Process Bypass }; \u0026amp; \u0027C:\\Users\\<USER>\\Desktop\\MYSCRIPT.ps1\u0027\u0026quot;",
                                               "type \u0026quot;script_path\u0026quot; | powershell.exe -c -",
                                               "# \u0026amp; cls \u0026amp; @powershell \u0026quot;gc \u0027%~0\u0027 | iex\u0026quot; \u0026amp; exit",
                                               "@powershell \u0026quot;gc \u0027%~0\u0027 | iex\u0026quot;"
                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:15:46Z",
                         "ViewCount":  3497460,
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_general_ad_819a5c1d",
                         "QuestionScore":  1091,
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T16:15:46Z",
                         "TotalScore":  3124,
                         "AnswerCount":  18,
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow Q\u0026A: How to run a PowerShell script",
                         "CodeTemplate":  "PS\u0026gt; cd C:\\my_path\\yada_yada\\ (enter)",
                         "CommonMistakes":  [
                                                "Contains warnings about common mistakes and errors"
                                            ],
                         "HasAcceptedAnswer":  true,
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "windows",
                                      "powershell",
                                      "scripting"
                                  ],
                         "Abstract":  "# Question: How to run a PowerShell script\n\n**Score:** 1091 | **Views:** 3497460 | **Tags:** windows, powershell, scripting\n\n**URL:** https://stackoverflow.com/questions/2035193/how-to-run-a-powershell-script\n\n## Question Body\n\n\u003cp\u003eHow do I run a PowerShell script?\u003c/p\u003e\n\n\u003cul\u003e\n\u003cli\u003eI have a script named myscript.ps1\u003c/li\u003e\n\u003cli\u003eI have all the necessary frameworks installed\u003c/li\u003e\n\u003cli\u003eI set that \u003ca href=\"https://stackoverflow.com/questions/10635/why-dont-my-powershell-scripts-run\"\u003eexecution policy\u003c/a\u003e thi...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow_qa",
                                             "CredibilityScore":  0.85,
                                             "PublishedAt":  "2025-07-28T16:15:46Z",
                                             "Url":  "https://stackoverflow.com/questions/2035193/how-to-run-a-powershell-script",
                                             "ScrapedAt":  "2025-07-28T16:15:46Z",
                                             "Id":  "86180f1a-76ed-4f74-90a5-ebf4ecf7c16b",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow Q\u0026A: How to run a PowerShell script"
                                         }
                                     ],
                         "Content":  "# Question: How to run a PowerShell script\n\n**Score:** 1091 | **Views:** 3497460 | **Tags:** windows, powershell, scripting\n\n**URL:** https://stackoverflow.com/questions/2035193/how-to-run-a-powershell-script\n\n## Question Body\n\n\u003cp\u003eHow do I run a PowerShell script?\u003c/p\u003e\n\n\u003cul\u003e\n\u003cli\u003eI have a script named myscript.ps1\u003c/li\u003e\n\u003cli\u003eI have all the necessary frameworks installed\u003c/li\u003e\n\u003cli\u003eI set that \u003ca href=\"https://stackoverflow.com/questions/10635/why-dont-my-powershell-scripts-run\"\u003eexecution policy\u003c/a\u003e thing\u003c/li\u003e\n\u003cli\u003eI have followed the instructions on \u003ca href=\"http://technet.microsoft.com/en-us/library/ee176949.aspx\" rel=\"noreferrer\"\u003ethis MSDN help page\u003c/a\u003e\nand am trying to run it like so:\n\u003ccode\u003epowershell.exe \u0027C:\\my_path\\yada_yada\\run_import_script.ps1\u0027\u003c/code\u003e (with or without \u003ccode\u003e--noexit\u003c/code\u003e)\u003c/li\u003e\n\u003c/ul\u003e\n\n\u003cp\u003ewhich returns exactly nothing, except that the file name is output.\u003c/p\u003e\n\n\u003cp\u003eNo error, no message, nothing. Oh, when I add \u003ccode\u003e-noexit\u003c/code\u003e, the same thing happens, but I remain within PowerShell and have to exit manually.\u003c/p\u003e\n\n\u003cp\u003eThe .ps1 file is supposed to run a program and return the error level dependent on that program\u0027s output. But I\u0027m quite sure I\u0027m not even getting there yet.\u003c/p\u003e\n\n\u003cp\u003eWhat am I doing wrong?\u003c/p\u003e\n\n\n## Answers (18 total)\n\n### Answer 1 (Score: 1102) âœ… ACCEPTED ANSWER\n\n\u003cp\u003ePrerequisites:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eYou need to be able to run PowerShell as an administrator\u003c/li\u003e\n\u003cli\u003eYou need to set your PowerShell execution policy to a permissive value or be able to bypass it\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eSteps:\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003e\u003cp\u003eLaunch Windows PowerShell as an Administrator, and wait for the \u003ccode\u003ePS\u0026gt;\u003c/code\u003e prompt to appear\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eNavigate within PowerShell to the directory where the script lives:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePS\u0026gt; cd C:\\my_path\\yada_yada\\ (enter)\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eExecute the script:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePS\u0026gt; .\\run_import_script.ps1 (enter)\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eOr: you can run the PowerShell script from the Command Prompt (\u003ccode\u003ecmd.exe\u003c/code\u003e) like this:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003epowershell -noexit \u0026quot;\u0026amp; \u0026quot;\u0026quot;C:\\my_path\\yada_yada\\run_import_script.ps1\u0026quot;\u0026quot;\u0026quot; (enter)\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eaccording to \u003ca href=\"http://poshoholic.com/2007/09/27/invoking-a-powershell-script-from-cmdexe-or-start-run/\" rel=\"noreferrer\"\u003e\u003cem\u003eInvoking a PowerShell script from cmd.exe (or Start | Run)\u003c/em\u003e\u003c/a\u003e by Kirk Munro.\u003c/p\u003e\n\u003cp\u003eOr you could even \u003ca href=\"http://www.codeproject.com/KB/threads/AsyncPowerShell.aspx\" rel=\"noreferrer\"\u003erun your PowerShell script asynchronously from your C# application\u003c/a\u003e.\u003c/p\u003e\n\n\n### Answer 2 (Score: 340)\n\n\u003cp\u003eIf you are on PowerShell 2.0, use PowerShell.exe\u0027s \u003ccode\u003e-File\u003c/code\u003e parameter to invoke a script from another environment, like cmd.exe. For example:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ePowershell.exe -File C:\\my_path\\yada_yada\\run_import_script.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 3 (Score: 246)\n\n\u003cp\u003eIf you want to run a script without modifying the default script execution policy, you can use the \u003cem\u003ebypass\u003c/em\u003e switch when launching \u003cstrong\u003eWindows PowerShell\u003c/strong\u003e.  \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003epowershell [-noexit] -executionpolicy bypass -File \u0026lt;Filename\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 4 (Score: 138)\n\n\u003cp\u003eIn a command prompt type:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003epowershell -executionpolicy bypass -File .\\Test.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eNOTE:  Here \u003ccode\u003eTest.ps1\u003c/code\u003e is the PowerShell script.\u003c/p\u003e\n\n\n### Answer 5 (Score: 42)\n\n\u003cp\u003eI\u0027ve had the same problem, and I tried and tried... Finally I used:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003epowershell.exe -noexit \"\u0026amp; \u0027c:\\Data\\ScheduledScripts\\ShutdownVM.ps1\u0027\"\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eAnd put this line in a batch-file, and this works.\u003c/p\u003e\n\n\n### Answer 6 (Score: 30)\n\n\u003cp\u003eIf you only have \u003cstrong\u003ePowerShell 1.0\u003c/strong\u003e, this seems to do the trick well enough:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003epowershell -command - \u0026lt; c:\\mypath\\myscript.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eIt pipes the script file to the PowerShell command line.\u003c/p\u003e\n\n\n### Answer 7 (Score: 28)\n\n\u003col\u003e\n\u003cli\u003eOpen PowerShell in administrator mode\u003c/li\u003e\n\u003cli\u003eRun: \u003ccode\u003eset-executionpolicy unrestricted\u003c/code\u003e\u003c/li\u003e\n\u003cli\u003eOpen a regular PowerShell window and run your script.\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eI found this solution following the link that was given as part of the error message: \u003cem\u003e\u003ca href=\"https://go.microsoft.com/fwlink/?LinkID=135170\" rel=\"noreferrer\"\u003eAbout Execution Policies\u003c/a\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cp\u003eMake sure to run \u003ccode\u003eset-ExecutionPolicy default\u003c/code\u003e once you\u0027re done, or you will be exposed to security risks.\u003c/p\u003e\n\n\n### Answer 8 (Score: 27)\n\n\u003cp\u003ePretty easy. Right click the .ps1 file in Windows and in the shell menu click on \u003cem\u003eRun with PowerShell\u003c/em\u003e.\u003c/p\u003e\n\n\n### Answer 9 (Score: 16)\n\n\u003cp\u003eIf your script is named with the \u003ccode\u003e.ps1\u003c/code\u003e extension and you\u0027re in a PowerShell window, you just run \u003ccode\u003e./myscript.ps1\u003c/code\u003e (assuming the file is in your working directory).\u003c/p\u003e\n\n\u003cp\u003eThis is true for me anyway on Windows 10 with PowerShell version 5.1 anyway, and I don\u0027t think I\u0027ve done anything to make it possible.\u003c/p\u003e\n\n\n### Answer 10 (Score: 13)\n\n\u003cp\u003eUsing cmd (BAT) file:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e@echo off\ncolor 1F\necho.\n\nC:\\Windows\\system32\\WindowsPowerShell\\v1.0\\powershell.exe -ExecutionPolicy Bypass -File \"PrepareEnvironment.ps1\"\n\n:EOF\necho Waiting seconds\ntimeout /t 10 /nobreak \u0026gt; NUL\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eIf you need \u003cstrong\u003erun as administrator\u003c/strong\u003e:\u003c/p\u003e\n\n\u003col\u003e\n\u003cli\u003eMake a shortcut pointed to the command prompt (I named it\nAdministrative Command Prompt)\u003c/li\u003e\n\u003cli\u003eOpen the shortcut\u0027s properties and go to the Compatibility tab\u003c/li\u003e\n\u003cli\u003eUnder the Privilege Level section, make sure the checkbox next to \"Run this program as an administrator\" is checked\u003c/li\u003e\n\u003c/ol\u003e\n\n\n### Answer 11 (Score: 13)\n\n\u003cp\u003eIn case you want to run a PowerShell script with Windows Task Scheduler, please follow the steps below:\u003c/p\u003e\n\n\u003col\u003e\n\u003cli\u003e\u003cp\u003eCreate a task\u003c/p\u003e\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eSet \u003ccode\u003eProgram/Script\u003c/code\u003e to \u003ccode\u003ePowershell.exe\u003c/code\u003e\u003c/p\u003e\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eSet \u003ccode\u003eArguments\u003c/code\u003e to \u003ccode\u003e-File \"C:\\xxx.ps1\"\u003c/code\u003e\u003c/p\u003e\u003c/li\u003e\n\u003c/ol\u003e\n\n\u003cp\u003eIt\u0027s from another answer, \u003cem\u003e\u003ca href=\"https://stackoverflow.com/questions/23953926/how-to-execute-a-powershell-script-automatically-using-windows-task-scheduler/23954618#23954618\"\u003eHow do I execute a PowerShell script automatically using Windows task scheduler?\u003c/a\u003e\u003c/em\u003e.\u003c/p\u003e\n\n\n### Answer 12 (Score: 12)\n\n\u003cp\u003eAn easy way is to use \u003ca href=\"https://technet.microsoft.com/en-us/library/dd315244.aspx\" rel=\"noreferrer\"\u003ePowerShell ISE\u003c/a\u003e, open script, run and invoke your script, function...\u003c/p\u003e\n\n\u003cp\u003e\u003ca href=\"https://technet.microsoft.com/en-us/library/dd315244.aspx\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/h2jFM.png\" alt=\"Enter image description here\"\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 13 (Score: 9)\n\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003eGive the path of the script, that is, path setting by cmd:\u003c/p\u003e\n\n\u003cp\u003e\u003ccode\u003e$\u0026gt; . c:\\program file\\prog.ps1\u003c/code\u003e\u003c/p\u003e\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eRun the entry point function of PowerShell:\u003c/p\u003e\n\n\u003cp\u003eFor example, \u003ccode\u003e$\u0026gt; add or entry_func or main\u003c/code\u003e\u003c/p\u003e\u003c/li\u003e\n\u003c/ul\u003e\n\n\n### Answer 14 (Score: 5)\n\n\u003cp\u003eWith the appropriate execution policy, you should just be able to call the file directly and Windows will associate it with PowerShell\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eC:\\my_path\\yada_yada\\run_import_script.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThat does not do so well with arguments. The real answer to your question is that you are missing the \u003ccode\u003e\u0026amp;\u003c/code\u003e to say \u0026quot;execute this\u0026quot;\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003epowershell.exe \u0026quot;\u0026amp; \u0027C:\\my_path\\yada_yada\\run_import_script.ps1\u0027\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003chr /\u003e\n\u003cp\u003eFor \u003ca href=\"https://stackoverflow.com/users/648265/ivan-pozdeev\"\u003e@ivan_pozdeev\u003c/a\u003e\u003c/p\u003e\n\u003ch2\u003eTo Run Any Windows Script\u003c/h2\u003e\n\u003ch4\u003eBatch\u003c/h4\u003e\n\u003cul\u003e\n\u003cli\u003eTo run from File Explorer: double-click test.bat\u003c/li\u003e\n\u003cli\u003eTo run from cmd.exe or *.bat or *.cmd:\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003e.\\test.bat\n\u003c/code\u003e\u003c/pre\u003e\n\u003cul\u003e\n\u003cli\u003eTo run from PowerShell:\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003e.\\test.bat\n\u003c/code\u003e\u003c/pre\u003e\n\u003cul\u003e\n\u003cli\u003eAdditional Registry edits\u003c/li\u003e\n\u003cli\u003eMany other ways\u003c/li\u003e\n\u003c/ul\u003e\n\u003ch4\u003ePowerShell\u003c/h4\u003e\n\u003cul\u003e\n\u003cli\u003eTo run from File Explorer: right-click for context menu, then click \u0026quot;Run with PowerShell\u0026quot;\u003c/li\u003e\n\u003cli\u003eTo run from cmd.exe or *.bat or *.cmd:\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003epowershell.exe .\\test.ps1\npowershell.exe -File .\\test.ps1\npowershell.exe -C \u0026quot;\u0026amp; \u0027.\\test.ps1\u0027\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cul\u003e\n\u003cli\u003eTo run from PowerShell:\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003e.\\test.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\u003cul\u003e\n\u003cli\u003eAdditional Registry edits\u003c/li\u003e\n\u003cli\u003eMany other ways\u003c/li\u003e\n\u003c/ul\u003e\n\n\n### Answer 15 (Score: 5)\n\n\u003cp\u003eI\u0027ve just found the method what Microsoft do when we right click on a \u003ccode\u003eps1\u003c/code\u003e script and click on \u0026quot;Run with PowerShell\u0026quot; :\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e\u0026quot;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe\u0026quot; \u0026quot;-Command\u0026quot; \u0026quot;if((Get-ExecutionPolicy ) -ne \u0027AllSigned\u0027) { Set-ExecutionPolicy -Scope Process Bypass }; \u0026amp; \u0027C:\\Users\\<USER>\\Desktop\\MYSCRIPT.ps1\u0027\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 16 (Score: 4)\n\n\u003cp\u003eYou can run from cmd like this:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etype \u0026quot;script_path\u0026quot; | powershell.exe -c -\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 17 (Score: 3)\n\n\u003cp\u003eUse the \u003ccode\u003e-File\u003c/code\u003e parameter in front of the filename. The quotes make PowerShell think it is a string of commands.\u003c/p\u003e\n\n\n### Answer 18 (Score: 0)\n\n\u003cp\u003eTo create a PowerShell script with an easy way to bypass restrictions without unnecessary files and clicks, you can use the following method:\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003e\u003cp\u003eСreate a new file \u0026quot;your_script**.cmd\u0026quot;.**\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eOpen the file for editing and paste in the first line:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e# \u0026amp; cls \u0026amp; @powershell \u0026quot;gc \u0027%~0\u0027 | iex\u0026quot; \u0026amp; exit\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eStarting from the second line, paste your PowerShell script and save.\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eDone! Just run your script by double clicking.\u003c/p\u003e\n\u003cp\u003eHow does it work??\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003e\u003cp\u003eThe script is initially run in cmd, and the \u003ccode\u003e#\u003c/code\u003e symbol simply causes a do-nothing error.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003e\u0026amp;\u003c/code\u003e allows to execute one command after another.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003ecls\u003c/code\u003e simply clears the screen.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003e@powershell \u0026quot;gc \u0027%~0\u0027 | iex\u0026quot;\u003c/code\u003e is complex, so I will analyze it in more detail:\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003e@\u003c/code\u003e hides the executed command (to keep the screen clean).\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003epowershell\u003c/code\u003e starts an PowerShell and runs the command specified in quotation marks.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003egc \u0027%~0\u0027\u003c/code\u003e is the first command that runs in PowerShell. It reads the content of the current file, as \u0027%~0\u0027 is replaced by the current script location.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003e|\u003c/code\u003e allows redirecting the output of one command to the input of another.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003eiex\u003c/code\u003e executes all the commands it receives.\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ol\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003eexit\u003c/code\u003e terminates the execution of the cmd script after completion of PowerShell execution.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eAfter the current file is run in powershell, the first line will be ignored, since \u003ccode\u003e#\u003c/code\u003e in Powershell means comment.\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eIdea was taken from the site: \u003ca href=\"https://www.netspi.com/blog/technical-blog/network-pentesting/15-ways-to-bypass-the-powershell-execution-policy/\" rel=\"nofollow noreferrer\"\u003enetspi.com \u0026quot;15 ways to bypass the powershell execution policy\u0026quot;\u003c/a\u003e\u003c/p\u003e\n\n\n"
                     }
                 ],
    "SourceType":  "enhanced_stackoverflow",
    "TotalPatterns":  5
}
