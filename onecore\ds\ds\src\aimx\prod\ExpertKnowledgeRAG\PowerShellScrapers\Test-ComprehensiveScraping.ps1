# Simple comprehensive scraping test
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Starting Comprehensive PowerShell AD Knowledge Scraping" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    if (-not $result) {
        throw "Failed to initialize scraping framework"
    }
    
    Write-Host "Framework initialized successfully" -ForegroundColor Green
    
    # Track all patterns
    $allPatterns = @()
    
    # 1. Microsoft Docs - Get a few cmdlets
    Write-Host "`nPHASE 1: Microsoft Docs Scraping" -ForegroundColor Yellow
    Write-Host "-" * 40 -ForegroundColor Gray
    
    try {
        # Use our working Microsoft Docs approach
        $microsoftPatterns = @()
        $cmdlets = @("Get-ADUser", "Get-ADGroup", "Set-ADUser")
        
        foreach ($cmdletName in $cmdlets) {
            Write-Host "Processing $cmdletName..." -ForegroundColor White
            
            try {
                $cmdletUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($cmdletName.ToLower())"
                $cmdletResponse = Invoke-WebRequestWithRetry -Uri $cmdletUrl -MaxRetries 1
                
                # Determine domain and operation
                $domain = "general_ad"
                if ($cmdletName -like "*User*") { $domain = "user_management" }
                elseif ($cmdletName -like "*Group*") { $domain = "group_management" }
                
                $operation = "other"
                if ($cmdletName -like "Get-*") { $operation = "read" }
                elseif ($cmdletName -like "Set-*") { $operation = "update" }
                
                # Create pattern
                $pattern = New-KnowledgePattern -Title "Microsoft Docs: $cmdletName" -Content $cmdletResponse.Content -SourceUrl $cmdletUrl -SourceType "microsoft_docs" -Domain $domain -Operation $operation -Author "Microsoft" -CredibilityScore 0.95
                
                if ($pattern) {
                    # Extract code examples
                    $codeBlocks = Extract-CodeBlocks -Content $cmdletResponse.Content
                    if ($codeBlocks.Count -gt 0) {
                        $pattern.CodeTemplate = $codeBlocks[0]
                    }
                    
                    $microsoftPatterns += $pattern
                    Write-Host "  Created pattern: $($pattern.Id)" -ForegroundColor Green
                }
            }
            catch {
                Write-Host "  Failed: $($_.Exception.Message)" -ForegroundColor Red
            }
            
            Start-Sleep -Milliseconds 500
        }
        
        $allPatterns += $microsoftPatterns
        Write-Host "Microsoft Docs: $($microsoftPatterns.Count) patterns created" -ForegroundColor Green
    }
    catch {
        Write-Host "Microsoft Docs scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 2. Stack Overflow
    Write-Host "`nPHASE 2: Stack Overflow Scraping" -ForegroundColor Yellow
    Write-Host "-" * 40 -ForegroundColor Gray
    
    try {
        $baseUrl = "https://api.stackexchange.com/2.3/"
        $tags = "powershell;active-directory"
        $apiUrl = "${baseUrl}questions?order=desc&sort=votes&tagged=${tags}&site=stackoverflow&pagesize=3&filter=withbody"
        
        $response = Invoke-WebRequestWithRetry -Uri $apiUrl
        $data = $response.Content | ConvertFrom-Json
        
        Write-Host "Found $($data.items.Count) questions" -ForegroundColor White
        
        $stackOverflowPatterns = @()
        foreach ($question in $data.items) {
            Write-Host "Processing: $($question.title)" -ForegroundColor White
            
            $pattern = New-KnowledgePattern -Title "Stack Overflow: $($question.title)" -Content $question.body -SourceUrl $question.link -SourceType "stackoverflow" -Domain "general_ad" -Operation "read" -Author "Stack Overflow Community" -CredibilityScore 0.80
            
            if ($pattern) {
                $codeBlocks = Extract-CodeBlocks -Content $question.body
                if ($codeBlocks.Count -gt 0) {
                    $pattern.CodeTemplate = $codeBlocks[0]
                }
                
                $pattern.RelevanceScore = [Math]::Min(1.0, ($question.score / 100.0))
                if ($question.tags) { $pattern.Tags += $question.tags }
                
                $stackOverflowPatterns += $pattern
                Write-Host "  Created pattern: $($pattern.Id)" -ForegroundColor Green
            }
            
            Start-Sleep -Seconds 1
        }
        
        $allPatterns += $stackOverflowPatterns
        Write-Host "Stack Overflow: $($stackOverflowPatterns.Count) patterns created" -ForegroundColor Green
    }
    catch {
        Write-Host "Stack Overflow scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 3. GitHub
    Write-Host "`nPHASE 3: GitHub Scraping" -ForegroundColor Yellow
    Write-Host "-" * 40 -ForegroundColor Gray
    
    try {
        $baseUrl = "https://api.github.com/search/repositories"
        $query = "PowerShell ActiveDirectory language:PowerShell"
        $apiUrl = "${baseUrl}?q=${query}&sort=stars&order=desc&per_page=2"
        
        $headers = @{
            'User-Agent' = 'PowerShell-Scraper/1.0'
            'Accept' = 'application/vnd.github.v3+json'
        }
        
        $response = Invoke-WebRequestWithRetry -Uri $apiUrl -Headers $headers
        $data = $response.Content | ConvertFrom-Json
        
        Write-Host "Found $($data.items.Count) repositories" -ForegroundColor White
        
        $githubPatterns = @()
        foreach ($repo in $data.items) {
            Write-Host "Processing: $($repo.full_name)" -ForegroundColor White
            
            $repoContent = "# $($repo.full_name)`n`n**Description:** $($repo.description)`n`n**Stars:** $($repo.stargazers_count)`n**Language:** $($repo.language)`n**URL:** $($repo.html_url)"
            
            $domain = "general_ad"
            if ($repo.full_name -like "*user*" -or $repo.description -like "*user*") { $domain = "user_management" }
            elseif ($repo.full_name -like "*group*" -or $repo.description -like "*group*") { $domain = "group_management" }
            
            $pattern = New-KnowledgePattern -Title "GitHub: $($repo.full_name)" -Content $repoContent -SourceUrl $repo.html_url -SourceType "github" -Domain $domain -Operation "read" -Author $repo.owner.login -CredibilityScore 0.75
            
            if ($pattern) {
                $pattern.RelevanceScore = [Math]::Min(1.0, [Math]::Log10($repo.stargazers_count + 1) / 4.0)
                if ($repo.topics) { $pattern.Tags += $repo.topics }
                $pattern.Tags += @("github", "repository", $repo.language.ToLower())
                
                $githubPatterns += $pattern
                Write-Host "  Created pattern: $($pattern.Id)" -ForegroundColor Green
            }
            
            Start-Sleep -Seconds 1
        }
        
        $allPatterns += $githubPatterns
        Write-Host "GitHub: $($githubPatterns.Count) patterns created" -ForegroundColor Green
    }
    catch {
        Write-Host "GitHub scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Final Results
    Write-Host "`nCOMPREHENSIVE SCRAPING RESULTS" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Gray
    
    Write-Host "TOTAL PATTERNS: $($allPatterns.Count)" -ForegroundColor Cyan
    
    # Save comprehensive results
    if ($allPatterns.Count -gt 0) {
        $outputFile = Save-ScrapingResults -Patterns $allPatterns -SourceType "comprehensive"
        Write-Host "All patterns saved to: $outputFile" -ForegroundColor Green
        
        # Domain breakdown
        $domainStats = $allPatterns | Group-Object Domain | Sort-Object Count -Descending
        Write-Host "`nDomain breakdown:" -ForegroundColor Yellow
        foreach ($domain in $domainStats) {
            Write-Host "  $($domain.Name): $($domain.Count) patterns" -ForegroundColor Gray
        }
        
        # Source breakdown
        $sourceStats = $allPatterns | Group-Object { $_.Sources[0].SourceType } | Sort-Object Count -Descending
        Write-Host "`nSource breakdown:" -ForegroundColor Yellow
        foreach ($source in $sourceStats) {
            Write-Host "  $($source.Name): $($source.Count) patterns" -ForegroundColor Gray
        }
        
        # Show sample patterns
        Write-Host "`nSample patterns:" -ForegroundColor Yellow
        $sampleCount = [Math]::Min(3, $allPatterns.Count)
        for ($i = 0; $i -lt $sampleCount; $i++) {
            $pattern = $allPatterns[$i]
            Write-Host "  $($i + 1). $($pattern.Title)" -ForegroundColor Cyan
            Write-Host "     Domain: $($pattern.Domain), Operation: $($pattern.Operation)" -ForegroundColor Gray
            Write-Host "     Relevance: $($pattern.RelevanceScore.ToString('F2')), Credibility: $($pattern.CredibilityScore)" -ForegroundColor Gray
            Write-Host "     Code template: $($pattern.CodeTemplate.Length) characters" -ForegroundColor Gray
        }
    }
    
    Write-Host "`nComprehensive scraping pipeline completed successfully!" -ForegroundColor Green
}
catch {
    Write-Host "`nComprehensive scraping pipeline failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    exit 1
}
