# Test simple case to see improved step generation
param(
    [string]$BaseUrl = "http://localhost:8082"
)

Write-Host "=== TESTING SIMPLE CASE: RESET PASSWORD ===" -ForegroundColor Green
Write-Host "This should generate ONLY 1 step, not 5!" -ForegroundColor Yellow

$requestBody = @{
    requestId = [System.Guid]::NewGuid().ToString()
    userInput = "Reset password for user <PERSON>"
    priority = "normal"
    environment = "production"
    userId = "test.user"
} | ConvertTo-Json

Write-Host "`nRequest Body:" -ForegroundColor Gray
Write-Host $requestBody -ForegroundColor Gray

Write-Host "`nSending request..." -ForegroundColor White
$startTime = Get-Date

try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/IntentPlanning/analyze" -Method POST -Body $requestBody -ContentType "application/json"
    
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalMilliseconds
    
    Write-Host "`n" + "=" * 60 -ForegroundColor Green
    Write-Host "RESULTS" -ForegroundColor Green
    Write-Host "=" * 60
    
    Write-Host "Success: $($response.success)" -ForegroundColor $(if($response.success) { "Green" } else { "Red" })
    Write-Host "Steps Generated: $($response.primaryWorkflow.steps.Count)" -ForegroundColor $(if($response.primaryWorkflow.steps.Count -eq 1) { "Green" } else { "Red" })
    Write-Host "Response Time: $([math]::Round($duration))ms" -ForegroundColor Gray
    
    if ($response.success -and $response.primaryWorkflow.steps.Count -gt 0) {
        Write-Host "`nGENERATED STEPS:" -ForegroundColor White
        for ($i = 0; $i -lt $response.primaryWorkflow.steps.Count; $i++) {
            $step = $response.primaryWorkflow.steps[$i]
            Write-Host "  $($i + 1). $($step.description)" -ForegroundColor White
            Write-Host "     Command: $($step.operation)" -ForegroundColor Gray
        }
        
        Write-Host "`nANALYSIS:" -ForegroundColor Yellow
        if ($response.primaryWorkflow.steps.Count -eq 1) {
            Write-Host "✅ PERFECT: Exactly 1 step for password reset!" -ForegroundColor Green
        } elseif ($response.primaryWorkflow.steps.Count -le 2) {
            Write-Host "✅ GOOD: Reasonable number of steps" -ForegroundColor Green
        } else {
            Write-Host "❌ BAD: Too many steps for simple password reset!" -ForegroundColor Red
            Write-Host "   Password reset should be 1 step only!" -ForegroundColor Red
        }
    } else {
        Write-Host "`n❌ FAILED: No workflow generated" -ForegroundColor Red
    }
    
} catch {
    Write-Host "`n❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "EXPECTATION" -ForegroundColor Cyan
Write-Host "=" * 60
Write-Host "For 'Reset password for user Mary Johnson':" -ForegroundColor White
Write-Host "  Expected: 1 step only" -ForegroundColor Green
Write-Host "  Should be: 'Reset Active Directory account password for user Mary Johnson'" -ForegroundColor Green
Write-Host "  Command should be: Set-ADAccountPassword or similar" -ForegroundColor Green
