# Check Group Tools
Write-Host "Checking group-related tools..." -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:8082/api/IntentPlanning/health/tools' -Method Get
    $allTools = $response.PSObject.Properties.Name | Sort-Object
    
    Write-Host "Total tools: $($allTools.Count)" -ForegroundColor Yellow
    Write-Host ""
    
    $groupTools = $allTools | Where-Object { $_ -like '*group*' }
    Write-Host "Group-related tools ($($groupTools.Count)):" -ForegroundColor Green
    foreach ($tool in $groupTools) {
        Write-Host "  - $tool" -ForegroundColor White
    }
    Write-Host ""
    
    $memberTools = $allTools | Where-Object { $_ -like '*member*' }
    Write-Host "Member-related tools ($($memberTools.Count)):" -ForegroundColor Green
    foreach ($tool in $memberTools) {
        Write-Host "  - $tool" -ForegroundColor White
    }
    Write-Host ""
    
    $addTools = $allTools | Where-Object { $_ -like '*add*' }
    Write-Host "Add-related tools ($($addTools.Count)):" -ForegroundColor Green
    foreach ($tool in $addTools | Select-Object -First 10) {
        Write-Host "  - $tool" -ForegroundColor White
    }
    if ($addTools.Count -gt 10) {
        Write-Host "  ... and $($addTools.Count - 10) more" -ForegroundColor Gray
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Check complete!" -ForegroundColor Green
