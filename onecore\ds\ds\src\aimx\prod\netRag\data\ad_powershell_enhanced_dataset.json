﻿{
    "commands":  [
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Add-ADCentralAccessPolicyMember",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Add-ADCentralAccessPolicyMember",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Add-ADCentralAccessPolicyMember",
                         "rag_document":  "Command: Add-ADCentralAccessPolicyMember\nPurpose: Add to ADCentralAccessPolicyMember\nPrimary Task: Add items to CentralAccessPolicyMember in Active Directory\nCommon Tasks:\n- Add members to CentralAccessPolicyMember\n- Associate objects with CentralAccessPolicyMember\n- Include items in CentralAccessPolicyMember\nKey Parameters:\n- Members (Required): Specifies a set of central access rule (CAR) objects in a comma-separated list to add to a central a...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command adds the central access rules Finance Documents Rule and Corporate Documents Rule to th...\n- This command gets all central access policies that have a name that starts with Corporate and then p...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Add-ADComputerServiceAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Add-ADComputerServiceAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Add-ADComputerServiceAccount",
                         "rag_document":  "Command: Add-ADComputerServiceAccount\nPurpose: Add to ADComputerServiceAccount\nPrimary Task: Add items to ComputerServiceAccount in Active Directory\nCommon Tasks:\n- Add members to ComputerServiceAccount\n- Associate objects with ComputerServiceAccount\n- Include items in ComputerServiceAccount\nKey Parameters:\n- Identity (Required): Specifies an Active Directory computer object by providing one of the following property values. The...\nExample Scenarios:\n- This command adds the service account SvcAcct1 to a Computer Account ComputerAcct1.\n- This command adds two service accounts, SvcAcct1 and SvcAcct2, to a Computer Account ComputerAcct1.\nCategory: Computer Management Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Add-ADDomainControllerPasswordReplicationPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Add-ADDomainControllerPasswordReplicationPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Add-ADDomainControllerPasswordReplicationPolicy",
                         "rag_document":  "Command: Add-ADDomainControllerPasswordReplicationPolicy\nPurpose: Add to ADDomainControllerPasswordReplicationPolicy\nPrimary Task: Add items to DomainControllerPasswordReplicationPolicy in Active Directory\nCommon Tasks:\n- Add members to DomainControllerPasswordReplicationPolicy\n- Associate objects with DomainControllerPasswordReplicationPolicy\n- Include items in DomainControllerPasswordReplicationPolicy\nKey Parameters:\n- Identity: Specifies an Active Directory domain controller object by providing one of the following values. The...\nExample Scenarios:\n- This command adds user accounts with the specified SamAccountNames to the Allowed list on the RODC s...\n- This command adds user accounts with the specified SamAccountNames to the Denied list on the RODC sp...\nCategory: Domain Management Replication Management Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Add-ADFineGrainedPasswordPolicySubject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Add-ADFineGrainedPasswordPolicySubject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  3,
                         "usage_category":  "rare",
                         "command_name":  "Add-ADFineGrainedPasswordPolicySubject",
                         "rag_document":  "Command: Add-ADFineGrainedPasswordPolicySubject\nPurpose: Add to ADFineGrainedPasswordPolicySubject\nPrimary Task: Add items to FineGrainedPasswordPolicySubject in Active Directory\nCommon Tasks:\n- Add members to FineGrainedPasswordPolicySubject\n- Associate objects with FineGrainedPasswordPolicySubject\n- Include items in FineGrainedPasswordPolicySubject\nKey Parameters:\n- Identity (Required): Specifies an Active Directory fine-grained password policy object by providing one of the following ...\n- Subjects (Required): Specifies one or more users or groups. To specify more than one user or group, use a comma-separated...\nExample Scenarios:\n- This command applies the fine-grained password policy named DomainUsersPSO to the Domain Users globa...\n- This command applies the fine-grained password policy named DlgtdAdminsPSO to users with the SAM acc...\n- This command applies the fine-grained password policy named DlgtdAdminsPSO to the group DlgtdAdminGr...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Add user John Doe to the Sales group",
                                                         "description":  "Add a single user to a group by SAM account name",
                                                         "powershell_command":  "Add-ADGroupMember -Identity \"Sales\" -Members \"jdoe\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Add multiple users to the Administrators group",
                                                         "description":  "Add multiple users to a group using comma-separated list",
                                                         "powershell_command":  "Add-ADGroupMember -Identity \"Administrators\" -Members \"SQL01\",\"SQL02\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Add user from another domain to a group",
                                                         "description":  "Add user from one domain to a group in another domain",
                                                         "powershell_command":  "$User = Get-ADUser -Identity \"CN=Chew David,OU=UserAccounts,DC=NORTHAMERICA,DC=FABRIKAM,DC=COM\" -Server \"northamerica.fabrikam.com\"; Add-ADGroupMember -Identity \"CN=AccountLeads,OU=UserAccounts,DC=EUROPE,DC=FABRIKAM,DC=COM\" -Members $User -Server \"europe.fabrikam.com\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Add all users from Finance OU to a group",
                                                         "description":  "Bulk add users from an OU to a group",
                                                         "powershell_command":  "Get-ADUser -Filter * -SearchBase \"OU=Finance,DC=contoso,DC=com\" | ForEach-Object { Add-ADGroupMember -Identity \"FinanceTeam\" -Members $_.SamAccountName }",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Add computer accounts to a group",
                                                         "description":  "Add computer accounts to a security group",
                                                         "powershell_command":  "Add-ADGroupMember -Identity \"ComputerGroup\" -Members \"COMPUTER01$\",\"COMPUTER02$\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Add service account to SQL Admins group",
                                                         "description":  "Add service account to administrative group",
                                                         "powershell_command":  "Add-ADGroupMember -Identity \"SQL Admins\" -Members \"svc-sql\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Add user to group using distinguished name",
                                                         "description":  "Use distinguished names for both group and member",
                                                         "powershell_command":  "Add-ADGroupMember -Identity \"CN=Sales,OU=Groups,DC=contoso,DC=com\" -Members \"CN=John Doe,OU=Users,DC=contoso,DC=com\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Add nested group to parent group",
                                                         "description":  "Add a group as a member of another group (nesting)",
                                                         "powershell_command":  "Add-ADGroupMember -Identity \"AllStaff\" -Members \"SalesTeam\"",
                                                         "source":  "Community Examples"
                                                     }
                                                 ],
                         "usage_frequency":  75,
                         "usage_category":  "very_common",
                         "command_name":  "Add-ADGroupMember",
                         "rag_document":  "Command: Add-ADGroupMember\nPurpose: Add to ADGroupMember\nPrimary Task: Add items to GroupMember in Active Directory\nCommon Tasks:\n- Add members to GroupMember\n- Associate objects with GroupMember\n- Include items in GroupMember\nKey Parameters:\n- Members (Required): Specifies an array of user, group, and computer objects in a comma-separated list to add to a group....\n- Identity (Required): Specifies an Active Directory group object by providing one of the following values. The identifier ...\nExample Scenarios:\n- This command adds the user accounts with the SAM account names SQL01 and SQL02 to the group SvcAccPS...\n- This command gets a group from the organizational unit OU=AccountDeptOU,DC=AppNC in the AD LDS insta...\n- This command adds the user CN=Chew David,OU=UserAccounts from the North America domain to the group ...\nCategory: Group Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Add-ADPrincipalGroupMembership",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Add-ADPrincipalGroupMembership",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  15,
                         "usage_category":  "moderate",
                         "command_name":  "Add-ADPrincipalGroupMembership",
                         "rag_document":  "Command: Add-ADPrincipalGroupMembership\nPurpose: Add to ADPrincipalGroupMembership\nPrimary Task: Add items to PrincipalGroupMembership in Active Directory\nCommon Tasks:\n- Add members to PrincipalGroupMembership\n- Associate objects with PrincipalGroupMembership\n- Include items in PrincipalGroupMembership\nKey Parameters:\n- Identity (Required): Specifies an Active Directory principal object by providing one of the following property values. Th...\nExample Scenarios:\n- This command adds the user with SAM account name SQLAdmin1 to the group DlgtdAdminsPSOGroup.\n- This command gets all users with SvcAccount in their name and adds them to the group SvcAccPSOGroup.\n- This command adds all employees in Branch1 in the AD LDS instance localhost:60000 whose title is Acc...\nCategory: Group Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Add-ADResourcePropertyListMember",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Add-ADResourcePropertyListMember",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Add-ADResourcePropertyListMember",
                         "rag_document":  "Command: Add-ADResourcePropertyListMember\nPurpose: Add to ADResourcePropertyListMember\nPrimary Task: Add items to ResourcePropertyListMember in Active Directory\nCommon Tasks:\n- Add members to ResourcePropertyListMember\n- Associate objects with ResourcePropertyListMember\n- Include items in ResourcePropertyListMember\nKey Parameters:\n- Members (Required): Specifies a set of ADResourceProperty objects in a comma-separated list to add to a resource propert...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command adds the resource members named Country and Authors to the list named Global Resource P...\n- This command gets any resource property list that has a name that begins with Corporate and then pas...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Clear-ADAccountExpiration",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Clear-ADAccountExpiration",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  12,
                         "usage_category":  "uncommon",
                         "command_name":  "Clear-ADAccountExpiration",
                         "rag_document":  "Command: Clear-ADAccountExpiration\nPurpose: Manage ADAccountExpiration\nPrimary Task: Manage ADAccountExpiration\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command clears the account expiration date for the user with SamAccountName PattiFuller.\n- This command clears the account expiration date for the user with DistinguishedName CN=PattiFuller,D...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Clear-ADClaimTransformLink",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Clear-ADClaimTransformLink",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Clear-ADClaimTransformLink",
                         "rag_document":  "Command: Clear-ADClaimTransformLink\nPurpose: Manage ADClaimTransformLink\nPrimary Task: Manage ADClaimTransformLink\nKey Parameters:\n- Identity (Required): Specifies an Active Directory trust object by providing one of the following values. The identifier ...\nExample Scenarios:\n- This command removes the policy named DenyAllPolicy from the corp.contoso.com trust.\n- This command removes any policies that are applied to where this forest acts as the trusted forest i...\n- This command removes DenyAllPolicy that is applied to where this forest acts as the trusted domain i...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Complete-ADServiceAccountMigration",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Complete-ADServiceAccountMigration",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Complete-ADServiceAccountMigration",
                         "rag_document":  "Command: Complete-ADServiceAccountMigration\nPurpose: Manage ADServiceAccountMigration\nPrimary Task: Manage ADServiceAccountMigration\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- \n- \nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Disable a specific user account",
                                                         "description":  "Disable a user account by SAM account name",
                                                         "powershell_command":  "Disable-ADAccount -Identity \"jdoe\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Disable user account using distinguished name",
                                                         "description":  "Disable account using full distinguished name",
                                                         "powershell_command":  "Disable-ADAccount -Identity \"CN=John Doe,OU=Users,DC=contoso,DC=com\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Disable all accounts in Finance OU",
                                                         "description":  "Bulk disable user accounts in organizational unit",
                                                         "powershell_command":  "Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=Users,DC=contoso,DC=com\" | Disable-ADAccount",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Disable inactive user accounts",
                                                         "description":  "Disable accounts that haven\u0027t logged in recently",
                                                         "powershell_command":  "$Date = (Get-Date).AddDays(-90); Get-ADUser -Filter \"LastLogonTimeStamp -lt $Date\" | Disable-ADAccount",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Disable computer account",
                                                         "description":  "Disable a computer account in Active Directory",
                                                         "powershell_command":  "Disable-ADAccount -Identity \"COMPUTER01$\"",
                                                         "source":  "Community Examples"
                                                     }
                                                 ],
                         "usage_frequency":  55,
                         "usage_category":  "common",
                         "command_name":  "Disable-ADAccount",
                         "rag_document":  "Command: Disable-ADAccount\nPurpose: Disable ADAccount\nPrimary Task: Disable Account functionality\nCommon Tasks:\n- Disable Account functionality\n- Deactivate Account\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command disables the account with identity SAMAccountName PattiFul.\n- This command disables the account with DistinguishedName CN=Patti Fuller,OU=Finance,OU=Users,DC=FABR...\n- This command disables all accounts in the organizational unit OU=Finance,OU=Users,DC=FABRIKAM,DC=COM...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Disable-ADOptionalFeature",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Disable-ADOptionalFeature",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Disable-ADOptionalFeature",
                         "rag_document":  "Command: Disable-ADOptionalFeature\nPurpose: Disable ADOptionalFeature\nPrimary Task: Disable OptionalFeature functionality\nCommon Tasks:\n- Disable OptionalFeature functionality\n- Deactivate OptionalFeature\nKey Parameters:\n- Target (Required): Specifies the domain or forest in which to modify the optional feature. You can identify the target ...\n- Scope (Required): Specifies the scope at which the feature is enabled or disabled. The acceptable values for this para...\n- Identity (Required): Specifies an Active Directory optional feature object by providing one of the following values. The ...\nExample Scenarios:\n- This command disables the optional feature named Feature 1 for the forest that has the NetBIOS name ...\n- This command disables the optional feature that has the distinguished name CN=Feature 1,CN=Optional ...\n- This command disables the optional feature that has the GUID 54ec6e43-75a8-445b-aa7b-346a1e096659 fo...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Enable a disabled user account",
                                                         "description":  "Enable a user account by SAM account name",
                                                         "powershell_command":  "Enable-ADAccount -Identity \"jdoe\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Enable account using distinguished name",
                                                         "description":  "Enable account using full distinguished name",
                                                         "powershell_command":  "Enable-ADAccount -Identity \"CN=John Doe,OU=Users,DC=contoso,DC=com\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Enable all accounts in specific OU",
                                                         "description":  "Bulk enable user accounts in organizational unit",
                                                         "powershell_command":  "Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=UserAccounts,DC=contoso,DC=com\" | Enable-ADAccount",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Enable computer account",
                                                         "description":  "Enable a computer account in Active Directory",
                                                         "powershell_command":  "Enable-ADAccount -Identity \"COMPUTER01$\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Enable accounts from CSV list",
                                                         "description":  "Bulk enable accounts from CSV file",
                                                         "powershell_command":  "Import-Csv \"EnableUsers.csv\" | ForEach-Object { Enable-ADAccount -Identity $_.SamAccountName }",
                                                         "source":  "Community Examples"
                                                     }
                                                 ],
                         "usage_frequency":  50,
                         "usage_category":  "common",
                         "command_name":  "Enable-ADAccount",
                         "rag_document":  "Command: Enable-ADAccount\nPurpose: Enable ADAccount\nPrimary Task: Enable Account functionality\nCommon Tasks:\n- Enable Account functionality\n- Activate Account\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command enables the account with identity SamAccountName PattiFul.\n- This command enables the account with DistinguishedName CN=Patti Fuller,OU=Finance,OU=UserAccounts,D...\n- This command enables all accounts in the organizational unit: OU=Finance,OU=UserAccounts,DC=FABRIKAM...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Enable-ADOptionalFeature",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Enable-ADOptionalFeature",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Enable-ADOptionalFeature",
                         "rag_document":  "Command: Enable-ADOptionalFeature\nPurpose: Enable ADOptionalFeature\nPrimary Task: Enable OptionalFeature functionality\nCommon Tasks:\n- Enable OptionalFeature functionality\n- Activate OptionalFeature\nKey Parameters:\n- Target (Required): Specifies the domain or forest in which to modify the optional feature. You can identify the target ...\n- Scope (Required): Specifies the scope at which the feature is enabled or disabled. The acceptable values for this para...\n- Identity (Required): Specifies an Active Directory optional feature object by providing one of the following values. The ...\nExample Scenarios:\n- This command enables the optional feature Recycle Bin Feature for the forest fabrikam.com. This oper...\n- This command enables the optional feature Feature 1 for the AD LDS instance lds.fabrikam.com. This o...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADAccountAuthorizationGroup",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADAccountAuthorizationGroup",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  5,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADAccountAuthorizationGroup",
                         "rag_document":  "Command: Get-ADAccountAuthorizationGroup\nPurpose: Retrieve ADAccountAuthorizationGroup information\nPrimary Task: Retrieve AccountAuthorizationGroup information from Active Directory\nCommon Tasks:\n- Find a specific AccountAuthorizationGroup\n- List all AccountAuthorizationGroup objects\n- Search for AccountAuthorizationGroup by criteria\n- Retrieve AccountAuthorizationGroup properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command returns all security groups for the specified account with SamAccountName DavidCh.\n- This command returns all security groups for the specified account with DistinguishedName CN=DavidCh...\n- This command returns a filtered list of built-in security groups that do not have an empty or null s...\nCategory: Group Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADAccountResultantPasswordReplicationPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADAccountResultantPasswordReplicationPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADAccountResultantPasswordReplicationPolicy",
                         "rag_document":  "Command: Get-ADAccountResultantPasswordReplicationPolicy\nPurpose: Retrieve ADAccountResultantPasswordReplicationPolicy information\nPrimary Task: Retrieve AccountResultantPasswordReplicationPolicy information from Active Directory\nCommon Tasks:\n- Find a specific AccountResultantPasswordReplicationPolicy\n- List all AccountResultantPasswordReplicationPolicy objects\n- Search for AccountResultantPasswordReplicationPolicy by criteria\n- Retrieve AccountResultantPasswordReplicationPolicy properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command gets the password replication policy on the domain specified by the DomainController pa...\n- This command gets the password replication policy on the domain controller specified by the DomainCo...\nCategory: Replication Management Policy Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADAuthenticationPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADAuthenticationPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  3,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADAuthenticationPolicy",
                         "rag_document":  "Command: Get-ADAuthenticationPolicy\nPurpose: Retrieve ADAuthenticationPolicy information\nPrimary Task: Retrieve AuthenticationPolicy information from Active Directory\nCommon Tasks:\n- Find a specific AuthenticationPolicy\n- List all AuthenticationPolicy objects\n- Search for AuthenticationPolicy by criteria\n- Retrieve AuthenticationPolicy properties\nKey Parameters:\n- LDAPFilter (Required): Specifies a filter using the LDAP search filter syntax defined in RFC2254 to filter Active Directory...\n- Filter (Required): Specifies a query string that retrieves Active Directory Domain Services objects. This string uses t...\n- Identity (Required): Specifies an Active Directory Domain Services authentication policy object. Specify the authenticati...\n- Properties: Specifies the properties of the output object to get from the server. Use this parameter to get prop...\nExample Scenarios:\n- This command gets an authentication policy object by specifying the object name.\n- This command gets all authentication policies that match the LDAP filter specified by the LDAPFilter...\n- This command gets all authentication policies that match the filter specified by the Filter paramete...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADAuthenticationPolicySilo",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADAuthenticationPolicySilo",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADAuthenticationPolicySilo",
                         "rag_document":  "Command: Get-ADAuthenticationPolicySilo\nPurpose: Retrieve ADAuthenticationPolicySilo information\nPrimary Task: Retrieve AuthenticationPolicySilo information from Active Directory\nCommon Tasks:\n- Find a specific AuthenticationPolicySilo\n- List all AuthenticationPolicySilo objects\n- Search for AuthenticationPolicySilo by criteria\n- Retrieve AuthenticationPolicySilo properties\nKey Parameters:\n- LDAPFilter (Required): Specifies a filter using the LDAP search filter syntax defined in RFC2254 to filter Active Directory...\n- Filter (Required): Specifies a query string that retrieves Active Directory Domain Services objects. This string uses t...\n- Identity (Required): Specifies an Active Directory Domain Services authentication policy silo object. Specify the authent...\n- Properties: Specifies the properties of the output object to get from the server. Use this parameter to get prop...\nExample Scenarios:\n- This command gets an authentication policy silo object named AuthenticationPolicySilo01.\n- This command gets all the authentication policy silos that match the filter specified by the Filter ...\n- This command gets all properties for the authentication policy silo named AuthenticationPolicySilo02...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADCentralAccessPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADCentralAccessPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADCentralAccessPolicy",
                         "rag_document":  "Command: Get-ADCentralAccessPolicy\nPurpose: Retrieve ADCentralAccessPolicy information\nPrimary Task: Retrieve CentralAccessPolicy information from Active Directory\nCommon Tasks:\n- Find a specific CentralAccessPolicy\n- List all CentralAccessPolicy objects\n- Search for CentralAccessPolicy by criteria\n- Retrieve CentralAccessPolicy properties\nKey Parameters:\n- LDAPFilter (Required): Specifies a filter using the LDAP search filter syntax defined in RFC2254 to filter Active Directory...\n- Filter (Required): Specifies a query string that retrieves Active Directory Domain Services objects. This string uses t...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Properties: Specifies the properties of the output object to get from the server. Use this parameter to get prop...\nExample Scenarios:\n- This command retrieves a list of all central access policies.\n- This command gets the central access policies that have the central access rule Finance Documents Ru...\n- This command gets information for a central access policy named Finance Policy.\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADCentralAccessRule",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADCentralAccessRule",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADCentralAccessRule",
                         "rag_document":  "Command: Get-ADCentralAccessRule\nPurpose: Retrieve ADCentralAccessRule information\nPrimary Task: Retrieve CentralAccessRule information from Active Directory\nCommon Tasks:\n- Find a specific CentralAccessRule\n- List all CentralAccessRule objects\n- Search for CentralAccessRule by criteria\n- Retrieve CentralAccessRule properties\nKey Parameters:\n- LDAPFilter (Required): Specifies a filter using the LDAP search filter syntax defined in RFC2254 to filter Active Directory...\n- Filter (Required): Specifies a query string that retrieves Active Directory Domain Services objects. This string uses t...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Properties: Specifies the properties of the output object to get from the server. Use this parameter to get prop...\nExample Scenarios:\n- This command retrieves a list of all central access rules.\n- This command retrieves the central access rules that have Department in its resource condition.\n- This command retrieves a central access rule named Finance Documents Rule.\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADClaimTransformPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADClaimTransformPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADClaimTransformPolicy",
                         "rag_document":  "Command: Get-ADClaimTransformPolicy\nPurpose: Retrieve ADClaimTransformPolicy information\nPrimary Task: Retrieve ClaimTransformPolicy information from Active Directory\nCommon Tasks:\n- Find a specific ClaimTransformPolicy\n- List all ClaimTransformPolicy objects\n- Search for ClaimTransformPolicy by criteria\n- Retrieve ClaimTransformPolicy properties\nKey Parameters:\n- LDAPFilter (Required): Specifies a filter using the LDAP search filter syntax defined in RFC2254 to filter Active Directory...\n- Filter (Required): Specifies a query string that retrieves Active Directory Domain Services objects. This string uses t...\n- Identity: Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Properties: Specifies the properties of the output object to get from the server. Use this parameter to get prop...\nExample Scenarios:\n- This command retrieves a list of all claims transformation policies.\n- This example gets all the claims transformation policies that are applied to trusts made with corp.c...\n- This command gets the claims transformation policy with the name DenyAllPolicy.\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADClaimType",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADClaimType",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADClaimType",
                         "rag_document":  "Command: Get-ADClaimType\nPurpose: Retrieve ADClaimType information\nPrimary Task: Retrieve ClaimType information from Active Directory\nCommon Tasks:\n- Find a specific ClaimType\n- List all ClaimType objects\n- Search for ClaimType by criteria\n- Retrieve ClaimType properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\nExample Scenarios:\n- This command retrieves a list of all claim types.\n- This command gets all the claim types that are sourced from the attribute title.\n- This command gets the claim type with display name Employee Type.\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Get all computers in the domain",
                                                         "description":  "Retrieve all computer objects from Active Directory",
                                                         "powershell_command":  "Get-ADComputer -Filter *",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Find computers with names starting with WS",
                                                         "description":  "Search for workstations using naming convention",
                                                         "powershell_command":  "Get-ADComputer -Filter \"Name -like \u0027WS*\u0027\" | Format-Table Name,DNSHostName,IPv4Address -AutoSize",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Get computers that changed password in last 90 days",
                                                         "description":  "Find recently active computer accounts",
                                                         "powershell_command":  "$Date = (Get-Date).AddDays(-90); Get-ADComputer -Filter \"PasswordLastSet -ge $Date\" -Properties PasswordLastSet",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Show me all properties for a specific computer",
                                                         "description":  "Get complete computer information including all attributes",
                                                         "powershell_command":  "Get-ADComputer -Identity \"COMPUTER01\" -Properties *",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Find disabled computer accounts",
                                                         "description":  "Locate inactive computer accounts in AD",
                                                         "powershell_command":  "Get-ADComputer -Filter \"Enabled -eq $false\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Get computers in a specific OU",
                                                         "description":  "Search for computers within an organizational unit",
                                                         "powershell_command":  "Get-ADComputer -Filter * -SearchBase \"OU=Workstations,DC=contoso,DC=com\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Find computers by operating system",
                                                         "description":  "Filter computers by OS version",
                                                         "powershell_command":  "Get-ADComputer -Filter \"OperatingSystem -like \u0027*Windows 10*\u0027\" -Properties OperatingSystem",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Get computer accounts with IPv4 addresses",
                                                         "description":  "Find computers that have IP addresses assigned",
                                                         "powershell_command":  "Get-ADComputer -Filter * -Properties IPv4Address | Where-Object {$_.IPv4Address -ne $null}",
                                                         "source":  "Community Examples"
                                                     }
                                                 ],
                         "usage_frequency":  85,
                         "usage_category":  "very_common",
                         "command_name":  "Get-ADComputer",
                         "rag_document":  "Command: Get-ADComputer\nPurpose: Retrieve ADComputer information\nPrimary Task: Retrieve Computer information from Active Directory\nCommon Tasks:\n- Find a specific Computer\n- List all Computer objects\n- Search for Computer by criteria\n- Retrieve Computer properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\n- Identity (Required): Specifies an Active Directory computer object by providing one of the following property values. The...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the Windows Power...\nExample Scenarios:\n- This command gets a specific computer showing all the properties.\n- This command gets all the computers with a name starting with a particular string and shows the name...\n- This command gets all the computers that have changed their password in the last 90 days.\nCategory: Computer Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADComputerServiceAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADComputerServiceAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  3,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADComputerServiceAccount",
                         "rag_document":  "Command: Get-ADComputerServiceAccount\nPurpose: Retrieve ADComputerServiceAccount information\nPrimary Task: Retrieve ComputerServiceAccount information from Active Directory\nCommon Tasks:\n- Find a specific ComputerServiceAccount\n- List all ComputerServiceAccount objects\n- Search for ComputerServiceAccount by criteria\n- Retrieve ComputerServiceAccount properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory computer object by providing one of the following property values. The...\nExample Scenarios:\n- This command gets the service accounts hosted on a computer account ComputerAcct1.\nCategory: Computer Management Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADDCCloningExcludedApplicationList",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADDCCloningExcludedApplicationList",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADDCCloningExcludedApplicationList",
                         "rag_document":  "Command: Get-ADDCCloningExcludedApplicationList\nPurpose: Retrieve ADDCCloningExcludedApplicationList information\nPrimary Task: Retrieve DCCloningExcludedApplicationList information from Active Directory\nCommon Tasks:\n- Find a specific DCCloningExcludedApplicationList\n- List all DCCloningExcludedApplicationList objects\n- Search for DCCloningExcludedApplicationList by criteria\n- Retrieve DCCloningExcludedApplicationList properties\nKey Parameters:\n- Path: Specifies the folder path to use when creating the CustomDCCloneAllowList.xml file using the Generat...\nExample Scenarios:\n- This command displays the excluded application list to the console. If there is already a CustomDCCl...\n- This command generates the excluded application list as a file named CustomDCCloneAllowList.xml at t...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Get the default domain password policy",
                                                         "description":  "Retrieve current domain password policy settings",
                                                         "powershell_command":  "Get-ADDefaultDomainPasswordPolicy",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Get password policy for specific domain",
                                                         "description":  "Get password policy for a specific domain",
                                                         "powershell_command":  "Get-ADDefaultDomainPasswordPolicy -Identity \"contoso.com\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Get password policy from current logged on user domain",
                                                         "description":  "Get policy from the current user\u0027s domain",
                                                         "powershell_command":  "Get-ADDefaultDomainPasswordPolicy -Current LoggedOnUser",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Export password policy settings to file",
                                                         "description":  "Export password policy configuration to CSV",
                                                         "powershell_command":  "Get-ADDefaultDomainPasswordPolicy | Export-Csv -Path \"PasswordPolicy.csv\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Compare password policies across domains",
                                                         "description":  "Get password policies for all domains in forest",
                                                         "powershell_command":  "Get-ADForest | Select-Object -ExpandProperty Domains | ForEach-Object { Get-ADDefaultDomainPasswordPolicy -Identity $_ }",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Check if password complexity is enabled",
                                                         "description":  "Check specific password policy setting",
                                                         "powershell_command":  "(Get-ADDefaultDomainPasswordPolicy).ComplexityEnabled",
                                                         "source":  "Community Examples"
                                                     }
                                                 ],
                         "usage_frequency":  60,
                         "usage_category":  "very_common",
                         "command_name":  "Get-ADDefaultDomainPasswordPolicy",
                         "rag_document":  "Command: Get-ADDefaultDomainPasswordPolicy\nPurpose: Retrieve ADDefaultDomainPasswordPolicy information\nPrimary Task: Retrieve DefaultDomainPasswordPolicy information from Active Directory\nCommon Tasks:\n- Find a specific DefaultDomainPasswordPolicy\n- List all DefaultDomainPasswordPolicy objects\n- Search for DefaultDomainPasswordPolicy by criteria\n- Retrieve DefaultDomainPasswordPolicy properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory domain object by providing one of the following property values. The i...\nExample Scenarios:\n- This command gets the default domain password policy from current logged on user domain.\n- This command gets the default domain password policy from current local computer.\n- This command gets the default domain password policy from the domain specified by the Site parameter...\nCategory: Domain Management Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADDomain",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADDomain",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  30,
                         "usage_category":  "common",
                         "command_name":  "Get-ADDomain",
                         "rag_document":  "Command: Get-ADDomain\nPurpose: Retrieve ADDomain information\nPrimary Task: Retrieve Domain information from Active Directory\nCommon Tasks:\n- Find a specific Domain\n- List all Domain objects\n- Search for Domain by criteria\n- Retrieve Domain properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory domain object by providing one of the following property values. The i...\nExample Scenarios:\n- This command gets the domain information for the domain user.com.\n- This command gets the domain information of the current local computer domain.\n- This command gets the domain information for the domain of the currently logged on user.\nCategory: Domain Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADDomainController",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADDomainController",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  30,
                         "usage_category":  "common",
                         "command_name":  "Get-ADDomainController",
                         "rag_document":  "Command: Get-ADDomainController\nPurpose: Retrieve ADDomainController information\nPrimary Task: Retrieve DomainController information from Active Directory\nCommon Tasks:\n- Find a specific DomainController\n- List all DomainController objects\n- Search for DomainController by criteria\n- Retrieve DomainController properties\nKey Parameters:\n- Discover (Required): Specifies to return a discoverable domain controller that meets the conditions specified by the cmdl...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the Windows Power...\n- Identity: Specifies an Active Directory domain controller object by providing one of the following values. The...\nExample Scenarios:\n- This command gets one available domain controller in the site specified by the Site parameter. The c...\n- This command force discovers or finds one available domain controller in the site specified by the S...\n- This command gets a global catalog in the current forest using Discovery.\nCategory: Domain Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADDomainControllerPasswordReplicationPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADDomainControllerPasswordReplicationPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADDomainControllerPasswordReplicationPolicy",
                         "rag_document":  "Command: Get-ADDomainControllerPasswordReplicationPolicy\nPurpose: Retrieve ADDomainControllerPasswordReplicationPolicy information\nPrimary Task: Retrieve DomainControllerPasswordReplicationPolicy information from Active Directory\nCommon Tasks:\n- Find a specific DomainControllerPasswordReplicationPolicy\n- List all DomainControllerPasswordReplicationPolicy objects\n- Search for DomainControllerPasswordReplicationPolicy by criteria\n- Retrieve DomainControllerPasswordReplicationPolicy properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory domain controller object by providing one of the following values. The...\n- Denied (Required): Specifies the users, computers, groups or other accounts to add to the list of accounts that are den...\nExample Scenarios:\n- This command gets from an RODC domain controller password replication policy the allowed accounts sh...\n- This command gets the password replication policy allowed lists from all RODCs in the domain.\nCategory: Domain Management Replication Management Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADDomainControllerPasswordReplicationPolicyUsage",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADDomainControllerPasswordReplicationPolicyUsage",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADDomainControllerPasswordReplicationPolicyUsage",
                         "rag_document":  "Command: Get-ADDomainControllerPasswordReplicationPolicyUsage\nPurpose: Retrieve ADDomainControllerPasswordReplicationPolicyUsage information\nPrimary Task: Retrieve DomainControllerPasswordReplicationPolicyUsage information from Active Directory\nCommon Tasks:\n- Find a specific DomainControllerPasswordReplicationPolicyUsage\n- List all DomainControllerPasswordReplicationPolicyUsage objects\n- Search for DomainControllerPasswordReplicationPolicyUsage by criteria\n- Retrieve DomainControllerPasswordReplicationPolicyUsage properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory domain controller object by providing one of the following values. The...\nExample Scenarios:\n- This command gets the authenticated accounts for the RODC specified by the Identity parameter. The c...\n- This command gets the revealed accounts for the RODC specified by the Identity parameter. The comman...\n- This command gets the list of accounts cached across all RODCs in the domain.\nCategory: Domain Management Replication Management Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADFineGrainedPasswordPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADFineGrainedPasswordPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  7,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADFineGrainedPasswordPolicy",
                         "rag_document":  "Command: Get-ADFineGrainedPasswordPolicy\nPurpose: Retrieve ADFineGrainedPasswordPolicy information\nPrimary Task: Retrieve FineGrainedPasswordPolicy information from Active Directory\nCommon Tasks:\n- Find a specific FineGrainedPasswordPolicy\n- List all FineGrainedPasswordPolicy objects\n- Search for FineGrainedPasswordPolicy by criteria\n- Retrieve FineGrainedPasswordPolicy properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\n- Identity (Required): Specifies an Active Directory fine-grained password policy object by providing one of the following ...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\nExample Scenarios:\n- This command gets the fine-grained password policy named AdminsPSO.\n- This command gets all the properties for the fine-grained password policy with DistinguishedName CN=...\n- This command gets all the fine-grained password policy objects that have a name that begins with adm...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADFineGrainedPasswordPolicySubject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADFineGrainedPasswordPolicySubject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  4,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADFineGrainedPasswordPolicySubject",
                         "rag_document":  "Command: Get-ADFineGrainedPasswordPolicySubject\nPurpose: Retrieve ADFineGrainedPasswordPolicySubject information\nPrimary Task: Retrieve FineGrainedPasswordPolicySubject information from Active Directory\nCommon Tasks:\n- Find a specific FineGrainedPasswordPolicySubject\n- List all FineGrainedPasswordPolicySubject objects\n- Search for FineGrainedPasswordPolicySubject by criteria\n- Retrieve FineGrainedPasswordPolicySubject properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory fine-grained password policy object by providing one of the following ...\nExample Scenarios:\n- This command gets the fine-grained password policy subject of the password policy named DomainUsersP...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADForest",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADForest",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  15,
                         "usage_category":  "moderate",
                         "command_name":  "Get-ADForest",
                         "rag_document":  "Command: Get-ADForest\nPurpose: Retrieve ADForest information\nPrimary Task: Retrieve Forest information from Active Directory\nCommon Tasks:\n- Find a specific Forest\n- List all Forest objects\n- Search for Forest by criteria\n- Retrieve Forest properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory forest object by providing one of the following attribute values. The ...\nExample Scenarios:\n- This command gets information for the Fabrikam.com forest.\n- This command gets the information for the current local computer\u0027s forest.\n- This command gets the forest information of the currently logged on user.\nCategory: Domain Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Get the Administrators group information",
                                                         "description":  "Retrieve a specific group by SAM account name",
                                                         "powershell_command":  "Get-ADGroup -Identity Administrators",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Show me the Administrators group with all members",
                                                         "description":  "Get group information including member list using SID",
                                                         "powershell_command":  "Get-ADGroup -Identity S-1-5-32-544 -Properties member",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Find all security groups that are not domain local",
                                                         "description":  "Filter groups by category and scope",
                                                         "powershell_command":  "Get-ADGroup -Filter \u0027GroupCategory -eq \"Security\" -and GroupScope -ne \"DomainLocal\"\u0027",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Get all domain local groups from AD LDS",
                                                         "description":  "Search for groups in Active Directory Lightweight Directory Services",
                                                         "powershell_command":  "Get-ADGroup -Server localhost:60000 -Filter \"GroupScope -eq \u0027DomainLocal\u0027\" -SearchBase \"DC=AppNC\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Find all groups with names starting with Sales",
                                                         "description":  "Search for groups using wildcard pattern matching",
                                                         "powershell_command":  "Get-ADGroup -Filter \"Name -like \u0027Sales*\u0027\" -Properties Description",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Get all distribution groups in the organization",
                                                         "description":  "Find all distribution groups",
                                                         "powershell_command":  "Get-ADGroup -Filter \"GroupCategory -eq \u0027Distribution\u0027\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Show me all groups in a specific OU",
                                                         "description":  "Get all groups within a specific organizational unit",
                                                         "powershell_command":  "Get-ADGroup -Filter * -SearchBase \"OU=Groups,DC=contoso,DC=com\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Find empty groups with no members",
                                                         "description":  "Identify groups that have no members",
                                                         "powershell_command":  "Get-ADGroup -Filter * -Properties Members | Where-Object {$_.Members.Count -eq 0}",
                                                         "source":  "Community Examples"
                                                     }
                                                 ],
                         "usage_frequency":  90,
                         "usage_category":  "very_common",
                         "command_name":  "Get-ADGroup",
                         "rag_document":  "Command: Get-ADGroup\nPurpose: Retrieve ADGroup information\nPrimary Task: Retrieve Group information from Active Directory\nCommon Tasks:\n- Find a specific Group\n- List all Group objects\n- Search for Group by criteria\n- Retrieve Group properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\n- Identity (Required): Specifies an Active Directory group object by providing one of the following values. The identifier ...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\nExample Scenarios:\n- This command gets the group with the SAM account name Administrators.\n- This command gets the group with SID S-1-5-32-544 and the property member.\n- This command gets all groups that have a GroupCategory of Security but do not have a GroupScope of D...\nCategory: Group Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Get all members of the Administrators group",
                                                         "description":  "List all members of a specific group",
                                                         "powershell_command":  "Get-ADGroupMember -Identity \"Administrators\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Show members of Domain Admins including nested groups",
                                                         "description":  "Get group members including members from child groups",
                                                         "powershell_command":  "Get-ADGroupMember -Identity \"Domain Admins\" -Recursive",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Find all groups a user belongs to",
                                                         "description":  "List group memberships for a specific user",
                                                         "powershell_command":  "Get-ADUser -Identity \"jdoe\" | Get-ADPrincipalGroupMembership",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Get members of all domain local groups",
                                                         "description":  "List members of domain local groups",
                                                         "powershell_command":  "Get-ADGroup -Filter \"GroupScope -eq \u0027DomainLocal\u0027\" | Get-ADGroupMember",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Export group membership to CSV",
                                                         "description":  "Export group membership information to file",
                                                         "powershell_command":  "Get-ADGroupMember -Identity \"Sales\" | Select-Object Name,SamAccountName,ObjectClass | Export-Csv -Path \"SalesMembers.csv\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Find empty groups with no members",
                                                         "description":  "Identify groups that have no members",
                                                         "powershell_command":  "Get-ADGroup -Filter * | Where-Object {(Get-ADGroupMember -Identity $_.SamAccountName).Count -eq 0}",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Get only user members from a group",
                                                         "description":  "Filter group members to show only user accounts",
                                                         "powershell_command":  "Get-ADGroupMember -Identity \"IT Staff\" | Where-Object {$_.ObjectClass -eq \"user\"}",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Count members in multiple groups",
                                                         "description":  "Get member count for multiple groups",
                                                         "powershell_command":  "Get-ADGroup -Filter \"Name -like \u0027*Admin*\u0027\" | ForEach-Object { [PSCustomObject]@{GroupName=$_.Name; MemberCount=(Get-ADGroupMember -Identity $_.SamAccountName).Count} }",
                                                         "source":  "Community Examples"
                                                     }
                                                 ],
                         "usage_frequency":  80,
                         "usage_category":  "very_common",
                         "command_name":  "Get-ADGroupMember",
                         "rag_document":  "Command: Get-ADGroupMember\nPurpose: Retrieve ADGroupMember information\nPrimary Task: Retrieve GroupMember information from Active Directory\nCommon Tasks:\n- Find a specific GroupMember\n- List all GroupMember objects\n- Search for GroupMember by criteria\n- Retrieve GroupMember properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory group object by providing one of the following values. The identifier ...\nExample Scenarios:\n- This command gets all the members of the Administrators group.\n- This command gets the group members of all domain local groups in the AD LDS instance.\n- This command gets all the group members of the Administrators group.\nCategory: Group Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADObject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADObject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  20,
                         "usage_category":  "moderate",
                         "command_name":  "Get-ADObject",
                         "rag_document":  "Command: Get-ADObject\nPurpose: Retrieve ADObject information\nPrimary Task: Retrieve Object information from Active Directory\nCommon Tasks:\n- Find a specific Object\n- List all Object objects\n- Search for Object by criteria\n- Retrieve Object properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\nExample Scenarios:\n- This command displays a list of sites for Fabrikam using the LDAP filter syntax.\n- This command gets the Site objects from the configuration naming context and displays a list of site...\n- This command gets all the objects, including the deleted ones, whose whenChanged attribute is greate...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADOptionalFeature",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADOptionalFeature",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  3,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADOptionalFeature",
                         "rag_document":  "Command: Get-ADOptionalFeature\nPurpose: Retrieve ADOptionalFeature information\nPrimary Task: Retrieve OptionalFeature information from Active Directory\nCommon Tasks:\n- Find a specific OptionalFeature\n- List all OptionalFeature objects\n- Search for OptionalFeature by criteria\n- Retrieve OptionalFeature properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\n- Identity (Required): Specifies an Active Directory optional feature object by providing one of the following values. The ...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\nExample Scenarios:\n- This command gets all of the available optional features in the current forest.\n- This command gets the optional feature with the name Recycle Bin Feature.\n- This command gets the optional feature with the feature GUID 766ddcd8-acd0-445e-f3b9-a7f9b6744f2a.\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADOrganizationalUnit",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADOrganizationalUnit",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  35,
                         "usage_category":  "common",
                         "command_name":  "Get-ADOrganizationalUnit",
                         "rag_document":  "Command: Get-ADOrganizationalUnit\nPurpose: Retrieve ADOrganizationalUnit information\nPrimary Task: Retrieve OrganizationalUnit information from Active Directory\nCommon Tasks:\n- Find a specific OrganizationalUnit\n- List all OrganizationalUnit objects\n- Search for OrganizationalUnit by criteria\n- Retrieve OrganizationalUnit properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\n- Identity (Required): Specifies an Active Directory organizational unit object by providing one of the following values. T...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\nExample Scenarios:\n- This command gets all of the OUs in a domain.\n- This command gets the OU with the distinguished name OU=AsiaPacific,OU=Sales,OU=UserAccounts,DC=FABR...\n- This command gets OUs underneath the Sales OU using an LDAP filter.\nCategory: Organizational Unit Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADPrincipalGroupMembership",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADPrincipalGroupMembership",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  35,
                         "usage_category":  "common",
                         "command_name":  "Get-ADPrincipalGroupMembership",
                         "rag_document":  "Command: Get-ADPrincipalGroupMembership\nPurpose: Retrieve ADPrincipalGroupMembership information\nPrimary Task: Retrieve PrincipalGroupMembership information from Active Directory\nCommon Tasks:\n- Find a specific PrincipalGroupMembership\n- List all PrincipalGroupMembership objects\n- Search for PrincipalGroupMembership by criteria\n- Retrieve PrincipalGroupMembership properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory principal object by providing one of the following property values. Th...\nExample Scenarios:\n- This command gets all of the group memberships for the user CN=DavidChew,DC=AppNC in an AD LDS insta...\n- This command gets all the group memberships for the Administrator.\n- This command gets all of the group memberships for the Administrator account in the local domain in ...\nCategory: Group Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADReplicationAttributeMetadata",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADReplicationAttributeMetadata",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  4,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADReplicationAttributeMetadata",
                         "rag_document":  "Command: Get-ADReplicationAttributeMetadata\nPurpose: Retrieve ADReplicationAttributeMetadata information\nPrimary Task: Retrieve ReplicationAttributeMetadata information from Active Directory\nCommon Tasks:\n- Find a specific ReplicationAttributeMetadata\n- List all ReplicationAttributeMetadata objects\n- Search for ReplicationAttributeMetadata by criteria\n- Retrieve ReplicationAttributeMetadata properties\nKey Parameters:\n- Server (Required): Specifies the Active Directory Domain Services (AD DS) instance to connect to, by providing one of t...\n- Object (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Properties: Specifies a list of one or more attribute names as a comma separated list to return the metadata for...\n- Filter: Specifies a filter in the provider\u0027s format or language. The value of this parameter qualifies the P...\nExample Scenarios:\n- This command gets the replication metadata for the attributes of a group with distinguished name CN=...\n- This command gets the replication metadata for the attributes of an object with the GUID 1A7BFEC6-C9...\n- This command gets all groups that have any of their attributes modified on 11/10/2011.\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADReplicationConnection",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADReplicationConnection",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  5,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADReplicationConnection",
                         "rag_document":  "Command: Get-ADReplicationConnection\nPurpose: Retrieve ADReplicationConnection information\nPrimary Task: Retrieve ReplicationConnection information from Active Directory\nCommon Tasks:\n- Find a specific ReplicationConnection\n- List all ReplicationConnection objects\n- Search for ReplicationConnection by criteria\n- Retrieve ReplicationConnection properties\nKey Parameters:\n- Filter: Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\nExample Scenarios:\n- This command gets all of the replication connections.\n- This command gets all replication connections that replicate from corp-DC01.\n- This command gets the replication connection with the GUID 5f98e288-19e0-47a0-9677-57f05ed54f6b.\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADReplicationFailure",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADReplicationFailure",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  8,
                         "usage_category":  "uncommon",
                         "command_name":  "Get-ADReplicationFailure",
                         "rag_document":  "Command: Get-ADReplicationFailure\nPurpose: Retrieve ADReplicationFailure information\nPrimary Task: Retrieve ReplicationFailure information from Active Directory\nCommon Tasks:\n- Find a specific ReplicationFailure\n- List all ReplicationFailure objects\n- Search for ReplicationFailure by criteria\n- Retrieve ReplicationFailure properties\nKey Parameters:\n- Filter: Specifies a filter in the provider\u0027s format or language. The value of this parameter qualifies the P...\n- Scope (Required): Specifies the type of object used as input by the Target parameter. The acceptable values for this p...\n- Target (Required): Specifies either one or more (using a comma separated list) of Active Directory domain controllers, ...\nExample Scenarios:\n- This command gets a collection of data that describes an Active Directory replication failure for co...\n- This command gets a collection of data that describes an Active Directory replication failure from c...\n- This command gets a collection of data describing an Active Directory replication failure from corp-...\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADReplicationPartnerMetadata",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADReplicationPartnerMetadata",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  3,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADReplicationPartnerMetadata",
                         "rag_document":  "Command: Get-ADReplicationPartnerMetadata\nPurpose: Retrieve ADReplicationPartnerMetadata information\nPrimary Task: Retrieve ReplicationPartnerMetadata information from Active Directory\nCommon Tasks:\n- Find a specific ReplicationPartnerMetadata\n- List all ReplicationPartnerMetadata objects\n- Search for ReplicationPartnerMetadata by criteria\n- Retrieve ReplicationPartnerMetadata properties\nKey Parameters:\n- Filter: Specifies a filter in the provider\u0027s format or language. The value of this parameter qualifies the P...\n- Scope (Required): Specifies the scope type for the Target parameter when used as input. The acceptable values for this...\n- Target (Required): Specifies the target for returning replication partner metadata as either one or more domain control...\nExample Scenarios:\n- This command gets the replication metadata between corp-DC01 and its inbound partners for the defaul...\n- This command gets the replication metadata between corp-DC01 and its inbound partners for the defaul...\n- This command gets the replication metadata between corp-DC01, corp-DC02 and their respective partner...\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADReplicationQueueOperation",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADReplicationQueueOperation",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADReplicationQueueOperation",
                         "rag_document":  "Command: Get-ADReplicationQueueOperation\nPurpose: Retrieve ADReplicationQueueOperation information\nPrimary Task: Retrieve ReplicationQueueOperation information from Active Directory\nCommon Tasks:\n- Find a specific ReplicationQueueOperation\n- List all ReplicationQueueOperation objects\n- Search for ReplicationQueueOperation by criteria\n- Retrieve ReplicationQueueOperation properties\nKey Parameters:\n- Filter: Specifies a filter in the provider\u0027s format or language. The value of this parameter qualifies the P...\n- Server (Required): Specifies the AD DS instance to connect to, by providing one of the following values for a correspon...\nExample Scenarios:\n- This command gets the pending operations in the replication queue for the domain controller corp-DC0...\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADReplicationSite",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADReplicationSite",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  5,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADReplicationSite",
                         "rag_document":  "Command: Get-ADReplicationSite\nPurpose: Retrieve ADReplicationSite information\nPrimary Task: Retrieve ReplicationSite information from Active Directory\nCommon Tasks:\n- Find a specific ReplicationSite\n- List all ReplicationSite objects\n- Search for ReplicationSite by criteria\n- Retrieve ReplicationSite properties\nKey Parameters:\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\n- Identity: Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\nExample Scenarios:\n- This command gets all Active Directory Replication sites.\n- This command gets all sites that have the WindowsServer2003KCCBehaviorEnabled flag turned on. The Pr...\n- This command gets the site with name NorthAmerica.\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADReplicationSiteLink",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADReplicationSiteLink",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  3,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADReplicationSiteLink",
                         "rag_document":  "Command: Get-ADReplicationSiteLink\nPurpose: Retrieve ADReplicationSiteLink information\nPrimary Task: Retrieve ReplicationSiteLink information from Active Directory\nCommon Tasks:\n- Find a specific ReplicationSiteLink\n- List all ReplicationSiteLink objects\n- Search for ReplicationSiteLink by criteria\n- Retrieve ReplicationSiteLink properties\nKey Parameters:\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\nExample Scenarios:\n- This command gets all the site links.\n- This command gets all site links that include NorthAmerica.\n- This command gets all site links that have a cost greater than 100 and a replication frequency less ...\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADReplicationSiteLinkBridge",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADReplicationSiteLinkBridge",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADReplicationSiteLinkBridge",
                         "rag_document":  "Command: Get-ADReplicationSiteLinkBridge\nPurpose: Retrieve ADReplicationSiteLinkBridge information\nPrimary Task: Retrieve ReplicationSiteLinkBridge information from Active Directory\nCommon Tasks:\n- Find a specific ReplicationSiteLinkBridge\n- List all ReplicationSiteLinkBridge objects\n- Search for ReplicationSiteLinkBridge by criteria\n- Retrieve ReplicationSiteLinkBridge properties\nKey Parameters:\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\nExample Scenarios:\n- This command gets all of the site link bridges.\n- This command gets all site link bridges that include the site link NorthAmerica-Europe.\n- This command gets the site link bridge with the name NorthAmerica-Europe.\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADReplicationUpToDatenessVectorTable",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADReplicationUpToDatenessVectorTable",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADReplicationUpToDatenessVectorTable",
                         "rag_document":  "Command: Get-ADReplicationUpToDatenessVectorTable\nPurpose: Retrieve ADReplicationUpToDatenessVectorTable information\nPrimary Task: Retrieve ReplicationUpToDatenessVectorTable information from Active Directory\nCommon Tasks:\n- Find a specific ReplicationUpToDatenessVectorTable\n- List all ReplicationUpToDatenessVectorTable objects\n- Search for ReplicationUpToDatenessVectorTable by criteria\n- Retrieve ReplicationUpToDatenessVectorTable properties\nKey Parameters:\n- Filter: Specifies a filter in the provider\u0027s format or language. The value of this parameter qualifies the P...\n- Scope (Required): Specifies the type of object used as input by the Target parameter. The acceptable values for this p...\n- Target (Required): Specifies either one or more (using a comma separated list) of Active Directory domain controllers, ...\nExample Scenarios:\n- This command gets the highest USN information for the default partition from corp-DC01.\n- This command gets the highest USN information for the default partition from corp-DC01.\n- This command gets the highest USN information for the schema partition from corp-DC01 and corp-DC02.\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADResourcePropertyValueType",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADResourcePropertyValueType",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADResourcePropertyValueType",
                         "rag_document":  "Command: Get-ADResourcePropertyValueType\nPurpose: Retrieve ADResourcePropertyValueType information\nPrimary Task: Retrieve ResourcePropertyValueType information from Active Directory\nCommon Tasks:\n- Find a specific ResourcePropertyValueType\n- List all ResourcePropertyValueType objects\n- Search for ResourcePropertyValueType by criteria\n- Retrieve ResourcePropertyValueType properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\n- Identity (Required): Specifies an Active Directory user object by providing one of the following property values. The ide...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\nExample Scenarios:\n- This command gets the names of all resource property value types.\n- This command gets all resource property value types that the resource properties Country and Authors...\n- This command gets a resource property value type named MS-DS-Text.\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADRootDSE",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADRootDSE",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  6,
                         "usage_category":  "rare",
                         "command_name":  "Get-ADRootDSE",
                         "rag_document":  "Command: Get-ADRootDSE\nPurpose: Retrieve ADRootDSE information\nPrimary Task: Retrieve RootDSE information from Active Directory\nCommon Tasks:\n- Find a specific RootDSE\n- List all RootDSE objects\n- Search for RootDSE by criteria\n- Retrieve RootDSE properties\nKey Parameters:\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\nExample Scenarios:\n- This command gets the root of the directory server information tree of the directory server from the...\n- This command gets the root of the directory server information tree including the supportedExtension...\n- This command gets the root of the directory server information tree of FABRIKAM-ADLDS1 using the FAB...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADServiceAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADServiceAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  8,
                         "usage_category":  "uncommon",
                         "command_name":  "Get-ADServiceAccount",
                         "rag_document":  "Command: Get-ADServiceAccount\nPurpose: Retrieve ADServiceAccount information\nPrimary Task: Retrieve ServiceAccount information from Active Directory\nCommon Tasks:\n- Find a specific ServiceAccount\n- List all ServiceAccount objects\n- Search for ServiceAccount by criteria\n- Retrieve ServiceAccount properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\nExample Scenarios:\n- This command gets a managed service account with SAM account name service1.\n- This command gets the managed service account with SID S-1-5-21-*********-**********-**********-2977...\n- This command gets the managed service accounts allowed to be used on the computer CN=SQL-Server-1,DC...\nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADTrust",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADTrust",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  10,
                         "usage_category":  "uncommon",
                         "command_name":  "Get-ADTrust",
                         "rag_document":  "Command: Get-ADTrust\nPurpose: Retrieve ADTrust information\nPrimary Task: Retrieve Trust information from Active Directory\nCommon Tasks:\n- Find a specific Trust\n- List all Trust objects\n- Search for Trust by criteria\n- Retrieve Trust properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\nExample Scenarios:\n- This command gets all of the trusted domain objects in the forest.\n- This command gets all the trusted domain objects with corp.contoso.com as the trust partner.\n- This command gets the trusted domain object with name corp.contoso.com.\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Get me the user information with display name as John Doe",
                                                         "description":  "Find a specific user by display name and retrieve all properties",
                                                         "powershell_command":  "Get-ADUser -Filter \"DisplayName -eq \u0027John Doe\u0027\" -Properties *",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Find all users in the Finance department",
                                                         "description":  "Search for users by department attribute",
                                                         "powershell_command":  "Get-ADUser -Filter \"Department -eq \u0027Finance\u0027\" -Properties Department",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Get all users whose email ends with @contoso.com",
                                                         "description":  "Find users by email domain using wildcard matching",
                                                         "powershell_command":  "Get-ADUser -Filter \"EmailAddress -like \u0027*@contoso.com\u0027\" -Properties EmailAddress",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Show me all disabled user accounts",
                                                         "description":  "Find all disabled user accounts in Active Directory",
                                                         "powershell_command":  "Get-ADUser -Filter \"Enabled -eq $false\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Get users who haven\u0027t logged in for 60 days",
                                                         "description":  "Find inactive users based on last logon timestamp",
                                                         "powershell_command":  "$Date = (Get-Date).AddDays(-60); Get-ADUser -Filter \"LastLogonTimeStamp -lt $Date\" -Properties LastLogonTimeStamp",
                                                         "source":  "Easy365Manager Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Find all users in a specific organizational unit",
                                                         "description":  "Search for users within a specific OU container",
                                                         "powershell_command":  "Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Get user accounts with names ending in SvcAccount",
                                                         "description":  "Find service accounts by naming convention",
                                                         "powershell_command":  "Get-ADUser -Filter \"Name -like \u0027*SvcAccount\u0027\" | Format-Table Name,SamAccountName -A",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Show me all properties for user ChewDavid",
                                                         "description":  "Get complete user information including all extended properties",
                                                         "powershell_command":  "Get-ADUser -Identity ChewDavid -Properties *",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Find users in AD LDS instance by name",
                                                         "description":  "Search for users in Active Directory Lightweight Directory Services",
                                                         "powershell_command":  "Get-ADUser -Filter \"Name -eq \u0027ChewDavid\u0027\" -SearchBase \"DC=AppNC\" -Properties \"mail\" -Server lds.Fabrikam.com:50000",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Get all enabled user accounts using LDAP filter",
                                                         "description":  "Use LDAP filter to find enabled accounts",
                                                         "powershell_command":  "Get-ADUser -LDAPFilter \"(!userAccountControl:1.2.840.113556.1.4.803:=2)\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     }
                                                 ],
                         "usage_frequency":  100,
                         "usage_category":  "very_common",
                         "command_name":  "Get-ADUser",
                         "rag_document":  "Command: Get-ADUser\nPurpose: Retrieve ADUser information\nPrimary Task: Retrieve User information from Active Directory\nCommon Tasks:\n- Find a specific User\n- List all User objects\n- Search for User by criteria\n- Retrieve User properties\nKey Parameters:\n- LDAPFilter (Required): Specifies an LDAP query string that is used to filter Active Directory objects. You can use this par...\n- Properties: Specifies the properties of the output object to retrieve from the server. Use this parameter to ret...\n- Identity (Required): Specifies an Active Directory user object by providing one of the following property values. The ide...\n- Filter (Required): Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Ex...\nExample Scenarios:\n- This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.\n- This command gets all users that have a name that ends with SvcAccount.\n- This command gets all of the properties of the user with the SAM account name ChewDavid.\nCategory: User Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Get-ADUserResultantPasswordPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Get-ADUserResultantPasswordPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  10,
                         "usage_category":  "uncommon",
                         "command_name":  "Get-ADUserResultantPasswordPolicy",
                         "rag_document":  "Command: Get-ADUserResultantPasswordPolicy\nPurpose: Retrieve ADUserResultantPasswordPolicy information\nPrimary Task: Retrieve UserResultantPasswordPolicy information from Active Directory\nCommon Tasks:\n- Find a specific UserResultantPasswordPolicy\n- List all UserResultantPasswordPolicy objects\n- Search for UserResultantPasswordPolicy by criteria\n- Retrieve UserResultantPasswordPolicy properties\nKey Parameters:\n- Identity (Required): Specifies an Active Directory user object by providing one of the following property values. The ide...\nExample Scenarios:\n- This command gets the resultant password policy for the user with SAM account name BobKe.\nCategory: User Management Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Grant-ADAuthenticationPolicySiloAccess",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Grant-ADAuthenticationPolicySiloAccess",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Grant-ADAuthenticationPolicySiloAccess",
                         "rag_document":  "Command: Grant-ADAuthenticationPolicySiloAccess\nPurpose: Manage ADAuthenticationPolicySiloAccess\nPrimary Task: Manage ADAuthenticationPolicySiloAccess\nKey Parameters:\n- Identity (Required): Specifies an ADAuthenticationPolicySilo object. Specify the authentication policy silo object in one...\n- Account (Required): Specifies the account to which to grant access to the authentication policy silo. Specify the accoun...\nExample Scenarios:\n- This command grants access to the authentication policy silo named AuthenticationPolicySilo01 to the...\n- This example first uses the Get-ADComputer cmdlet to get a list of computers that match the filter s...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Install-ADServiceAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Install-ADServiceAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  3,
                         "usage_category":  "rare",
                         "command_name":  "Install-ADServiceAccount",
                         "rag_document":  "Command: Install-ADServiceAccount\nPurpose: Manage ADServiceAccount\nPrimary Task: Manage ADServiceAccount\nKey Parameters:\n- Identity (Required): Specifies an Active Directory group object by providing one of the following values. The identifier ...\nExample Scenarios:\n- This command installs a managed service account with name SQL-HR-svc-01 on the local computer. If a ...\n- This command gets a managed service account with name SQL-HR-svc-01 from the default directory and i...\n- This command installs a standalone managed service account identified as SQL-HR-svc-01 in a read-onl...\nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Move-ADDirectoryServer",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Move-ADDirectoryServer",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Move-ADDirectoryServer",
                         "rag_document":  "Command: Move-ADDirectoryServer\nPurpose: Manage ADDirectoryServer\nPrimary Task: Manage ADDirectoryServer\nKey Parameters:\n- Site (Required): Specifies the new site for the directory server. You can identify the site by one of the following p...\n- Identity (Required): Specifies an Active Directory server object by providing one of the following values. The identifier...\nExample Scenarios:\n- This command moves the domain controller USER01-DC2 to the site Branch-Office-Site.\n- This command moves all Read-Only domain controllers to the site RODC-Site-Name.\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Move-ADDirectoryServerOperationMasterRole",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Move-ADDirectoryServerOperationMasterRole",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Move-ADDirectoryServerOperationMasterRole",
                         "rag_document":  "Command: Move-ADDirectoryServerOperationMasterRole\nPurpose: Manage ADDirectoryServerOperationMasterRole\nPrimary Task: Manage ADDirectoryServerOperationMasterRole\nKey Parameters:\n- Identity (Required): Specifies an Active Directory server object by providing one of the following values. The identifier...\nExample Scenarios:\n- This command moves the primary domain controller (PDC) Emulator role to the domain controller USER01...\n- This command moves the PDC Emulator and schema master roles to the domain controller USER02-DC2.\n- This command moves the schema master flexible single master operations (FSMO) owner to the AD LDS in...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Move-ADObject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Move-ADObject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  20,
                         "usage_category":  "moderate",
                         "command_name":  "Move-ADObject",
                         "rag_document":  "Command: Move-ADObject\nPurpose: Manage ADObject\nPrimary Task: Manage ADObject\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command moves the organizational unit (OU) ManagedGroups to a new location. The OU ManagedGroup...\n- This command moves the object identified by the specified GUID to the new location.\n- This command moves an object to a new location. Both the object and the target path are specified us...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADAuthenticationPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADAuthenticationPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "New-ADAuthenticationPolicy",
                         "rag_document":  "Command: New-ADAuthenticationPolicy\nPurpose: Create new ADAuthenticationPolicy\nPrimary Task: Create a new AuthenticationPolicy in Active Directory\nCommon Tasks:\n- Create a new AuthenticationPolicy\n- Provision AuthenticationPolicy with specific properties\n- Add AuthenticationPolicy to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the Active Directory Doma...\nExample Scenarios:\n- This command creates an authentication policy object named AuthenticationPolicy01 and sets the TGT l...\n- This command creates an authentication policy named AuthenticationPolicy02 and enforces it by specif...\n- This command creates an authentication policy named TestAuthenticationPolicy. The UserAllowedToAuthe...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADAuthenticationPolicySilo",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADAuthenticationPolicySilo",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "New-ADAuthenticationPolicySilo",
                         "rag_document":  "Command: New-ADAuthenticationPolicySilo\nPurpose: Create new ADAuthenticationPolicySilo\nPrimary Task: Create a new AuthenticationPolicySilo in Active Directory\nCommon Tasks:\n- Create a new AuthenticationPolicySilo\n- Provision AuthenticationPolicySilo with specific properties\n- Add AuthenticationPolicySilo to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the Active Directory Doma...\nExample Scenarios:\n- This command creates an authentication policy silo object and enforces it.\n- This command creates an authentication policy silo object but does not enforce it.\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADCentralAccessPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADCentralAccessPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "New-ADCentralAccessPolicy",
                         "rag_document":  "Command: New-ADCentralAccessPolicy\nPurpose: Create new ADCentralAccessPolicy\nPrimary Task: Create a new CentralAccessPolicy in Active Directory\nCommon Tasks:\n- Create a new CentralAccessPolicy\n- Provision CentralAccessPolicy with specific properties\n- Add CentralAccessPolicy to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the Active Directory obje...\nExample Scenarios:\n- This command creates a central access rule named Finance Documents Rule with a new resource conditio...\n- This command creates a central access rule named Finance Documents Rule with a new resource conditio...\n- This command creates a central access policy named Human Resources Policy using the property values ...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADCentralAccessRule",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADCentralAccessRule",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "New-ADCentralAccessRule",
                         "rag_document":  "Command: New-ADCentralAccessRule\nPurpose: Create new ADCentralAccessRule\nPrimary Task: Create a new CentralAccessRule in Active Directory\nCommon Tasks:\n- Create a new CentralAccessRule\n- Provision CentralAccessRule with specific properties\n- Add CentralAccessRule to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the Active Directory obje...\nExample Scenarios:\n- This command creates a new central access rule named Finance Documents Rule.\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADClaimTransformPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADClaimTransformPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "New-ADClaimTransformPolicy",
                         "rag_document":  "Command: New-ADClaimTransformPolicy\nPurpose: Create new ADClaimTransformPolicy\nPrimary Task: Create a new ClaimTransformPolicy in Active Directory\nCommon Tasks:\n- Create a new ClaimTransformPolicy\n- Provision ClaimTransformPolicy with specific properties\n- Add ClaimTransformPolicy to Active Directory\nKey Parameters:\n- Rule (Required): Specifies the claims transformation rule. To specify the rule, you can either (1) type the rule in a...\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the Active Directory obje...\nExample Scenarios:\n- This command creates a new claims transformation policy named DenyAllPolicy that denies all claims, ...\n- This command creates a new claims transformation policy named AllowAllExceptCompanyAndDepartmentPoli...\n- This command creates a new claims transformation policy named HumanResourcesToHrPolicy that transfor...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADClaimType",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADClaimType",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "New-ADClaimType",
                         "rag_document":  "Command: New-ADClaimType\nPurpose: Create new ADClaimType\nPrimary Task: Create a new ClaimType in Active Directory\nCommon Tasks:\n- Create a new ClaimType\n- Provision ClaimType with specific properties\n- Add ClaimType to Active Directory\nKey Parameters:\n- SourceOID (Required): Specifies a string that can be used to configure a certificate-based claim type source. For example,...\nExample Scenarios:\n- This command creates a new user claim type with display name Title that is sourced from the Active D...\n- This example creates a new user claim type with display name Employee Type that is sourced from the ...\n- This command creates a new device claim type with display name Bitlocker Enabled with the source OID...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADComputer",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADComputer",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  18,
                         "usage_category":  "moderate",
                         "command_name":  "New-ADComputer",
                         "rag_document":  "Command: New-ADComputer\nPurpose: Create new ADComputer\nPrimary Task: Create a new Computer in Active Directory\nCommon Tasks:\n- Create a new Computer\n- Provision Computer with specific properties\n- Add Computer to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the Active Directory obje...\n- Path: Specifies the X.500 path of the Organizational Unit (OU) or container where the new object is create...\nExample Scenarios:\n- This command creates a new computer account in the OU OU=ApplicationServers,OU=ComputerAccounts,OU=M...\n- This command creates a new computer account under a particular OU, which is enabled and located in R...\n- This example creates a new computer account from a template object.\nCategory: Computer Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADDCCloneConfigFile",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADDCCloneConfigFile",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "New-ADDCCloneConfigFile",
                         "rag_document":  "Command: New-ADDCCloneConfigFile\nPurpose: Create new ADDCCloneConfigFile\nPrimary Task: Create a new DCCloneConfigFile in Active Directory\nCommon Tasks:\n- Create a new DCCloneConfigFile\n- Provision DCCloneConfigFile with specific properties\n- Add DCCloneConfigFile to Active Directory\nKey Parameters:\n- Path: Specifies the folder path to use when writing the clone configuration file. If the cmdlet is run and...\n- Offline (Required): Indicates whether the cmdlet is being run against an offline media or on the domain controller being...\nExample Scenarios:\n- This command creates a clone domain controller named VirtualDC2 with a static IPv4 address.\n- This command creates a clone domain controller named Clone1 with a static IPv6 setting.\n- This command creates a clone domain controller named Clone2 with dynamic IPv4 settings.\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADFineGrainedPasswordPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADFineGrainedPasswordPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  5,
                         "usage_category":  "rare",
                         "command_name":  "New-ADFineGrainedPasswordPolicy",
                         "rag_document":  "Command: New-ADFineGrainedPasswordPolicy\nPurpose: Create new ADFineGrainedPasswordPolicy\nPrimary Task: Create a new FineGrainedPasswordPolicy in Active Directory\nCommon Tasks:\n- Create a new FineGrainedPasswordPolicy\n- Provision FineGrainedPasswordPolicy with specific properties\n- Add FineGrainedPasswordPolicy to Active Directory\nKey Parameters:\n- Precedence (Required): Specifies a value that defines the precedence of a fine-grained password policy among all fine-grain...\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the Active Directory obje...\nExample Scenarios:\n- This command creates a fine-grained password policy object named DomainUsersPSO and set the Preceden...\n- This example creates two new fine-grained password policy objects using a template object.\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADGroup",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADGroup",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  25,
                         "usage_category":  "moderate",
                         "command_name":  "New-ADGroup",
                         "rag_document":  "Command: New-ADGroup\nPurpose: Create new ADGroup\nPrimary Task: Create a new Group in Active Directory\nCommon Tasks:\n- Create a new Group\n- Provision Group with specific properties\n- Add Group to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the Active Directory obje...\n- Path: Specifies the X.500 path of the Organizational Unit (OU) or container where the new object is create...\nExample Scenarios:\n- This command creates a group named RODC Admins in the container CN=Users,DC=Fabrikam,DC=Com and set ...\n- This command creates a new group using the property values from a current group.\n- This command creates a group named AccountLeads on an AD LDS instance.\nCategory: Group Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADObject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADObject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  5,
                         "usage_category":  "uncommon",
                         "command_name":  "New-ADObject",
                         "rag_document":  "Command: New-ADObject\nPurpose: Create new ADObject\nPrimary Task: Create a new Object in Active Directory\nCommon Tasks:\n- Create a new Object\n- Provision Object with specific properties\n- Add Object to Active Directory\nKey Parameters:\n- Type (Required): Specifies the type of object to create. Set the Type parameter to the LDAP display name of the Activ...\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the Active Directory obje...\n- Path: Specifies the X.500 path of the OU or container where the new object is created.\nExample Scenarios:\n- This command creates a subnet object in the HQ site with the described attributes.\n- This example creates a new subnet object, using a different subnet object as a template.\n- This command creates a new contact object, sets the msDS-SourceObjectDN property and protects the ob...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADOrganizationalUnit",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADOrganizationalUnit",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  15,
                         "usage_category":  "moderate",
                         "command_name":  "New-ADOrganizationalUnit",
                         "rag_document":  "Command: New-ADOrganizationalUnit\nPurpose: Create new ADOrganizationalUnit\nPrimary Task: Create a new OrganizationalUnit in Active Directory\nCommon Tasks:\n- Create a new OrganizationalUnit\n- Provision OrganizationalUnit with specific properties\n- Add OrganizationalUnit to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the OU object. The LDAP d...\n- Path: Specifies the X.500 path of the OU or container where the new object is created.\nExample Scenarios:\n- This command creates an OU named UserAccounts that is protected from accidental deletion. Note that ...\n- This command creates an OU named UserAccounts that is not protected from accidental deletion.\n- This command creates an OU named UserAccounts that is protected from accidental deletion. The seeAls...\nCategory: Organizational Unit Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADReplicationSite",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADReplicationSite",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "New-ADReplicationSite",
                         "rag_document":  "Command: New-ADReplicationSite\nPurpose: Create new ADReplicationSite\nPrimary Task: Create a new ReplicationSite in Active Directory\nCommon Tasks:\n- Create a new ReplicationSite\n- Provision ReplicationSite with specific properties\n- Add ReplicationSite to Active Directory\nKey Parameters:\n- Name (Required): Specifies a name for the replication site object.\nExample Scenarios:\n- This command creates a new site named NorthAmerica.\n- This command creates a new site named Europe, and sets the AutomaticInterSiteTopologyGenerationEnabl...\n- This example creates a new site named Asia, and sets the daily ReplicationSchedule from 20:00 to 22:...\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADReplicationSiteLink",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADReplicationSiteLink",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "New-ADReplicationSiteLink",
                         "rag_document":  "Command: New-ADReplicationSiteLink\nPurpose: Create new ADReplicationSiteLink\nPrimary Task: Create a new ReplicationSiteLink in Active Directory\nCommon Tasks:\n- Create a new ReplicationSiteLink\n- Provision ReplicationSiteLink with specific properties\n- Add ReplicationSiteLink to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the site link. This parameter sets the Name property of the Active Directory o...\nExample Scenarios:\n- This command creates a new site link named NorthAmerica-Europe linking the two sites NorthAmerica an...\n- This command creates a new site link named Europe-Asia linking two sites Europe and Asia, and set th...\n- This example creates a new site link named NorthAmerica-SouthAmerica linking two sites NorthAmerica ...\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADReplicationSiteLinkBridge",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADReplicationSiteLinkBridge",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "New-ADReplicationSiteLinkBridge",
                         "rag_document":  "Command: New-ADReplicationSiteLinkBridge\nPurpose: Create new ADReplicationSiteLinkBridge\nPrimary Task: Create a new ReplicationSiteLinkBridge in Active Directory\nCommon Tasks:\n- Create a new ReplicationSiteLinkBridge\n- Provision ReplicationSiteLinkBridge with specific properties\n- Add ReplicationSiteLinkBridge to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the replication site link bridge object.\nExample Scenarios:\n- This command creates a site link bridge named NorthAmerica-Asia that bridges the site links NorthAme...\n- This command creates a site link bridge named NorthAmerica-Asia that bridges the site links NorthAme...\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of New-ADServiceAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "New-ADServiceAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  4,
                         "usage_category":  "rare",
                         "command_name":  "New-ADServiceAccount",
                         "rag_document":  "Command: New-ADServiceAccount\nPurpose: Create new ADServiceAccount\nPrimary Task: Create a new ServiceAccount in Active Directory\nCommon Tasks:\n- Create a new ServiceAccount\n- Provision ServiceAccount with specific properties\n- Add ServiceAccount to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of the Active Directory obje...\n- Path: Specifies the X.500 path of the organizational unit (OU) or container where the new object is create...\nExample Scenarios:\n- This command creates an enabled managed service account in Active Directory Domain Services (AD DS).\n- This command creates a managed service account and registers its service principal name.\n- This command creates a managed service account and restricts its use to a single computer.\nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Create a new user with basic information",
                                                         "description":  "Create a new user account with essential properties",
                                                         "powershell_command":  "New-ADUser -Name \"John Doe\" -SamAccountName \"jdoe\" -UserPrincipalName \"<EMAIL>\" -Path \"OU=Users,DC=contoso,DC=com\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Create user with password and enable account",
                                                         "description":  "Create enabled user account with initial password",
                                                         "powershell_command":  "$SecurePassword = ConvertTo-SecureString \"P@ssw0rd123\" -AsPlainText -Force; New-ADUser -Name \"Jane Smith\" -SamAccountName \"jsmith\" -AccountPassword $SecurePassword -Enabled $true",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Create user with detailed contact information",
                                                         "description":  "Create user with comprehensive profile information",
                                                         "powershell_command":  "New-ADUser -Name \"Bob Johnson\" -SamAccountName \"bjohnson\" -GivenName \"Bob\" -Surname \"Johnson\" -EmailAddress \"<EMAIL>\" -Department \"Sales\" -Title \"Sales Manager\" -Office \"Building A\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Create service account",
                                                         "description":  "Create a service account with appropriate settings",
                                                         "powershell_command":  "New-ADUser -Name \"SQL Service Account\" -SamAccountName \"svc-sql\" -Description \"Service account for SQL Server\" -PasswordNeverExpires $true -CannotChangePassword $true",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Create user from CSV import",
                                                         "description":  "Bulk create users from CSV file",
                                                         "powershell_command":  "Import-Csv \"NewUsers.csv\" | ForEach-Object { New-ADUser -Name $_.Name -SamAccountName $_.SamAccountName -Department $_.Department -Title $_.Title }",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Create user with home directory",
                                                         "description":  "Create user with home directory and profile path",
                                                         "powershell_command":  "New-ADUser -Name \"Alice Brown\" -SamAccountName \"abrown\" -HomeDirectory \"\\\\server\\users\\abrown\" -HomeDrive \"H:\" -ProfilePath \"\\\\server\\profiles\\abrown\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Create user with group membership",
                                                         "description":  "Create user and add to specific groups",
                                                         "powershell_command":  "New-ADUser -Name \"Mike Wilson\" -SamAccountName \"mwilson\" -Path \"OU=IT,DC=contoso,DC=com\"; Add-ADGroupMember -Identity \"IT Staff\" -Members \"mwilson\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Create user with certificate",
                                                         "description":  "Create user account with certificate for authentication",
                                                         "powershell_command":  "$Cert = Get-Content \"user.cer\" -Encoding Byte; New-ADUser -Name \"David Lee\" -SamAccountName \"dlee\" -Certificates $Cert",
                                                         "source":  "Microsoft Learn Documentation"
                                                     }
                                                 ],
                         "usage_frequency":  65,
                         "usage_category":  "very_common",
                         "command_name":  "New-ADUser",
                         "rag_document":  "Command: New-ADUser\nPurpose: Create new ADUser\nPrimary Task: Create a new User in Active Directory\nCommon Tasks:\n- Create a new User\n- Provision User with specific properties\n- Add User to Active Directory\nKey Parameters:\n- Name (Required): Specifies the name of the object. This parameter sets the Name property of a user object. The LDAP d...\n- Path: Specifies the X.500 path of the OU or container where the new object is created. In many cases, a de...\nExample Scenarios:\n- This command creates a user named ChewDavid with a certificate imported from the file Export.cer.\n- This command creates a new user named ChewDavid and sets the title and mail properties on the new ob...\n- This command creates a new user named ChewDavid and sets the account password.\nCategory: User Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADAuthenticationPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADAuthenticationPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADAuthenticationPolicy",
                         "rag_document":  "Command: Remove-ADAuthenticationPolicy\nPurpose: Delete ADAuthenticationPolicy\nPrimary Task: Delete a AuthenticationPolicy from Active Directory\nCommon Tasks:\n- Delete a AuthenticationPolicy\n- Remove AuthenticationPolicy from Active Directory\n- Clean up AuthenticationPolicy objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory Domain Services authentication policy object. Specify the authenticati...\nExample Scenarios:\n- This command removes the authentication policy specified by the Identity parameter.\n- This command uses the Get-ADAuthenticationPolicy cmdlet with the Filter parameter to get all authent...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADAuthenticationPolicySilo",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADAuthenticationPolicySilo",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADAuthenticationPolicySilo",
                         "rag_document":  "Command: Remove-ADAuthenticationPolicySilo\nPurpose: Delete ADAuthenticationPolicySilo\nPrimary Task: Delete a AuthenticationPolicySilo from Active Directory\nCommon Tasks:\n- Delete a AuthenticationPolicySilo\n- Remove AuthenticationPolicySilo from Active Directory\n- Clean up AuthenticationPolicySilo objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory Domain Services authentication policy silo object. Specify the authent...\nExample Scenarios:\n- This command removes the authentication policy silo object named AuthenticationPolicySilo01.\n- This command uses the Get-ADAuthenticationPolicySilo cmdlet with the Filter parameter to get all aut...\n- This command uses the Get-ADAuthenticationPolicySilo cmdlet with the Filter parameter to get all aut...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADCentralAccessPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADCentralAccessPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADCentralAccessPolicy",
                         "rag_document":  "Command: Remove-ADCentralAccessPolicy\nPurpose: Delete ADCentralAccessPolicy\nPrimary Task: Delete a CentralAccessPolicy from Active Directory\nCommon Tasks:\n- Delete a CentralAccessPolicy\n- Remove CentralAccessPolicy from Active Directory\n- Clean up CentralAccessPolicy objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command removes the central access policy named Finance Policy.\n- This command gets all resource property lists whose name starts with Finance and then remove them.\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADCentralAccessPolicyMember",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADCentralAccessPolicyMember",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADCentralAccessPolicyMember",
                         "rag_document":  "Command: Remove-ADCentralAccessPolicyMember\nPurpose: Delete ADCentralAccessPolicyMember\nPrimary Task: Delete a CentralAccessPolicyMember from Active Directory\nCommon Tasks:\n- Delete a CentralAccessPolicyMember\n- Remove CentralAccessPolicyMember from Active Directory\n- Clean up CentralAccessPolicyMember objects\nKey Parameters:\n- Members (Required): Specifies a set of central access rule (CAR) objects in a comma-separated list to add to a central a...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command removes the resource property named Finance Documents Rule from the central access poli...\n- This command removes the central access rules named Finance Documents Rule and Corporate Documents R...\n- This command gets the central access policies that begin with Corporate in its name, and then pipes ...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADCentralAccessRule",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADCentralAccessRule",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADCentralAccessRule",
                         "rag_document":  "Command: Remove-ADCentralAccessRule\nPurpose: Delete ADCentralAccessRule\nPrimary Task: Delete a CentralAccessRule from Active Directory\nCommon Tasks:\n- Delete a CentralAccessRule\n- Remove CentralAccessRule from Active Directory\n- Clean up CentralAccessRule objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command removes the specified central access rule, Finance Documents Rule.\n- This command removes the central access rules with Department in their resource conditions.\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADClaimTransformPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADClaimTransformPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADClaimTransformPolicy",
                         "rag_document":  "Command: Remove-ADClaimTransformPolicy\nPurpose: Delete ADClaimTransformPolicy\nPrimary Task: Delete a ClaimTransformPolicy from Active Directory\nCommon Tasks:\n- Delete a ClaimTransformPolicy\n- Remove ClaimTransformPolicy from Active Directory\n- Clean up ClaimTransformPolicy objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command removes the claims transformation policy with the name DenyAllPolicy.\n- This command gets all claims transformation policies that were marked in their description as for te...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADClaimType",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADClaimType",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADClaimType",
                         "rag_document":  "Command: Remove-ADClaimType\nPurpose: Delete ADClaimType\nPrimary Task: Delete a ClaimType from Active Directory\nCommon Tasks:\n- Delete a ClaimType\n- Remove ClaimType from Active Directory\n- Clean up ClaimType objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command removes the claim type with the name Title.\n- This command gets all the disabled claim types and remove them.\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADComputer",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADComputer",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  10,
                         "usage_category":  "uncommon",
                         "command_name":  "Remove-ADComputer",
                         "rag_document":  "Command: Remove-ADComputer\nPurpose: Delete ADComputer\nPrimary Task: Delete a Computer from Active Directory\nCommon Tasks:\n- Delete a Computer\n- Remove Computer from Active Directory\n- Clean up Computer objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory computer object by providing one of the following property values. The...\nExample Scenarios:\n- This command removes a specified computer from Active Directory.\n- This command removes all computers in the location specified by using the Filter parameter.\n- This command removes all computers from the location specified by using the Filter parameter. The co...\nCategory: Computer Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADComputerServiceAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADComputerServiceAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADComputerServiceAccount",
                         "rag_document":  "Command: Remove-ADComputerServiceAccount\nPurpose: Delete ADComputerServiceAccount\nPrimary Task: Delete a ComputerServiceAccount from Active Directory\nCommon Tasks:\n- Delete a ComputerServiceAccount\n- Remove ComputerServiceAccount from Active Directory\n- Clean up ComputerServiceAccount objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory computer object by providing one of the following property values. The...\nExample Scenarios:\n- This command removes a service account SvcAcct1 from a Computer Account ComputerAcct1.\n- This command removes service accounts SvcAcct1 and SvcAcct2 from a Computer Account ComputerAcct1.\nCategory: Computer Management Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADDomainControllerPasswordReplicationPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADDomainControllerPasswordReplicationPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADDomainControllerPasswordReplicationPolicy",
                         "rag_document":  "Command: Remove-ADDomainControllerPasswordReplicationPolicy\nPurpose: Delete ADDomainControllerPasswordReplicationPolicy\nPrimary Task: Delete a DomainControllerPasswordReplicationPolicy from Active Directory\nCommon Tasks:\n- Delete a DomainControllerPasswordReplicationPolicy\n- Remove DomainControllerPasswordReplicationPolicy from Active Directory\n- Clean up DomainControllerPasswordReplicationPolicy objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory domain controller object by providing one of the following values. The...\nExample Scenarios:\n- This command removes the users with samAccountNames PattiFuller and DavidChew from the Allowed list ...\n- This command removes the users with samAccountNames Elisa Daugherty and Evan Narvaez from the Denied...\nCategory: Domain Management Replication Management Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADFineGrainedPasswordPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADFineGrainedPasswordPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADFineGrainedPasswordPolicy",
                         "rag_document":  "Command: Remove-ADFineGrainedPasswordPolicy\nPurpose: Delete ADFineGrainedPasswordPolicy\nPrimary Task: Delete a FineGrainedPasswordPolicy from Active Directory\nCommon Tasks:\n- Delete a FineGrainedPasswordPolicy\n- Remove FineGrainedPasswordPolicy from Active Directory\n- Clean up FineGrainedPasswordPolicy objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory fine-grained password policy object by providing one of the following ...\nExample Scenarios:\n- This command removes the fine-grained password policy object named MyPolicy.\n- This command removes the fine-grained password policy object with DistinguishedName CN=MyPolicy,CN=P...\n- This command removes all fine-grained password policy objects that contain user in their names.\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADFineGrainedPasswordPolicySubject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADFineGrainedPasswordPolicySubject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  3,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADFineGrainedPasswordPolicySubject",
                         "rag_document":  "Command: Remove-ADFineGrainedPasswordPolicySubject\nPurpose: Delete ADFineGrainedPasswordPolicySubject\nPrimary Task: Delete a FineGrainedPasswordPolicySubject from Active Directory\nCommon Tasks:\n- Delete a FineGrainedPasswordPolicySubject\n- Remove FineGrainedPasswordPolicySubject from Active Directory\n- Clean up FineGrainedPasswordPolicySubject objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory fine-grained password policy object by providing one of the following ...\n- Subjects (Required): Specifies one or more users or groups. To specify more than one user or group, use a comma-separated...\nExample Scenarios:\n- This command removes the fine-grained password policy subject named DlgtdAdminsPSO from the users wi...\n- This command removes any subjects that have names ending with Price from the name list on which the ...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADGroup",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADGroup",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  12,
                         "usage_category":  "uncommon",
                         "command_name":  "Remove-ADGroup",
                         "rag_document":  "Command: Remove-ADGroup\nPurpose: Delete ADGroup\nPrimary Task: Delete a Group from Active Directory\nCommon Tasks:\n- Delete a Group\n- Remove Group from Active Directory\n- Clean up Group objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory group object by providing one of the following values. The identifier ...\nExample Scenarios:\n- This command removes the group that has SAM account name SanjaysReports.\n- This command gets all groups whose name starts with Sanjay and then removes them.\nCategory: Group Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Remove user from a group",
                                                         "description":  "Remove a single user from a group",
                                                         "powershell_command":  "Remove-ADGroupMember -Identity \"Sales\" -Members \"jdoe\" -Confirm:$false",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Remove multiple users from group",
                                                         "description":  "Remove multiple users from a group at once",
                                                         "powershell_command":  "Remove-ADGroupMember -Identity \"Administrators\" -Members \"user1\",\"user2\" -Confirm:$false",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Remove all users from Finance department from a group",
                                                         "description":  "Bulk remove users based on department",
                                                         "powershell_command":  "Get-ADUser -Filter \"Department -eq \u0027Finance\u0027\" | Remove-ADGroupMember -Identity \"TempProject\" -Members {$_.SamAccountName} -Confirm:$false",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Remove user using distinguished name",
                                                         "description":  "Remove user using distinguished names",
                                                         "powershell_command":  "Remove-ADGroupMember -Identity \"CN=Sales,OU=Groups,DC=contoso,DC=com\" -Members \"CN=John Doe,OU=Users,DC=contoso,DC=com\" -Confirm:$false",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Remove computer from group",
                                                         "description":  "Remove computer account from security group",
                                                         "powershell_command":  "Remove-ADGroupMember -Identity \"ComputerGroup\" -Members \"COMPUTER01$\" -Confirm:$false",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Remove nested group from parent group",
                                                         "description":  "Remove group nesting relationship",
                                                         "powershell_command":  "Remove-ADGroupMember -Identity \"AllStaff\" -Members \"TempWorkers\" -Confirm:$false",
                                                         "source":  "Community Examples"
                                                     }
                                                 ],
                         "usage_frequency":  70,
                         "usage_category":  "very_common",
                         "command_name":  "Remove-ADGroupMember",
                         "rag_document":  "Command: Remove-ADGroupMember\nPurpose: Delete ADGroupMember\nPrimary Task: Delete a GroupMember from Active Directory\nCommon Tasks:\n- Delete a GroupMember\n- Remove GroupMember from Active Directory\n- Clean up GroupMember objects\nKey Parameters:\n- Members (Required): Specifies an array of user, group, and computer objects in a comma-separated list to remove from a g...\n- Identity (Required): Specifies an Active Directory group object by providing one of the following values. The identifier ...\nExample Scenarios:\n- This command removes the user with the SAM account name DavidChew from the group DocumentReaders.\n- This command removes the users with SAM account name administrator and DavidChew from the group Docu...\n- This command removes the user with the distinguished name CN=GlenJohn,DC=AppNC from the group Access...\nCategory: Group Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADObject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADObject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  8,
                         "usage_category":  "uncommon",
                         "command_name":  "Remove-ADObject",
                         "rag_document":  "Command: Remove-ADObject\nPurpose: Delete ADObject\nPrimary Task: Delete a Object from Active Directory\nCommon Tasks:\n- Delete a Object\n- Remove Object from Active Directory\n- Clean up Object objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command removes the object identified by the distinguished name CN=AmyAl-LPTOP,CN=Computers,DC=...\n- This command deletes the container with the distinguished name OU=Finance,OU=UserAccounts,DC=FABRIKA...\n- This command removes the object with the GUID 65511e76-ea80-45e1-bc93-08a78d8c4853 without prompting...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADOrganizationalUnit",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADOrganizationalUnit",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  5,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADOrganizationalUnit",
                         "rag_document":  "Command: Remove-ADOrganizationalUnit\nPurpose: Delete ADOrganizationalUnit\nPrimary Task: Delete a OrganizationalUnit from Active Directory\nCommon Tasks:\n- Delete a OrganizationalUnit\n- Remove OrganizationalUnit from Active Directory\n- Clean up OrganizationalUnit objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory group object by providing one of the following values. The identifier ...\nExample Scenarios:\n- This command removes an OU and all of its children. If the OU is protected from deletion, then the O...\n- This command removes an OU that is specified by its objectGUID and suppresses the confirmation promp...\n- This command removes the Accounting OU.\nCategory: Organizational Unit Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADPrincipalGroupMembership",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADPrincipalGroupMembership",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  12,
                         "usage_category":  "uncommon",
                         "command_name":  "Remove-ADPrincipalGroupMembership",
                         "rag_document":  "Command: Remove-ADPrincipalGroupMembership\nPurpose: Delete ADPrincipalGroupMembership\nPrimary Task: Delete a PrincipalGroupMembership from Active Directory\nCommon Tasks:\n- Delete a PrincipalGroupMembership\n- Remove PrincipalGroupMembership from Active Directory\n- Clean up PrincipalGroupMembership objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory principal object by providing one of the following property values. Th...\nExample Scenarios:\n- This command removes the user David Chew from the Administrators group.\n- This command retrieves the user with the distinguished name CN=DavidChew,DC=AppNC and removes it fro...\nCategory: Group Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADReplicationSite",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADReplicationSite",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADReplicationSite",
                         "rag_document":  "Command: Remove-ADReplicationSite\nPurpose: Delete ADReplicationSite\nPrimary Task: Delete a ReplicationSite from Active Directory\nCommon Tasks:\n- Delete a ReplicationSite\n- Remove ReplicationSite from Active Directory\n- Clean up ReplicationSite objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command removes the site with name Europe.\n- This command gets the sites that are for testing only and removes them.\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADReplicationSiteLink",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADReplicationSiteLink",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADReplicationSiteLink",
                         "rag_document":  "Command: Remove-ADReplicationSiteLink\nPurpose: Delete ADReplicationSiteLink\nPrimary Task: Delete a ReplicationSiteLink from Active Directory\nCommon Tasks:\n- Delete a ReplicationSiteLink\n- Remove ReplicationSiteLink from Active Directory\n- Clean up ReplicationSiteLink objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command removes the site link with the name Europe-Asia.\n- This command gets the site links that include NorthAmerica and removes them.\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADReplicationSiteLinkBridge",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADReplicationSiteLinkBridge",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADReplicationSiteLinkBridge",
                         "rag_document":  "Command: Remove-ADReplicationSiteLinkBridge\nPurpose: Delete ADReplicationSiteLinkBridge\nPrimary Task: Delete a ReplicationSiteLinkBridge from Active Directory\nCommon Tasks:\n- Delete a ReplicationSiteLinkBridge\n- Remove ReplicationSiteLinkBridge from Active Directory\n- Clean up ReplicationSiteLinkBridge objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command removes the site link bridge named NorthAmerica-Asia.\n- This command gets the site link bridges that include Europe-Asia and removes them.\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADResourcePropertyListMember",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADResourcePropertyListMember",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADResourcePropertyListMember",
                         "rag_document":  "Command: Remove-ADResourcePropertyListMember\nPurpose: Delete ADResourcePropertyListMember\nPrimary Task: Delete a ResourcePropertyListMember from Active Directory\nCommon Tasks:\n- Delete a ResourcePropertyListMember\n- Remove ResourcePropertyListMember from Active Directory\n- Clean up ResourcePropertyListMember objects\nKey Parameters:\n- Members (Required): Specifies an array of ADResourceProperty objects in a comma-separated list to add to a resource prop...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command removes the resource property specified as a list member, Country, from the specified r...\n- This command removes the resource properties named Department and Country from the resource property...\n- This command gets the resource property lists that have a name that begins with Corporate and then p...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADServiceAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADServiceAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Remove-ADServiceAccount",
                         "rag_document":  "Command: Remove-ADServiceAccount\nPurpose: Delete ADServiceAccount\nPrimary Task: Delete a ServiceAccount from Active Directory\nCommon Tasks:\n- Delete a ServiceAccount\n- Remove ServiceAccount from Active Directory\n- Clean up ServiceAccount objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command removes the managed service account identified as SQL-SRV1.\n- This command removes all managed service accounts whose names start with SQL.\nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Remove-ADUser",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Remove-ADUser",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  15,
                         "usage_category":  "moderate",
                         "command_name":  "Remove-ADUser",
                         "rag_document":  "Command: Remove-ADUser\nPurpose: Delete ADUser\nPrimary Task: Delete a User from Active Directory\nCommon Tasks:\n- Delete a User\n- Remove User from Active Directory\n- Clean up User objects\nKey Parameters:\n- Identity (Required): Specifies an Active Directory user object by providing one of the following property values. The ide...\nExample Scenarios:\n- This command removes the user with SAM account name GlenJohn.\n- This command searches for any users that have disabled accounts and removes them.\n- This command removes the user with the distinguished name CN=Glen John,OU=Finance,OU=UserAccounts,DC...\nCategory: User Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Rename-ADObject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Rename-ADObject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  6,
                         "usage_category":  "rare",
                         "command_name":  "Rename-ADObject",
                         "rag_document":  "Command: Rename-ADObject\nPurpose: Manage ADObject\nPrimary Task: Manage ADObject\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command renames the name of an existing site HQ to the new name UnitedKingdomHQ. If the disting...\n- This command renames the object with the GUID 4777c8e8-cd29-4699-91e8-c507705a0966 to SiteNewName. T...\n- This command renames the object with the distinguished name OU=ManagedGroups,OU=Managed,DC=Fabrikam,...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Reset-ADServiceAccountMigration",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Reset-ADServiceAccountMigration",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Reset-ADServiceAccountMigration",
                         "rag_document":  "Command: Reset-ADServiceAccountMigration\nPurpose: Manage ADServiceAccountMigration\nPrimary Task: Manage ADServiceAccountMigration\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- \n- \nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Reset-ADServiceAccountPassword",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Reset-ADServiceAccountPassword",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Reset-ADServiceAccountPassword",
                         "rag_document":  "Command: Reset-ADServiceAccountPassword\nPurpose: Manage ADServiceAccountPassword\nPrimary Task: Manage ADServiceAccountPassword\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command resets the password on the standalone managed service account ServiceAccount1.\nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Restore-ADObject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Restore-ADObject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  8,
                         "usage_category":  "uncommon",
                         "command_name":  "Restore-ADObject",
                         "rag_document":  "Command: Restore-ADObject\nPurpose: Manage ADObject\nPrimary Task: Manage ADObject\nKey Parameters:\n- Identity (Required): Specifies an Active Directory group object by providing one of the following values. The identifier ...\nExample Scenarios:\n- This command restores the ADObject while setting the msDS-LastKnownRDN attribute of the deleted obje...\n- This command restores the ADObject while setting the msDS-LastKnownRDN attribute of the deleted obje...\n- This command finds a deleted user whose SAM account name is pattifuller and restores it.\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Revoke-ADAuthenticationPolicySiloAccess",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Revoke-ADAuthenticationPolicySiloAccess",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Revoke-ADAuthenticationPolicySiloAccess",
                         "rag_document":  "Command: Revoke-ADAuthenticationPolicySiloAccess\nPurpose: Manage ADAuthenticationPolicySiloAccess\nPrimary Task: Manage ADAuthenticationPolicySiloAccess\nKey Parameters:\n- Identity (Required): Specifies an ADAuthenticationPolicySilo object. Specify the authentication policy silo object in one...\n- Account (Required): Specifies the account to remove from the authentication policy silo. Specify the account in one of t...\nExample Scenarios:\n- This command revokes access to the authentication policy silo named AuthenticationPolicySilo01 for t...\n- This command first uses the Get-ADComputer cmdlet to get a list of computers that match the filter s...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Search-ADAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Search-ADAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  40,
                         "usage_category":  "common",
                         "command_name":  "Search-ADAccount",
                         "rag_document":  "Command: Search-ADAccount\nPurpose: Manage ADAccount\nPrimary Task: Manage ADAccount\nKey Parameters:\nExample Scenarios:\n- This command returns all users, computers, and service accounts that are disabled.\n- This command returns all users that are disabled.\n- This command returns all users, computers, and service accounts that are expired.\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADAccountAuthenticationPolicySilo",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADAccountAuthenticationPolicySilo",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADAccountAuthenticationPolicySilo",
                         "rag_document":  "Command: Set-ADAccountAuthenticationPolicySilo\nPurpose: Modify ADAccountAuthenticationPolicySilo properties\nPrimary Task: Modify AccountAuthenticationPolicySilo properties in Active Directory\nCommon Tasks:\n- Modify AccountAuthenticationPolicySilo properties\n- Update AccountAuthenticationPolicySilo configuration\n- Change AccountAuthenticationPolicySilo attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory Domain Services object. Specify the Active Directory Domain Services o...\nExample Scenarios:\n- This example assigns the authentication policy silo named AuthenticationPolicySilo01 and the authent...\n- This example first uses the Get-ADComputer cmdlet to get all computer accounts that match the filter...\nCategory: Policy Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADAccountControl",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADAccountControl",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  6,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADAccountControl",
                         "rag_document":  "Command: Set-ADAccountControl\nPurpose: Modify ADAccountControl properties\nPrimary Task: Modify AccountControl properties in Active Directory\nCommon Tasks:\n- Modify AccountControl properties\n- Update AccountControl configuration\n- Change AccountControl attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command sets the flag on userAccountControl to make sure that a password is required for logon.\n- This command sets the security descriptor of the user to make sure they cannot change their own pass...\n- This command sets the flag on userAccountControl to make sure that the account cannot be delegated.\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADAccountExpiration",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADAccountExpiration",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  8,
                         "usage_category":  "uncommon",
                         "command_name":  "Set-ADAccountExpiration",
                         "rag_document":  "Command: Set-ADAccountExpiration\nPurpose: Modify ADAccountExpiration properties\nPrimary Task: Modify AccountExpiration properties in Active Directory\nCommon Tasks:\n- Modify AccountExpiration properties\n- Update AccountExpiration configuration\n- Change AccountExpiration attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command sets the account with SamAccountName PattiFu to expire on the 18th of October, 2008.\n- This command sets the expiration date of all the user accounts who are a member of the group BO1Acco...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADAccountPassword",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADAccountPassword",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  45,
                         "usage_category":  "common",
                         "command_name":  "Set-ADAccountPassword",
                         "rag_document":  "Command: Set-ADAccountPassword\nPurpose: Modify ADAccountPassword properties\nPrimary Task: Modify AccountPassword properties in Active Directory\nCommon Tasks:\n- Modify AccountPassword properties\n- Update AccountPassword configuration\n- Change AccountPassword attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory user object by providing one of the following property values. The ide...\nExample Scenarios:\n- This command sets the password of the user account with DistinguishedName CN=Elisa Daugherty,OU=Acco...\n- This command sets the password of the user account with SamAccountName elisada to qwert@12345. Using...\n- This command sets the password of the user account with DistinguishedName CN=Evan Narvaez,CN=Users,D...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADAuthenticationPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADAuthenticationPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADAuthenticationPolicy",
                         "rag_document":  "Command: Set-ADAuthenticationPolicy\nPurpose: Modify ADAuthenticationPolicy properties\nPrimary Task: Modify AuthenticationPolicy properties in Active Directory\nCommon Tasks:\n- Modify AuthenticationPolicy properties\n- Update AuthenticationPolicy configuration\n- Change AuthenticationPolicy attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory Domain Services authentication policy object. Specify the authenticati...\n- Instance (Required): Specifies a modified copy of an ADAuthenticationPolicy object to use to update the actual ADAuthenti...\nExample Scenarios:\n- This command modifies the description and the UserTGTLifetimeMins properties of the specified authen...\n- This example first gets the authentication policy named AuthenticationPolicy02 by using the Get-ADAu...\n- This command uses the Get-ADAuthenticationPolicy cmdlet with the Filter parameter to get all authent...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADAuthenticationPolicySilo",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADAuthenticationPolicySilo",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADAuthenticationPolicySilo",
                         "rag_document":  "Command: Set-ADAuthenticationPolicySilo\nPurpose: Modify ADAuthenticationPolicySilo properties\nPrimary Task: Modify AuthenticationPolicySilo properties in Active Directory\nCommon Tasks:\n- Modify AuthenticationPolicySilo properties\n- Update AuthenticationPolicySilo configuration\n- Change AuthenticationPolicySilo attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory Domain Services authentication policy silo object. Specify the authent...\n- Instance (Required): Specifies a modified copy of an ADAuthenticationPolicySilo object to use to update the actual ADAuth...\nExample Scenarios:\n- This command modifies the user authentication policy for the authentication policy silo named Authen...\n- This example first gets an authentication policy silo object and stores it in the variable named $Au...\n- This example first gets all authentication policy silos that match the filter specified by the Filte...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADCentralAccessPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADCentralAccessPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADCentralAccessPolicy",
                         "rag_document":  "Command: Set-ADCentralAccessPolicy\nPurpose: Modify ADCentralAccessPolicy properties\nPrimary Task: Modify CentralAccessPolicy properties in Active Directory\nCommon Tasks:\n- Modify CentralAccessPolicy properties\n- Update CentralAccessPolicy configuration\n- Change CentralAccessPolicy attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Instance (Required): Specifies a modified copy of a central access policy object to use to update the actual central acce...\nExample Scenarios:\n- This command updates the central access policy named Finance Policy to include the description For t...\n- This command gets the central access policy named Finance Policy, and then sets its description to F...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADCentralAccessRule",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADCentralAccessRule",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADCentralAccessRule",
                         "rag_document":  "Command: Set-ADCentralAccessRule\nPurpose: Modify ADCentralAccessRule properties\nPrimary Task: Modify CentralAccessRule properties in Active Directory\nCommon Tasks:\n- Modify CentralAccessRule properties\n- Update CentralAccessRule configuration\n- Change CentralAccessRule attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Instance (Required): Specifies a modified copy of an central access rule object to use to update the actual central acces...\nExample Scenarios:\n- This command sets the central access rule named Finance Documents Rule with a new resource condition...\n- This example sets the central access rule named Finance Documents Rule with a new resource condition...\n- This command gets the central access rule named Finance Documents Rule, and set the description to F...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADClaimTransformLink",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADClaimTransformLink",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADClaimTransformLink",
                         "rag_document":  "Command: Set-ADClaimTransformLink\nPurpose: Modify ADClaimTransformLink properties\nPrimary Task: Modify ClaimTransformLink properties in Active Directory\nCommon Tasks:\n- Modify ClaimTransformLink properties\n- Update ClaimTransformLink configuration\n- Change ClaimTransformLink attributes\nKey Parameters:\n- Policy (Required): Specifies the claims transformation policy to apply to the cross-forest trust relationship. This par...\n- Identity (Required): Specifies an Active Directory group object by providing one of the following values. The identifier ...\nExample Scenarios:\n- This command applies the claims transformation policy DenyAllPolicy to the trust corp.contoso.com. T...\n- This command applies the claims transformation policy AllowAllExceptCompanyAndDepartmentPolicy to th...\n- This command applies the claims transformation policy HumanResourcesToHrPolicy to the trust corp.con...\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADClaimTransformPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADClaimTransformPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADClaimTransformPolicy",
                         "rag_document":  "Command: Set-ADClaimTransformPolicy\nPurpose: Modify ADClaimTransformPolicy properties\nPrimary Task: Modify ClaimTransformPolicy properties in Active Directory\nCommon Tasks:\n- Modify ClaimTransformPolicy properties\n- Update ClaimTransformPolicy configuration\n- Change ClaimTransformPolicy attributes\nKey Parameters:\n- Identity (Required): Specifies one of the following as valid identities for the ADClaimTransformPolicy object:\n- Instance (Required): Specifies an instance of an Active Directory object to use as a template for a new claims transforma...\nExample Scenarios:\n- This command sets the transformation rule on the claims transformation policy named DenyAllPolicy to...\n- This command sets the transformation rule on the claims transformation policy named AllowAllExceptCo...\n- This command sets the transformation rule on the claims transformation policy named HumanResourcesTo...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADClaimType",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADClaimType",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADClaimType",
                         "rag_document":  "Command: Set-ADClaimType\nPurpose: Modify ADClaimType properties\nPrimary Task: Modify ClaimType properties in Active Directory\nCommon Tasks:\n- Modify ClaimType properties\n- Update ClaimType configuration\n- Change ClaimType attributes\nKey Parameters:\n- SourceOID (Required): Specifies a string to use to configure a certificate-based claim type source. For example, use this ...\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Instance (Required): Specifies an instance of a claim type object to use as a template for a new claim type object.\nExample Scenarios:\n- This command sets the user claim type with display name Title to source from the Active Directory at...\n- This command sets the suggested values of the user claim type with display name Employee Type to FTE...\n- This example sets the source OID of the claim type with display name Bitlocker Enabled to *******.4....\nCategory: General Operations"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADComputer",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADComputer",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  18,
                         "usage_category":  "moderate",
                         "command_name":  "Set-ADComputer",
                         "rag_document":  "Command: Set-ADComputer\nPurpose: Modify ADComputer properties\nPrimary Task: Modify Computer properties in Active Directory\nCommon Tasks:\n- Modify Computer properties\n- Update Computer configuration\n- Change Computer attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory computer object by providing one of the following property values. The...\n- Instance (Required): Specifies a modified copy of a computer object to use to update the actual Active Directory computer...\nExample Scenarios:\n- This command modifies the service principal name (SPN) value for the computer specified by the Ident...\n- This command sets the location for the computer specified by the Identity parameter.\n- This command sets the ManagedBy attribute value for the computer specified by the Identity parameter...\nCategory: Computer Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADDefaultDomainPasswordPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADDefaultDomainPasswordPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  6,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADDefaultDomainPasswordPolicy",
                         "rag_document":  "Command: Set-ADDefaultDomainPasswordPolicy\nPurpose: Modify ADDefaultDomainPasswordPolicy properties\nPrimary Task: Modify DefaultDomainPasswordPolicy properties in Active Directory\nCommon Tasks:\n- Modify DefaultDomainPasswordPolicy properties\n- Update DefaultDomainPasswordPolicy configuration\n- Change DefaultDomainPasswordPolicy attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory domain object by providing one of the following property values. The i...\nExample Scenarios:\n- This command sets the default domain password policy for a domain specified by using the Identity pa...\n- This command sets the default domain password policy for the current logged on user domain.\nCategory: Domain Management Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADDomain",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADDomain",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADDomain",
                         "rag_document":  "Command: Set-ADDomain\nPurpose: Modify ADDomain properties\nPrimary Task: Modify Domain properties in Active Directory\nCommon Tasks:\n- Modify Domain properties\n- Update Domain configuration\n- Change Domain attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory domain object by providing one of the following property values. The i...\n- Instance (Required): Specifies a modified copy of a domain object to use to update the actual Active Directory domain obj...\nExample Scenarios:\n- This command sets the value of AllowedDNSSuffixes to {\"USER01.com\",\"corp.USER01.com\"} in domain USER...\n- This command adds the value corp.USER01.com to the AllowedDNSSuffixes in domain USER01.\n- This command sets the LastLogonReplicationInterval of the current logged on user domain to 10.\nCategory: Domain Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADDomainMode",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADDomainMode",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADDomainMode",
                         "rag_document":  "Command: Set-ADDomainMode\nPurpose: Modify ADDomainMode properties\nPrimary Task: Modify DomainMode properties in Active Directory\nCommon Tasks:\n- Modify DomainMode properties\n- Update DomainMode configuration\n- Change DomainMode attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory domain object by providing one of the following property values. The i...\nExample Scenarios:\n- This command sets the DomainMode property of the user01.com domain to Windows2003Domain.\n- This example sets the DomainMode of the current logged on user\u0027s domain to Windows2003Domain. The se...\nCategory: Domain Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADFineGrainedPasswordPolicy",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADFineGrainedPasswordPolicy",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  4,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADFineGrainedPasswordPolicy",
                         "rag_document":  "Command: Set-ADFineGrainedPasswordPolicy\nPurpose: Modify ADFineGrainedPasswordPolicy properties\nPrimary Task: Modify FineGrainedPasswordPolicy properties in Active Directory\nCommon Tasks:\n- Modify FineGrainedPasswordPolicy properties\n- Update FineGrainedPasswordPolicy configuration\n- Change FineGrainedPasswordPolicy attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory fine-grained password policy object by providing one of the following ...\n- Instance (Required): Specifies a modified copy of a fine-grained password policy object to use to update the actual Activ...\nExample Scenarios:\n- This command updates the Precedence, LockoutDuration, LockoutObservationWindow, ComplexityEnabled, R...\n- This command sets the MinPasswordLength property on the FineGrainedPasswordPolicy object with distin...\n- This example gets the FineGrainedPasswordPolicy object with name MyPolicy, updates a set of properti...\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADForest",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADForest",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADForest",
                         "rag_document":  "Command: Set-ADForest\nPurpose: Modify ADForest properties\nPrimary Task: Modify Forest properties in Active Directory\nCommon Tasks:\n- Modify Forest properties\n- Update Forest configuration\n- Change Forest attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory forest object by providing one of the following attribute values. The ...\nExample Scenarios:\n- This command sets the UPNSuffixes property for the fabrikam.com forest.\n- This command adds corp.fabrikam.com to the SPNSuffixes property on the forest fabrikam.com.\n- This command gets the forest of the current logged on user and updates the SPNSuffixes property.\nCategory: Domain Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADForestMode",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADForestMode",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADForestMode",
                         "rag_document":  "Command: Set-ADForestMode\nPurpose: Modify ADForestMode properties\nPrimary Task: Modify ForestMode properties in Active Directory\nCommon Tasks:\n- Modify ForestMode properties\n- Update ForestMode configuration\n- Change ForestMode attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory forest object by providing one of the following attribute values. The ...\nExample Scenarios:\n- This command sets the ForestMode to Windows2003Forest in the forest fabrikam.com.\n- This example sets the forest mode of the current user\u0027s forest. The set operation targets the schema...\nCategory: Domain Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADGroup",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADGroup",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  22,
                         "usage_category":  "moderate",
                         "command_name":  "Set-ADGroup",
                         "rag_document":  "Command: Set-ADGroup\nPurpose: Modify ADGroup properties\nPrimary Task: Modify Group properties in Active Directory\nCommon Tasks:\n- Modify Group properties\n- Update Group configuration\n- Change Group attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory group object by providing one of the following values. The identifier ...\n- Instance (Required): Specifies a modified copy of a group object to use to update the actual Active Directory group objec...\nExample Scenarios:\n- This command sets the Description property of the group named AccessControl to Access Group on an Ac...\n- This command modifies the Description property on all groups that have a name that starts with Acces...\n- This example sets the Description property on the AccessControl group by using the Instance paramete...\nCategory: Group Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADObject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADObject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  10,
                         "usage_category":  "uncommon",
                         "command_name":  "Set-ADObject",
                         "rag_document":  "Command: Set-ADObject\nPurpose: Modify ADObject properties\nPrimary Task: Modify Object properties in Active Directory\nCommon Tasks:\n- Modify Object properties\n- Update Object configuration\n- Change Object attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Instance (Required): Specifies a modified copy of an Active Directory object to use to update the actual Active Directory...\nExample Scenarios:\n- This command sets the Description property on the object with the distinguished name CN=PattiFu Dire...\n- This command adds the site CN=BO3,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM to the property siteL...\n- This command adds two new URLs to the urlValues property in the object with the GUID cdadd380-d3a8-4...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADOrganizationalUnit",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADOrganizationalUnit",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  12,
                         "usage_category":  "uncommon",
                         "command_name":  "Set-ADOrganizationalUnit",
                         "rag_document":  "Command: Set-ADOrganizationalUnit\nPurpose: Modify ADOrganizationalUnit properties\nPrimary Task: Modify OrganizationalUnit properties in Active Directory\nCommon Tasks:\n- Modify OrganizationalUnit properties\n- Update OrganizationalUnit configuration\n- Change OrganizationalUnit attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Instance (Required): Specifies a modified copy of an OU object to use to update the actual Active Directory OU object. Wh...\nExample Scenarios:\n- This command sets the description of the OU with the distinguished name OU=UserAccounts,DC=FABRIKAM,...\n- This command sets the Country, City, State, PostalCode, and Country properties on the OU OU=AsiaPaci...\n- This command sets the Country property of the OU OU=Managed,DC=AppNC in an AD LDS instance.\nCategory: Organizational Unit Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADReplicationConnection",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADReplicationConnection",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADReplicationConnection",
                         "rag_document":  "Command: Set-ADReplicationConnection\nPurpose: Modify ADReplicationConnection properties\nPrimary Task: Modify ReplicationConnection properties in Active Directory\nCommon Tasks:\n- Modify ReplicationConnection properties\n- Update ReplicationConnection configuration\n- Change ReplicationConnection attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Instance (Required): Specifies an instance of an Active Directory object to use as a template for a new Active Directory ...\nExample Scenarios:\n- This command sets the replication connection with GUID 5f98e288-19e0-47a0-9677-57f05ed54f6b to repli...\n- This command gets all of the replication connections in the directory that replicates from corp-DC01...\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADReplicationSite",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADReplicationSite",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADReplicationSite",
                         "rag_document":  "Command: Set-ADReplicationSite\nPurpose: Modify ADReplicationSite properties\nPrimary Task: Modify ReplicationSite properties in Active Directory\nCommon Tasks:\n- Modify ReplicationSite properties\n- Update ReplicationSite configuration\n- Change ReplicationSite attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Instance (Required): Specifies an instance of a site object to use as a template for a new site object.\nExample Scenarios:\n- The command sets the properties of the site with name NorthAmerica to prevent its intersite topology...\n- This command returns all the sites in the directory and sets the ScheduleHashingEnabled property to ...\n- This example sets the daily replication schedule of the site with name Asia.\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADReplicationSiteLink",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADReplicationSiteLink",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADReplicationSiteLink",
                         "rag_document":  "Command: Set-ADReplicationSiteLink\nPurpose: Modify ADReplicationSiteLink properties\nPrimary Task: Modify ReplicationSiteLink properties in Active Directory\nCommon Tasks:\n- Modify ReplicationSiteLink properties\n- Update ReplicationSiteLink configuration\n- Change ReplicationSiteLink attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command adds the site Asia2 to the replication site link Europe-Asia, and removes the site Asia...\n- This command gets all the site links in the directory with replication frequency greater than or equ...\n- This command sets the daily replication schedule of the site link with name NorthAmerica-SouthAmeric...\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADReplicationSiteLinkBridge",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADReplicationSiteLinkBridge",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADReplicationSiteLinkBridge",
                         "rag_document":  "Command: Set-ADReplicationSiteLinkBridge\nPurpose: Modify ADReplicationSiteLinkBridge properties\nPrimary Task: Modify ReplicationSiteLinkBridge properties in Active Directory\nCommon Tasks:\n- Modify ReplicationSiteLinkBridge properties\n- Update ReplicationSiteLinkBridge configuration\n- Change ReplicationSiteLinkBridge attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\nExample Scenarios:\n- This command updates the site link bridge NorthAmerica-Asia to use Europe2 instead of Europe.\n- This command gets all the site link bridges in the directory that includes site links NorthAmerica-E...\nCategory: Replication Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Set-ADServiceAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Set-ADServiceAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  3,
                         "usage_category":  "rare",
                         "command_name":  "Set-ADServiceAccount",
                         "rag_document":  "Command: Set-ADServiceAccount\nPurpose: Modify ADServiceAccount properties\nPrimary Task: Modify ServiceAccount properties in Active Directory\nCommon Tasks:\n- Modify ServiceAccount properties\n- Update ServiceAccount configuration\n- Change ServiceAccount attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\n- Instance (Required): Specifies a modified copy of a service account object to use to update the actual Active Directory s...\nExample Scenarios:\n- This command sets the description of the MSA identified as Service1 to Secretive Data Server.\n- This command replaces the value of property ServicePrincipalNames with ADAMwdb/a.contoso.com, ADAMbd...\n- This command sets the principals allowed to retrieve the password for this MSA to be limited to memb...\nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Update user\u0027s email address",
                                                         "description":  "Set the email address for a specific user",
                                                         "powershell_command":  "Set-ADUser -Identity \"jdoe\" -EmailAddress \"<EMAIL>\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Change user\u0027s department and title",
                                                         "description":  "Update multiple user properties at once",
                                                         "powershell_command":  "Set-ADUser -Identity \"jdoe\" -Department \"Sales\" -Title \"Sales Manager\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Set user\u0027s manager",
                                                         "description":  "Assign a manager to a user account",
                                                         "powershell_command":  "Set-ADUser -Identity \"jdoe\" -Manager \"CN=Jane Smith,OU=Users,DC=contoso,DC=com\"",
                                                         "source":  "Microsoft Learn Documentation"
                                                     },
                                                     {
                                                         "natural_language":  "Update user\u0027s phone number and office location",
                                                         "description":  "Set contact information for a user",
                                                         "powershell_command":  "Set-ADUser -Identity \"jdoe\" -OfficePhone \"555-1234\" -Office \"Building A, Room 101\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Enable user account and set password to never expire",
                                                         "description":  "Configure user account settings",
                                                         "powershell_command":  "Set-ADUser -Identity \"jdoe\" -Enabled $true -PasswordNeverExpires $true",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Set user\u0027s home directory and drive mapping",
                                                         "description":  "Configure user profile settings",
                                                         "powershell_command":  "Set-ADUser -Identity \"jdoe\" -HomeDirectory \"\\\\server\\users\\jdoe\" -HomeDrive \"H:\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Update user\u0027s description field",
                                                         "description":  "Set descriptive information for a user",
                                                         "powershell_command":  "Set-ADUser -Identity \"jdoe\" -Description \"Sales Representative - West Coast\"",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Clear user\u0027s manager assignment",
                                                         "description":  "Remove manager assignment from user account",
                                                         "powershell_command":  "Set-ADUser -Identity \"jdoe\" -Clear Manager",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Set custom attribute for user",
                                                         "description":  "Add custom attribute value to user account",
                                                         "powershell_command":  "Set-ADUser -Identity \"jdoe\" -Add @{extensionAttribute1=\"VIP Customer\"}",
                                                         "source":  "Community Examples"
                                                     },
                                                     {
                                                         "natural_language":  "Update multiple users in HR department",
                                                         "description":  "Bulk update users based on filter criteria",
                                                         "powershell_command":  "Get-ADUser -Filter \"Department -eq \u0027HR\u0027\" | Set-ADUser -Company \"Contoso Corp\"",
                                                         "source":  "Community Examples"
                                                     }
                                                 ],
                         "usage_frequency":  95,
                         "usage_category":  "very_common",
                         "command_name":  "Set-ADUser",
                         "rag_document":  "Command: Set-ADUser\nPurpose: Modify ADUser properties\nPrimary Task: Modify User properties in Active Directory\nCommon Tasks:\n- Modify User properties\n- Update User configuration\n- Change User attributes\nKey Parameters:\n- Identity (Required): Specifies an Active Directory user object by providing one of the following property values. The ide...\n- Instance (Required): Specifies an ADUser object that identifies the Active Directory user object that should be modified ...\nExample Scenarios:\n- This command sets the specified user\u0027s homepage property to http://fabrikam.com/employees/ChewDavid ...\n- This command gets all the users in the directory that are located in the OU=HumanResources,OU=UserAc...\n- This command sets the specified user\u0027s title property to director and the mail property to glenjohn@...\nCategory: User Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Show-ADAuthenticationPolicyExpression",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Show-ADAuthenticationPolicyExpression",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Show-ADAuthenticationPolicyExpression",
                         "rag_document":  "Command: Show-ADAuthenticationPolicyExpression\nPurpose: Manage ADAuthenticationPolicyExpression\nPrimary Task: Manage ADAuthenticationPolicyExpression\nKey Parameters:\nCategory: Policy Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Start-ADServiceAccountMigration",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Start-ADServiceAccountMigration",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Start-ADServiceAccountMigration",
                         "rag_document":  "Command: Start-ADServiceAccountMigration\nPurpose: Manage ADServiceAccountMigration\nPrimary Task: Manage ADServiceAccountMigration\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- \n- \nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Sync-ADObject",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Sync-ADObject",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  5,
                         "usage_category":  "rare",
                         "command_name":  "Sync-ADObject",
                         "rag_document":  "Command: Sync-ADObject\nPurpose: Manage ADObject\nPrimary Task: Manage ADObject\nKey Parameters:\n- Object (Required): Specifies an Active Directory object by providing one of the following property values. The identifi...\n- Destination (Required): Specifies the identity of the Active Directory server that acts as the destination for synchronizing...\nExample Scenarios:\n- This command replicates an object with the distinguished name CN=AccountManagers,OU=AccountDeptOU,DC...\n- This command pre-caches the password of Patti Fuller to the read-only domain controller corp-RODC01 ...\nCategory: Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Test-ADServiceAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Test-ADServiceAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  4,
                         "usage_category":  "rare",
                         "command_name":  "Test-ADServiceAccount",
                         "rag_document":  "Command: Test-ADServiceAccount\nPurpose: Manage ADServiceAccount\nPrimary Task: Manage ADServiceAccount\nKey Parameters:\n- Identity (Required): Specifies an Active Directory managed service account object by providing one of the following prope...\nExample Scenarios:\n- This command tests the specified service account, MSA1, from the local computer. The test indicates ...\nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Undo-ADServiceAccountMigration",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Undo-ADServiceAccountMigration",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  1,
                         "usage_category":  "rare",
                         "command_name":  "Undo-ADServiceAccountMigration",
                         "rag_document":  "Command: Undo-ADServiceAccountMigration\nPurpose: Manage ADServiceAccountMigration\nPrimary Task: Manage ADServiceAccountMigration\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- \n- \nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Uninstall-ADServiceAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Uninstall-ADServiceAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  2,
                         "usage_category":  "rare",
                         "command_name":  "Uninstall-ADServiceAccount",
                         "rag_document":  "Command: Uninstall-ADServiceAccount\nPurpose: Manage ADServiceAccount\nPrimary Task: Manage ADServiceAccount\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command uninstalls the MSA identified as SQL-SRV1 from the local machine.\n- This command uninstalls the specified standalone MSA from a server located in a read-only domain con...\nCategory: Service Account Management Security Management"
                     },
                     {
                         "real_world_examples":  [
                                                     {
                                                         "natural_language":  "Basic usage of Unlock-ADAccount",
                                                         "description":  "Standard usage pattern",
                                                         "powershell_command":  "Unlock-ADAccount",
                                                         "source":  "Generated from RAG document"
                                                     }
                                                 ],
                         "usage_frequency":  45,
                         "usage_category":  "common",
                         "command_name":  "Unlock-ADAccount",
                         "rag_document":  "Command: Unlock-ADAccount\nPurpose: Manage ADAccount\nPrimary Task: Manage ADAccount\nKey Parameters:\n- Identity (Required): Specifies an Active Directory account object by providing one of the following property values. The ...\nExample Scenarios:\n- This command unlocks the account with the SAM account name PattiFu.\n- This command unlocks the account with the distinguished name CN=Patti Fuller,OU=Finance,OU=UserAccou...\nCategory: Security Management"
                     }
                 ],
    "metadata":  {
                     "description":  "Enhanced PowerShell AD commands dataset with comprehensive real-world examples",
                     "sources":  [
                                     "https://learn.microsoft.com/en-us/powershell/module/activedirectory",
                                     "https://stackoverflow.com/questions/tagged/powershell",
                                     "https://serverfault.com/questions/tagged/powershell",
                                     "https://4sysops.com/",
                                     "https://devblogs.microsoft.com/powershell/",
                                     "https://docs.risual.com/",
                                     "https://adamtheautomator.com/",
                                     "https://techcommunity.microsoft.com/t5/windows-powershell/bd-p/WindowsPowerShell"
                                 ],
                     "high_frequency_commands":  11,
                     "example_requirements":  {
                                                  "standard_frequency":  "3-5 examples (usage_frequency \u003c 50)",
                                                  "high_frequency":  "8+ examples (usage_frequency \u003e= 50)"
                                              },
                     "created_at":  "2025-07-27 08:33:52",
                     "source_files":  [
                                          "netRag/data/ad_powershell_filtered_data.json"
                                      ],
                     "total_commands":  139
                 }
}
