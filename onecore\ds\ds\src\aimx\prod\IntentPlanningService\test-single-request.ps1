$body = @{
    userInput = "Create a user account for <PERSON>"
    userId = "test.user"
    priority = "normal"
    environment = "production"
    requestId = [guid]::NewGuid().ToString()
} | ConvertTo-Json

Write-Host "Request Body:" -ForegroundColor Yellow
Write-Host $body

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8082/api/IntentPlanning/analyze" -Method Post -Body $body -ContentType "application/json"
    
    Write-Host "`nResponse:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
    
    Write-Host "`nUser Goal: $($response.userGoal.primaryObjective)" -ForegroundColor Cyan
    Write-Host "Confidence: $($response.userGoal.extractionConfidence)" -ForegroundColor Cyan
    Write-Host "Category: $($response.userGoal.context.intentCategory)" -ForegroundColor Cyan

    if ($response.workflow -and $response.workflow.steps) {
        Write-Host "Workflow Steps: $($response.workflow.steps.Count)" -ForegroundColor Green
    }

    if ($response.primaryWorkflow -and $response.primaryWorkflow.steps) {
        Write-Host "Primary Workflow Steps: $($response.primaryWorkflow.steps.Count)" -ForegroundColor Green
        Write-Host "First Step: $($response.primaryWorkflow.steps[0].stepName)" -ForegroundColor Green
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
