using IntentPlanningService.Configuration;
using IntentPlanningService.Models;
using Microsoft.Extensions.AI;
using Microsoft.SemanticKernel;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace IntentPlanningService.Services;

/// <summary>
/// LLM API response models
/// </summary>
public class LlmResponse
{
    public List<LlmChoice> Choices { get; set; } = new();
}

public class LlmChoice
{
    public LlmMessage Message { get; set; } = new();
}

public class LlmMessage
{
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// Service for analyzing user intent using SLM (Small Language Model)
/// </summary>
public class IntentAnalysisService
{
    private readonly ILogger<IntentAnalysisService> _logger;
    private readonly IntentPlanningConfiguration _config;
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly Kernel _kernel;
    private readonly Dictionary<string, UserGoal> _goalCache;
    private readonly SemaphoreSlim _semaphore;
    private readonly IHttpClientFactory _httpClientFactory;

    public IntentAnalysisService(
        ILogger<IntentAnalysisService> logger,
        IntentPlanningConfiguration config,
        IEmbeddingGenerator<string, Embedding<float>> embeddingService,
        Kernel kernel,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _config = config;
        _embeddingService = embeddingService;
        _kernel = kernel;
        _goalCache = new Dictionary<string, UserGoal>();
        _semaphore = new SemaphoreSlim(_config.Service.MaxConcurrentRequests, _config.Service.MaxConcurrentRequests);
        _httpClientFactory = httpClientFactory;
    }

    /// <summary>
    /// Analyze user input to extract intent and goals
    /// </summary>
    public async Task<UserGoal> AnalyzeIntentAsync(UserRequest request)
    {
        await _semaphore.WaitAsync();
        try
        {
            _logger.LogInformation("=== STARTING INTENT ANALYSIS ===");
            _logger.LogInformation("Request ID: {RequestId}", request.RequestId);
            _logger.LogInformation("User Input: '{UserInput}'", request.UserInput);
            _logger.LogInformation("User ID: {UserId}", request.UserId);
            _logger.LogInformation("Priority: {Priority}", request.Priority);
            _logger.LogInformation("Environment: {Environment}", request.Environment);

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Check cache first if enabled
            if (_config.Slm.EnableCaching)
            {
                _logger.LogInformation("STEP 0: Checking cache for existing analysis");
                var cacheKey = GenerateCacheKey(request.UserInput);
                if (_goalCache.TryGetValue(cacheKey, out var cachedGoal))
                {
                    _logger.LogInformation("✅ Retrieved goal from cache for request {RequestId}", request.RequestId);
                    _logger.LogInformation("=== INTENT ANALYSIS COMPLETED (CACHED) ===");
                    return cachedGoal;
                }
                _logger.LogInformation("❌ No cached result found");
            }

            // Step 1: Scope filtering - quick check if this is an IT operation request
            _logger.LogInformation("STEP 1: AI-Powered Scope Filtering");
            var isITOperation = await IsITOperationRequestAsync(request.UserInput);
            _logger.LogInformation("Scope filtering result: {IsInScope}", isITOperation);

            if (!isITOperation)
            {
                _logger.LogWarning("❌ Request {RequestId} does not appear to be an IT operation", request.RequestId);
                _logger.LogInformation("=== INTENT ANALYSIS COMPLETED (OUT OF SCOPE) ===");
                return CreateErrorGoal("Input does not appear to be an IT operation request", 0.1);
            }

            _logger.LogInformation("✅ Request is in scope for IT operations");

            // Step 2: Intent classification using SLM
            _logger.LogInformation("STEP 2: Intent Classification using RAG + LLM");
            var intentCategory = await ClassifyIntentAsync(request.UserInput);
            _logger.LogInformation("✅ Classified intent as '{Category}' for request {RequestId}", intentCategory, request.RequestId);

            // Step 3: Goal extraction using SLM
            _logger.LogInformation("STEP 3: Goal Extraction using LLM");
            var userGoal = await ExtractGoalAsync(request, intentCategory);
            _logger.LogInformation("✅ Goal extraction completed with confidence {Confidence}", userGoal.ExtractionConfidence);

            // Step 4: Context enrichment
            if (_config.IntentAnalysis.EnableContextEnrichment)
            {
                await EnrichContextAsync(userGoal, request);
            }

            // Step 5: Confidence validation
            if (userGoal.ExtractionConfidence < _config.IntentAnalysis.MinConfidenceThreshold)
            {
                _logger.LogWarning("Low confidence ({Confidence}) for request {RequestId}, attempting fallback", 
                    userGoal.ExtractionConfidence, request.RequestId);

                // TODO: Implement AI-driven fallback mechanism
                _logger.LogWarning("Goal extraction failed for request {RequestId}, no fallback implemented", request.RequestId);
            }

            stopwatch.Stop();
            _logger.LogInformation("Intent analysis completed for request {RequestId} in {ElapsedMs}ms with confidence {Confidence}", 
                request.RequestId, stopwatch.ElapsedMilliseconds, userGoal.ExtractionConfidence);

            // Cache the result if enabled
            if (_config.Slm.EnableCaching && userGoal.ExtractionConfidence >= _config.IntentAnalysis.MinConfidenceThreshold)
            {
                var cacheKey = GenerateCacheKey(request.UserInput);
                _goalCache[cacheKey] = userGoal;
                
                // Simple cache cleanup - remove oldest entries if cache is too large
                if (_goalCache.Count > 1000)
                {
                    var oldestKey = _goalCache.Keys.First();
                    _goalCache.Remove(oldestKey);
                }
            }

            return userGoal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing intent for request {RequestId}", request.RequestId);
            return CreateErrorGoal($"Error analyzing intent: {ex.Message}", 0.0);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// AI-powered scope filtering to determine if input is an IT operation request
    /// </summary>
    private async Task<bool> IsITOperationRequestAsync(string userInput)
    {
        try
        {
            // Create AI prompt for scope filtering
            var prompt = $@"Is this an IT administration request?
Input: ""{userInput}""

Respond with only: YES or NO";

            var response = await CallLlmAsync(prompt);
            var isITOperation = response.Trim().ToUpperInvariant().StartsWith("YES");

            _logger.LogDebug("Scope filtering result for '{Input}': {Result} - {Response}",
                userInput.Substring(0, Math.Min(50, userInput.Length)), isITOperation, response);

            return isITOperation;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error in AI scope filtering, using fallback");

            // Fallback to LLM without RAG
            try
            {
                var fallbackPrompt = $@"Is this an IT administration or system management request?
Input: ""{userInput}""
Respond with only YES or NO.";

                var response = await CallLlmAsync(fallbackPrompt);
                return response.Trim().ToUpperInvariant().StartsWith("YES");
            }
            catch
            {
                return true; // Default to true to avoid false negatives
            }
        }
    }

    /// <summary>
    /// AI-powered intent classification using RAG and LLM
    /// </summary>
    private async Task<string> ClassifyIntentAsync(string userInput)
    {
        try
        {
            var categories = "user_management, group_management, computer_management, security_operations, system_administration, troubleshooting, reporting, maintenance";

            var prompt = $@"You are an expert IT operations classifier. Classify the user request into the most appropriate category.

Available Categories: {categories}

Examples:
- ""Create user account"" → user_management
- ""Add user to group"" → group_management
- ""Join computer to domain"" → computer_management
- ""Reset password"" → user_management
- ""System is slow"" → troubleshooting
- ""Generate report"" → reporting

User Request: ""{userInput}""

Based on your expertise, classify this request. Consider:
- The primary IT domain (users, groups, computers, security, etc.)
- The type of operation (management, troubleshooting, reporting, etc.)
- The technical context and requirements

Respond with only the category name (e.g., 'user_management').";

            var response = await CallLlmAsync(prompt);
            var category = response.Trim().ToLowerInvariant();

            // Validate the category
            var supportedCategories = new[] { "user_management", "group_management", "computer_management", "security_operations", "system_administration", "troubleshooting", "reporting", "maintenance" };
            if (supportedCategories.Contains(category))
            {
                _logger.LogDebug("AI classified intent '{Input}' as '{Category}'",
                    userInput.Substring(0, Math.Min(50, userInput.Length)), category);
                return category;
            }

            // If invalid category, ask LLM to choose from valid options
            var fallbackPrompt = $@"The category '{category}' is not valid. Choose from: {categories}
For request: ""{userInput}""
Respond with only a valid category name.";

            var fallbackResponse = await CallLlmAsync(fallbackPrompt);
            var fallbackCategory = fallbackResponse.Trim().ToLowerInvariant();

            // Final fallback to safe category
            return "system_administration";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in AI intent classification");
            return "system_administration"; // Safe fallback
        }
    }

    /// <summary>
    /// AI-powered goal extraction using LLM
    /// </summary>
    private async Task<UserGoal> ExtractGoalAsync(UserRequest request, string intentCategory)
    {
        try
        {
            var prompt = $@"You are an expert IT operations analyst. Extract the user's goal from this IT request with precision and context awareness.

Category: {intentCategory}
Priority: {request.Priority}
Environment: {request.Environment}

Examples:
- ""Create user account for John Smith"" → Goal: Create new user account, Criteria: Account exists and is accessible, Entities: John Smith
- ""Reset password for alice.johnson"" → Goal: Reset user password, Criteria: User can login with new password, Entities: alice.johnson
- ""Add bob to Finance group"" → Goal: Add user to security group, Criteria: User has group permissions, Entities: bob, Finance group

User Request: ""{request.UserInput}""

Based on your expertise, extract:
1. Primary objective (what the user wants to accomplish - be specific and actionable)
2. Success criteria (measurable outcomes that indicate completion)
3. Constraints (technical, security, or business limitations)
4. Urgency level (low, normal, high, critical) - consider priority and context
5. Required entities (users, groups, computers, etc. mentioned or implied)

Consider the environment ({request.Environment}) and priority ({request.Priority}) in your analysis.

Respond in JSON format:
{{
  ""primaryObjective"": ""specific, actionable description of what user wants"",
  ""successCriteria"": ""measurable outcomes indicating success"",
  ""constraints"": [""constraint1"", ""constraint2""],
  ""urgencyLevel"": ""normal"",
  ""requiredEntities"": [""entity1"", ""entity2""],
  ""technicalRequirements"": [""requirement1"", ""requirement2""]
}}";

            var response = await CallLlmAsync(prompt);
            var goalData = ParseLlmJsonResponse(response);

            var userGoal = new UserGoal
            {
                PrimaryObjective = goalData.GetValueOrDefault("primaryObjective", "")?.ToString() ?? "",
                SuccessCriteria = goalData.GetValueOrDefault("successCriteria", "")?.ToString() ?? "",
                Constraints = ParseStringArray(goalData.GetValueOrDefault("constraints")),
                UrgencyLevel = goalData.GetValueOrDefault("urgencyLevel", request.Priority)?.ToString() ?? request.Priority,
                ExtractionConfidence = 0.8, // TODO: Replace with AI-driven confidence calculation
                ExtractionMethod = "ai_rag_llm"
            };

            // Add extracted entities and requirements to context
            var requiredEntities = ParseStringArray(goalData.GetValueOrDefault("requiredEntities"));
            var technicalRequirements = ParseStringArray(goalData.GetValueOrDefault("technicalRequirements"));

            userGoal.Context["requestId"] = request.RequestId;
            userGoal.Context["userId"] = request.UserId;
            userGoal.Context["intentCategory"] = intentCategory;
            userGoal.Context["environment"] = request.Environment;
            userGoal.Context["priority"] = request.Priority;
            userGoal.Context["requiredEntities"] = string.Join(",", requiredEntities);
            userGoal.Context["technicalRequirements"] = string.Join(",", technicalRequirements);
            userGoal.Context["extractionMethod"] = "llm_only";

            _logger.LogDebug("AI extracted goal for request {RequestId}: {Objective} (Confidence: {Confidence})",
                request.RequestId, userGoal.PrimaryObjective, userGoal.ExtractionConfidence);

            return userGoal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in AI goal extraction");
            return await FallbackToRulesBasedExtraction(request, intentCategory);
        }
    }

    /// <summary>
    /// Fallback to rules-based goal extraction
    /// </summary>
    private Task<UserGoal> FallbackToRulesBasedExtraction(UserRequest request, string intentCategory)
    {
        _logger.LogInformation("Using rules-based fallback for goal extraction");

        var userGoal = new UserGoal
        {
            ExtractionMethod = "rule_based",
            ExtractionConfidence = 0.6 // Lower confidence for rule-based
        };

        // Simple rule-based extraction based on keywords and patterns
        var input = request.UserInput.ToLowerInvariant();

        // Extract primary objective based on action verbs
        if (input.Contains("create") || input.Contains("add") || input.Contains("new"))
        {
            userGoal.PrimaryObjective = $"Create or add new {intentCategory.Replace("_", " ")} resource";
        }
        else if (input.Contains("delete") || input.Contains("remove"))
        {
            userGoal.PrimaryObjective = $"Delete or remove {intentCategory.Replace("_", " ")} resource";
        }
        else if (input.Contains("modify") || input.Contains("update") || input.Contains("change"))
        {
            userGoal.PrimaryObjective = $"Modify or update {intentCategory.Replace("_", " ")} resource";
        }
        else if (input.Contains("find") || input.Contains("search") || input.Contains("list"))
        {
            userGoal.PrimaryObjective = $"Find or search {intentCategory.Replace("_", " ")} resources";
        }
        else
        {
            userGoal.PrimaryObjective = $"Perform {intentCategory.Replace("_", " ")} operation";
        }

        userGoal.SuccessCriteria = "Operation completed successfully without errors";
        userGoal.UrgencyLevel = request.Priority;

        // Add context
        userGoal.Context["requestId"] = request.RequestId;
        userGoal.Context["userId"] = request.UserId;
        userGoal.Context["intentCategory"] = intentCategory;
        userGoal.Context["environment"] = request.Environment;
        userGoal.Context["fallbackReason"] = "SLM confidence too low";

        return Task.FromResult(userGoal);
    }

    /// <summary>
    /// Enrich goal context with additional information
    /// </summary>
    private Task EnrichContextAsync(UserGoal userGoal, UserRequest request)
    {
        try
        {
            // Add timestamp and tracking information
            userGoal.Context["extractedAt"] = DateTime.UtcNow.ToString("O");
            userGoal.Context["originalInput"] = request.UserInput;

            // Extract entities (users, groups, computers) mentioned in the request
            var entities = ExtractEntities(request.UserInput);
            if (entities.Any())
            {
                userGoal.Context["mentionedEntities"] = string.Join(",", entities);
            }

            // Add environment-specific context
            if (request.Environment == "production")
            {
                userGoal.Constraints.Add("Requires extra caution in production environment");
            }

            // Add priority-based constraints
            if (request.Priority == "high" || request.Priority == "critical")
            {
                userGoal.Constraints.Add("High priority - expedited processing required");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error enriching context for goal {GoalId}", userGoal.GoalId);
        }
        
        return Task.CompletedTask;
    }

    #region Helper Methods

    /// <summary>
    /// Query RAG database for scope filtering examples
    /// </summary>
    private async Task<List<RagScopeResult>> QueryRagForScopeExamplesAsync(string userInput)
    {
        try
        {
            var ragRequest = new
            {
                query = userInput,
                type = "scope_examples",
                maxResults = 5,
                minSimilarity = 0.7
            };

            using var httpClient = _httpClientFactory.CreateClient("RagService");
            var json = JsonSerializer.Serialize(ragRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("/api/rag/query", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var ragResponse = JsonSerializer.Deserialize<RagQueryResponse>(responseContent);

                return ragResponse?.Results?.Select(r => new RagScopeResult
                {
                    Content = r.Content,
                    Confidence = r.Similarity,
                    IsITOperation = r.Metadata.GetValueOrDefault("isITOperation", "true").ToString() == "true"
                }).ToList() ?? new List<RagScopeResult>();
            }

            return new List<RagScopeResult>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error querying RAG for scope examples");
            return new List<RagScopeResult>();
        }
    }

    /// <summary>
    /// Query RAG database for intent classification examples
    /// </summary>
    private async Task<List<RagIntentResult>> QueryRagForIntentExamplesAsync(string userInput)
    {
        try
        {
            var ragRequest = new
            {
                query = userInput,
                type = "intent_examples",
                maxResults = 10,
                minSimilarity = 0.6
            };

            using var httpClient = _httpClientFactory.CreateClient("RagService");
            var json = JsonSerializer.Serialize(ragRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("/api/rag/query", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var ragResponse = JsonSerializer.Deserialize<RagQueryResponse>(responseContent);

                return ragResponse?.Results?.Select(r => new RagIntentResult
                {
                    Query = r.Content ?? string.Empty,
                    Category = r.Metadata?.GetValueOrDefault("category", "system_administration")?.ToString() ?? "system_administration",
                    Confidence = r.Similarity
                }).ToList() ?? new List<RagIntentResult>();
            }

            return new List<RagIntentResult>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error querying RAG for intent examples");
            return new List<RagIntentResult>();
        }
    }

    /// <summary>
    /// Query RAG database for goal extraction examples
    /// </summary>
    private async Task<List<RagGoalResult>> QueryRagForGoalExamplesAsync(string userInput, string intentCategory)
    {
        try
        {
            var ragRequest = new
            {
                query = userInput,
                type = "goal_examples",
                category = intentCategory,
                maxResults = 8,
                minSimilarity = 0.6
            };

            using var httpClient = _httpClientFactory.CreateClient("RagService");
            var json = JsonSerializer.Serialize(ragRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("/api/rag/query", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var ragResponse = JsonSerializer.Deserialize<RagQueryResponse>(responseContent);

                return ragResponse?.Results?.Select(r => new RagGoalResult
                {
                    Query = r.Content ?? string.Empty,
                    ExtractedGoal = r.Metadata?.GetValueOrDefault("extractedGoal", "")?.ToString() ?? string.Empty,
                    SuccessCriteria = r.Metadata?.GetValueOrDefault("successCriteria", "")?.ToString() ?? string.Empty,
                    Constraints = r.Metadata?.GetValueOrDefault("constraints", "")?.ToString()?.Split(',').ToList() ?? new List<string>(),
                    Confidence = r.Similarity
                }).ToList() ?? new List<RagGoalResult>();
            }

            return new List<RagGoalResult>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error querying RAG for goal examples");
            return new List<RagGoalResult>();
        }
    }

    /// <summary>
    /// Call LLM for complex reasoning and analysis
    /// </summary>
    private async Task<string> CallLlmAsync(string prompt)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(10); // Shorter timeout to avoid hanging

            var requestBody = new
            {
                model = "Phi-4-mini-instruct-cuda-gpu",
                messages = new[]
                {
                    new { role = "system", content = "You are an IT expert. Answer concisely." },
                    new { role = "user", content = prompt }
                },
                max_tokens = 4096,
                temperature = 0.7f,
                top_k = 40,
                top_p = 0.9f,
                stream = false
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("http://10.226.71.65:5273/v1/chat/completions", content);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("LLM service returned {StatusCode}: {ReasonPhrase}",
                    response.StatusCode, response.ReasonPhrase);
                throw new HttpRequestException($"LLM service error: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            _logger.LogDebug("Raw LLM response: {Response}", responseContent);

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            var llmResponse = JsonSerializer.Deserialize<LlmResponse>(responseContent, options);

            var result = llmResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? "";

            _logger.LogDebug("LLM response length: {Length} characters", result.Length);
            _logger.LogDebug("Parsed LLM content: {Content}", result);

            return result;
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogWarning(ex, "LLM service request timed out");
            return "LLM service timeout - using fallback response";
        }
        catch (HttpRequestException ex)
        {
            _logger.LogWarning(ex, "LLM service connection failed");
            return "LLM service unavailable - using fallback response";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling LLM service");
            return "LLM service error - using fallback response";
        }
    }

    private Dictionary<string, object?> ParseLlmJsonResponse(string response)
    {
        try
        {
            // Clean up the response - remove any markdown formatting
            var cleanResponse = response.Trim();
            if (cleanResponse.StartsWith("```json"))
            {
                cleanResponse = cleanResponse.Substring(7);
            }
            if (cleanResponse.EndsWith("```"))
            {
                cleanResponse = cleanResponse.Substring(0, cleanResponse.Length - 3);
            }

            return JsonSerializer.Deserialize<Dictionary<string, object?>>(cleanResponse) ?? new();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error parsing SLM JSON response: {Response}", response);
            return new Dictionary<string, object?>();
        }
    }

    private List<string> ParseStringArray(object? value)
    {
        if (value is JsonElement element && element.ValueKind == JsonValueKind.Array)
        {
            return element.EnumerateArray()
                .Where(e => e.ValueKind == JsonValueKind.String)
                .Select(e => e.GetString() ?? "")
                .Where(s => !string.IsNullOrEmpty(s))
                .ToList();
        }
        return new List<string>();
    }





    private List<string> ExtractEntities(string userInput)
    {
        var entities = new List<string>();

        // Simple regex patterns for common entities
        var patterns = new Dictionary<string, string>
        {
            { "username", @"\b[a-zA-Z][a-zA-Z0-9._-]{2,19}\b" },
            { "email", @"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b" },
            { "computer", @"\b[A-Za-z0-9-]{3,15}\b(?=\s|$)" },
            { "group", @"\b(?:group|grp)[\s_-]?[A-Za-z0-9_-]+\b" }
        };

        foreach (var pattern in patterns)
        {
            var matches = Regex.Matches(userInput, pattern.Value, RegexOptions.IgnoreCase);
            entities.AddRange(matches.Cast<Match>().Select(m => m.Value));
        }

        return entities.Distinct().ToList();
    }

    private string GenerateCacheKey(string userInput)
    {
        // Simple hash-based cache key
        return $"intent_{userInput.GetHashCode():X}";
    }

    private UserGoal CreateErrorGoal(string errorMessage, double confidence)
    {
        return new UserGoal
        {
            PrimaryObjective = "Error in intent analysis",
            SuccessCriteria = "N/A",
            UrgencyLevel = "normal",
            ExtractionConfidence = confidence,
            ExtractionMethod = "error",
            Context = new Dictionary<string, string>
            {
                { "error", errorMessage },
                { "timestamp", DateTime.UtcNow.ToString("O") }
            }
        };
    }



    #endregion
}

/// <summary>
/// RAG result for scope filtering
/// </summary>
public class RagScopeResult
{
    public string Content { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public bool IsITOperation { get; set; }
}

/// <summary>
/// RAG result for intent classification
/// </summary>
public class RagIntentResult
{
    public string Query { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public double Confidence { get; set; }
}

/// <summary>
/// RAG result for goal extraction
/// </summary>
public class RagGoalResult
{
    public string Query { get; set; } = string.Empty;
    public string ExtractedGoal { get; set; } = string.Empty;
    public string SuccessCriteria { get; set; } = string.Empty;
    public List<string> Constraints { get; set; } = new();
    public double Confidence { get; set; }
}

/// <summary>
/// RAG query response structure
/// </summary>
public class RagQueryResponse
{
    public List<RagResult> Results { get; set; } = new();
    public int TotalCount { get; set; }
    public double QueryTime { get; set; }
}

/// <summary>
/// Individual RAG result
/// </summary>
public class RagResult
{
    public string Content { get; set; } = string.Empty;
    public double Similarity { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}
