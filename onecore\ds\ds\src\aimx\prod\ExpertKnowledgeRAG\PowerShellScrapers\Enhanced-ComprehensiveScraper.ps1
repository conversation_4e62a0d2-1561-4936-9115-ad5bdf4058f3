# Enhanced Comprehensive PowerShell AD Knowledge Scraper
# Orchestrates all enhanced scrapers to build massive offline knowledge base
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxMicrosoftCmdlets = 20,
    
    [Parameter(Mandatory = $false)]
    [int]$MaxStackOverflowQuestions = 15,
    
    [Parameter(Mandatory = $false)]
    [int]$MaxGitHubRepos = 8,
    
    [Parameter(Mandatory = $false)]
    [switch]$SkipMicrosoft = $false,
    
    [Parameter(Mandatory = $false)]
    [switch]$SkipStackOverflow = $false,
    
    [Parameter(Mandatory = $false)]
    [switch]$SkipGitHub = $false
)

Write-Host "Enhanced Comprehensive PowerShell AD Knowledge Scraper" -ForegroundColor Cyan
Write-Host "Building Massive Offline Knowledge Base with Full Content" -ForegroundColor Cyan
Write-Host "=" * 70 -ForegroundColor Gray

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    if (-not $result) {
        throw "Failed to initialize scraping framework"
    }
    
    Write-Host "Enhanced scraping framework initialized successfully" -ForegroundColor Green
    
    # Track all patterns and statistics
    $allPatterns = @()
    $scrapingResults = @{
        Microsoft = @{ Patterns = @(); Success = $false; Error = $null; ContentSize = 0; ProcessingTime = 0 }
        StackOverflow = @{ Patterns = @(); Success = $false; Error = $null; ContentSize = 0; ProcessingTime = 0 }
        GitHub = @{ Patterns = @(); Success = $false; Error = $null; ContentSize = 0; ProcessingTime = 0 }
    }
    
    $totalStartTime = Get-Date
    
    # 1. Enhanced Microsoft Docs Scraping
    if (-not $SkipMicrosoft) {
        Write-Host "`nPHASE 1: Enhanced Microsoft Docs Scraping" -ForegroundColor Yellow
        Write-Host "Capturing full cmdlet documentation, parameters, examples, and syntax" -ForegroundColor Gray
        Write-Host "-" * 60 -ForegroundColor Gray
        
        $startTime = Get-Date
        try {
            Write-Host "Executing Enhanced Microsoft Docs Scraper..." -ForegroundColor White
            
            # Execute enhanced Microsoft Docs scraper
            $microsoftProcess = Start-Process -FilePath "powershell.exe" -ArgumentList @(
                "-ExecutionPolicy", "Bypass",
                "-File", ".\Enhanced-MicrosoftDocsScraper.ps1",
                "-MaxCmdlets", $MaxMicrosoftCmdlets,
                "-ConfigPath", $ConfigPath
            ) -Wait -PassThru -NoNewWindow -RedirectStandardOutput "temp_microsoft_output.txt" -RedirectStandardError "temp_microsoft_error.txt"
            
            if ($microsoftProcess.ExitCode -eq 0) {
                # Load the results
                $microsoftFiles = Get-ChildItem -Path ".\ScrapedData" -Filter "scraped_enhanced_microsoft_docs_*.json" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
                if ($microsoftFiles) {
                    $microsoftData = Get-Content $microsoftFiles.FullName | ConvertFrom-Json
                    $scrapingResults.Microsoft.Patterns = $microsoftData.Patterns
                    $scrapingResults.Microsoft.Success = $true
                    $scrapingResults.Microsoft.ContentSize = ($microsoftData.Patterns | ForEach-Object { $_.Content.Length }) | Measure-Object -Sum | Select-Object -ExpandProperty Sum
                    $allPatterns += $microsoftData.Patterns
                    
                    Write-Host "Microsoft Docs: $($microsoftData.Patterns.Count) patterns, $([Math]::Round($scrapingResults.Microsoft.ContentSize / 1024, 2)) KB" -ForegroundColor Green
                } else {
                    throw "No Microsoft Docs output file found"
                }
            } else {
                throw "Microsoft Docs scraper failed with exit code $($microsoftProcess.ExitCode)"
            }
        }
        catch {
            $scrapingResults.Microsoft.Error = $_.Exception.Message
            Write-Host "Microsoft Docs scraping failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        finally {
            $scrapingResults.Microsoft.ProcessingTime = (Get-Date) - $startTime
            # Cleanup temp files
            if (Test-Path "temp_microsoft_output.txt") { Remove-Item "temp_microsoft_output.txt" -Force }
            if (Test-Path "temp_microsoft_error.txt") { Remove-Item "temp_microsoft_error.txt" -Force }
        }
    } else {
        Write-Host "`nPHASE 1: Enhanced Microsoft Docs Scraping - SKIPPED" -ForegroundColor Yellow
    }
    
    # 2. Enhanced Stack Overflow Scraping
    if (-not $SkipStackOverflow) {
        Write-Host "`nPHASE 2: Enhanced Stack Overflow Scraping" -ForegroundColor Yellow
        Write-Host "Capturing full Q&A content, all answers, code blocks, and solutions" -ForegroundColor Gray
        Write-Host "-" * 60 -ForegroundColor Gray
        
        $startTime = Get-Date
        try {
            Write-Host "Executing Enhanced Stack Overflow Scraper..." -ForegroundColor White
            
            # Execute enhanced Stack Overflow scraper
            $stackOverflowProcess = Start-Process -FilePath "powershell.exe" -ArgumentList @(
                "-ExecutionPolicy", "Bypass",
                "-File", ".\Enhanced-StackOverflowScraper-Working.ps1",
                "-MaxQuestions", $MaxStackOverflowQuestions,
                "-MinScore", "10",
                "-ConfigPath", $ConfigPath
            ) -Wait -PassThru -NoNewWindow -RedirectStandardOutput "temp_stackoverflow_output.txt" -RedirectStandardError "temp_stackoverflow_error.txt"
            
            if ($stackOverflowProcess.ExitCode -eq 0) {
                # Load the results
                $stackOverflowFiles = Get-ChildItem -Path ".\ScrapedData" -Filter "scraped_enhanced_stackoverflow_*.json" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
                if ($stackOverflowFiles) {
                    $stackOverflowData = Get-Content $stackOverflowFiles.FullName | ConvertFrom-Json
                    $scrapingResults.StackOverflow.Patterns = $stackOverflowData.Patterns
                    $scrapingResults.StackOverflow.Success = $true
                    $scrapingResults.StackOverflow.ContentSize = ($stackOverflowData.Patterns | ForEach-Object { $_.Content.Length }) | Measure-Object -Sum | Select-Object -ExpandProperty Sum
                    $allPatterns += $stackOverflowData.Patterns
                    
                    Write-Host "Stack Overflow: $($stackOverflowData.Patterns.Count) patterns, $([Math]::Round($scrapingResults.StackOverflow.ContentSize / 1024, 2)) KB" -ForegroundColor Green
                } else {
                    throw "No Stack Overflow output file found"
                }
            } else {
                throw "Stack Overflow scraper failed with exit code $($stackOverflowProcess.ExitCode)"
            }
        }
        catch {
            $scrapingResults.StackOverflow.Error = $_.Exception.Message
            Write-Host "Stack Overflow scraping failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        finally {
            $scrapingResults.StackOverflow.ProcessingTime = (Get-Date) - $startTime
            # Cleanup temp files
            if (Test-Path "temp_stackoverflow_output.txt") { Remove-Item "temp_stackoverflow_output.txt" -Force }
            if (Test-Path "temp_stackoverflow_error.txt") { Remove-Item "temp_stackoverflow_error.txt" -Force }
        }
    } else {
        Write-Host "`nPHASE 2: Enhanced Stack Overflow Scraping - SKIPPED" -ForegroundColor Yellow
    }
    
    # 3. Enhanced GitHub Scraping
    if (-not $SkipGitHub) {
        Write-Host "`nPHASE 3: Enhanced GitHub Scraping" -ForegroundColor Yellow
        Write-Host "Capturing actual PowerShell scripts, README content, and code files" -ForegroundColor Gray
        Write-Host "-" * 60 -ForegroundColor Gray
        
        $startTime = Get-Date
        try {
            Write-Host "Executing Enhanced GitHub Scraper..." -ForegroundColor White
            
            # Execute enhanced GitHub scraper
            $githubProcess = Start-Process -FilePath "powershell.exe" -ArgumentList @(
                "-ExecutionPolicy", "Bypass",
                "-File", ".\Enhanced-GitHubScraper.ps1",
                "-MaxRepos", $MaxGitHubRepos,
                "-MinStars", "50",
                "-MaxFilesPerRepo", "10",
                "-ConfigPath", $ConfigPath
            ) -Wait -PassThru -NoNewWindow -RedirectStandardOutput "temp_github_output.txt" -RedirectStandardError "temp_github_error.txt"
            
            if ($githubProcess.ExitCode -eq 0) {
                # Load the results
                $githubFiles = Get-ChildItem -Path ".\ScrapedData" -Filter "scraped_enhanced_github_*.json" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
                if ($githubFiles) {
                    $githubData = Get-Content $githubFiles.FullName | ConvertFrom-Json
                    $scrapingResults.GitHub.Patterns = $githubData.Patterns
                    $scrapingResults.GitHub.Success = $true
                    $scrapingResults.GitHub.ContentSize = ($githubData.Patterns | ForEach-Object { $_.Content.Length }) | Measure-Object -Sum | Select-Object -ExpandProperty Sum
                    $allPatterns += $githubData.Patterns
                    
                    Write-Host "GitHub: $($githubData.Patterns.Count) patterns, $([Math]::Round($scrapingResults.GitHub.ContentSize / 1024, 2)) KB" -ForegroundColor Green
                } else {
                    throw "No GitHub output file found"
                }
            } else {
                throw "GitHub scraper failed with exit code $($githubProcess.ExitCode)"
            }
        }
        catch {
            $scrapingResults.GitHub.Error = $_.Exception.Message
            Write-Host "GitHub scraping failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        finally {
            $scrapingResults.GitHub.ProcessingTime = (Get-Date) - $startTime
            # Cleanup temp files
            if (Test-Path "temp_github_output.txt") { Remove-Item "temp_github_output.txt" -Force }
            if (Test-Path "temp_github_error.txt") { Remove-Item "temp_github_error.txt" -Force }
        }
    } else {
        Write-Host "`nPHASE 3: Enhanced GitHub Scraping - SKIPPED" -ForegroundColor Yellow
    }
    
    $totalProcessingTime = (Get-Date) - $totalStartTime
    
    # Final Comprehensive Results
    Write-Host "`nENHANCED COMPREHENSIVE SCRAPING RESULTS" -ForegroundColor Cyan
    Write-Host "=" * 70 -ForegroundColor Gray
    
    $totalContentSize = $scrapingResults.Microsoft.ContentSize + $scrapingResults.StackOverflow.ContentSize + $scrapingResults.GitHub.ContentSize
    
    Write-Host "Microsoft Docs:  " -NoNewline
    if ($scrapingResults.Microsoft.Success) {
        Write-Host "$($scrapingResults.Microsoft.Patterns.Count) patterns, $([Math]::Round($scrapingResults.Microsoft.ContentSize / 1024, 2)) KB" -ForegroundColor Green -NoNewline
        Write-Host " ($([Math]::Round($scrapingResults.Microsoft.ProcessingTime.TotalSeconds, 1))s)" -ForegroundColor Gray
    } else {
        Write-Host "FAILED - $($scrapingResults.Microsoft.Error)" -ForegroundColor Red
    }
    
    Write-Host "Stack Overflow: " -NoNewline
    if ($scrapingResults.StackOverflow.Success) {
        Write-Host "$($scrapingResults.StackOverflow.Patterns.Count) patterns, $([Math]::Round($scrapingResults.StackOverflow.ContentSize / 1024, 2)) KB" -ForegroundColor Green -NoNewline
        Write-Host " ($([Math]::Round($scrapingResults.StackOverflow.ProcessingTime.TotalSeconds, 1))s)" -ForegroundColor Gray
    } else {
        Write-Host "FAILED - $($scrapingResults.StackOverflow.Error)" -ForegroundColor Red
    }
    
    Write-Host "GitHub:         " -NoNewline
    if ($scrapingResults.GitHub.Success) {
        Write-Host "$($scrapingResults.GitHub.Patterns.Count) patterns, $([Math]::Round($scrapingResults.GitHub.ContentSize / 1024, 2)) KB" -ForegroundColor Green -NoNewline
        Write-Host " ($([Math]::Round($scrapingResults.GitHub.ProcessingTime.TotalSeconds, 1))s)" -ForegroundColor Gray
    } else {
        Write-Host "FAILED - $($scrapingResults.GitHub.Error)" -ForegroundColor Red
    }
    
    Write-Host "`nTOTAL PATTERNS: $($allPatterns.Count)" -ForegroundColor Cyan
    Write-Host "TOTAL OFFLINE CONTENT: $([Math]::Round($totalContentSize / 1024, 2)) KB" -ForegroundColor Cyan
    Write-Host "TOTAL PROCESSING TIME: $([Math]::Round($totalProcessingTime.TotalMinutes, 1)) minutes" -ForegroundColor Cyan
    
    # Save massive comprehensive results
    if ($allPatterns.Count -gt 0) {
        $outputFile = Save-ScrapingResults -Patterns $allPatterns -SourceType "enhanced_comprehensive"
        Write-Host "`nMASSIVE OFFLINE KNOWLEDGE BASE SAVED TO:" -ForegroundColor Green
        Write-Host "$outputFile" -ForegroundColor Cyan
        
        # Comprehensive statistics
        $domainStats = $allPatterns | Group-Object Domain | Sort-Object Count -Descending
        $sourceStats = $allPatterns | Group-Object { $_.Sources[0].SourceType } | Sort-Object Count -Descending
        $operationStats = $allPatterns | Group-Object Operation | Sort-Object Count -Descending
        
        Write-Host "`nCOMPREHENSIVE KNOWLEDGE BASE STATISTICS:" -ForegroundColor Yellow
        Write-Host "Domain distribution:" -ForegroundColor Gray
        foreach ($domain in $domainStats) {
            Write-Host "  $($domain.Name): $($domain.Count) patterns" -ForegroundColor Gray
        }
        
        Write-Host "Source distribution:" -ForegroundColor Gray
        foreach ($source in $sourceStats) {
            Write-Host "  $($source.Name): $($source.Count) patterns" -ForegroundColor Gray
        }
        
        Write-Host "Operation distribution:" -ForegroundColor Gray
        foreach ($operation in $operationStats) {
            Write-Host "  $($operation.Name): $($operation.Count) patterns" -ForegroundColor Gray
        }
        
        # Quality metrics
        $avgCredibility = ($allPatterns | ForEach-Object { $_.CredibilityScore }) | Measure-Object -Average
        $avgRelevance = ($allPatterns | ForEach-Object { $_.RelevanceScore }) | Measure-Object -Average
        
        Write-Host "`nQUALITY METRICS:" -ForegroundColor Yellow
        Write-Host "  Average credibility score: $([Math]::Round($avgCredibility.Average, 2))" -ForegroundColor Gray
        Write-Host "  Average relevance score: $([Math]::Round($avgRelevance.Average, 2))" -ForegroundColor Gray
        Write-Host "  Content density: $([Math]::Round($totalContentSize / $allPatterns.Count, 0)) bytes per pattern" -ForegroundColor Gray
    }
    
    Write-Host "`nENHANCED COMPREHENSIVE SCRAPING COMPLETED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "Ready for AIMX RAG integration with massive offline knowledge base!" -ForegroundColor Green
}
catch {
    Write-Host "`nEnhanced comprehensive scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    exit 1
}
