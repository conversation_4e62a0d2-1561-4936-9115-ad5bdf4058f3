# Data Consolidation Script - Merge Clean Knowledge Base
# Combines all clean scraped data into a single RAG-optimized knowledge base
param(
    [Parameter(Mandatory = $false)]
    [string]$InputDirectory = "./ScrapedData",
    
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "./ScrapedData/consolidated_knowledge_base_$(Get-Date -Format 'yyyyMMdd_HHmmss').json",
    
    [Parameter(Mandatory = $false)]
    [switch]$RemoveDuplicates = $true,
    
    [Parameter(Mandatory = $false)]
    [int]$MinContentLength = 100
)

Write-Host "Data Consolidation Script - Clean Knowledge Base" -ForegroundColor Cyan
Write-Host "Merging all clean scraped data for RAG usage" -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Gray

# Helper function to calculate content similarity
function Get-ContentSimilarity {
    param(
        [string]$Content1,
        [string]$Content2
    )
    
    if (-not $Content1 -or -not $Content2) { return 0 }
    
    # Simple similarity based on common words
    $words1 = ($Content1 -split '\s+' | Where-Object { $_.Length -gt 3 }) | Select-Object -Unique
    $words2 = ($Content2 -split '\s+' | Where-Object { $_.Length -gt 3 }) | Select-Object -Unique
    
    if ($words1.Count -eq 0 -or $words2.Count -eq 0) { return 0 }
    
    $commonWords = $words1 | Where-Object { $words2 -contains $_ }
    $similarity = $commonWords.Count / [Math]::Max($words1.Count, $words2.Count)
    
    return $similarity
}

# Helper function to merge similar entries
function Merge-SimilarEntries {
    param([array]$Entries)
    
    $merged = @()
    $processed = @{}
    
    for ($i = 0; $i -lt $Entries.Count; $i++) {
        if ($processed.ContainsKey($i)) { continue }
        
        $currentEntry = $Entries[$i]
        $similarEntries = @($currentEntry)
        $processed[$i] = $true
        
        # Find similar entries
        for ($j = $i + 1; $j -lt $Entries.Count; $j++) {
            if ($processed.ContainsKey($j)) { continue }
            
            $otherEntry = $Entries[$j]
            $similarity = Get-ContentSimilarity $currentEntry.content $otherEntry.content
            
            # If very similar (>80%), merge them
            if ($similarity -gt 0.8) {
                $similarEntries += $otherEntry
                $processed[$j] = $true
            }
        }
        
        # Merge similar entries
        if ($similarEntries.Count -gt 1) {
            Write-Host "  Merging $($similarEntries.Count) similar entries: $($currentEntry.title)" -ForegroundColor Yellow
            
            # Use the entry with highest credibility as base
            $bestEntry = $similarEntries | Sort-Object { $_.source.credibility } -Descending | Select-Object -First 1
            
            # Combine content
            $combinedContent = $bestEntry.content
            $allCodeExamples = @()
            $allCmdlets = @()
            $allTags = @()
            $allSources = @()
            
            foreach ($entry in $similarEntries) {
                $allCodeExamples += $entry.code_examples
                $allCmdlets += $entry.cmdlets
                $allTags += $entry.tags
                $allSources += $entry.source
            }
            
            $bestEntry.code_examples = $allCodeExamples | Select-Object -Unique
            $bestEntry.cmdlets = $allCmdlets | Select-Object -Unique
            $bestEntry.tags = $allTags | Select-Object -Unique
            $bestEntry.metadata.merged_sources = $allSources
            
            $merged += $bestEntry
        } else {
            $merged += $currentEntry
        }
    }
    
    return $merged
}

try {
    # Find all clean data files
    $cleanFiles = Get-ChildItem -Path $InputDirectory -Filter "clean_*.json" | Sort-Object LastWriteTime -Descending
    
    if ($cleanFiles.Count -eq 0) {
        Write-Host "No clean data files found in $InputDirectory" -ForegroundColor Red
        Write-Host "Expected files with pattern: clean_*.json" -ForegroundColor Yellow
        exit 1
    }
    
    Write-Host "Found $($cleanFiles.Count) clean data files to consolidate:" -ForegroundColor Green
    foreach ($file in $cleanFiles) {
        Write-Host "  - $($file.Name)" -ForegroundColor White
    }
    
    # Initialize consolidated knowledge base
    $consolidatedKB = @{
        schema_version = "1.0"
        consolidated_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        source_files = $cleanFiles.Name
        total_entries = 0
        entries = @()
        statistics = @{
            by_source = @{}
            by_cmdlet = @{}
            total_code_examples = 0
            total_cmdlets = 0
        }
    }
    
    $allEntries = @()
    
    # Load and combine all entries
    foreach ($file in $cleanFiles) {
        Write-Host "`nProcessing file: $($file.Name)" -ForegroundColor Yellow
        
        try {
            $data = Get-Content $file.FullName | ConvertFrom-Json
            
            if ($data.entries) {
                $validEntries = $data.entries | Where-Object { 
                    $_.content -and $_.content.Length -ge $MinContentLength 
                }
                
                Write-Host "  Loaded $($validEntries.Count) valid entries (filtered by min length: $MinContentLength)" -ForegroundColor Green
                
                # Track statistics
                $sourceType = $data.source
                if (-not $consolidatedKB.statistics.by_source.ContainsKey($sourceType)) {
                    $consolidatedKB.statistics.by_source[$sourceType] = 0
                }
                $consolidatedKB.statistics.by_source[$sourceType] += $validEntries.Count
                
                $allEntries += $validEntries
            }
        }
        catch {
            Write-Host "  Failed to load $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "`nTotal entries loaded: $($allEntries.Count)" -ForegroundColor Green
    
    # Remove duplicates and merge similar entries if requested
    if ($RemoveDuplicates -and $allEntries.Count -gt 0) {
        Write-Host "`nRemoving duplicates and merging similar entries..." -ForegroundColor Yellow
        $allEntries = Merge-SimilarEntries $allEntries
        Write-Host "After deduplication: $($allEntries.Count) entries" -ForegroundColor Green
    }
    
    # Build final statistics
    $consolidatedKB.entries = $allEntries
    $consolidatedKB.total_entries = $allEntries.Count
    
    # Calculate cmdlet statistics
    $allCmdlets = @{}
    $totalCodeExamples = 0
    
    foreach ($entry in $allEntries) {
        $totalCodeExamples += $entry.code_examples.Count
        
        foreach ($cmdlet in $entry.cmdlets) {
            if (-not $allCmdlets.ContainsKey($cmdlet)) {
                $allCmdlets[$cmdlet] = 0
            }
            $allCmdlets[$cmdlet]++
        }
    }
    
    $consolidatedKB.statistics.by_cmdlet = $allCmdlets
    $consolidatedKB.statistics.total_code_examples = $totalCodeExamples
    $consolidatedKB.statistics.total_cmdlets = $allCmdlets.Keys.Count
    
    # Save consolidated knowledge base
    Write-Host "`nSaving consolidated knowledge base..." -ForegroundColor Yellow
    $consolidatedKB | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "`nConsolidation completed successfully!" -ForegroundColor Green
    Write-Host "=" * 60 -ForegroundColor Gray
    Write-Host "Final Statistics:" -ForegroundColor Cyan
    Write-Host "  Total entries: $($consolidatedKB.total_entries)" -ForegroundColor White
    Write-Host "  Total cmdlets covered: $($consolidatedKB.statistics.total_cmdlets)" -ForegroundColor White
    Write-Host "  Total code examples: $($consolidatedKB.statistics.total_code_examples)" -ForegroundColor White
    Write-Host "  Output saved to: $OutputPath" -ForegroundColor White
    
    Write-Host "`nEntries by source:" -ForegroundColor Cyan
    foreach ($source in $consolidatedKB.statistics.by_source.Keys) {
        Write-Host "  $source : $($consolidatedKB.statistics.by_source[$source]) entries" -ForegroundColor White
    }
    
    Write-Host "`nTop 10 cmdlets by coverage:" -ForegroundColor Cyan
    $topCmdlets = $consolidatedKB.statistics.by_cmdlet.GetEnumerator() | Sort-Object Value -Descending | Select-Object -First 10
    foreach ($cmdlet in $topCmdlets) {
        Write-Host "  $($cmdlet.Key) : $($cmdlet.Value) entries" -ForegroundColor White
    }
    
    # Show sample entry
    if ($consolidatedKB.entries.Count -gt 0) {
        $sample = $consolidatedKB.entries[0]
        Write-Host "`nSample entry:" -ForegroundColor Yellow
        Write-Host "  ID: $($sample.id)" -ForegroundColor White
        Write-Host "  Title: $($sample.title)" -ForegroundColor White
        Write-Host "  Source: $($sample.source.type)" -ForegroundColor White
        Write-Host "  Content length: $($sample.content.Length) characters" -ForegroundColor White
        Write-Host "  Code examples: $($sample.code_examples.Count)" -ForegroundColor White
        Write-Host "  Cmdlets: $($sample.cmdlets -join ', ')" -ForegroundColor White
    }
}
catch {
    Write-Host "Consolidation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
