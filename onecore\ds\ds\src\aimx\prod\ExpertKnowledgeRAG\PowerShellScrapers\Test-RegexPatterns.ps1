# Test regex patterns for code extraction
$testContent = @"
Here is some PowerShell code:
```powershell
Get-ADUser -Filter "Name -eq '<PERSON>'"
Set-ADUser -Identity jsmith -Title "Manager"
```
And some more text.

Another code block:
<code>
New-ADGroup -Name "TestGroup" -GroupScope Global
</code>
"@

Write-Host "Regex Pattern Test" -ForegroundColor Cyan
Write-Host "`nTest content:" -ForegroundColor Yellow
Write-Host $testContent -ForegroundColor Gray

# Test different pattern variations
$patterns = @(
    @{ Name = "Simple backticks"; Pattern = '```([^`]+)```' },
    @{ Name = "Powershell specific"; Pattern = '```powershell\s*\n(.*?)\n```' },
    @{ Name = "Code tags"; Pattern = '<code>\s*(.*?)\s*</code>' },
    @{ Name = "Multiline powershell"; Pattern = '(?s)```powershell(.*?)```' },
    @{ Name = "Multiline code"; Pattern = '(?s)<code>(.*?)</code>' }
)

foreach ($patternInfo in $patterns) {
    Write-Host "`nTesting: $($patternInfo.Name)" -ForegroundColor Yellow
    Write-Host "Pattern: $($patternInfo.Pattern)" -ForegroundColor Gray
    
    try {
        $matches = [regex]::Matches($testContent, $patternInfo.Pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Singleline)
        Write-Host "Found $($matches.Count) matches" -ForegroundColor Green
        
        foreach ($match in $matches) {
            if ($match.Groups.Count -gt 1) {
                $code = $match.Groups[1].Value.Trim()
                Write-Host "  Code: $($code.Substring(0, [Math]::Min(50, $code.Length)))..." -ForegroundColor Cyan
            }
        }
    }
    catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nRegex test completed!" -ForegroundColor Cyan
