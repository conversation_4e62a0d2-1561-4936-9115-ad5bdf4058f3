using Microsoft.Extensions.AI;
using Microsoft.Extensions.Logging;

namespace netRag;

public class DocumentIngestionService
{
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly VectorStoreService _vectorStoreService;
    private readonly DocumentIngestionConfig _config;
    private readonly ILogger<DocumentIngestionService> _logger;

    public DocumentIngestionService(
        IEmbeddingGenerator<string, Embedding<float>> embeddingService,
        VectorStoreService vectorStoreService,
        DocumentIngestionConfig config,
        ILogger<DocumentIngestionService> logger)
    {
        _embeddingService = embeddingService;
        _vectorStoreService = vectorStoreService;
        _config = config;
        _logger = logger;
    }

    public async Task IngestDocumentAsync(string documentPath, string documentId)
    {
        if (!File.Exists(documentPath))
        {
            throw new FileNotFoundException($"Document not found: {documentPath}");
        }

        _logger.LogInformation("Starting ingestion of document: {DocumentPath}", documentPath);
        
        var content = await File.ReadAllTextAsync(documentPath);
        var chunks = ChunkText(content, _config.ChunkSize, _config.ChunkOverlap);

        _logger.LogInformation("Document split into {ChunkCount} chunks", chunks.Count);

        for (int i = 0; i < chunks.Count; i++)
        {
            var chunk = chunks[i];
            
            try
            {
                var embeddingResult = await _embeddingService.GenerateAsync(chunk);
                var embedding = embeddingResult.Vector;

                await _vectorStoreService.UpsertAsync(
                    id: Guid.NewGuid().ToString(),
                    embedding: embedding,
                    metadata: new Dictionary<string, object>
                    {
                        ["document_id"] = documentId,
                        ["chunk_index"] = i,
                        ["text"] = chunk,
                        ["document_path"] = documentPath,
                        ["chunk_length"] = chunk.Length,
                        ["ingestion_timestamp"] = DateTime.UtcNow.ToString("O")
                    }
                );

                _logger.LogDebug("Processed chunk {ChunkIndex}/{TotalChunks} for document {DocumentId}", 
                    i + 1, chunks.Count, documentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process chunk {ChunkIndex} for document {DocumentId}", 
                    i, documentId);
                throw;
            }
        }

        _logger.LogInformation("Successfully ingested document {DocumentId} with {ChunkCount} chunks", 
            documentId, chunks.Count);
    }

    public async Task IngestMultipleDocumentsAsync(Dictionary<string, string> documentPaths)
    {
        _logger.LogInformation("Starting batch ingestion of {DocumentCount} documents", documentPaths.Count);

        foreach (var kvp in documentPaths)
        {
            try
            {
                await IngestDocumentAsync(kvp.Value, kvp.Key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to ingest document {DocumentId} from path {DocumentPath}", 
                    kvp.Key, kvp.Value);
                throw;
            }
        }

        _logger.LogInformation("Completed batch ingestion of {DocumentCount} documents", documentPaths.Count);
    }

    private List<string> ChunkText(string text, int chunkSize, int overlap)
    {
        var chunks = new List<string>();
        var words = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        if (words.Length == 0)
        {
            return chunks;
        }

        for (int i = 0; i < words.Length; i += chunkSize - overlap)
        {
            var chunkWords = words.Skip(i).Take(chunkSize).ToArray();
            var chunk = string.Join(" ", chunkWords);
            chunks.Add(chunk);

            if (i + chunkSize >= words.Length)
                break;
        }

        return chunks;
    }
}
