# Test web request functionality
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing Web Request Functionality" -ForegroundColor Cyan

# Test 1: Direct PowerShell web request
Write-Host "`nTest 1: Direct PowerShell web request" -ForegroundColor Yellow
$testUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/"

try {
    $response = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 30
    Write-Host "✅ Direct request successful" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
    Write-Host "  Content length: $($response.Content.Length)" -ForegroundColor Gray
}
catch {
    Write-Host "❌ Direct request failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: With custom headers
Write-Host "`nTest 2: With custom headers" -ForegroundColor Yellow
try {
    $headers = @{
        'User-Agent' = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    $response = Invoke-WebRequest -Uri $testUrl -Headers $headers -UseBasicParsing -TimeoutSec 30
    Write-Host "✅ Request with headers successful" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
    Write-Host "  Content length: $($response.Content.Length)" -ForegroundColor Gray
}
catch {
    Write-Host "❌ Request with headers failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Framework web request function
Write-Host "`nTest 3: Framework web request function" -ForegroundColor Yellow
try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    
    if ($result) {
        Write-Host "✅ Framework initialized" -ForegroundColor Green
        
        # Test the framework function
        $response = Invoke-WebRequestWithRetry -Uri $testUrl -MaxRetries 1
        Write-Host "✅ Framework request successful" -ForegroundColor Green
        Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
        Write-Host "  Content length: $($response.Content.Length)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Framework initialization failed" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Framework request failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    
    # Check if it's a null reference
    if ($_.Exception -is [System.NullReferenceException]) {
        Write-Host "  This is a null reference exception - likely an issue with the HTTP client setup" -ForegroundColor Red
    }
}

# Test 4: Alternative URL
Write-Host "`nTest 4: Alternative URL (docs.microsoft.com)" -ForegroundColor Yellow
$altUrl = "https://docs.microsoft.com/en-us/powershell/module/activedirectory/"

try {
    $response = Invoke-WebRequest -Uri $altUrl -UseBasicParsing -TimeoutSec 30
    Write-Host "✅ Alternative URL successful" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
    Write-Host "  Content length: $($response.Content.Length)" -ForegroundColor Gray
}
catch {
    Write-Host "❌ Alternative URL failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n✅ Web request test completed!" -ForegroundColor Cyan
