/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    aimxservice.cpp

Abstract:
    Service entry point and main loop for AIMX Agent Planner and Orchestration service.

Author:

    <PERSON> (SNAKE FIGHTER) (lindakup) 06/03/2025

--*/

#include "pch.hxx"
#include "AimxRpcServer.h" // needs to be included straight after pch
#include "AimxService.h"

#include <cpprest/http_client.h>

#include "../server/RequestHandler.h"
#include "../server/McpSvrMgr.h"
#include "../server/McpToolManager.h"
#include "../server/LLMInfer.h"
#include "../server/ConversationManager.h"
#include "../server/RagServiceManager.h"

#include "../../McpServerSample/HelloMcpServer.h"
#include "../../AdMcpSvr/AdMcpSvr.h"
#include "aimxservice.cpp.tmh"

// Service name and description
#define AIMXSRV_SERVICE_NAME        L"AIMXSrv"
#define AIMXSRV_DISPLAY_NAME        L"AI Agent Planner and Orchestration Service"
#define AIMXSRV_DESCRIPTION         L"AI Agent Planner and Orchestration service"

AimxService* g_aimxsrv = nullptr;

// Service entry point for SCM
extern "C" void
WINAPI
AimxServiceMain(
    _In_ DWORD /*argc*/,
    _In_ PWSTR* /*argv*/)
/*++
Routine Description:

    Main entry point for the AIMX Agent Planner and Orchestration service.

    This function initializes the service, starts the main service loop, and handles requests.

Return Value:

    None. The service runs indefinitely until stopped by the SCM or an error occurs.
--*/
{
    HRESULT hr = S_OK;

    ASSERT(!g_aimxsrv);

    // Initialize the service
    g_aimxsrv = new AimxService();
    if (!g_aimxsrv)
    {
        // Log or handle error
        return;
    }

    // Start the service
    hr = g_aimxsrv->ServiceMain();
    if (FAILED(hr))
    {
        // Log or handle error
        delete g_aimxsrv;
        g_aimxsrv = nullptr;
        return;
    }
}

DWORD
WINAPI
StaticAimxServiceHandler(
    _In_ DWORD dwControlCode,
    _In_ DWORD /*dwEventType*/,
    _In_ LPVOID /*lpEventData*/,
    _In_ LPVOID /*lpContext*/
    )
/*++
Routine Description:
    Static service control handler function that forwards control codes to the AimxService instance.
Arguments:

    dwControlCode - The control code sent by the SCM.
    dwEventType - The type of event (not used in this implementation).
    lpEventData - Pointer to event-specific data (not used in this implementation).
    lpContext - Pointer to context data (not used in this implementation).
Return Value:
    ERROR_SUCCESS if the handler was called successfully, or an error code if the handler failed.
--*/
{
    if (g_aimxsrv)
    {
        g_aimxsrv->ServiceHandler(dwControlCode);
    }
    return ERROR_SUCCESS;
}

AimxService::AimxService()
/*++
Routine Description:
    Constructs a new AimxService instance and initializes service status fields.
Arguments:
    None.
Return Value:
    None.
--*/
{
    m_serviceStatus.dwServiceType = SERVICE_WIN32_SHARE_PROCESS;
    m_serviceStatus.dwCurrentState = SERVICE_STOPPED;
    m_serviceStatus.dwControlsAccepted = SERVICE_ACCEPT_STOP | SERVICE_ACCEPT_SHUTDOWN;
    m_serviceStatus.dwWin32ExitCode = NO_ERROR;
    m_serviceStatus.dwServiceSpecificExitCode = 0;
    m_serviceStatus.dwCheckPoint = 0;
    m_serviceStatus.dwWaitHint = 0;

    m_serviceStatusHandle = nullptr;
    m_hStopEvent = nullptr;
}

//Set service status
HRESULT AimxService::_SetStatus(DWORD dwCurrentState, DWORD dwCheckPoint, DWORD dwWaitHint)
/*++
Routine Description:
    Sets the current status of the service for the SCM.
Arguments:
    dwCurrentState - The new state of the service.
    dwCheckPoint - The checkpoint value for lengthy operations.
    dwWaitHint - Estimated time for pending operations.
Return Value:
    HRESULT indicating success or failure.
--*/
{
    TraceInfo(AimxService, "Entry (state=%u, checkpoint=%u, waitHint=%u)", dwCurrentState, dwCheckPoint, dwWaitHint);

    m_serviceStatus.dwCurrentState = dwCurrentState;
    m_serviceStatus.dwCheckPoint = dwCheckPoint;
    m_serviceStatus.dwWaitHint = dwWaitHint;

    if (!m_serviceStatusHandle)
    {
        TraceErr(AimxService, "m_serviceStatusHandle is null");
        TraceInfo(AimxService, "Exit (error)");
        return HRESULT_FROM_WIN32(ERROR_SERVICE_NOT_ACTIVE);
    }
    HRESULT hr = SetServiceStatus(m_serviceStatusHandle, &m_serviceStatus) ? S_OK : HRESULT_FROM_WIN32(GetLastError());
    if (FAILED(hr))
    {
        TraceErr(AimxService, "SetServiceStatus failed: %!HRESULT!", hr);
    }
    TraceInfo(AimxService, "_SetStatus: Exit (hr=0x%08x)", hr);
    return hr;
}

//service control handler
HRESULT AimxService::ServiceHandler(DWORD dwControlCode)
/*++
Routine Description:
    Handles service control requests from the SCM (Service Control Manager).
Arguments:
    dwControlCode - The control code sent by the SCM.
Return Value:
    HRESULT indicating success or failure.
--*/
{

    HRESULT hr = S_OK;

    auto lock = m_serviceLock.lock();

    switch (dwControlCode)
    {
        case SERVICE_CONTROL_STOP:
        case SERVICE_CONTROL_SHUTDOWN:
             TraceInfo(AimxService, "[ServiceHandler] Signaling m_hStopEvent handle: 0x%p", m_hStopEvent.get());
            _SetStatus(SERVICE_STOP_PENDING, 1, 3000);
            if (!SetEvent(m_hStopEvent.get()))
            {
                hr = HRESULT_FROM_WIN32(GetLastError());
                TraceErr(AimxService, "SetEvent(m_hStopEvent) failed: %!WINERROR!", GetLastError());
            }
            break;
        default:
            break;
    }

    return hr;
}


/*++

Routine Description:
    When the service starts up, it will remain in the SERVICE_START_PENDING 
    state until a debugger is attached. To use this, set the following reg 
    value in the registry before starting the service:
    
        HKLM\SYSTEM\CurrentControlSet\Services\AIMXSrv\Parameters
            WaitForDebugger = 1

--*/
void WaitForDebugger()
{
    do
    {
        DWORD waitForDebugger = 0;
        DWORD sizeWaitForDebugger = sizeof(waitForDebugger);

        if ((RegGetValue(HKEY_LOCAL_MACHINE, 
                AimxConstants::Registry::AIMX_REGISTRY_PARAMETERS, 
                AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_SRV_WAIT_FOR_DEUBBGER, 
                RRF_RT_DWORD,
                nullptr,
                &waitForDebugger, 
                &sizeWaitForDebugger) != ERROR_SUCCESS)  
                || (waitForDebugger == 0))
        {
            TraceInfo(AimxService, "Aimx Service: not waiting for debugger, startng now ...");
            return;
        }

        TraceInfo(AimxService, "Aimx Service: waiting for debugger attach ...");
        ::Sleep(400);

    } while (!::IsDebuggerPresent());
}

HRESULT AimxService::_StartService()
/*++

Routine Description:
    Starts the AIMX service, initializes resources, and sets the service status to running.

Arguments:
    None.

Return Value:
    HRESULT indicating success or failure.

--*/
{
    TraceInfo(AimxService, "Entry");
    HRESULT hr = S_OK;
    auto lock = m_serviceLock.lock();

    ASSERT(m_serviceStatusHandle);

    // Debugging hook - if this is enabled, the process will wait here until a debugger is attached
    WaitForDebugger();

    // Set initial service status
    hr = _SetStatus(SERVICE_START_PENDING, 1, 30*1000);
    if (FAILED(hr))
    {
        TraceErr(AimxService, "_SetStatus(SERVICE_START_PENDING) failed: %!HRESULT!", hr);
        goto Exit;
    }

    // Create stop event
    hr = m_hStopEvent.create(wil::EventOptions::ManualReset);
    if (FAILED(hr))
    {
        TraceErr(AimxService, "m_hStopEvent.create failed: %!HRESULT!", hr);
        goto Exit;
    }

    // Initialize RAG Service Manager
    TraceInfo(AimxService, "Initializing RAG Service Manager...");
    hr = RagServiceManager::Initialize();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "RagServiceManager::Initialize failed: %!HRESULT!", hr);
        goto Exit;
    }
    TraceInfo(AimxService, "RAG Service Manager initialized successfully");

    // Start RAG service before MCP registration
    TraceInfo(AimxService, "Starting RAG service...");
    hr = RagServiceManager::StartRagService();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "RagServiceManager::StartRagService failed: %!HRESULT!", hr);
        goto Exit;
    }
    TraceInfo(AimxService, "RAG service started successfully");

    // Initialize MCP Server Manager
    TraceInfo(AimxService, "Initializing MCP Server Manager...");
    hr = McpSvrMgr::Initialize();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "McpSvrMgr::Initialize failed: %!HRESULT!", hr);
        goto Exit;
    }
    TraceInfo(AimxService, "MCP Server Manager initialized successfully");

    // Initialize MCP Tool Manager with default configuration
    TraceInfo(AimxService, "Initializing MCP Tool Manager...");
    hr = McpToolManager::Initialize();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "McpToolManager::Initialize failed: %!HRESULT!", hr);
        goto Exit;
    }    
    TraceInfo(AimxService, "MCP Tool Manager initialized successfully");

    /* 
    // Register Hello MCP Server (sample Inpro server for test.)
    TraceInfo(AimxService, "Registering Hello MCP Server...");
    hr = RegisterHelloMcpServer();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "RegisterHelloMcpServer failed: %!HRESULT!", hr);
        goto Exit;
    }
    TraceInfo(AimxService, "Hello MCP Server registered successfully");

    // Register AD MCP Server (Active Directory tools)
    TraceInfo(AimxService, "Registering AD MCP Server...");
    hr = RegisterAdMcpSvr();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "RegisterAdMcpSvr failed: %!HRESULT!", hr);
        goto Exit;
    }
    TraceInfo(AimxService, "AD MCP Server registered successfully");
    */

    // Register all MCP tools with RAG service
    TraceInfo(AimxService, "Registering MCP tools with RAG service...");
    hr = McpSvrMgr::RegisterAllToolsWithRagService();
    if (FAILED(hr))
    {
        TraceWarn(AimxService, "McpSvrMgr::RegisterAllToolsWithRagService failed: %!HRESULT!", hr);
        // Continue anyway - RAG service is optional for basic functionality
    }
    else
    {
        TraceInfo(AimxService, "MCP tools registered with RAG service successfully");
    }

    // Initialize LLM Inference component
    TraceInfo(AimxService, "Initializing LLM Inference component...");
    hr = LLMInfer::Initialize();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "LLMInfer::Initialize failed: %!HRESULT!", hr);
        goto Exit;
    }
    TraceInfo(AimxService, "LLM Inference component initialized successfully");

    // Initialize Conversation Session Manager
    TraceInfo(AimxService, "Initializing Conversation Session Manager...");
    hr = ConversationSessionManager::Initialize();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "ConversationSessionManager::Initialize failed: %!HRESULT!", hr);
        goto Exit;
    }
    TraceInfo(AimxService, "Conversation Session Manager initialized successfully");

    //
    // Start the RPC server to listen for incoming connections
    // Note: RPC server should be started last, after all other initialization is complete.
    //
    TraceInfo(AimxService, "Starting RPC server...");
    hr = AimxRpcServer::RpcStartListening();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "AimxRpcServer::RpcStartListening failed: %!HRESULT!", hr);
        goto Exit;
    }
    TraceInfo(AimxService, "RPC server started successfully - ready for client connections");

    // Set service to running state
    hr = _SetStatus(SERVICE_RUNNING, 0, 0);
    if (FAILED(hr))
    {
        TraceErr(AimxService, "_SetStatus(SERVICE_RUNNING) failed: %!HRESULT!", hr);
        goto Exit;
    }
Exit:
    if (FAILED(hr))
    {
        // If initialization failed, clean up
        TraceErr(AimxService, "Service startup failed, cleaning up...");
        // Stop RPC server if it was started
        AimxRpcServer::RpcStopListening();
        // If the stop event was created, reset it
        if (m_hStopEvent)
        {
            m_hStopEvent.reset();
        }
        _SetStatus(SERVICE_STOPPED, 0, 0);
        TraceErr(AimxService, "_StartService cleanup on error: %!HRESULT!", hr);
    }
    TraceInfo(AimxService, "Exit (hr=0x%08x)", hr);
    return hr;
}

// Stop the service
HRESULT AimxService::_StopService()
/*++
Routine Description:
    Stops the AIMX service, cleans up resources, and sets the service status to stopped.
Arguments:
    None.
Return Value:
    HRESULT indicating success or failure.
--*/
{
    TraceInfo(AimxService, "Entry");
    HRESULT hr = S_OK;
    auto lock = m_serviceLock.lock();

    // Set service status to stopping
    hr = _SetStatus(SERVICE_STOP_PENDING, 1, 30*1000);
    if (FAILED(hr))
    {
        TraceErr(AimxService, "_SetStatus(SERVICE_STOP_PENDING) failed: %!HRESULT!", hr);
        goto Exit;
    }

    //
    // Stop the RPC server
    // Note: RPC server should be stopped first, before other components are uninitialized.
    //
    TraceInfo(AimxService, "Stopping RPC server...");
    hr = AimxRpcServer::RpcStopListening();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "AimxRpcServer::RpcStopListening failed: %!HRESULT!", hr);
        // Continue with cleanup even if RPC stop fails
    }
    else
    {
        TraceInfo(AimxService, "RPC server stopped successfully");
    }

    /*
    // Unregister SystemInfo MCP Server
    TraceInfo(AimxService, "Unregistering Inproc Sample MCP Server...");
    HRESULT hrUnregister = UnregisterHelloMcpServer();
    if (FAILED(hrUnregister))
    {
        TraceWarn(AimxService, "UnregisterHelloMcpServer failed: %!HRESULT!", hrUnregister);
        // Continue with cleanup even if unregistration fails
    }
    else
    {
        TraceInfo(AimxService, "SystemInfo MCP Server unregistered successfully");
    }

    // Unregister AD MCP Server
    TraceInfo(AimxService, "Unregistering AD MCP Server...");
    HRESULT hrUnregisterAd = UnregisterAdMcpSvr();
    if (FAILED(hrUnregisterAd))
    {
        TraceWarn(AimxService, "UnregisterAdMcpSvr failed: %!HRESULT!", hrUnregisterAd);
        // Continue with cleanup even if unregistration fails
    }
    else
    {
        TraceInfo(AimxService, "AD MCP Server unregistered successfully");
    }*/

    // Uninitialize Conversation Session Manager
    TraceInfo(AimxService, "Uninitializing Conversation Session Manager...");
    ConversationSessionManager::Uninitialize();
    TraceInfo(AimxService, "Conversation Session Manager uninitialized");

    // Uninitialize LLM Inference component
    TraceInfo(AimxService, "Uninitializing LLM Inference component...");
    LLMInfer::Uninitialize();
    TraceInfo(AimxService, "LLM Inference component uninitialized");

    // Uninitialize MCP Tool Manager
    TraceInfo(AimxService, "Uninitializing MCP Tool Manager...");
    McpToolManager::Uninitialize();
    TraceInfo(AimxService, "MCP Tool Manager uninitialized");

    // Uninitialize MCP Server Manager
    TraceInfo(AimxService, "Uninitializing MCP Server Manager...");
    McpSvrMgr::Uninitialize();
    TraceInfo(AimxService, "MCP Server Manager uninitialized");

    // Stop and uninitialize RAG Service Manager
    TraceInfo(AimxService, "Stopping RAG service...");
    HRESULT hrRagStop = RagServiceManager::StopRagService();
    if (FAILED(hrRagStop))
    {
        TraceWarn(AimxService, "RagServiceManager::StopRagService failed: %!HRESULT!", hrRagStop);
        // Continue with cleanup even if RAG stop fails
    }
    else
    {
        TraceInfo(AimxService, "RAG service stopped successfully");
    }

    TraceInfo(AimxService, "Uninitializing RAG Service Manager...");
    RagServiceManager::Uninitialize();
    TraceInfo(AimxService, "RAG Service Manager uninitialized");

    // Signal the stop event
    if (m_hStopEvent)
    {
        if (!SetEvent(m_hStopEvent.get()))
        {
            hr = HRESULT_FROM_WIN32(GetLastError());
            TraceErr(AimxService, "SetEvent(m_hStopEvent) failed: %!WINERROR!", GetLastError());
        }
        else
        {
            TraceInfo(AimxService, "Stop event signaled");
        }
    }

    // Set service status to stopped
    hr = _SetStatus(SERVICE_STOPPED, 0, 0);
    if (FAILED(hr))
    {
        TraceErr(AimxService, "_SetStatus(SERVICE_STOPPED) failed: %!HRESULT!", hr);
    }
Exit:
    if (FAILED(hr))
    {
        // If stopping gracefully failed, ensure the service status is set to stopped
        _SetStatus(SERVICE_STOPPED, 0, 0);
        TraceErr(AimxService, "_StopService cleanup on error: %!HRESULT!", hr);
    }
    TraceInfo(AimxService, "Exit (hr=0x%08x)", hr);
    return hr;
}

HRESULT AimxService::ServiceMain()
/*++
Routine Description:
    Main entry point for the AIMX Agent Planner and Orchestration service. Registers the service control handler, starts the service, and runs the main loop.
Arguments:
    None.
Return Value:
    HRESULT indicating success or failure.
--*/
{
    TraceInfo(AimxService, "Entry");
    HRESULT hr = S_OK;
    // Register service control handler (lock only for this critical section)
    {
        auto lock = m_serviceLock.lock();
        m_serviceStatusHandle = RegisterServiceCtrlHandlerEx(AIMXSRV_SERVICE_NAME, ::StaticAimxServiceHandler, nullptr);
    }
    if (!m_serviceStatusHandle)
    {
        TraceErr(AimxService, "RegisterServiceCtrlHandlerEx failed: %!WINERROR!", GetLastError());
        TraceInfo(AimxService, "Exiting (error)");
        return HRESULT_FROM_WIN32(GetLastError());
    }

    // Start the service
    hr = _StartService();
    if (FAILED(hr))
    {
        TraceErr(AimxService, "_StartService failed: %!HRESULT!", hr);
        TraceInfo(AimxService, "Exiting (error)");
        return hr;
    }

    // Main service loop - RPC server handles requests on separate threads
    // This thread just waits for the stop event
    TraceInfo(AimxService, "Service main loop started - RPC server handling requests");
    TraceInfo(AimxService, "[ServiceMain] Waiting on m_hStopEvent handle: 0x%p", m_hStopEvent.get());
    WaitForSingleObject(m_hStopEvent.get(), INFINITE);
    TraceInfo(AimxService, "[ServiceMain] Stop event signaled - shutting down service. Handle: 0x%p", m_hStopEvent.get());

    // Stop the service
    hr = _StopService();
    TraceInfo(AimxService, "Exit (hr=0x%08x)", hr);
    return hr;
}
