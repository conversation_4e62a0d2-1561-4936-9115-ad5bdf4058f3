# IntentPlanningService API Test Script
# Tests all endpoints of the Intent Planning Service
# Service runs on localhost:8082

param(
    [string]$BaseUrl = "http://localhost:8082",
    [string]$OutputPath = "test-results.json",
    [switch]$Verbose = $false
)

# Set UTF-8 encoding for PowerShell output
$PSDefaultParameterValues['Out-File:Encoding'] = 'utf8'

# Test configuration
$script:TestResults = @()
$script:TestCounter = 0

# Helper function to log test results
function Write-TestResult {
    param(
        [string]$TestName,
        [string]$Method,
        [string]$Endpoint,
        [int]$StatusCode,
        [object]$Response,
        [bool]$Success,
        [string]$ErrorMessage = "",
        [double]$ResponseTimeMs = 0
    )
    
    $script:TestCounter++
    $result = @{
        TestNumber = $script:TestCounter
        TestName = $TestName
        Method = $Method
        Endpoint = $Endpoint
        StatusCode = $StatusCode
        Success = $Success
        ErrorMessage = $ErrorMessage
        ResponseTimeMs = $ResponseTimeMs
        Response = $Response
        Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    }
    
    $script:TestResults += $result
    
    $status = if ($Success) { "PASS" } else { "FAIL" }
    $color = if ($Success) { "Green" } else { "Red" }
    
    Write-Host "[$script:TestCounter] $status - $TestName" -ForegroundColor $color
    if ($Verbose -and $ErrorMessage) {
        Write-Host "    Error: $ErrorMessage" -ForegroundColor Yellow
    }
    if ($Verbose -and $ResponseTimeMs -gt 0) {
        Write-Host "    Response Time: ${ResponseTimeMs}ms" -ForegroundColor Cyan
    }
}

# Helper function to make HTTP requests
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Uri,
        [object]$Body = $null,
        [hashtable]$Headers = @{}
    )
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        $requestParams = @{
            Uri = $Uri
            Method = $Method
            Headers = $Headers + @{ "Content-Type" = "application/json" }
            UseBasicParsing = $true
        }
        
        if ($Body -and $Method -ne "GET") {
            $requestParams.Body = ($Body | ConvertTo-Json -Depth 10)
        }
        
        $response = Invoke-WebRequest @requestParams
        $stopwatch.Stop()
        
        $responseObj = $null
        if ($response.Content) {
            try {
                $responseObj = $response.Content | ConvertFrom-Json
            } catch {
                $responseObj = $response.Content
            }
        }
        
        return @{
            StatusCode = $response.StatusCode
            Response = $responseObj
            Success = $true
            ErrorMessage = ""
            ResponseTimeMs = $stopwatch.ElapsedMilliseconds
        }
    }
    catch {
        $stopwatch.Stop()
        return @{
            StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
            Response = $null
            Success = $false
            ErrorMessage = $_.Exception.Message
            ResponseTimeMs = $stopwatch.ElapsedMilliseconds
        }
    }
}

# Test data
$script:TestUserId = "test-user-$(Get-Random)"
$script:TestRequestId = [System.Guid]::NewGuid().ToString()

Write-Host "Starting IntentPlanningService API Tests" -ForegroundColor Cyan
Write-Host "Base URL: $BaseUrl" -ForegroundColor Cyan
Write-Host "Test User ID: $script:TestUserId" -ForegroundColor Cyan
Write-Host "=" * 60

# Test 1: Health Check - Get Available Tools
Write-Host "`n1. Testing Tool Discovery Endpoints" -ForegroundColor Yellow

$result = Invoke-ApiRequest -Method "GET" -Uri "$BaseUrl/api/IntentPlanning/tools"
Write-TestResult -TestName "Get All Available Tools" -Method "GET" -Endpoint "/api/IntentPlanning/tools" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 2: Get Tools with Category Filter
$result = Invoke-ApiRequest -Method "GET" -Uri "$BaseUrl/api/IntentPlanning/tools?category=ad"
Write-TestResult -TestName "Get Tools by Category (AD)" -Method "GET" -Endpoint "/api/IntentPlanning/tools?category=ad" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 3: Get Tools with Capabilities Filter
$result = Invoke-ApiRequest -Method "GET" -Uri "$BaseUrl/api/IntentPlanning/tools?capabilities=user_management,group_management"
Write-TestResult -TestName "Get Tools by Capabilities" -Method "GET" -Endpoint "/api/IntentPlanning/tools?capabilities=user_management,group_management" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 4: Get Specific Tool by ID
$result = Invoke-ApiRequest -Method "GET" -Uri "$BaseUrl/api/IntentPlanning/tools/ad-powershell-tool"
Write-TestResult -TestName "Get Specific Tool by ID" -Method "GET" -Endpoint "/api/IntentPlanning/tools/ad-powershell-tool" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 5: Get Non-existent Tool
$result = Invoke-ApiRequest -Method "GET" -Uri "$BaseUrl/api/IntentPlanning/tools/non-existent-tool"
Write-TestResult -TestName "Get Non-existent Tool (Should Return 404)" -Method "GET" -Endpoint "/api/IntentPlanning/tools/non-existent-tool" `
    -StatusCode $result.StatusCode -Response $result.Response -Success ($result.StatusCode -eq 404) `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 6: Tool Health Check
Write-Host "`n2. Testing Health Check Endpoints" -ForegroundColor Yellow

$result = Invoke-ApiRequest -Method "GET" -Uri "$BaseUrl/api/IntentPlanning/health/tools"
Write-TestResult -TestName "Check Tool Health Status" -Method "GET" -Endpoint "/api/IntentPlanning/health/tools" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 7: Refresh Tool Cache
$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/tools/refresh"
Write-TestResult -TestName "Refresh Tool Cache" -Method "POST" -Endpoint "/api/IntentPlanning/tools/refresh" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 8: Intent Analysis - Valid Request
Write-Host "`n3. Testing Intent Analysis Endpoints" -ForegroundColor Yellow

$validRequest = @{
    requestId = $script:TestRequestId
    userId = $script:TestUserId
    userInput = "Create a new user account for John Smith <NAME_EMAIL> and add him to the Marketing group"
    context = @{
        department = "Marketing"
        requestType = "user_creation"
    }
    priority = "normal"
    environment = "production"
}

$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/analyze" -Body $validRequest
Write-TestResult -TestName "Analyze Intent - Valid User Creation Request" -Method "POST" -Endpoint "/api/IntentPlanning/analyze" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Store workflow for risk assessment test
$script:TestWorkflow = $null
if ($result.Success -and $result.Response.primaryWorkflow) {
    $script:TestWorkflow = $result.Response.primaryWorkflow
}

# Test 9: Intent Analysis - Complex Request
$complexRequest = @{
    requestId = [System.Guid]::NewGuid().ToString()
    userId = $script:TestUserId
    userInput = "Reset password for user jane.doe, disable the old account, create a new account with the same permissions, and send notification to the manager"
    context = @{
        urgency = "high"
        requiresApproval = $true
    }
    priority = "high"
    environment = "production"
}

$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/analyze" -Body $complexRequest
Write-TestResult -TestName "Analyze Intent - Complex Multi-step Request" -Method "POST" -Endpoint "/api/IntentPlanning/analyze" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 10: Intent Analysis - Invalid Request (Missing Required Fields)
$invalidRequest = @{
    userInput = ""
    userId = ""
}

$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/analyze" -Body $invalidRequest
Write-TestResult -TestName "Analyze Intent - Invalid Request (Should Return 400)" -Method "POST" -Endpoint "/api/IntentPlanning/analyze" `
    -StatusCode $result.StatusCode -Response $result.Response -Success ($result.StatusCode -eq 400) `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 11: Intent Analysis - Oversized Request
$oversizedRequest = @{
    requestId = [System.Guid]::NewGuid().ToString()
    userId = $script:TestUserId
    userInput = "A" * 6000  # Exceeds 5000 character limit
    priority = "normal"
    environment = "production"
}

$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/analyze" -Body $oversizedRequest
Write-TestResult -TestName "Analyze Intent - Oversized Request (Should Return 400)" -Method "POST" -Endpoint "/api/IntentPlanning/analyze" `
    -StatusCode $result.StatusCode -Response $result.Response -Success ($result.StatusCode -eq 400) `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 12: Risk Assessment - Valid Workflow
Write-Host "`n4. Testing Risk Assessment Endpoints" -ForegroundColor Yellow

if ($script:TestWorkflow) {
    $result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/assess-risk" -Body $script:TestWorkflow
    Write-TestResult -TestName "Assess Workflow Risk - Valid Workflow" -Method "POST" -Endpoint "/api/IntentPlanning/assess-risk" `
        -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
        -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs
} else {
    Write-TestResult -TestName "Assess Workflow Risk - Valid Workflow" -Method "POST" -Endpoint "/api/IntentPlanning/assess-risk" `
        -StatusCode 0 -Response $null -Success $false -ErrorMessage "No workflow available from previous test"
}

# Test 13: Risk Assessment - High Risk Workflow
$highRiskWorkflow = @{
    workflowId = [System.Guid]::NewGuid().ToString()
    workflowName = "High Risk Operations"
    description = "Workflow containing high-risk operations"
    steps = @(
        @{
            stepId = [System.Guid]::NewGuid().ToString()
            stepName = "Delete User Account"
            description = "Delete user account permanently"
            toolId = "ad-powershell-tool"
            operation = "delete_user"
            parameters = @{
                operationType = "delete_user"
                username = "test.user"
                force = $true
            }
            timeoutSeconds = 300
            retryPolicy = @{
                maxRetries = 0
                retryDelaySeconds = 0
                exponentialBackoff = $false
            }
            rollbackOperation = $null
            isOptional = $false
            executionOrder = 1
        },
        @{
            stepId = [System.Guid]::NewGuid().ToString()
            stepName = "Remove from All Groups"
            description = "Remove user from all security groups"
            toolId = "ad-powershell-tool"
            operation = "remove_from_groups"
            parameters = @{
                operationType = "remove_user_from_groups"
                username = "test.user"
                groups = @("Domain Admins", "Enterprise Admins")
            }
            timeoutSeconds = 600
            retryPolicy = @{
                maxRetries = 1
                retryDelaySeconds = 5
                exponentialBackoff = $true
            }
            rollbackOperation = $null
            isOptional = $false
            executionOrder = 2
        }
    )
    errorHandling = @{
        onFailure = "rollback"
        notifyOnError = $true
    }
    requiredPermissions = @("DOMAIN_ADMIN", "ENTERPRISE_ADMIN")
    riskLevel = "critical"
    estimatedTotalTimeSeconds = 900
    createdBy = $script:TestUserId
    approvalRequired = $true
    approvalReason = "Contains high-risk operations that could affect domain security"
    planningConfidence = 0.95
    planningMethod = "ai_rag_llm"
}

$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/assess-risk" -Body $highRiskWorkflow
Write-TestResult -TestName "Assess Workflow Risk - High Risk Workflow" -Method "POST" -Endpoint "/api/IntentPlanning/assess-risk" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 14: Risk Assessment - Invalid Workflow (Empty Steps)
$invalidWorkflow = @{
    workflowId = [System.Guid]::NewGuid().ToString()
    workflowName = "Invalid Workflow"
    description = "Workflow with no steps"
    steps = @()
    requiredPermissions = @()
    riskLevel = "low"
}

$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/assess-risk" -Body $invalidWorkflow
Write-TestResult -TestName "Assess Workflow Risk - Invalid Workflow (Should Return 400)" -Method "POST" -Endpoint "/api/IntentPlanning/assess-risk" `
    -StatusCode $result.StatusCode -Response $result.Response -Success ($result.StatusCode -eq 400) `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 15: Risk Assessment - Null Workflow
$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/assess-risk" -Body $null
Write-TestResult -TestName "Assess Workflow Risk - Null Workflow (Should Return 400)" -Method "POST" -Endpoint "/api/IntentPlanning/assess-risk" `
    -StatusCode $result.StatusCode -Response $result.Response -Success ($result.StatusCode -eq 400) `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test 16: Various Intent Analysis Scenarios
Write-Host "`n5. Testing Various Intent Analysis Scenarios" -ForegroundColor Yellow

# User Management Scenarios
$userManagementRequests = @(
    @{
        name = "Create User Account"
        input = "Create a new user account for Sarah Johnson <NAME_EMAIL> in the Sales department"
        priority = "normal"
    },
    @{
        name = "Reset User Password"
        input = "Reset password for user mike.wilson and force password change on next login"
        priority = "high"
    },
    @{
        name = "Disable User Account"
        input = "Disable the user account for former employee lisa.brown and move to disabled users OU"
        priority = "normal"
    },
    @{
        name = "Unlock User Account"
        input = "Unlock the account for david.smith who is locked out and cannot access his computer"
        priority = "high"
    }
)

foreach ($req in $userManagementRequests) {
    $requestBody = @{
        requestId = [System.Guid]::NewGuid().ToString()
        userId = $script:TestUserId
        userInput = $req.input
        priority = $req.priority
        environment = "production"
        context = @{
            category = "user_management"
        }
    }

    $result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/analyze" -Body $requestBody
    Write-TestResult -TestName "Intent Analysis - $($req.name)" -Method "POST" -Endpoint "/api/IntentPlanning/analyze" `
        -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
        -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs
}

# Group Management Scenarios
$groupManagementRequests = @(
    @{
        name = "Create Security Group"
        input = "Create a new security group called 'Finance-ReadOnly' with read-only access to finance folders"
        priority = "normal"
    },
    @{
        name = "Add User to Group"
        input = "Add user john.doe to the Marketing group and IT-Support group"
        priority = "normal"
    },
    @{
        name = "Remove User from Group"
        input = "Remove user jane.smith from the Administrators group for security compliance"
        priority = "high"
    }
)

foreach ($req in $groupManagementRequests) {
    $requestBody = @{
        requestId = [System.Guid]::NewGuid().ToString()
        userId = $script:TestUserId
        userInput = $req.input
        priority = $req.priority
        environment = "production"
        context = @{
            category = "group_management"
        }
    }

    $result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/analyze" -Body $requestBody
    Write-TestResult -TestName "Intent Analysis - $($req.name)" -Method "POST" -Endpoint "/api/IntentPlanning/analyze" `
        -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
        -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs
}

# Computer Management Scenarios
$computerManagementRequests = @(
    @{
        name = "Join Computer to Domain"
        input = "Join computer LAPTOP-001 to the company domain and move to Workstations OU"
        priority = "normal"
    },
    @{
        name = "Reset Computer Account"
        input = "Reset the computer account for SERVER-DB01 due to trust relationship issues"
        priority = "high"
    }
)

foreach ($req in $computerManagementRequests) {
    $requestBody = @{
        requestId = [System.Guid]::NewGuid().ToString()
        userId = $script:TestUserId
        userInput = $req.input
        priority = $req.priority
        environment = "production"
        context = @{
            category = "computer_management"
        }
    }

    $result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/analyze" -Body $requestBody
    Write-TestResult -TestName "Intent Analysis - $($req.name)" -Method "POST" -Endpoint "/api/IntentPlanning/analyze" `
        -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
        -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs
}

# Test 17: Performance and Load Testing
Write-Host "`n6. Testing Performance and Concurrent Requests" -ForegroundColor Yellow

$concurrentRequests = @()
for ($i = 1; $i -le 5; $i++) {
    $concurrentRequests += @{
        requestId = [System.Guid]::NewGuid().ToString()
        userId = "$script:TestUserId-concurrent-$i"
        userInput = "Create user account for test.user$i with email test.user$<EMAIL>"
        priority = "normal"
        environment = "test"
    }
}

$jobs = @()
foreach ($req in $concurrentRequests) {
    $job = Start-Job -ScriptBlock {
        param($BaseUrl, $RequestBody)

        $requestParams = @{
            Uri = "$BaseUrl/api/IntentPlanning/analyze"
            Method = "POST"
            Headers = @{ "Content-Type" = "application/json" }
            Body = ($RequestBody | ConvertTo-Json -Depth 10)
            UseBasicParsing = $true
        }

        try {
            $response = Invoke-WebRequest @requestParams
            return @{
                Success = $true
                StatusCode = $response.StatusCode
                ResponseTime = (Measure-Command { $response }).TotalMilliseconds
            }
        }
        catch {
            return @{
                Success = $false
                StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
                Error = $_.Exception.Message
                ResponseTime = 0
            }
        }
    } -ArgumentList $BaseUrl, $req

    $jobs += $job
}

# Wait for all jobs to complete
$jobResults = $jobs | Wait-Job | Receive-Job
$jobs | Remove-Job

$successfulRequests = ($jobResults | Where-Object { $_.Success }).Count
$averageResponseTime = ($jobResults | Where-Object { $_.Success } | Measure-Object -Property ResponseTime -Average).Average

Write-TestResult -TestName "Concurrent Requests Performance Test" -Method "POST" -Endpoint "/api/IntentPlanning/analyze" `
    -StatusCode 200 -Response @{ SuccessfulRequests = $successfulRequests; AverageResponseTime = $averageResponseTime } `
    -Success ($successfulRequests -eq 5) -ErrorMessage "" -ResponseTimeMs $averageResponseTime

# Test 18: Edge Cases and Error Handling
Write-Host "`n7. Testing Edge Cases and Error Handling" -ForegroundColor Yellow

# Test with special characters
$specialCharRequest = @{
    requestId = [System.Guid]::NewGuid().ToString()
    userId = $script:TestUserId
    userInput = "Create user with special characters: üñíçødé@company.com and add to group 'Special-Characters_Group'"
    priority = "normal"
    environment = "production"
}

$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/analyze" -Body $specialCharRequest
Write-TestResult -TestName "Intent Analysis - Special Characters" -Method "POST" -Endpoint "/api/IntentPlanning/analyze" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test with very long but valid input
$longValidRequest = @{
    requestId = [System.Guid]::NewGuid().ToString()
    userId = $script:TestUserId
    userInput = "Create a comprehensive user account for John Alexander Smith-Johnson <NAME_EMAIL>, assign him to multiple groups including Marketing, Sales, IT-Support, and Finance-ReadOnly, set his <NAME_EMAIL>, configure his office location as Building A Floor 3 Room 301, set his phone number as ******-123-4567, configure his department as Marketing and Sales Coordination, set his job title as Senior Marketing Sales Coordinator, and ensure he has access to the shared marketing drive, sales CRM system, and finance reporting tools with appropriate permissions for his role while maintaining security compliance and following company policies for new user account creation and group membership assignment procedures."
    priority = "normal"
    environment = "production"
}

$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/analyze" -Body $longValidRequest
Write-TestResult -TestName "Intent Analysis - Long Valid Input" -Method "POST" -Endpoint "/api/IntentPlanning/analyze" `
    -StatusCode $result.StatusCode -Response $result.Response -Success $result.Success `
    -ErrorMessage $result.ErrorMessage -ResponseTimeMs $result.ResponseTimeMs

# Test Summary and Results
Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "TEST SUMMARY" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan

$totalTests = $script:TestResults.Count
$passedTests = ($script:TestResults | Where-Object { $_.Success }).Count
$failedTests = $totalTests - $passedTests
$averageResponseTime = ($script:TestResults | Where-Object { $_.ResponseTimeMs -gt 0 } | Measure-Object -Property ResponseTimeMs -Average).Average

Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $failedTests" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passedTests / $totalTests) * 100, 2))%" -ForegroundColor Cyan
Write-Host "Average Response Time: $([math]::Round($averageResponseTime, 2))ms" -ForegroundColor Cyan

# Show failed tests
if ($failedTests -gt 0) {
    Write-Host "`nFAILED TESTS:" -ForegroundColor Red
    $script:TestResults | Where-Object { -not $_.Success } | ForEach-Object {
        Write-Host "  [$($_.TestNumber)] $($_.TestName) - $($_.ErrorMessage)" -ForegroundColor Red
    }
}

# Save detailed results to file
$detailedResults = @{
    TestSummary = @{
        TotalTests = $totalTests
        PassedTests = $passedTests
        FailedTests = $failedTests
        SuccessRate = [math]::Round(($passedTests / $totalTests) * 100, 2)
        AverageResponseTime = [math]::Round($averageResponseTime, 2)
        TestExecutionTime = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        BaseUrl = $BaseUrl
        TestUserId = $script:TestUserId
    }
    TestResults = $script:TestResults
}

$detailedResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8
Write-Host "`nDetailed test results saved to: $OutputPath" -ForegroundColor Green

# API Documentation Summary
Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "INTENT PLANNING SERVICE API DOCUMENTATION" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan

Write-Host @"
BASE URL: $BaseUrl

ENDPOINTS:

1. INTENT ANALYSIS
   POST /api/IntentPlanning/analyze
   - Analyzes user intent and generates executable workflow
   - Request Body: UserRequest object
   - Response: IntentAnalysisResult object

2. TOOL DISCOVERY
   GET /api/IntentPlanning/tools
   - Gets all available tools
   - Query Parameters: category, capabilities
   - Response: List of AvailableTool objects

   GET /api/IntentPlanning/tools/{toolId}
   - Gets specific tool by ID
   - Response: AvailableTool object

3. RISK ASSESSMENT
   POST /api/IntentPlanning/assess-risk
   - Assesses workflow risk level
   - Request Body: ExecutableWorkflow object
   - Response: WorkflowRiskAssessment object

4. HEALTH & MAINTENANCE
   GET /api/IntentPlanning/health/tools
   - Checks health status of all tools
   - Response: Dictionary of tool health statuses

   POST /api/IntentPlanning/tools/refresh
   - Refreshes tool cache
   - Response: Success message with timestamp

RESPONSE CODES:
- 200: Success
- 400: Bad Request (validation errors)
- 404: Not Found (tool not found)
- 500: Internal Server Error

"@ -ForegroundColor White

Write-Host "Test execution completed!" -ForegroundColor Green
