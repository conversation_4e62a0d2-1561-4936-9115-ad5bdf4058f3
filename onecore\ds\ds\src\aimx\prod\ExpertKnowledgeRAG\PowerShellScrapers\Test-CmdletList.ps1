# Test what cmdlets are found by the scraper
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing Cmdlet List Discovery" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    
    # Test the Get-ADCmdletList function directly
    Write-Host "`nTesting Get-ADCmdletList function..." -ForegroundColor Yellow
    
    # Load the scraper functions
    . (Join-Path $PSScriptRoot "Scrapers/MicrosoftDocsScraper.ps1")
    
    # Call the function
    $cmdlets = Get-ADCmdletList
    
    Write-Host "✅ Found $($cmdlets.Count) cmdlets" -ForegroundColor Green
    
    # Show first 20 cmdlets
    Write-Host "`nFirst 20 cmdlets found:" -ForegroundColor Yellow
    $count = 0
    foreach ($cmdlet in $cmdlets) {
        if ($count -ge 20) { break }
        Write-Host "  $($cmdlet.Name) -> $($cmdlet.Domain)/$($cmdlet.Operation)" -ForegroundColor Gray
        $count++
    }
    
    if ($cmdlets.Count -gt 20) {
        Write-Host "  ... and $($cmdlets.Count - 20) more" -ForegroundColor Gray
    }
    
    # Look for specific cmdlets
    Write-Host "`nLooking for specific cmdlets..." -ForegroundColor Yellow
    $targetCmdlets = @("Get-ADUser", "Set-ADUser", "New-ADUser", "Get-ADGroup", "Set-ADGroup")
    
    foreach ($target in $targetCmdlets) {
        $found = $cmdlets | Where-Object { $_.Name -eq $target }
        if ($found) {
            Write-Host "  ✅ Found: $target" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Missing: $target" -ForegroundColor Red
        }
    }
    
    # Check for case variations
    Write-Host "`nChecking for case variations..." -ForegroundColor Yellow
    foreach ($target in $targetCmdlets) {
        $foundCaseInsensitive = $cmdlets | Where-Object { $_.Name -like $target }
        if ($foundCaseInsensitive) {
            Write-Host "  ✅ Found (case insensitive): $($foundCaseInsensitive.Name)" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Missing (case insensitive): $target" -ForegroundColor Red
        }
    }
    
    Write-Host "`n✅ Cmdlet list test completed!" -ForegroundColor Green
}
catch {
    Write-Host "`n❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
}
