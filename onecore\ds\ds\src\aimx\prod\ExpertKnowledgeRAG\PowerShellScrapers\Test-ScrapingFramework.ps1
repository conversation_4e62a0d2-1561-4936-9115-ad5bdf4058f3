# Expert Knowledge RAG - Scraping Framework Test Suite
# Comprehensive testing for all scraping components

#Requires -Version 5.1

param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [switch]$TestFrameworkOnly = $false,
    
    [Parameter(Mandatory = $false)]
    [switch]$TestScrapersOnly = $false,
    
    [Parameter(Mandatory = $false)]
    [switch]$Verbose = $false
)

# Import the core framework
Import-Module -Name (Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1") -Force

function Test-ScrapingFramework {
    [CmdletBinding()]
    param()
    
    Write-Host "🧪 Testing Scraping Framework Core Functions" -ForegroundColor Cyan
    Write-Host "=" * 50 -ForegroundColor Cyan
    
    $testResults = @()
    
    # Test 1: Framework Initialization
    Write-Host "`n📋 Test 1: Framework Initialization" -ForegroundColor Yellow
    try {
        $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
        if ($result) {
            Write-Host "   ✅ Framework initialization: PASSED" -ForegroundColor Green
            $testResults += @{
                Test = "Framework Initialization"
                Status = "PASSED"
                Error = $null
            }
        } else {
            Write-Host "   ❌ Framework initialization: FAILED" -ForegroundColor Red
            $testResults += @{
                Test = "Framework Initialization"
                Status = "FAILED"
                Error = "Initialization returned false"
            }
        }
    }
    catch {
        Write-Host "   ❌ Framework initialization: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{
            Test = "Framework Initialization"
            Status = "FAILED"
            Error = $_.Exception.Message
        }
    }
    
    # Test 2: Web Request with Retry
    Write-Host "`n🌐 Test 2: Web Request with Retry" -ForegroundColor Yellow
    try {
        $testUrl = "https://docs.microsoft.com/en-us/powershell/module/activedirectory/"
        $response = Invoke-WebRequestWithRetry -Uri $testUrl -MaxRetries 2
        if ($response -and $response.StatusCode -eq 200) {
            Write-Host "   ✅ Web request: PASSED" -ForegroundColor Green
            $testResults += @{ Test = "Web Request"; Status = "PASSED"; Error = $null }
        } else {
            Write-Host "   ❌ Web request: FAILED - Invalid response" -ForegroundColor Red
            $testResults += @{ Test = "Web Request"; Status = "FAILED"; Error = "Invalid response" }
        }
    }
    catch {
        Write-Host "   ❌ Web request: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{ Test = "Web Request"; Status = "FAILED"; Error = $_.Exception.Message }
    }
    
    # Test 3: Code Block Extraction
    Write-Host "`n📝 Test 3: Code Block Extraction" -ForegroundColor Yellow
    try {
        $testContent = @"
Here is some PowerShell code:
```powershell
Get-ADUser -Filter "Name -eq 'John Smith'"
Set-ADUser -Identity jsmith -Title "Manager"
```
And some more text.
"@
        $codeBlocks = Extract-CodeBlocks -Content $testContent
        if ($codeBlocks.Count -gt 0 -and $codeBlocks[0] -match "Get-ADUser") {
            Write-Host "   ✅ Code extraction: PASSED ($($codeBlocks.Count) blocks found)" -ForegroundColor Green
            $testResults += @{ Test = "Code Extraction"; Status = "PASSED"; Error = $null }
        } else {
            Write-Host "   ❌ Code extraction: FAILED - No valid code blocks found" -ForegroundColor Red
            $testResults += @{ Test = "Code Extraction"; Status = "FAILED"; Error = "No valid code blocks found" }
        }
    }
    catch {
        Write-Host "   ❌ Code extraction: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{ Test = "Code Extraction"; Status = "FAILED"; Error = $_.Exception.Message }
    }
    
    # Test 4: PowerShell Command Extraction
    Write-Host "`n⚡ Test 4: PowerShell Command Extraction" -ForegroundColor Yellow
    try {
        $testContent = "Use Get-ADUser to retrieve users and Set-ADUser to modify them. Also try New-ADGroup for groups."
        $commands = Extract-PowerShellCommands -Content $testContent
        if ($commands.Count -ge 3) {
            Write-Host "   ✅ Command extraction: PASSED ($($commands.Count) commands found)" -ForegroundColor Green
            $testResults += @{ Test = "Command Extraction"; Status = "PASSED"; Error = $null }
        } else {
            Write-Host "   ❌ Command extraction: FAILED - Expected 3+ commands, found $($commands.Count)" -ForegroundColor Red
            $testResults += @{ Test = "Command Extraction"; Status = "FAILED"; Error = "Insufficient commands found" }
        }
    }
    catch {
        Write-Host "   ❌ Command extraction: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{ Test = "Command Extraction"; Status = "FAILED"; Error = $_.Exception.Message }
    }
    
    # Test 5: Content Quality Testing
    Write-Host "`n⭐ Test 5: Content Quality Testing" -ForegroundColor Yellow
    try {
        $testContent = @"
This is a comprehensive guide to PowerShell Active Directory management.
It includes examples of Get-ADUser, Set-ADUser, and other cmdlets.
```powershell
Get-ADUser -Filter * | Where-Object {$_.Enabled -eq $false}
```
This content demonstrates best practices for AD administration.
"@
        $qualityResult = Test-ContentQuality -Content $testContent -Title "PowerShell AD Guide"
        if ($qualityResult.PassesMinimumThreshold) {
            Write-Host "   ✅ Quality testing: PASSED (Score: $([math]::Round($qualityResult.Score, 3)))" -ForegroundColor Green
            $testResults += @{ Test = "Quality Testing"; Status = "PASSED"; Error = $null }
        } else {
            Write-Host "   ❌ Quality testing: FAILED - Score too low: $([math]::Round($qualityResult.Score, 3))" -ForegroundColor Red
            $testResults += @{ Test = "Quality Testing"; Status = "FAILED"; Error = "Quality score below threshold" }
        }
    }
    catch {
        Write-Host "   ❌ Quality testing: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{ Test = "Quality Testing"; Status = "FAILED"; Error = $_.Exception.Message }
    }
    
    # Test 6: Knowledge Pattern Creation
    Write-Host "`n🧠 Test 6: Knowledge Pattern Creation" -ForegroundColor Yellow
    try {
        $pattern = New-KnowledgePattern -Title "Test Pattern" -Content "Test content with Get-ADUser examples" -SourceUrl "https://test.com" -SourceType "test" -Domain "user_management" -Operation "read"
        if ($pattern -and $pattern.Id -and $pattern.Domain -eq "user_management") {
            Write-Host "   ✅ Pattern creation: PASSED" -ForegroundColor Green
            $testResults += @{ Test = "Pattern Creation"; Status = "PASSED"; Error = $null }
        } else {
            Write-Host "   ❌ Pattern creation: FAILED - Invalid pattern structure" -ForegroundColor Red
            $testResults += @{ Test = "Pattern Creation"; Status = "FAILED"; Error = "Invalid pattern structure" }
        }
    }
    catch {
        Write-Host "   ❌ Pattern creation: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{ Test = "Pattern Creation"; Status = "FAILED"; Error = $_.Exception.Message }
    }
    
    # Test 7: Results Saving
    Write-Host "`n💾 Test 7: Results Saving" -ForegroundColor Yellow
    try {
        $testPatterns = @(
            @{ Id = "test1"; Title = "Test Pattern 1"; Domain = "user_management" },
            @{ Id = "test2"; Title = "Test Pattern 2"; Domain = "group_management" }
        )
        $outputFile = Save-ScrapingResults -Patterns $testPatterns -SourceType "test"
        if (Test-Path $outputFile) {
            Write-Host "   ✅ Results saving: PASSED" -ForegroundColor Green
            $testResults += @{ Test = "Results Saving"; Status = "PASSED"; Error = $null }
            
            # Clean up test file
            Remove-Item $outputFile -Force -ErrorAction SilentlyContinue
        } else {
            Write-Host "   ❌ Results saving: FAILED - Output file not created" -ForegroundColor Red
            $testResults += @{ Test = "Results Saving"; Status = "FAILED"; Error = "Output file not created" }
        }
    }
    catch {
        Write-Host "   ❌ Results saving: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{ Test = "Results Saving"; Status = "FAILED"; Error = $_.Exception.Message }
    }
    
    return $testResults
}

function Test-IndividualScrapers {
    [CmdletBinding()]
    param()
    
    Write-Host "`n🔧 Testing Individual Scrapers" -ForegroundColor Cyan
    Write-Host "=" * 50 -ForegroundColor Cyan
    
    $scraperTests = @()
    
    # Test Microsoft Docs Scraper
    Write-Host "`n📚 Testing Microsoft Docs Scraper" -ForegroundColor Yellow
    $microsoftDocsPath = Join-Path $PSScriptRoot "Scrapers/MicrosoftDocsScraper.ps1"
    if (Test-Path $microsoftDocsPath) {
        try {
            # Test with a specific cmdlet
            $result = & $microsoftDocsPath -SpecificCmdlets @("Get-ADUser") -ConfigPath $ConfigPath
            if ($result -and $result.Count -gt 0) {
                Write-Host "   ✅ Microsoft Docs Scraper: PASSED ($($result.Count) patterns)" -ForegroundColor Green
                $scraperTests += @{
                    Scraper = "Microsoft Docs"
                    Status = "PASSED"
                    Patterns = $result.Count
                    Error = $null
                }
            } else {
                Write-Host "   ⚠️  Microsoft Docs Scraper: NO PATTERNS" -ForegroundColor Yellow
                $scraperTests += @{
                    Scraper = "Microsoft Docs"
                    Status = "NO_PATTERNS"
                    Patterns = 0
                    Error = "No patterns extracted"
                }
            }
        }
        catch {
            Write-Host "   ❌ Microsoft Docs Scraper: FAILED - $($_.Exception.Message)" -ForegroundColor Red
            $scraperTests += @{
                Scraper = "Microsoft Docs"
                Status = "FAILED"
                Patterns = 0
                Error = $_.Exception.Message
            }
        }
    } else {
        Write-Host "   ❌ Microsoft Docs Scraper: NOT FOUND" -ForegroundColor Red
        $scraperTests += @{
            Scraper = "Microsoft Docs"
            Status = "NOT_FOUND"
            Patterns = 0
            Error = "Script file not found"
        }
    }
    
    # Test Stack Overflow Scraper
    Write-Host "`n📊 Testing Stack Overflow Scraper" -ForegroundColor Yellow
    $stackOverflowPath = Join-Path $PSScriptRoot "Scrapers/StackOverflowScraper.ps1"
    if (Test-Path $stackOverflowPath) {
        try {
            # Test with limited questions
            $result = & $stackOverflowPath -MaxQuestions 5 -ConfigPath $ConfigPath
            if ($result -and $result.Count -gt 0) {
                Write-Host "   ✅ Stack Overflow Scraper: PASSED ($($result.Count) patterns)" -ForegroundColor Green
                $scraperTests += @{
                    Scraper = "Stack Overflow"
                    Status = "PASSED"
                    Patterns = $result.Count
                    Error = $null
                }
            } else {
                Write-Host "   ⚠️  Stack Overflow Scraper: NO PATTERNS" -ForegroundColor Yellow
                $scraperTests += @{
                    Scraper = "Stack Overflow"
                    Status = "NO_PATTERNS"
                    Patterns = 0
                    Error = "No patterns extracted"
                }
            }
        }
        catch {
            Write-Host "   ❌ Stack Overflow Scraper: FAILED - $($_.Exception.Message)" -ForegroundColor Red
            $scraperTests += @{
                Scraper = "Stack Overflow"
                Status = "FAILED"
                Patterns = 0
                Error = $_.Exception.Message
            }
        }
    } else {
        Write-Host "   ❌ Stack Overflow Scraper: NOT FOUND" -ForegroundColor Red
        $scraperTests += @{
            Scraper = "Stack Overflow"
            Status = "NOT_FOUND"
            Patterns = 0
            Error = "Script file not found"
        }
    }
    
    # Test GitHub Scraper
    Write-Host "`n🐙 Testing GitHub Scraper" -ForegroundColor Yellow
    $githubPath = Join-Path $PSScriptRoot "Scrapers/GitHubScraper.ps1"
    if (Test-Path $githubPath) {
        try {
            # Test with limited repositories
            $result = & $githubPath -MaxRepositories 5 -ConfigPath $ConfigPath
            if ($result -and $result.Count -gt 0) {
                Write-Host "   ✅ GitHub Scraper: PASSED ($($result.Count) patterns)" -ForegroundColor Green
                $scraperTests += @{
                    Scraper = "GitHub"
                    Status = "PASSED"
                    Patterns = $result.Count
                    Error = $null
                }
            } else {
                Write-Host "   ⚠️  GitHub Scraper: NO PATTERNS" -ForegroundColor Yellow
                $scraperTests += @{
                    Scraper = "GitHub"
                    Status = "NO_PATTERNS"
                    Patterns = 0
                    Error = "No patterns extracted"
                }
            }
        }
        catch {
            Write-Host "   ❌ GitHub Scraper: FAILED - $($_.Exception.Message)" -ForegroundColor Red
            $scraperTests += @{
                Scraper = "GitHub"
                Status = "FAILED"
                Patterns = 0
                Error = $_.Exception.Message
            }
        }
    } else {
        Write-Host "   ❌ GitHub Scraper: NOT FOUND" -ForegroundColor Red
        $scraperTests += @{
            Scraper = "GitHub"
            Status = "NOT_FOUND"
            Patterns = 0
            Error = "Script file not found"
        }
    }
    
    return $scraperTests
}

function Test-FullOrchestration {
    [CmdletBinding()]
    param()
    
    Write-Host "`n🎯 Testing Full Orchestration" -ForegroundColor Cyan
    Write-Host "=" * 50 -ForegroundColor Cyan
    
    try {
        $orchestratorPath = Join-Path $PSScriptRoot "Start-ExpertKnowledgeScraping.ps1"
        if (Test-Path $orchestratorPath) {
            Write-Host "   🚀 Running limited orchestration test..." -ForegroundColor Yellow
            
            # Run with limited scope for testing
            $result = & $orchestratorPath -ScrapersToRun @("MicrosoftDocs") -ConfigPath $ConfigPath
            
            if ($result -and $result.AllPatterns.Count -gt 0) {
                Write-Host "   ✅ Full Orchestration: PASSED ($($result.AllPatterns.Count) total patterns)" -ForegroundColor Green
                return @{ Status = "PASSED"; TotalPatterns = $result.AllPatterns.Count; Error = $null }
            } else {
                Write-Host "   ⚠️  Full Orchestration: NO PATTERNS" -ForegroundColor Yellow
                return @{ Status = "NO_PATTERNS"; TotalPatterns = 0; Error = "No patterns in orchestration result" }
            }
        } else {
            Write-Host "   ❌ Orchestrator script not found" -ForegroundColor Red
            return @{ Status = "NOT_FOUND"; TotalPatterns = 0; Error = "Orchestrator script not found" }
        }
    }
    catch {
        Write-Host "   ❌ Full Orchestration: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        return @{ Status = "FAILED"; TotalPatterns = 0; Error = $_.Exception.Message }
    }
}

function Generate-TestReport {
    [CmdletBinding()]
    param(
        [array]$FrameworkTests,
        [array]$ScraperTests,
        [hashtable]$OrchestrationTest
    )
    
    Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
    Write-Host "📊 EXPERT KNOWLEDGE RAG - TEST REPORT" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Cyan
    
    # Framework tests summary
    $frameworkPassed = ($FrameworkTests | Where-Object { $_.Status -eq "PASSED" }).Count
    $frameworkTotal = $FrameworkTests.Count
    Write-Host "`n🧪 FRAMEWORK TESTS: $frameworkPassed/$frameworkTotal PASSED" -ForegroundColor $(if ($frameworkPassed -eq $frameworkTotal) { "Green" } else { "Yellow" })
    
    foreach ($test in $FrameworkTests) {
        $status = if ($test.Status -eq "PASSED") { "✅" } else { "❌" }
        Write-Host "   $status $($test.Test)" -ForegroundColor $(if ($test.Status -eq "PASSED") { "Green" } else { "Red" })
        if ($test.Error) {
            Write-Host "      Error: $($test.Error)" -ForegroundColor Red
        }
    }
    
    # Scraper tests summary
    if ($ScraperTests.Count -gt 0) {
        $scraperPassed = ($ScraperTests | Where-Object { $_.Status -eq "PASSED" }).Count
        $scraperTotal = $ScraperTests.Count
        Write-Host "`n🔧 SCRAPER TESTS: $scraperPassed/$scraperTotal PASSED" -ForegroundColor $(if ($scraperPassed -eq $scraperTotal) { "Green" } else { "Yellow" })
        
        foreach ($test in $ScraperTests) {
            $status = switch ($test.Status) {
                "PASSED" { "✅" }
                "NO_PATTERNS" { "⚠️ " }
                default { "❌" }
            }
            Write-Host "   $status $($test.Scraper): $($test.Patterns) patterns" -ForegroundColor $(if ($test.Status -eq "PASSED") { "Green" } elseif ($test.Status -eq "NO_PATTERNS") { "Yellow" } else { "Red" })
            if ($test.Error) {
                Write-Host "      Error: $($test.Error)" -ForegroundColor Red
            }
        }
    }
    
    # Orchestration test summary
    if ($OrchestrationTest) {
        Write-Host "`n🎯 ORCHESTRATION TEST:" -ForegroundColor Yellow
        $status = switch ($OrchestrationTest.Status) {
            "PASSED" { "✅" }
            "NO_PATTERNS" { "⚠️ " }
            default { "❌" }
        }
        Write-Host "   $status Full Orchestration: $($OrchestrationTest.TotalPatterns) patterns" -ForegroundColor $(if ($OrchestrationTest.Status -eq "PASSED") { "Green" } elseif ($OrchestrationTest.Status -eq "NO_PATTERNS") { "Yellow" } else { "Red" })
        if ($OrchestrationTest.Error) {
            Write-Host "      Error: $($OrchestrationTest.Error)" -ForegroundColor Red
        }
    }
    
    # Overall assessment
    $overallSuccess = ($frameworkPassed -eq $frameworkTotal) -and 
                     ($ScraperTests.Count -eq 0 -or ($ScraperTests | Where-Object { $_.Status -in @("PASSED", "NO_PATTERNS") }).Count -eq $ScraperTests.Count) -and
                     ($OrchestrationTest -eq $null -or $OrchestrationTest.Status -in @("PASSED", "NO_PATTERNS"))
    
    Write-Host "`n🎯 OVERALL ASSESSMENT:" -ForegroundColor Yellow
    if ($overallSuccess) {
        Write-Host "   ✅ Expert Knowledge RAG system is ready for production use!" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  Some components need attention before production use." -ForegroundColor Yellow
    }
    
    Write-Host "`nGenerated at: $(Get-Date)" -ForegroundColor Gray
    Write-Host "=" * 60 -ForegroundColor Cyan
}

# Main execution
function Start-ComprehensiveTesting {
    [CmdletBinding()]
    param()
    
    Write-Host "🧪 Expert Knowledge RAG - Comprehensive Test Suite" -ForegroundColor Cyan
    Write-Host "Starting comprehensive testing..." -ForegroundColor Yellow
    
    $frameworkTests = @()
    $scraperTests = @()
    $orchestrationTest = $null
    
    # Run framework tests
    if (-not $TestScrapersOnly) {
        $frameworkTests = Test-ScrapingFramework
    }
    
    # Run scraper tests
    if (-not $TestFrameworkOnly) {
        $scraperTests = Test-IndividualScrapers
        $orchestrationTest = Test-FullOrchestration
    }
    
    # Generate report
    Generate-TestReport -FrameworkTests $frameworkTests -ScraperTests $scraperTests -OrchestrationTest $orchestrationTest
}

# Execute if run directly
if ($MyInvocation.InvocationName -ne '.') {
    Start-ComprehensiveTesting
}
