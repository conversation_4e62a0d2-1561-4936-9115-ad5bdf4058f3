# Comprehensive End-to-End Test Script for IntentPlanningService
param(
    [string]$BaseUrl = "http://localhost:8082"
)

Write-Host "Testing IntentPlanningService - Complete Flow" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "=" * 80

# Helper function to make API requests
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Uri,
        [object]$Body = $null
    )
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        $headers = @{ "Content-Type" = "application/json" }
        
        if ($Body) {
            $jsonBody = $Body | ConvertTo-Json -Depth 10
            Write-Host "Request Body: $jsonBody" -ForegroundColor Gray
            $response = Invoke-RestMethod -Uri $Uri -Method $Method -Body $jsonBody -Headers $headers -TimeoutSec 30
        } else {
            $response = Invoke-RestMethod -Uri $Uri -Method $Method -Headers $headers -TimeoutSec 30
        }
        
        $stopwatch.Stop()
        
        return @{
            Success = $true
            StatusCode = 200
            Response = $response
            ResponseTimeMs = $stopwatch.ElapsedMilliseconds
            ErrorMessage = ""
        }
    }
    catch {
        $stopwatch.Stop()
        
        $statusCode = 500
        if ($_.Exception.Response) {
            $statusCode = [int]$_.Exception.Response.StatusCode
        }
        
        return @{
            Success = $false
            StatusCode = $statusCode
            Response = $null
            ResponseTimeMs = $stopwatch.ElapsedMilliseconds
            ErrorMessage = $_.Exception.Message
        }
    }
}

# Test scenarios - both positive and negative
$testScenarios = @(
    @{
        Name = "Positive: User Account Creation"
        UserInput = "Create a new user account for John Smith in the Sales department <NAME_EMAIL>"
        ExpectedCategory = "user_management"
        ShouldSucceed = $true
    },
    @{
        Name = "Positive: Password Reset"
        UserInput = "Reset password for user alice.johnson and send temporary password via email"
        ExpectedCategory = "user_management"
        ShouldSucceed = $true
    },
    @{
        Name = "Positive: Group Management"
        UserInput = "Add user bob.wilson to the Finance security group and remove from Marketing group"
        ExpectedCategory = "group_management"
        ShouldSucceed = $true
    },
    @{
        Name = "Positive: Computer Management"
        UserInput = "Join computer DESKTOP-001 to the domain and move to Finance OU"
        ExpectedCategory = "computer_management"
        ShouldSucceed = $true
    },
    @{
        Name = "Negative: Non-IT Request"
        UserInput = "Book a conference room for tomorrow's meeting and order lunch for 10 people"
        ExpectedCategory = "none"
        ShouldSucceed = $false
    },
    @{
        Name = "Negative: Personal Request"
        UserInput = "What's the weather like today? Can you help me plan my vacation?"
        ExpectedCategory = "none"
        ShouldSucceed = $false
    },
    @{
        Name = "Edge Case: Ambiguous Request"
        UserInput = "The system is slow"
        ExpectedCategory = "troubleshooting"
        ShouldSucceed = $true
    }
)

$testResults = @()

foreach ($scenario in $testScenarios) {
    Write-Host "`n" + "=" * 80 -ForegroundColor Yellow
    Write-Host "Testing: $($scenario.Name)" -ForegroundColor Cyan
    Write-Host "Input: $($scenario.UserInput)" -ForegroundColor White
    Write-Host "Expected: $($scenario.ExpectedCategory) | Should Succeed: $($scenario.ShouldSucceed)" -ForegroundColor Gray
    Write-Host "-" * 80

    $intentRequest = @{
        userId = "test-user-$(Get-Random)"
        userInput = $scenario.UserInput
        context = @{
            department = "IT"
            role = "administrator"
            previousActions = @()
        }
        priority = "normal"
        environment = "production"
    }

    $result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/analyze" -Body $intentRequest

    $testResult = @{
        Scenario = $scenario.Name
        Input = $scenario.UserInput
        Expected = $scenario.ExpectedCategory
        ShouldSucceed = $scenario.ShouldSucceed
        Success = $result.Success
        ResponseTime = $result.ResponseTimeMs
        IntentCategory = ""
        Confidence = 0
        GoalsCount = 0
        WorkflowSteps = 0
        Issues = @()
    }

    if ($result.Success) {
        Write-Host "[PASS] API Call Successful" -ForegroundColor Green

        $testResult.IntentCategory = $result.Response.intentCategory
        $testResult.Confidence = $result.Response.confidence
        $testResult.GoalsCount = $result.Response.extractedGoals.Count

        Write-Host "Intent Category: '$($result.Response.intentCategory)'" -ForegroundColor White
        Write-Host "Confidence: $($result.Response.confidence)" -ForegroundColor White
        Write-Host "Goals Count: $($result.Response.extractedGoals.Count)" -ForegroundColor White
        Write-Host "Response Time: $($result.ResponseTimeMs)ms" -ForegroundColor Gray

        # Analyze goals
        if ($result.Response.extractedGoals.Count -gt 0) {
            foreach ($goal in $result.Response.extractedGoals) {
                Write-Host "`nGoal Details:" -ForegroundColor Yellow
                Write-Host "  Primary Objective: $($goal.primaryObjective)" -ForegroundColor White
                Write-Host "  Success Criteria: $($goal.successCriteria)" -ForegroundColor White
                Write-Host "  Urgency: $($goal.urgencyLevel)" -ForegroundColor White
                Write-Host "  Confidence: $($goal.extractionConfidence)" -ForegroundColor White

                if ($goal.constraints -and $goal.constraints.Count -gt 0) {
                    Write-Host "  Constraints: $($goal.constraints -join ', ')" -ForegroundColor Gray
                }
            }
        } else {
            $testResult.Issues += "No goals extracted"
            Write-Host "[ISSUE] No goals were extracted" -ForegroundColor Yellow
        }

        # Test workflow planning for this goal
        if ($result.Response.extractedGoals.Count -gt 0) {
            Write-Host "`nTesting Workflow Planning..." -ForegroundColor Cyan
            $workflowRequest = @{
                userId = $intentRequest.userId
                goals = $result.Response.extractedGoals
                context = $intentRequest.context
                priority = $intentRequest.priority
            }

            $workflowResult = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/plan" -Body $workflowRequest
            if ($workflowResult.Success) {
                $testResult.WorkflowSteps = $workflowResult.Response.steps.Count
                Write-Host "[PASS] Workflow Planning" -ForegroundColor Green
                Write-Host "Workflow Steps: $($workflowResult.Response.steps.Count)" -ForegroundColor White
                Write-Host "Risk Level: $($workflowResult.Response.riskAssessment.riskLevel)" -ForegroundColor White
                Write-Host "Requires Approval: $($workflowResult.Response.riskAssessment.requiresApproval)" -ForegroundColor White

                if ($workflowResult.Response.steps.Count -eq 0) {
                    $testResult.Issues += "No workflow steps generated"
                    Write-Host "[ISSUE] No workflow steps were generated" -ForegroundColor Yellow
                } else {
                    Write-Host "`nWorkflow Steps:" -ForegroundColor Yellow
                    foreach ($step in $workflowResult.Response.steps) {
                        Write-Host "  $($step.stepNumber). $($step.description)" -ForegroundColor White
                        Write-Host "     Tool: $($step.toolName) | Operation: $($step.operationType)" -ForegroundColor Gray
                        if ($step.parameters -and $step.parameters.Count -gt 0) {
                            Write-Host "     Parameters: $($step.parameters.Keys -join ', ')" -ForegroundColor Gray
                        }
                    }
                }
            } else {
                $testResult.Issues += "Workflow planning failed: $($workflowResult.ErrorMessage)"
                Write-Host "[FAIL] Workflow Planning: $($workflowResult.ErrorMessage)" -ForegroundColor Red
            }
        }

        # Validate against expectations
        if ($scenario.ShouldSucceed) {
            if ($testResult.GoalsCount -eq 0) {
                Write-Host "[VALIDATION FAIL] Expected goals but none were extracted" -ForegroundColor Red
            } elseif ($testResult.WorkflowSteps -eq 0) {
                Write-Host "[VALIDATION WARN] Goals extracted but no workflow steps generated" -ForegroundColor Yellow
            } else {
                Write-Host "[VALIDATION PASS] Goals and workflow steps generated successfully" -ForegroundColor Green
            }
        } else {
            if ($testResult.GoalsCount -gt 0) {
                Write-Host "[VALIDATION WARN] Expected no goals but $($testResult.GoalsCount) were extracted" -ForegroundColor Yellow
            } else {
                Write-Host "[VALIDATION PASS] Correctly identified as non-IT request" -ForegroundColor Green
            }
        }

    } else {
        Write-Host "[FAIL] API Call Failed: $($result.ErrorMessage)" -ForegroundColor Red
        Write-Host "Status Code: $($result.StatusCode)" -ForegroundColor Red
        $testResult.Issues += "API call failed: $($result.ErrorMessage)"
    }

    $testResults += $testResult
}

# Summary Report
Write-Host "`n" + "=" * 80 -ForegroundColor Green
Write-Host "COMPREHENSIVE TEST SUMMARY" -ForegroundColor Green
Write-Host "=" * 80 -ForegroundColor Green

$successCount = ($testResults | Where-Object { $_.Success }).Count
$totalCount = $testResults.Count

Write-Host "`nOverall Results:" -ForegroundColor Yellow
Write-Host "  Total Tests: $totalCount" -ForegroundColor White
Write-Host "  Successful API Calls: $successCount" -ForegroundColor White
Write-Host "  Failed API Calls: $($totalCount - $successCount)" -ForegroundColor White

$avgResponseTime = ($testResults | Where-Object { $_.Success } | Measure-Object -Property ResponseTime -Average).Average
if ($avgResponseTime) {
    Write-Host "  Average Response Time: $([math]::Round($avgResponseTime, 0))ms" -ForegroundColor White
}

Write-Host "`nDetailed Results:" -ForegroundColor Yellow
foreach ($result in $testResults) {
    $status = if ($result.Success) { "PASS" } else { "FAIL" }
    $statusColor = if ($result.Success) { "Green" } else { "Red" }

    Write-Host "`n$($result.Scenario):" -ForegroundColor Cyan
    Write-Host "  Status: $status" -ForegroundColor $statusColor
    if ($result.Success) {
        Write-Host "  Category: $($result.IntentCategory)" -ForegroundColor White
        Write-Host "  Goals: $($result.GoalsCount) | Workflow Steps: $($result.WorkflowSteps)" -ForegroundColor White
        Write-Host "  Response Time: $($result.ResponseTime)ms" -ForegroundColor Gray
    }
    if ($result.Issues.Count -gt 0) {
        Write-Host "  Issues:" -ForegroundColor Yellow
        foreach ($issue in $result.Issues) {
            Write-Host "    - $issue" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n" + "=" * 80 -ForegroundColor Green
Write-Host "End-to-End Testing Completed!" -ForegroundColor Green
