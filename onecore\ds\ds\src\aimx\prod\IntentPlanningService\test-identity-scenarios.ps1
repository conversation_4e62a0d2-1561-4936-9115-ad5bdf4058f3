# Test various identity resolution scenarios
$baseUrl = "http://localhost:8082"

# Test scenarios with different identity challenges
$testCases = @(
    @{
        Name = "User Creation with Full Name"
        Input = "Create a new user account for <PERSON> in the Sales department"
        ExpectedBehavior = "Should create user with proper SamAccountName generation"
    },
    @{
        Name = "Group Management with Display Name"
        Input = "Add user <PERSON> to the Finance group"
        ExpectedBehavior = "Should resolve both user and group identities"
    },
    @{
        Name = "Computer Account with Hostname"
        Input = "Reset computer account password for DESKTOP-ABC123"
        ExpectedBehavior = "Should handle computer account identity properly"
    },
    @{
        Name = "User Disable with Email-like Name"
        Input = "Disable user <NAME_EMAIL>"
        ExpectedBehavior = "Should recognize this as UserPrincipalName format"
    },
    @{
        Name = "Group Creation with Spaces"
        Input = "Create a new security group called Marketing Team"
        ExpectedBehavior = "Should handle group names with spaces properly"
    },
    @{
        Name = "User Property Update"
        Input = "Update the title for employee <PERSON> to Senior Manager"
        ExpectedBehavior = "Should resolve user identity and set title property"
    }
)

Write-Host "=== TESTING IDENTITY RESOLUTION SCENARIOS ===" -ForegroundColor Cyan
Write-Host ""

foreach ($test in $testCases) {
    Write-Host "TEST: $($test.Name)" -ForegroundColor Yellow
    Write-Host "Input: $($test.Input)" -ForegroundColor White
    Write-Host "Expected: $($test.ExpectedBehavior)" -ForegroundColor Gray
    
    try {
        $requestBody = @{
            requestId = [System.Guid]::NewGuid().ToString()
            userInput = $test.Input
            priority = "normal"
            environment = "production"
            userId = "test.user"
        } | ConvertTo-Json
        
        $startTime = Get-Date
        $response = Invoke-RestMethod -Uri "$baseUrl/api/IntentPlanning/analyze" -Method Post -Body $requestBody -ContentType "application/json"
        $endTime = Get-Date
        $responseTime = ($endTime - $startTime).TotalMilliseconds
        
        Write-Host "✅ SUCCESS" -ForegroundColor Green
        Write-Host "Response Time: $([math]::Round($responseTime))ms" -ForegroundColor Gray
        Write-Host "Steps Generated: $($response.primaryWorkflow.steps.Count)" -ForegroundColor Gray
        
        if ($response.primaryWorkflow.steps.Count -gt 0) {
            Write-Host "Generated Command:" -ForegroundColor White
            foreach ($step in $response.primaryWorkflow.steps) {
                Write-Host "  $($step.operation)" -ForegroundColor Cyan
            }
        }
        
        Write-Host ""
    }
    catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
    }
    
    Start-Sleep -Seconds 1
}

Write-Host "=== IDENTITY RESOLUTION TEST COMPLETE ===" -ForegroundColor Cyan
