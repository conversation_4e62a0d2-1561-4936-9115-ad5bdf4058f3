using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Numerics.Tensors;
using System.Text.Json;
using System.IO.Compression;

namespace NetRagService;

public class VectorStoreService
{
    private readonly ConcurrentDictionary<string, VectorPoint> _vectors;
    private readonly ILogger<VectorStoreService> _logger;
    private readonly object _lockObject = new();
    private int _vectorSize;
    private readonly string _persistenceFilePath;

    public VectorStoreService(ILogger<VectorStoreService> logger, string? persistenceFilePath = null)
    {
        _vectors = new ConcurrentDictionary<string, VectorPoint>();
        _logger = logger;
        _persistenceFilePath = persistenceFilePath ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "vector_store.bin");
    }

    public Task InitializeAsync(int vectorSize = 768)
    {
        _vectorSize = vectorSize;
        _logger.LogInformation("In-memory vector store initialized with vector size {VectorSize}", vectorSize);
        return Task.CompletedTask;
    }

    public Task UpsertAsync(string id, ReadOnlyMemory<float> embedding, Dictionary<string, object> metadata)
    {
        if (embedding.Length != _vectorSize)
        {
            throw new ArgumentException($"Embedding size {embedding.Length} does not match expected size {_vectorSize}");
        }

        var point = new VectorPoint
        {
            Id = id,
            Vector = embedding.ToArray(),
            Metadata = new Dictionary<string, object>(metadata),
            Timestamp = DateTime.UtcNow
        };

        _vectors.AddOrUpdate(id, point, (key, oldValue) => point);
        _logger.LogDebug("Upserted point with ID: {Id}", id);

        return Task.CompletedTask;
    }

    public Task<List<SearchResult>> SearchAsync(ReadOnlyMemory<float> queryEmbedding, int limit = 3)
    {
        if (queryEmbedding.Length != _vectorSize)
        {
            throw new ArgumentException($"Query embedding size {queryEmbedding.Length} does not match expected size {_vectorSize}");
        }

        var queryVector = queryEmbedding.ToArray();
        var results = new List<(VectorPoint point, float score)>();

        // Calculate cosine similarity for all vectors
        foreach (var kvp in _vectors)
        {
            var point = kvp.Value;
            var similarity = CalculateCosineSimilarity(queryVector, point.Vector);
            results.Add((point, similarity));
        }

        // Sort by similarity (descending) and take top results
        var topResults = results
            .OrderByDescending(r => r.score)
            .Take(limit)
            .Select(r => new SearchResult
            {
                Id = r.point.Id,
                Score = r.score,
                Metadata = r.point.Metadata
            })
            .ToList();

        _logger.LogDebug("Found {Count} similar points from {TotalCount} vectors", topResults.Count, _vectors.Count);
        return Task.FromResult(topResults);
    }

    public Task ClearAsync()
    {
        var count = _vectors.Count;
        _vectors.Clear();
        _logger.LogInformation("Cleared {Count} vectors from in-memory store", count);
        return Task.CompletedTask;
    }

    public Task<int> GetVectorCountAsync()
    {
        return Task.FromResult(_vectors.Count);
    }

    public Task<bool> RemoveAsync(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            _logger.LogWarning("Cannot remove vector with empty ID");
            return Task.FromResult(false);
        }

        var removed = _vectors.TryRemove(id, out var removedPoint);

        if (removed)
        {
            _logger.LogDebug("Removed vector with ID: {Id}", id);
        }
        else
        {
            _logger.LogDebug("Vector with ID {Id} not found for removal", id);
        }

        return Task.FromResult(removed);
    }

    // SaveToDiskAsync removed - VectorStoreService is now read-only
    // Use the separate DatabaseConverter utility to create database.bin files

    /// <summary>
    /// Get all vectors for database conversion utility
    /// </summary>
    public Task<List<VectorPoint>> GetAllVectorsAsync()
    {
        return Task.FromResult(_vectors.Values.ToList());
    }

    /// <summary>
    /// Save vectors to database file (used by DatabaseConverter utility)
    /// </summary>
    public async Task SaveToDatabaseFileAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("Saving {VectorCount} vectors to database file: {FilePath}", _vectors.Count, filePath);

            var vectorData = new VectorStoreData
            {
                VectorSize = _vectorSize,
                Vectors = _vectors.Values.ToList(),
                SavedAt = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(vectorData, new JsonSerializerOptions
            {
                WriteIndented = false
            });

            var jsonBytes = System.Text.Encoding.UTF8.GetBytes(json);

            // Compress the data to save space
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
            using var gzipStream = new GZipStream(fileStream, CompressionLevel.Optimal);
            await gzipStream.WriteAsync(jsonBytes);

            _logger.LogInformation("Successfully saved database file. Size: {FileSize} bytes", new FileInfo(filePath).Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save database file");
            throw;
        }
    }

    public async Task LoadFromDiskAsync()
    {
        if (!File.Exists(_persistenceFilePath))
        {
            _logger.LogInformation("No existing vector store found at {FilePath}. Starting with empty store.", _persistenceFilePath);
            return;
        }

        try
        {
            _logger.LogInformation("Loading vector store from disk: {FilePath}", _persistenceFilePath);

            using var fileStream = new FileStream(_persistenceFilePath, FileMode.Open, FileAccess.Read);
            using var gzipStream = new GZipStream(fileStream, CompressionMode.Decompress);
            using var memoryStream = new MemoryStream();

            await gzipStream.CopyToAsync(memoryStream);
            var jsonBytes = memoryStream.ToArray();
            var json = System.Text.Encoding.UTF8.GetString(jsonBytes);

            var vectorData = JsonSerializer.Deserialize<VectorStoreData>(json);

            if (vectorData == null)
            {
                throw new InvalidOperationException("Failed to deserialize vector store data");
            }

            _vectorSize = vectorData.VectorSize;
            _vectors.Clear();

            foreach (var vector in vectorData.Vectors)
            {
                _vectors.TryAdd(vector.Id, vector);
            }

            _logger.LogInformation("Successfully loaded {VectorCount} vectors from disk. Data saved at: {SavedAt}",
                _vectors.Count, vectorData.SavedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load vector store from disk");
            throw;
        }
    }

    private static float CalculateCosineSimilarity(float[] vector1, float[] vector2)
    {
        if (vector1.Length != vector2.Length)
        {
            throw new ArgumentException("Vectors must have the same length");
        }

        // Use System.Numerics.Tensors for optimized operations
        var span1 = vector1.AsSpan();
        var span2 = vector2.AsSpan();

        var dotProduct = TensorPrimitives.Dot(span1, span2);
        var magnitude1 = MathF.Sqrt(TensorPrimitives.Dot(span1, span1));
        var magnitude2 = MathF.Sqrt(TensorPrimitives.Dot(span2, span2));

        if (magnitude1 == 0 || magnitude2 == 0)
        {
            return 0;
        }

        return dotProduct / (magnitude1 * magnitude2);
    }
}

public class VectorPoint
{
    public string Id { get; set; } = string.Empty;
    public float[] Vector { get; set; } = Array.Empty<float>();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

public class SearchResult
{
    public string Id { get; set; } = string.Empty;
    public float Score { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class VectorStoreData
{
    public int VectorSize { get; set; }
    public List<VectorPoint> Vectors { get; set; } = new();
    public DateTime SavedAt { get; set; }
}
