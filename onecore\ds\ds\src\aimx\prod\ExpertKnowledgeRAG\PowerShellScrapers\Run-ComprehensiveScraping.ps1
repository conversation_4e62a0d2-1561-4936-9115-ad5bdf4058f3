# Comprehensive PowerShell AD Knowledge Scraping Pipeline
# Orchestrates scraping across Microsoft Docs, Stack Overflow, and GitHub
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxMicrosoftCmdlets = 20,
    
    [Parameter(Mandatory = $false)]
    [int]$MaxStackOverflowQuestions = 10,
    
    [Parameter(Mandatory = $false)]
    [int]$MaxGitHubRepos = 5,
    
    [Parameter(Mandatory = $false)]
    [switch]$SkipMicrosoft = $false,
    
    [Parameter(Mandatory = $false)]
    [switch]$SkipStackOverflow = $false,
    
    [Parameter(Mandatory = $false)]
    [switch]$SkipGitHub = $false
)

Write-Host "Starting Comprehensive PowerShell AD Knowledge Scraping Pipeline" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Gray

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    if (-not $result) {
        throw "Failed to initialize scraping framework"
    }
    
    Write-Host "✅ Scraping framework initialized" -ForegroundColor Green
    
    # Track all patterns
    $allPatterns = @()
    $scrapingResults = @{
        Microsoft = @{ Patterns = @(); Success = $false; Error = $null }
        StackOverflow = @{ Patterns = @(); Success = $false; Error = $null }
        GitHub = @{ Patterns = @(); Success = $false; Error = $null }
    }
    
    # 1. Microsoft Docs Scraping
    if (-not $SkipMicrosoft) {
        Write-Host "`nPHASE 1: Microsoft Docs Scraping" -ForegroundColor Yellow
        Write-Host "-" * 50 -ForegroundColor Gray
        
        try {
            Write-Host "Scraping top $MaxMicrosoftCmdlets PowerShell AD cmdlets from Microsoft Docs..." -ForegroundColor White
            
            # Use our working simplified approach
            $baseUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/"
            $response = Invoke-WebRequestWithRetry -Uri $baseUrl
            $content = $response.Content
            
            # Extract cmdlet names
            $cmdletPattern = '>([A-Za-z]+-AD[A-Za-z]+)<'
            $matches = [regex]::Matches($content, $cmdletPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            
            $cmdletNames = @()
            foreach ($match in $matches) {
                $cmdletNames += $match.Groups[1].Value
            }
            
            # Take top cmdlets and limit
            $selectedCmdlets = $cmdletNames | Select-Object -First $MaxMicrosoftCmdlets
            
            Write-Host "Found $($cmdletNames.Count) total cmdlets, processing first $($selectedCmdlets.Count)" -ForegroundColor Gray
            
            # Process each cmdlet
            $microsoftPatterns = @()
            $count = 0
            foreach ($cmdletName in $selectedCmdlets) {
                $count++
                Write-Progress -Activity "Microsoft Docs Scraping" -Status "Processing $cmdletName ($count/$($selectedCmdlets.Count))" -PercentComplete (($count / $selectedCmdlets.Count) * 100)
                
                try {
                    $cmdletUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($cmdletName.ToLower())"
                    $cmdletResponse = Invoke-WebRequestWithRetry -Uri $cmdletUrl -MaxRetries 1
                    
                    # Determine domain and operation
                    $domain = "general_ad"
                    if ($cmdletName -like "*User*") { $domain = "user_management" }
                    elseif ($cmdletName -like "*Group*") { $domain = "group_management" }
                    elseif ($cmdletName -like "*Computer*") { $domain = "computer_management" }
                    
                    $operation = "other"
                    if ($cmdletName -like "Get-*") { $operation = "read" }
                    elseif ($cmdletName -like "Set-*") { $operation = "update" }
                    elseif ($cmdletName -like "New-*") { $operation = "create" }
                    elseif ($cmdletName -like "Remove-*") { $operation = "delete" }
                    
                    # Create pattern
                    $pattern = New-KnowledgePattern -Title "Microsoft Docs: $cmdletName" -Content $cmdletResponse.Content -SourceUrl $cmdletUrl -SourceType "microsoft_docs" -Domain $domain -Operation $operation -Author "Microsoft" -CredibilityScore 0.95
                    
                    if ($pattern) {
                        # Extract code examples
                        $codeBlocks = Extract-CodeBlocks -Content $cmdletResponse.Content
                        if ($codeBlocks.Count -gt 0) {
                            $pattern.CodeTemplate = $codeBlocks[0]
                        }
                        
                        $microsoftPatterns += $pattern
                        Write-Host "  ✅ $cmdletName" -ForegroundColor Green
                    }
                }
                catch {
                    Write-Host "  ❌ $cmdletName : $($_.Exception.Message)" -ForegroundColor Red
                }
                
                Start-Sleep -Milliseconds 500  # Rate limiting
            }
            
            Write-Progress -Activity "Microsoft Docs Scraping" -Completed
            
            $scrapingResults.Microsoft.Patterns = $microsoftPatterns
            $scrapingResults.Microsoft.Success = $true
            $allPatterns += $microsoftPatterns
            
            Write-Host "✅ Microsoft Docs: $($microsoftPatterns.Count) patterns created" -ForegroundColor Green
        }
        catch {
            $scrapingResults.Microsoft.Error = $_.Exception.Message
            Write-Host "❌ Microsoft Docs scraping failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "`n📚 PHASE 1: Microsoft Docs Scraping - SKIPPED" -ForegroundColor Yellow
    }
    
    # 2. Stack Overflow Scraping
    if (-not $SkipStackOverflow) {
        Write-Host "`nPHASE 2: Stack Overflow Scraping" -ForegroundColor Yellow
        Write-Host "-" * 50 -ForegroundColor Gray
        
        try {
            Write-Host "Scraping top $MaxStackOverflowQuestions PowerShell AD questions from Stack Overflow..." -ForegroundColor White
            
            $baseUrl = "https://api.stackexchange.com/2.3/"
            $tags = "powershell;active-directory"
            $apiUrl = "${baseUrl}questions?order=desc&sort=votes&tagged=${tags}&site=stackoverflow&pagesize=${MaxStackOverflowQuestions}&filter=withbody"
            
            $response = Invoke-WebRequestWithRetry -Uri $apiUrl
            $data = $response.Content | ConvertFrom-Json
            
            Write-Host "Found $($data.items.Count) questions, quota remaining: $($data.quota_remaining)" -ForegroundColor Gray
            
            $stackOverflowPatterns = @()
            $count = 0
            foreach ($question in $data.items) {
                $count++
                Write-Progress -Activity "Stack Overflow Scraping" -Status "Processing question $count" -PercentComplete (($count / $data.items.Count) * 100)
                
                $pattern = New-KnowledgePattern -Title "Stack Overflow: $($question.title)" -Content $question.body -SourceUrl $question.link -SourceType "stackoverflow" -Domain "general_ad" -Operation "read" -Author "Stack Overflow Community" -CredibilityScore 0.80
                
                if ($pattern) {
                    $codeBlocks = Extract-CodeBlocks -Content $question.body
                    if ($codeBlocks.Count -gt 0) {
                        $pattern.CodeTemplate = $codeBlocks[0]
                    }
                    
                    $pattern.RelevanceScore = [Math]::Min(1.0, ($question.score / 100.0))
                    if ($question.tags) { $pattern.Tags += $question.tags }
                    
                    $stackOverflowPatterns += $pattern
                }
                
                Start-Sleep -Seconds 1  # Rate limiting
            }
            
            Write-Progress -Activity "Stack Overflow Scraping" -Completed
            
            $scrapingResults.StackOverflow.Patterns = $stackOverflowPatterns
            $scrapingResults.StackOverflow.Success = $true
            $allPatterns += $stackOverflowPatterns
            
            Write-Host "✅ Stack Overflow: $($stackOverflowPatterns.Count) patterns created" -ForegroundColor Green
        }
        catch {
            $scrapingResults.StackOverflow.Error = $_.Exception.Message
            Write-Host "❌ Stack Overflow scraping failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "`n💬 PHASE 2: Stack Overflow Scraping - SKIPPED" -ForegroundColor Yellow
    }
    
    # 3. GitHub Scraping
    if (-not $SkipGitHub) {
        Write-Host "`nPHASE 3: GitHub Scraping" -ForegroundColor Yellow
        Write-Host "-" * 50 -ForegroundColor Gray
        
        try {
            Write-Host "Scraping top $MaxGitHubRepos PowerShell AD repositories from GitHub..." -ForegroundColor White
            
            $baseUrl = "https://api.github.com/search/repositories"
            $query = "PowerShell ActiveDirectory language:PowerShell"
            $apiUrl = "${baseUrl}?q=${query}&sort=stars&order=desc&per_page=${MaxGitHubRepos}"
            
            $headers = @{
                'User-Agent' = 'PowerShell-Scraper/1.0'
                'Accept' = 'application/vnd.github.v3+json'
            }
            
            $response = Invoke-WebRequestWithRetry -Uri $apiUrl -Headers $headers
            $data = $response.Content | ConvertFrom-Json
            
            $remaining = $response.Headers['X-RateLimit-Remaining']
            Write-Host "Found $($data.items.Count) repositories, rate limit remaining: $remaining" -ForegroundColor Gray
            
            $githubPatterns = @()
            $count = 0
            foreach ($repo in $data.items) {
                $count++
                Write-Progress -Activity "GitHub Scraping" -Status "Processing $($repo.full_name)" -PercentComplete (($count / $data.items.Count) * 100)
                
                $repoContent = "# $($repo.full_name)`n`n**Description:** $($repo.description)`n`n**Stars:** $($repo.stargazers_count)`n**Language:** $($repo.language)`n**URL:** $($repo.html_url)"
                
                $domain = "general_ad"
                if ($repo.full_name -like "*user*" -or $repo.description -like "*user*") { $domain = "user_management" }
                elseif ($repo.full_name -like "*group*" -or $repo.description -like "*group*") { $domain = "group_management" }
                
                $pattern = New-KnowledgePattern -Title "GitHub: $($repo.full_name)" -Content $repoContent -SourceUrl $repo.html_url -SourceType "github" -Domain $domain -Operation "read" -Author $repo.owner.login -CredibilityScore 0.75
                
                if ($pattern) {
                    $pattern.RelevanceScore = [Math]::Min(1.0, [Math]::Log10($repo.stargazers_count + 1) / 4.0)
                    if ($repo.topics) { $pattern.Tags += $repo.topics }
                    $pattern.Tags += @("github", "repository", $repo.language.ToLower())
                    
                    $githubPatterns += $pattern
                }
                
                Start-Sleep -Seconds 1  # Rate limiting
            }
            
            Write-Progress -Activity "GitHub Scraping" -Completed
            
            $scrapingResults.GitHub.Patterns = $githubPatterns
            $scrapingResults.GitHub.Success = $true
            $allPatterns += $githubPatterns
            
            Write-Host "✅ GitHub: $($githubPatterns.Count) patterns created" -ForegroundColor Green
        }
        catch {
            $scrapingResults.GitHub.Error = $_.Exception.Message
            Write-Host "❌ GitHub scraping failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "`n🐙 PHASE 3: GitHub Scraping - SKIPPED" -ForegroundColor Yellow
    }
    
    # Final Results
    Write-Host "`nCOMPREHENSIVE SCRAPING RESULTS" -ForegroundColor Cyan
    Write-Host "=" * 80 -ForegroundColor Gray
    
    Write-Host "Microsoft Docs: " -NoNewline
    if ($scrapingResults.Microsoft.Success) {
        Write-Host "$($scrapingResults.Microsoft.Patterns.Count) patterns ✅" -ForegroundColor Green
    } else {
        Write-Host "FAILED ❌ ($($scrapingResults.Microsoft.Error))" -ForegroundColor Red
    }
    
    Write-Host "Stack Overflow: " -NoNewline
    if ($scrapingResults.StackOverflow.Success) {
        Write-Host "$($scrapingResults.StackOverflow.Patterns.Count) patterns ✅" -ForegroundColor Green
    } else {
        Write-Host "FAILED ❌ ($($scrapingResults.StackOverflow.Error))" -ForegroundColor Red
    }
    
    Write-Host "GitHub:         " -NoNewline
    if ($scrapingResults.GitHub.Success) {
        Write-Host "$($scrapingResults.GitHub.Patterns.Count) patterns ✅" -ForegroundColor Green
    } else {
        Write-Host "FAILED ❌ ($($scrapingResults.GitHub.Error))" -ForegroundColor Red
    }
    
    Write-Host "`nTOTAL PATTERNS: $($allPatterns.Count)" -ForegroundColor Cyan
    
    # Save comprehensive results
    if ($allPatterns.Count -gt 0) {
        $outputFile = Save-ScrapingResults -Patterns $allPatterns -SourceType "comprehensive"
        Write-Host "All patterns saved to: $outputFile" -ForegroundColor Green
        
        # Domain breakdown
        $domainStats = $allPatterns | Group-Object Domain | Sort-Object Count -Descending
        Write-Host "`nDomain breakdown:" -ForegroundColor Yellow
        foreach ($domain in $domainStats) {
            Write-Host "  $($domain.Name): $($domain.Count) patterns" -ForegroundColor Gray
        }
        
        # Source breakdown
        $sourceStats = $allPatterns | Group-Object { $_.Sources[0].SourceType } | Sort-Object Count -Descending
        Write-Host "`nSource breakdown:" -ForegroundColor Yellow
        foreach ($source in $sourceStats) {
            Write-Host "  $($source.Name): $($source.Count) patterns" -ForegroundColor Gray
        }
    }
    
    Write-Host "`nComprehensive scraping pipeline completed successfully!" -ForegroundColor Green
}
catch {
    Write-Host "`nComprehensive scraping pipeline failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    exit 1
}
