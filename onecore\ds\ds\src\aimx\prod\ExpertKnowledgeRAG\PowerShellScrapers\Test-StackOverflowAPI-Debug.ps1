# Debug Stack Overflow API responses
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Debugging Stack Overflow API Responses" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Test basic questions API
    Write-Host "`nTesting basic questions API..." -ForegroundColor Yellow
    
    $baseUrl = "https://api.stackexchange.com/2.3/"
    $tags = "powershell%3Bactive-directory"  # URL encoded semicolon
    $questionsUrl = "${baseUrl}questions?order=desc&sort=votes&tagged=${tags}&site=stackoverflow&pagesize=2&filter=withbody"
    
    Write-Host "URL: $questionsUrl" -ForegroundColor Gray
    
    $questionsResponse = Invoke-WebRequestWithRetry -Uri $questionsUrl
    $questionsData = $questionsResponse.Content | ConvertFrom-<PERSON>son
    
    Write-Host "Questions found: $($questionsData.items.Count)" -ForegroundColor Green
    Write-Host "Quota remaining: $($questionsData.quota_remaining)" -ForegroundColor Gray
    
    if ($questionsData.items.Count -gt 0) {
        $firstQuestion = $questionsData.items[0]
        Write-Host "`nFirst question details:" -ForegroundColor Yellow
        Write-Host "  ID: $($firstQuestion.question_id)" -ForegroundColor Gray
        Write-Host "  Title: $($firstQuestion.title)" -ForegroundColor Gray
        Write-Host "  Score: $($firstQuestion.score)" -ForegroundColor Gray
        Write-Host "  Body length: $($firstQuestion.body.Length)" -ForegroundColor Gray
        Write-Host "  Body preview: $($firstQuestion.body.Substring(0, [Math]::Min(200, $firstQuestion.body.Length)))..." -ForegroundColor Gray
        
        # Test getting specific question details
        Write-Host "`nTesting specific question details API..." -ForegroundColor Yellow
        $questionId = $firstQuestion.question_id
        $detailsUrl = "${baseUrl}questions/${questionId}?order=desc&sort=votes&site=stackoverflow&filter=withbody"
        
        Write-Host "Details URL: $detailsUrl" -ForegroundColor Gray
        
        $detailsResponse = Invoke-WebRequestWithRetry -Uri $detailsUrl
        $detailsData = $detailsResponse.Content | ConvertFrom-Json
        
        if ($detailsData.items.Count -gt 0) {
            $questionDetails = $detailsData.items[0]
            Write-Host "Question details retrieved successfully" -ForegroundColor Green
            Write-Host "  Body length: $($questionDetails.body.Length)" -ForegroundColor Gray
            Write-Host "  Has body: $($questionDetails.body -ne $null -and $questionDetails.body.Length -gt 0)" -ForegroundColor Gray
        } else {
            Write-Host "No question details found" -ForegroundColor Red
        }
        
        # Test getting answers
        Write-Host "`nTesting answers API..." -ForegroundColor Yellow
        $answersUrl = "${baseUrl}questions/${questionId}/answers?order=desc&sort=votes&site=stackoverflow&filter=withbody"
        
        Write-Host "Answers URL: $answersUrl" -ForegroundColor Gray
        
        $answersResponse = Invoke-WebRequestWithRetry -Uri $answersUrl
        $answersData = $answersResponse.Content | ConvertFrom-Json
        
        Write-Host "Answers found: $($answersData.items.Count)" -ForegroundColor Green
        
        if ($answersData.items.Count -gt 0) {
            $firstAnswer = $answersData.items[0]
            Write-Host "  First answer score: $($firstAnswer.score)" -ForegroundColor Gray
            Write-Host "  First answer body length: $($firstAnswer.body.Length)" -ForegroundColor Gray
            Write-Host "  First answer is accepted: $($firstAnswer.is_accepted)" -ForegroundColor Gray
            Write-Host "  First answer preview: $($firstAnswer.body.Substring(0, [Math]::Min(200, $firstAnswer.body.Length)))..." -ForegroundColor Gray
        }
        
        # Test code extraction
        Write-Host "`nTesting code extraction..." -ForegroundColor Yellow
        $questionCode = Extract-CodeBlocks -Content $firstQuestion.body
        Write-Host "Code blocks in question: $($questionCode.Count)" -ForegroundColor Gray
        
        if ($questionCode.Count -gt 0) {
            Write-Host "  First code block: $($questionCode[0].Substring(0, [Math]::Min(100, $questionCode[0].Length)))..." -ForegroundColor Gray
        }
        
        if ($answersData.items.Count -gt 0) {
            $answerCode = Extract-CodeBlocks -Content $answersData.items[0].body
            Write-Host "Code blocks in first answer: $($answerCode.Count)" -ForegroundColor Gray
            
            if ($answerCode.Count -gt 0) {
                Write-Host "  First answer code block: $($answerCode[0].Substring(0, [Math]::Min(100, $answerCode[0].Length)))..." -ForegroundColor Gray
            }
        }
    }
    
    Write-Host "`nStack Overflow API debug completed successfully!" -ForegroundColor Green
}
catch {
    Write-Host "`nStack Overflow API debug failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
}
