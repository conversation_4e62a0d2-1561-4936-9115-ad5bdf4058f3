# PowerShell script to test the Microsoft Learn PowerShell command search functionality

param(
    [string]$BaseUrl = "http://localhost:5000",
    [string]$Query = "add user to group",
    [int]$Limit = 5
)

# Load System.Web for URL encoding
Add-Type -AssemblyName System.Web

Write-Host "Testing PowerShell Command Search Service" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "=" * 50

# Test health check
Write-Host "`n1. Testing Health Check..." -ForegroundColor Cyan
try {
    $healthResponse = Invoke-RestMethod -Uri "$BaseUrl/api/powershellcommand/health" -Method GET
    Write-Host "[PASS] Health Check Passed" -ForegroundColor Green
    Write-Host "Status: $($healthResponse.status)" -ForegroundColor White
    Write-Host "Vector Count: $($healthResponse.vector_count)" -ForegroundColor White
}
catch {
    Write-Host "[FAIL] Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
    return
}

# Test statistics
Write-Host "`n2. Testing Statistics..." -ForegroundColor Cyan
try {
    $statsResponse = Invoke-RestMethod -Uri "$BaseUrl/api/powershellcommand/stats" -Method GET
    Write-Host "[PASS] Statistics Retrieved" -ForegroundColor Green
    Write-Host "Total Vectors: $($statsResponse.total_vectors)" -ForegroundColor White
    Write-Host "Data Type: $($statsResponse.data_type)" -ForegroundColor White
}
catch {
    Write-Host "[FAIL] Statistics Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test command names
Write-Host "`n3. Testing Command Names..." -ForegroundColor Cyan
try {
    $namesResponse = Invoke-RestMethod -Uri "$BaseUrl/api/powershellcommand/names" -Method GET
    Write-Host "[PASS] Command Names Retrieved" -ForegroundColor Green
    Write-Host "Total Commands: $($namesResponse.Count)" -ForegroundColor White
    Write-Host "First 10 Commands:" -ForegroundColor White
    $namesResponse | Select-Object -First 10 | ForEach-Object { Write-Host "  - $_" -ForegroundColor Gray }
}
catch {
    Write-Host "[FAIL] Command Names Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test search functionality
Write-Host "`n4. Testing Search Functionality..." -ForegroundColor Cyan
Write-Host "Query: '$Query'" -ForegroundColor Yellow
try {
    $searchUrl = "$BaseUrl/api/powershellcommand/search?query=$([System.Web.HttpUtility]::UrlEncode($Query))&limit=$Limit"
    $searchResponse = Invoke-RestMethod -Uri $searchUrl -Method GET

    Write-Host "[PASS] Search Completed" -ForegroundColor Green
    Write-Host "Results Found: $($searchResponse.Count)" -ForegroundColor White

    if ($searchResponse.Count -gt 0) {
        Write-Host "`nTop Results:" -ForegroundColor White
        for ($i = 0; $i -lt $searchResponse.Count; $i++) {
            $result = $searchResponse[$i]
            Write-Host "  $($i + 1). $($result.commandName) (Score: $([math]::Round($result.score, 3)))" -ForegroundColor White
            Write-Host "     Parameters: $($result.parameterCount), Examples: $($result.exampleCount)" -ForegroundColor Gray
            if ($result.parameterNames) {
                Write-Host "     Key Parameters: $($result.parameterNames)" -ForegroundColor Gray
            }
            Write-Host ""
        }

        # Test getting a specific command by ID
        Write-Host "`n5. Testing Get Command by ID..." -ForegroundColor Cyan
        $firstResultId = $searchResponse[0].id
        Write-Host "Testing with ID: $firstResultId" -ForegroundColor Yellow

        try {
            $commandResponse = Invoke-RestMethod -Uri "$BaseUrl/api/powershellcommand/$firstResultId" -Method GET
            Write-Host "[PASS] Command Retrieved by ID" -ForegroundColor Green
            Write-Host "Command: $($commandResponse.commandName)" -ForegroundColor White
            Write-Host "Score: $([math]::Round($commandResponse.score, 3))" -ForegroundColor White

            # Show a snippet of the full text
            if ($commandResponse.fullText.Length -gt 200) {
                $snippet = $commandResponse.fullText.Substring(0, 200) + "..."
                Write-Host "Text Preview: $snippet" -ForegroundColor Gray
            } else {
                Write-Host "Full Text: $($commandResponse.fullText)" -ForegroundColor Gray
            }
        }
        catch {
            Write-Host "[FAIL] Get Command by ID Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}
catch {
    Write-Host "[FAIL] Search Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Interactive Query Test
Write-Host "`n6. Interactive Query Test..." -ForegroundColor Cyan
Write-Host "Enter your own queries to search for PowerShell commands" -ForegroundColor White

do {
    Write-Host "`n" + "-" * 40
    $userQuery = Read-Host "Enter search query (or 'quit' to exit)"

    if ($userQuery -eq 'quit' -or $userQuery -eq 'q' -or [string]::IsNullOrWhiteSpace($userQuery)) {
        break
    }

    $userLimit = Read-Host "Enter number of results to return (default: 5, max: 20)"
    if ([string]::IsNullOrWhiteSpace($userLimit) -or -not [int]::TryParse($userLimit, [ref]$null)) {
        $userLimit = 5
    } else {
        $userLimit = [math]::Min([int]$userLimit, 20)
    }

    try {
        Write-Host "`nSearching for: '$userQuery' (limit: $userLimit)" -ForegroundColor Yellow
        $searchUrl = "$BaseUrl/api/powershellcommand/search?query=$([System.Web.HttpUtility]::UrlEncode($userQuery))&limit=$userLimit"
        $searchResponse = Invoke-RestMethod -Uri $searchUrl -Method GET

        Write-Host "[PASS] Search completed. Found $($searchResponse.Count) results" -ForegroundColor Green

        if ($searchResponse.Count -gt 0) {
            # Ask if user wants full JSON or summary
            $outputChoice = Read-Host "`nOutput format: (s)ummary or (j)son? (default: summary)"

            if ($outputChoice -eq 'j' -or $outputChoice -eq 'json') {
                # Output full JSON
                Write-Host "`nFull JSON Response:" -ForegroundColor White
                $searchResponse | ConvertTo-Json -Depth 10 | Write-Host -ForegroundColor Gray
            } else {
                # Output summary
                Write-Host "`nSearch Results:" -ForegroundColor White
                for ($i = 0; $i -lt $searchResponse.Count; $i++) {
                    $result = $searchResponse[$i]
                    Write-Host "`n$($i + 1). $($result.commandName)" -ForegroundColor White
                    Write-Host "   Score: $([math]::Round($result.score, 4))" -ForegroundColor Gray
                    Write-Host "   Parameters: $($result.parameterCount), Examples: $($result.exampleCount)" -ForegroundColor Gray
                    Write-Host "   ID: $($result.id)" -ForegroundColor Gray

                    if ($result.parameterNames) {
                        Write-Host "   Key Parameters: $($result.parameterNames)" -ForegroundColor Gray
                    }

                    # Show first few lines of full text
                    if ($result.fullText) {
                        $lines = $result.fullText -split "`n" | Select-Object -First 3
                        Write-Host "   Preview:" -ForegroundColor Gray
                        foreach ($line in $lines) {
                            if (-not [string]::IsNullOrWhiteSpace($line)) {
                                Write-Host "     $($line.Trim())" -ForegroundColor DarkGray
                            }
                        }
                    }
                }
            }
        } else {
            Write-Host "No results found for query: '$userQuery'" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "[FAIL] Search failed: $($_.Exception.Message)" -ForegroundColor Red
    }

} while ($true)

Write-Host "`n" + "=" * 50
Write-Host "PowerShell Command Search Test Completed!" -ForegroundColor Green
