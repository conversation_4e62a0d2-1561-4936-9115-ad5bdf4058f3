# GitHub PowerShell AD Scripts Scraper
# Scrapes PowerShell Active Directory scripts and modules from GitHub

#Requires -Version 5.1

param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "../ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [string]$GitHubToken = "",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxRepositories = 100,
    
    [Parameter(Mandatory = $false)]
    [int]$MinStars = 5
)

Import-Module -Name (Join-Path $PSScriptRoot "../Core/ScrapingFramework.psm1") -Force

function Search-GitHubRepositories {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string[]]$SearchQueries,
        
        [Parameter(Mandatory = $false)]
        [string]$Token = "",
        
        [Parameter(Mandatory = $false)]
        [int]$MaxRepos = 100,
        
        [Parameter(Mandatory = $false)]
        [int]$MinStars = 5
    )
    
    Write-ScrapingLog -Message "Searching GitHub repositories for PowerShell AD content" -Source "GitHubScraper"
    
    $headers = @{
        'Accept' = 'application/vnd.github.v3+json'
        'User-Agent' = 'PowerShell-Expert-Knowledge-Scraper'
    }
    
    if ($Token) {
        $headers['Authorization'] = "token $Token"
    }
    
    $allRepositories = @()
    
    foreach ($query in $SearchQueries) {
        try {
            Write-ScrapingLog -Message "Searching for: $query" -Source "GitHubScraper"
            
            $encodedQuery = [System.Web.HttpUtility]::UrlEncode($query)
            $url = "https://api.github.com/search/repositories?q=$encodedQuery&sort=stars&order=desc&per_page=100"
            
            $response = Invoke-WebRequestWithRetry -Uri $url -Headers $headers
            $data = $response.Content | ConvertFrom-Json
            
            if ($data.items) {
                $filteredRepos = $data.items | Where-Object { 
                    $_.stargazers_count -ge $MinStars -and 
                    $_.language -eq "PowerShell" -and
                    (-not $_.fork) -and
                    ($_.description -match "Active Directory|AD|PowerShell" -or $_.name -match "AD|ActiveDirectory")
                }
                
                $allRepositories += $filteredRepos
                Write-ScrapingLog -Message "Found $($filteredRepos.Count) qualifying repositories for query: $query" -Source "GitHubScraper"
            }
            
            # Rate limiting for GitHub API
            Start-Sleep -Seconds 2
        }
        catch {
            Write-ScrapingLog -Message "Failed to search for '$query': $($_.Exception.Message)" -Level "Error" -Source "GitHubScraper"
        }
    }
    
    # Remove duplicates and limit results
    $uniqueRepos = $allRepositories | Sort-Object -Property full_name -Unique | Sort-Object -Property stargazers_count -Descending
    
    if ($uniqueRepos.Count -gt $MaxRepos) {
        $uniqueRepos = $uniqueRepos | Select-Object -First $MaxRepos
    }
    
    Write-ScrapingLog -Message "Selected $($uniqueRepos.Count) unique repositories for processing" -Source "GitHubScraper"
    return $uniqueRepos
}

function Get-RepositoryContents {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Repository,
        
        [Parameter(Mandatory = $false)]
        [string]$Token = ""
    )
    
    Write-ScrapingLog -Message "Getting contents for repository: $($Repository.full_name)" -Source "GitHubScraper"
    
    $headers = @{
        'Accept' = 'application/vnd.github.v3+json'
        'User-Agent' = 'PowerShell-Expert-Knowledge-Scraper'
    }
    
    if ($Token) {
        $headers['Authorization'] = "token $Token"
    }
    
    try {
        # Get repository tree
        $treeUrl = "https://api.github.com/repos/$($Repository.full_name)/git/trees/$($Repository.default_branch)?recursive=1"
        $response = Invoke-WebRequestWithRetry -Uri $treeUrl -Headers $headers
        $treeData = $response.Content | ConvertFrom-Json
        
        # Filter for PowerShell files
        $psFiles = $treeData.tree | Where-Object { 
            $_.type -eq "blob" -and 
            ($_.path -match "\.ps1$|\.psm1$|\.psd1$") -and
            ($_.path -match "AD|ActiveDirectory|User|Group|Computer" -or $_.size -gt 1000)
        }
        
        Write-ScrapingLog -Message "Found $($psFiles.Count) PowerShell files in $($Repository.full_name)" -Source "GitHubScraper"
        
        # Get content for each file (limit to prevent API exhaustion)
        $maxFiles = 20
        $fileContents = @()
        
        foreach ($file in ($psFiles | Select-Object -First $maxFiles)) {
            try {
                $fileUrl = "https://api.github.com/repos/$($Repository.full_name)/contents/$($file.path)"
                $fileResponse = Invoke-WebRequestWithRetry -Uri $fileUrl -Headers $headers
                $fileData = $fileResponse.Content | ConvertFrom-Json
                
                if ($fileData.content -and $fileData.encoding -eq "base64") {
                    $decodedContent = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($fileData.content))
                    
                    $fileContents += @{
                        Path = $file.path
                        Content = $decodedContent
                        Size = $file.size
                        Url = $fileData.html_url
                    }
                }
                
                # Rate limiting
                Start-Sleep -Milliseconds 500
            }
            catch {
                Write-ScrapingLog -Message "Failed to get content for file $($file.path): $($_.Exception.Message)" -Level "Warning" -Source "GitHubScraper"
            }
        }
        
        return $fileContents
    }
    catch {
        Write-ScrapingLog -Message "Failed to get repository contents for $($Repository.full_name): $($_.Exception.Message)" -Level "Error" -Source "GitHubScraper"
        return @()
    }
}

function Get-RepositoryReadme {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Repository,
        
        [Parameter(Mandatory = $false)]
        [string]$Token = ""
    )
    
    $headers = @{
        'Accept' = 'application/vnd.github.v3+json'
        'User-Agent' = 'PowerShell-Expert-Knowledge-Scraper'
    }
    
    if ($Token) {
        $headers['Authorization'] = "token $Token"
    }
    
    try {
        $readmeUrl = "https://api.github.com/repos/$($Repository.full_name)/readme"
        $response = Invoke-WebRequestWithRetry -Uri $readmeUrl -Headers $headers
        $readmeData = $response.Content | ConvertFrom-Json
        
        if ($readmeData.content -and $readmeData.encoding -eq "base64") {
            $decodedContent = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($readmeData.content))
            return $decodedContent
        }
        
        return ""
    }
    catch {
        Write-ScrapingLog -Message "No README found for $($Repository.full_name)" -Level "Debug" -Source "GitHubScraper"
        return ""
    }
}

function Process-GitHubRepository {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Repository,
        
        [Parameter(Mandatory = $false)]
        [string]$Token = ""
    )
    
    Write-ScrapingLog -Message "Processing GitHub repository: $($Repository.full_name)" -Source "GitHubScraper"
    
    try {
        # Get repository contents
        $fileContents = Get-RepositoryContents -Repository $Repository -Token $Token
        $readme = Get-RepositoryReadme -Repository $Repository -Token $Token
        
        $patterns = @()
        
        # Process README as a pattern if it contains useful information
        if ($readme -and $readme.Length -gt 500) {
            $readmePattern = Process-ReadmeContent -Repository $Repository -ReadmeContent $readme
            if ($readmePattern) {
                $patterns += $readmePattern
            }
        }
        
        # Process each PowerShell file
        foreach ($file in $fileContents) {
            $filePattern = Process-PowerShellFile -Repository $Repository -File $file
            if ($filePattern) {
                $patterns += $filePattern
            }
        }
        
        Write-ScrapingLog -Message "Extracted $($patterns.Count) patterns from $($Repository.full_name)" -Source "GitHubScraper"
        return $patterns
    }
    catch {
        Write-ScrapingLog -Message "Failed to process repository $($Repository.full_name): $($_.Exception.Message)" -Level "Error" -Source "GitHubScraper"
        return @()
    }
}

function Process-ReadmeContent {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Repository,
        
        [Parameter(Mandatory = $true)]
        [string]$ReadmeContent
    )
    
    # Check if README contains PowerShell AD content
    if ($ReadmeContent -notmatch "PowerShell|Active Directory|AD|Get-AD|Set-AD|New-AD") {
        return $null
    }
    
    $domain = Get-DomainFromContent -Content $ReadmeContent
    $operation = "documentation"
    
    # Calculate credibility based on repository metrics
    $credibility = Calculate-GitHubCredibility -Repository $Repository
    
    $pattern = New-KnowledgePattern -Title "GitHub Repository: $($Repository.name)" -Content $ReadmeContent -SourceUrl $Repository.html_url -SourceType "github" -Domain $domain -Operation $operation -Author $Repository.owner.login -CredibilityScore $credibility
    
    # Add GitHub-specific metadata
    $pattern.Tags += @("github", "repository", "documentation")
    $pattern.RelevanceScore = [Math]::Min(1.0, ($Repository.stargazers_count / 100.0))
    
    return $pattern
}

function Process-PowerShellFile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Repository,
        
        [Parameter(Mandatory = $true)]
        [hashtable]$File
    )
    
    # Check if file contains AD-related PowerShell content
    if ($File.Content -notmatch "Get-AD|Set-AD|New-AD|Remove-AD|Add-AD|Enable-AD|Disable-AD") {
        return $null
    }
    
    # Extract functions and their documentation
    $functions = Extract-PowerShellFunctions -Content $File.Content
    $domain = Get-DomainFromContent -Content $File.Content
    $operation = Get-OperationFromContent -Content $File.Content
    
    # Create comprehensive content
    $fullContent = @"
# PowerShell Script: $($File.Path)

**Repository:** $($Repository.full_name)
**File Size:** $($File.Size) bytes
**Stars:** $($Repository.stargazers_count)
**Language:** PowerShell

## Description
$($Repository.description)

## Functions
$($functions -join "`n`n")

## Full Script Content
```powershell
$($File.Content)
```
"@
    
    $credibility = Calculate-GitHubCredibility -Repository $Repository
    
    $pattern = New-KnowledgePattern -Title "PowerShell Script: $($File.Path)" -Content $fullContent -SourceUrl $File.Url -SourceType "github" -Domain $domain -Operation $operation -Author $Repository.owner.login -CredibilityScore $credibility
    
    # Add GitHub-specific metadata
    $pattern.Tags += @("github", "script", "powershell")
    $pattern.CodeTemplate = $File.Content
    
    return $pattern
}

function Extract-PowerShellFunctions {
    [CmdletBinding()]
    param([string]$Content)
    
    $functions = @()
    
    # Pattern to match PowerShell functions
    $functionPattern = '(?ms)^function\s+([^\s\{]+).*?\{.*?^}'
    $matches = [regex]::Matches($Content, $functionPattern)
    
    foreach ($match in $matches) {
        $functionName = $match.Groups[1].Value
        $functionBody = $match.Value
        
        # Extract function documentation if available
        $docPattern = "(?ms)<#.*?#>\s*function\s+$([regex]::Escape($functionName))"
        $docMatch = [regex]::Match($Content, $docPattern)
        
        $documentation = if ($docMatch.Success) { $docMatch.Value } else { "" }
        
        $functions += @"
### Function: $functionName

$documentation

```powershell
$functionBody
```
"@
    }
    
    return $functions
}

function Calculate-GitHubCredibility {
    [CmdletBinding()]
    param([PSCustomObject]$Repository)
    
    $baseScore = 0.6  # Base credibility for GitHub
    
    # Stars factor
    $starsFactor = [Math]::Min(0.2, ($Repository.stargazers_count / 500.0))
    
    # Forks factor
    $forksFactor = [Math]::Min(0.1, ($Repository.forks_count / 100.0))
    
    # Recency factor
    $lastUpdate = [DateTime]::Parse($Repository.updated_at)
    $daysSinceUpdate = (Get-Date) - $lastUpdate
    $recencyFactor = if ($daysSinceUpdate.Days -lt 365) { 0.1 } else { 0.0 }
    
    # Size factor (larger repositories often more comprehensive)
    $sizeFactor = [Math]::Min(0.1, ($Repository.size / 10000.0))
    
    $totalScore = $baseScore + $starsFactor + $forksFactor + $recencyFactor + $sizeFactor
    return [Math]::Min(1.0, $totalScore)
}

function Get-DomainFromContent {
    [CmdletBinding()]
    param([string]$Content)
    
    switch -Regex ($Content) {
        "Get-ADUser|Set-ADUser|New-ADUser|Remove-ADUser" { return "user_management" }
        "Get-ADGroup|Set-ADGroup|New-ADGroup|Add-ADGroupMember|Remove-ADGroupMember" { return "group_management" }
        "Get-ADComputer|Set-ADComputer|New-ADComputer" { return "computer_management" }
        "Get-ADOrganizationalUnit|New-ADOrganizationalUnit" { return "ou_management" }
        "password|Set-ADAccountPassword|Reset" { return "password_management" }
        "permission|ACL|security" { return "security_management" }
        default { return "general_ad" }
    }
}

function Get-OperationFromContent {
    [CmdletBinding()]
    param([string]$Content)
    
    switch -Regex ($Content) {
        "Get-AD|Search-AD|Find" { return "read" }
        "Set-AD|Update|Modify|Change" { return "update" }
        "New-AD|Create|Add" { return "create" }
        "Remove-AD|Delete" { return "delete" }
        "Enable-AD" { return "enable" }
        "Disable-AD" { return "disable" }
        "Unlock-AD" { return "unlock" }
        "Reset" { return "reset" }
        default { return "other" }
    }
}

# Main execution
function Start-GitHubScraping {
    [CmdletBinding()]
    param()
    
    Write-ScrapingLog -Message "Starting GitHub scraping process" -Source "GitHubScraper"
    
    try {
        # Initialize framework
        if (-not (Initialize-ScrapingFramework -ConfigPath $ConfigPath)) {
            throw "Failed to initialize scraping framework"
        }
        
        # Get search queries from config
        $searchQueries = $Script:Config.DataSources.GitHub.SearchQueries
        
        # Search repositories
        $repositories = Search-GitHubRepositories -SearchQueries $searchQueries -Token $GitHubToken -MaxRepos $MaxRepositories -MinStars $MinStars
        
        if ($repositories.Count -eq 0) {
            Write-ScrapingLog -Message "No repositories found matching criteria" -Level "Warning" -Source "GitHubScraper"
            return @()
        }
        
        # Process each repository
        $allPatterns = @()
        $totalRepos = $repositories.Count
        $currentIndex = 0
        
        foreach ($repo in $repositories) {
            $currentIndex++
            Write-Progress -Activity "Scraping GitHub" -Status "Processing repository $currentIndex of $totalRepos" -PercentComplete (($currentIndex / $totalRepos) * 100)
            
            $patterns = Process-GitHubRepository -Repository $repo -Token $GitHubToken
            $allPatterns += $patterns
            
            # Rate limiting
            Start-Sleep -Seconds 3
        }
        
        Write-Progress -Activity "Scraping GitHub" -Completed
        
        # Save results
        if ($allPatterns.Count -gt 0) {
            $outputFile = Save-ScrapingResults -Patterns $allPatterns -SourceType "github"
            Write-ScrapingLog -Message "Successfully scraped $($allPatterns.Count) patterns from GitHub" -Source "GitHubScraper"
            Write-ScrapingLog -Message "Results saved to: $outputFile" -Source "GitHubScraper"
        } else {
            Write-ScrapingLog -Message "No patterns extracted from GitHub" -Level "Warning" -Source "GitHubScraper"
        }
        
        return $allPatterns
    }
    catch {
        Write-ScrapingLog -Message "GitHub scraping failed: $($_.Exception.Message)" -Level "Error" -Source "GitHubScraper"
        throw
    }
}

# Execute if run directly
if ($MyInvocation.InvocationName -ne '.') {
    Start-GitHubScraping
}
