{"ScrapingConfiguration": {"OutputDirectory": "./ScrapedData", "LogDirectory": "./Logs", "MaxConcurrentJobs": 5, "DelayBetweenRequests": 1000, "UserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "TimeoutSeconds": 30, "MaxRetries": 3}, "DataSources": {"MicrosoftDocs": {"BaseUrls": ["https://learn.microsoft.com/en-us/powershell/module/activedirectory/", "https://docs.microsoft.com/en-us/powershell/module/activedirectory/"], "Patterns": ["*/powershell/module/activedirectory/*", "*/active-directory/*powershell*", "*/powershell/*active-directory*"], "Priority": 1, "CredibilityScore": 0.95}, "PowerShellGallery": {"BaseUrls": ["https://www.powershellgallery.com/packages?q=ActiveDirectory", "https://www.powershellgallery.com/packages?q=AD"], "ApiEndpoint": "https://www.powershellgallery.com/api/v2/", "Priority": 1, "CredibilityScore": 0.85}, "GitHub": {"SearchQueries": ["PowerShell ActiveDirectory language:PowerShell", "Get-ADUser language:PowerShell", "Set-ADUser language:PowerShell", "New-ADUser language:PowerShell", "Add-ADGroupMember language:PowerShell"], "ApiEndpoint": "https://api.github.com/search/repositories", "Priority": 2, "CredibilityScore": 0.75}, "StackOverflow": {"BaseUrl": "https://api.stackexchange.com/2.3/", "Tags": ["powershell", "active-directory", "powershell-activedirectory"], "SortBy": "votes", "Priority": 2, "CredibilityScore": 0.8}, "Reddit": {"Subreddits": ["PowerShell", "sysadmin", "activedirectory"], "SearchTerms": ["Get-ADUser", "PowerShell AD", "Active Directory PowerShell"], "Priority": 3, "CredibilityScore": 0.65}, "ExpertBlogs": {"Urls": ["https://devblogs.microsoft.com/powershell/", "https://powershell.org/", "https://www.powershellmagazine.com/", "https://adamtheautomator.com/", "https://blog.netwrix.com/category/powershell/"], "Priority": 2, "CredibilityScore": 0.85}, "TechCommunity": {"BaseUrls": ["https://techcommunity.microsoft.com/t5/windows-powershell/", "https://techcommunity.microsoft.com/t5/active-directory-identity/"], "Priority": 2, "CredibilityScore": 0.8}}, "PatternExtraction": {"CodeBlockPatterns": ["(?s)```powershell(.*?)```", "(?s)```ps(.*?)```", "(?s)```([^`]+)```", "(?s)<code>(.*?)</code>", "(?m)^\\s*PS.*?>"], "PowerShellCmdlets": ["Get-AD.*", "Set-AD.*", "New-AD.*", "Remove-AD.*", "Add-AD.*", "Enable-AD.*", "Disable-AD.*", "Unlock-AD.*", "Reset-AD.*", "Search-AD.*", "Move-AD.*"], "BestPracticeKeywords": ["best practice", "recommended", "should use", "avoid", "don't use", "security", "performance", "tip", "warning", "note"], "CommonMistakeKeywords": ["common mistake", "error", "wrong", "incorrect", "gotcha", "pitfall", "problem", "issue", "bug"]}, "QualityFilters": {"MinimumCodeLength": 10, "RequiredKeywords": ["PowerShell", "Active Directory", "AD"], "ExcludePatterns": ["lorem ipsum", "example.com", "test123", "password123"], "MinimumCredibilityScore": 0.5, "RequireCodeExamples": true}}