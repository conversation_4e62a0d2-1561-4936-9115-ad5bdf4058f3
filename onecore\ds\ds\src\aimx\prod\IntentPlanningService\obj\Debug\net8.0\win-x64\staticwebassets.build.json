{"Version": 1, "Hash": "tMwG9qrd+Nn71yRIoyy8o7r8TID1Oi1NtAfraM304Wc=", "Source": "IntentPlanningService", "BasePath": "_content/IntentPlanningService", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "E:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\netRag\\NetRagService\\NetRagService.csproj", "Version": 2, "Source": "NetRagService", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework"}], "DiscoveryPatterns": [], "Assets": []}