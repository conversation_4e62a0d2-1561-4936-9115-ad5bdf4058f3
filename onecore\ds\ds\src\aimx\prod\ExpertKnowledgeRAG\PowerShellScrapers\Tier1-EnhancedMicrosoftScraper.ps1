# Tier 1: Enhanced Microsoft Learn Cmdlet Reference Scraper
# Comprehensive scraping of Microsoft Learn PowerShell cmdlet documentation
param(
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "./ScrapedData/tier1_microsoft_enhanced_$(Get-Date -Format 'yyyyMMdd_HHmmss').json",
    [Parameter(Mandatory = $false)]
    [int]$MaxCmdlets = 200
)

Write-Host "Tier 1: Enhanced Microsoft Learn Cmdlet Reference Scraper" -ForegroundColor Cyan
Write-Host "Comprehensive PowerShell cmdlet documentation" -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Gray

# Microsoft Learn PowerShell modules and cmdlets
$powerShellModules = @(
    @{
        name = "ActiveDirectory"
        base_url = "https://docs.microsoft.com/en-us/powershell/module/activedirectory"
        cmdlets = @(
            "Get-ADUser", "Set-ADUser", "New-ADUser", "Remove-ADUser", "Enable-ADAccount", "Disable-ADAccount",
            "Get-ADGroup", "Set-ADGroup", "New-ADGroup", "Remove-ADGroup", "Add-ADGroupMember", "Remove-ADGroupMember",
            "Get-ADComputer", "Set-ADComputer", "New-ADComputer", "Remove-ADComputer",
            "Get-ADOrganizationalUnit", "Set-ADOrganizationalUnit", "New-ADOrganizationalUnit", "Remove-ADOrganizationalUnit",
            "Get-ADDomain", "Get-ADForest", "Get-ADDomainController", "Get-ADReplicationSite",
            "Search-ADAccount", "Unlock-ADAccount", "Reset-ADAccountPassword", "Set-ADAccountPassword"
        )
    },
    @{
        name = "Microsoft.PowerShell.Management"
        base_url = "https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.management"
        cmdlets = @(
            "Get-Process", "Stop-Process", "Start-Process", "Get-Service", "Start-Service", "Stop-Service", "Restart-Service",
            "Get-ChildItem", "New-Item", "Remove-Item", "Copy-Item", "Move-Item", "Rename-Item", "Test-Path",
            "Get-Location", "Set-Location", "Push-Location", "Pop-Location", "Get-Content", "Set-Content", "Add-Content",
            "Get-ItemProperty", "Set-ItemProperty", "New-ItemProperty", "Remove-ItemProperty", "Clear-ItemProperty"
        )
    },
    @{
        name = "Microsoft.PowerShell.Utility"
        base_url = "https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.utility"
        cmdlets = @(
            "Get-Variable", "Set-Variable", "New-Variable", "Remove-Variable", "Clear-Variable",
            "Write-Host", "Write-Output", "Write-Error", "Write-Warning", "Write-Verbose", "Write-Debug",
            "Select-Object", "Where-Object", "ForEach-Object", "Sort-Object", "Group-Object", "Measure-Object",
            "ConvertTo-Json", "ConvertFrom-Json", "ConvertTo-Csv", "ConvertFrom-Csv", "ConvertTo-Html"
        )
    },
    @{
        name = "Microsoft.PowerShell.Security"
        base_url = "https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.security"
        cmdlets = @(
            "Get-ExecutionPolicy", "Set-ExecutionPolicy", "Get-AuthenticodeSignature", "Set-AuthenticodeSignature",
            "Get-Credential", "ConvertTo-SecureString", "ConvertFrom-SecureString"
        )
    },
    @{
        name = "Exchange"
        base_url = "https://docs.microsoft.com/en-us/powershell/module/exchange"
        cmdlets = @(
            "Get-Mailbox", "Set-Mailbox", "New-Mailbox", "Remove-Mailbox", "Enable-Mailbox", "Disable-Mailbox",
            "Get-MailboxDatabase", "Set-MailboxDatabase", "New-MailboxDatabase", "Remove-MailboxDatabase"
        )
    }
)

function Get-CmdletDocumentation {
    param(
        [string]$CmdletName,
        [string]$ModuleName
    )
    
    try {
        # Construct Microsoft Learn URL for the cmdlet
        $cmdletUrl = "https://docs.microsoft.com/en-us/powershell/module/$($ModuleName.ToLower())/$($CmdletName.ToLower())"
        
        Write-Host "    Fetching: $CmdletName" -ForegroundColor Gray
        
        $response = Invoke-WebRequest -Uri $cmdletUrl -ErrorAction Stop -TimeoutSec 30
        $html = $response.Content
        
        # Extract structured content
        $documentation = Extract-CmdletContent -Html $html -CmdletName $CmdletName
        
        if ($documentation -and $documentation.description.Length -gt 100) {
            return $documentation
        }
        
        return $null
    }
    catch {
        Write-Host "      Failed to fetch $CmdletName`: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Extract-CmdletContent {
    param(
        [string]$Html,
        [string]$CmdletName
    )
    
    try {
        # Extract main content sections
        $content = @{
            synopsis = ""
            description = ""
            syntax = ""
            parameters = @()
            examples = @()
            inputs = ""
            outputs = ""
            notes = ""
            related_links = @()
        }
        
        # Extract synopsis
        $synopsisMatch = [regex]::Match($Html, '<h2[^>]*>Synopsis</h2>\s*<p[^>]*>(.*?)</p>', [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($synopsisMatch.Success) {
            $content.synopsis = Clean-HtmlText -Text $synopsisMatch.Groups[1].Value
        }
        
        # Extract description
        $descriptionMatch = [regex]::Match($Html, '<h2[^>]*>Description</h2>(.*?)(?=<h2|$)', [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($descriptionMatch.Success) {
            $content.description = Clean-HtmlText -Text $descriptionMatch.Groups[1].Value
        }
        
        # Extract syntax
        $syntaxMatch = [regex]::Match($Html, '<h2[^>]*>Syntax</h2>(.*?)(?=<h2|$)', [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($syntaxMatch.Success) {
            $syntaxContent = $syntaxMatch.Groups[1].Value
            # Extract code blocks from syntax section
            $codeMatches = [regex]::Matches($syntaxContent, '<code[^>]*>(.*?)</code>', [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            $syntaxBlocks = @()
            foreach ($match in $codeMatches) {
                $syntaxBlocks += Clean-HtmlText -Text $match.Groups[1].Value
            }
            $content.syntax = $syntaxBlocks -join "`n"
        }
        
        # Extract parameters
        $parametersMatch = [regex]::Match($Html, '<h2[^>]*>Parameters</h2>(.*?)(?=<h2|$)', [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($parametersMatch.Success) {
            $parametersContent = $parametersMatch.Groups[1].Value
            # Extract parameter definitions
            $paramMatches = [regex]::Matches($parametersContent, '<h3[^>]*>-([^<]+)</h3>(.*?)(?=<h3|$)', [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            foreach ($match in $paramMatches) {
                $paramName = $match.Groups[1].Value.Trim()
                $paramDesc = Clean-HtmlText -Text $match.Groups[2].Value
                $content.parameters += @{
                    name = $paramName
                    description = $paramDesc
                }
            }
        }
        
        # Extract examples
        $examplesMatch = [regex]::Match($Html, '<h2[^>]*>Examples</h2>(.*?)(?=<h2|$)', [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($examplesMatch.Success) {
            $examplesContent = $examplesMatch.Groups[1].Value
            # Extract example code blocks
            $exampleMatches = [regex]::Matches($examplesContent, '<h3[^>]*>Example \d+[^<]*</h3>(.*?)(?=<h3|$)', [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            foreach ($match in $exampleMatches) {
                $exampleContent = $match.Groups[1].Value
                $codeMatches = [regex]::Matches($exampleContent, '<code[^>]*>(.*?)</code>', [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
                foreach ($codeMatch in $codeMatches) {
                    $code = Clean-HtmlText -Text $codeMatch.Groups[1].Value
                    if ($code.Length -gt 10) {
                        $content.examples += $code
                    }
                }
            }
        }
        
        # Create comprehensive content text
        $fullContent = "# $CmdletName`n`n"
        
        if ($content.synopsis) {
            $fullContent += "## Synopsis`n$($content.synopsis)`n`n"
        }
        
        if ($content.description) {
            $fullContent += "## Description`n$($content.description)`n`n"
        }
        
        if ($content.syntax) {
            $fullContent += "## Syntax`n``````powershell`n$($content.syntax)`n```````n`n"
        }
        
        if ($content.parameters.Count -gt 0) {
            $fullContent += "## Parameters`n"
            foreach ($param in $content.parameters) {
                $fullContent += "### -$($param.name)`n$($param.description)`n`n"
            }
        }
        
        if ($content.examples.Count -gt 0) {
            $fullContent += "## Examples`n"
            for ($i = 0; $i -lt $content.examples.Count; $i++) {
                $fullContent += "### Example $($i + 1)`n``````powershell`n$($content.examples[$i])`n```````n`n"
            }
        }
        
        return @{
            content = $fullContent
            synopsis = $content.synopsis
            description = $content.description
            syntax = $content.syntax
            parameters = $content.parameters
            examples = $content.examples
        }
    }
    catch {
        Write-Host "      Failed to extract content: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Clean-HtmlText {
    param([string]$Text)
    
    if (-not $Text) { return "" }
    
    # Remove HTML tags but preserve structure
    $cleaned = $Text -replace '<br\s*/?>', "`n"
    $cleaned = $cleaned -replace '<p[^>]*>', "`n"
    $cleaned = $cleaned -replace '</p>', "`n"
    $cleaned = $cleaned -replace '<li[^>]*>', "- "
    $cleaned = $cleaned -replace '</li>', "`n"
    $cleaned = $cleaned -replace '<[^>]+>', ''
    
    # Clean up HTML entities
    $cleaned = $cleaned -replace '&nbsp;', ' ' -replace '&amp;', '&' -replace '&lt;', '<' -replace '&gt;', '>' -replace '&quot;', '"'
    
    # Clean up whitespace
    $cleaned = $cleaned -replace '\s+', ' ' -replace '\n\s*\n', "`n`n"
    $cleaned = $cleaned.Trim()
    
    return $cleaned
}

# Main scraping logic
try {
    $scrapedData = @{
        source = "microsoft_learn"
        tier = 1
        description = "Comprehensive PowerShell cmdlet reference documentation from Microsoft Learn"
        scraped_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        total_modules = $powerShellModules.Count
        entries = @()
        statistics = @{
            modules_processed = 0
            cmdlets_processed = 0
            cmdlets_with_examples = 0
            total_parameters = 0
            avg_content_length = 0
        }
    }
    
    $allEntries = @()
    $totalContentLength = 0
    $processedCmdlets = 0
    $cmdletsWithExamples = 0
    $totalParameters = 0
    
    foreach ($module in $powerShellModules) {
        Write-Host "Processing module: $($module.name)" -ForegroundColor Yellow
        
        $moduleProcessed = 0
        foreach ($cmdlet in $module.cmdlets) {
            if ($processedCmdlets -ge $MaxCmdlets) {
                Write-Host "  Reached maximum cmdlet limit ($MaxCmdlets)" -ForegroundColor Yellow
                break
            }
            
            $documentation = Get-CmdletDocumentation -CmdletName $cmdlet -ModuleName $module.name
            
            if ($documentation) {
                $entry = @{
                    id = "microsoft_$($module.name.Replace('.', '_').ToLower())_$($cmdlet.ToLower())"
                    title = "$cmdlet - $($module.name)"
                    content = $documentation.content
                    source = @{
                        url = "https://docs.microsoft.com/en-us/powershell/module/$($module.name.ToLower())/$($cmdlet.ToLower())"
                        type = "microsoft_learn"
                        module = $module.name
                        credibility = 1.0
                    }
                    cmdlets = @($cmdlet)
                    code_examples = $documentation.examples
                    tags = @("microsoft", "cmdlet", "documentation", $module.name.ToLower(), "tier1")
                    metadata = @{
                        module = $module.name
                        synopsis = $documentation.synopsis
                        parameters_count = $documentation.parameters.Count
                        examples_count = $documentation.examples.Count
                        has_syntax = [bool]$documentation.syntax
                    }
                    last_updated = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
                }
                
                $allEntries += $entry
                $processedCmdlets++
                $moduleProcessed++
                $totalContentLength += $documentation.content.Length
                $totalParameters += $documentation.parameters.Count
                
                if ($documentation.examples.Count -gt 0) {
                    $cmdletsWithExamples++
                }
            }
            
            Start-Sleep -Milliseconds 500  # Rate limiting
        }
        
        Write-Host "  Processed $moduleProcessed cmdlets from $($module.name)" -ForegroundColor Green
        $scrapedData.statistics.modules_processed++
        
        if ($processedCmdlets -ge $MaxCmdlets) { break }
        Start-Sleep -Milliseconds 1000  # Rate limiting between modules
    }
    
    # Finalize statistics
    $scrapedData.entries = $allEntries
    $scrapedData.statistics.cmdlets_processed = $processedCmdlets
    $scrapedData.statistics.cmdlets_with_examples = $cmdletsWithExamples
    $scrapedData.statistics.total_parameters = $totalParameters
    $scrapedData.statistics.avg_content_length = if ($processedCmdlets -gt 0) { [math]::Round($totalContentLength / $processedCmdlets) } else { 0 }
    
    # Save the scraped data
    $scrapedData | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "`nTier 1 Enhanced Microsoft Learn scraping completed!" -ForegroundColor Green
    Write-Host "=" * 60 -ForegroundColor Gray
    Write-Host "Statistics:" -ForegroundColor Cyan
    Write-Host "  Modules processed: $($scrapedData.statistics.modules_processed)" -ForegroundColor White
    Write-Host "  Cmdlets processed: $($scrapedData.statistics.cmdlets_processed)" -ForegroundColor White
    Write-Host "  Cmdlets with examples: $($scrapedData.statistics.cmdlets_with_examples)" -ForegroundColor White
    Write-Host "  Total parameters documented: $($scrapedData.statistics.total_parameters)" -ForegroundColor White
    Write-Host "  Average content length: $($scrapedData.statistics.avg_content_length) characters" -ForegroundColor White
    Write-Host "  Output saved to: $OutputPath" -ForegroundColor Green
}
catch {
    Write-Host "Enhanced Microsoft Learn scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Full error: $($_.Exception)" -ForegroundColor Red
    exit 1
}
