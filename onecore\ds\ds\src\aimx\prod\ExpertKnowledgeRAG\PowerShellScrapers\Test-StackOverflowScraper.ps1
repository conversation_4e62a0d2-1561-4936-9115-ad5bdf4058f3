# Simplified Stack Overflow scraper test
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxQuestions = 5
)

Write-Host "Testing Simplified Stack Overflow Scraper" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    
    Write-Host "✅ Framework initialized" -ForegroundColor Green
    
    # Get Stack Overflow questions
    Write-Host "`nFetching Stack Overflow questions..." -ForegroundColor Yellow
    
    $baseUrl = "https://api.stackexchange.com/2.3/"
    $tags = "powershell;active-directory"
    $apiUrl = "${baseUrl}questions?order=desc&sort=votes&tagged=${tags}&site=stackoverflow&pagesize=${MaxQuestions}&filter=withbody"
    
    $response = Invoke-WebRequestWithRetry -Uri $apiUrl -MaxRetries 2
    $data = $response.Content | ConvertFrom-Json
    
    Write-Host "✅ Found $($data.items.Count) questions" -ForegroundColor Green
    
    # Process each question into knowledge patterns
    $patterns = @()
    $count = 0
    
    foreach ($question in $data.items) {
        $count++
        Write-Host "`nProcessing question $count..." -ForegroundColor Yellow
        Write-Host "  Title: $($question.title)" -ForegroundColor Cyan
        Write-Host "  Score: $($question.score), Views: $($question.view_count)" -ForegroundColor Gray
        
        # Create knowledge pattern
        $pattern = New-KnowledgePattern -Title "Stack Overflow: $($question.title)" -Content $question.body -SourceUrl $question.link -SourceType "stackoverflow" -Domain "general_ad" -Operation "read" -Author "Stack Overflow Community" -CredibilityScore 0.80
        
        if ($pattern) {
            # Extract code blocks from the question body
            $codeBlocks = Extract-CodeBlocks -Content $question.body
            if ($codeBlocks.Count -gt 0) {
                $pattern.CodeTemplate = $codeBlocks[0]  # Use first code block as template
                Write-Host "  ✅ Extracted code template: $($codeBlocks.Count) blocks" -ForegroundColor Green
            }
            
            # Set relevance score based on question score
            $pattern.RelevanceScore = [Math]::Min(1.0, ($question.score / 100.0))
            
            # Add tags
            if ($question.tags) {
                $pattern.Tags += $question.tags
            }
            
            $patterns += $pattern
            Write-Host "  ✅ Created pattern: $($pattern.Id)" -ForegroundColor Green
        }
    }
    
    # Save results
    if ($patterns.Count -gt 0) {
        Write-Host "`nSaving results..." -ForegroundColor Yellow
        $outputFile = Save-ScrapingResults -Patterns $patterns -SourceType "stackoverflow"
        Write-Host "✅ Saved $($patterns.Count) patterns to: $outputFile" -ForegroundColor Green
        
        # Display summary
        Write-Host "`nSummary:" -ForegroundColor Yellow
        foreach ($pattern in $patterns) {
            Write-Host "  Pattern: $($pattern.Title)" -ForegroundColor Cyan
            Write-Host "    ID: $($pattern.Id)" -ForegroundColor Gray
            Write-Host "    Domain: $($pattern.Domain)" -ForegroundColor Gray
            Write-Host "    Relevance: $($pattern.RelevanceScore)" -ForegroundColor Gray
            Write-Host "    Code template: $($pattern.CodeTemplate.Length) characters" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ No patterns created" -ForegroundColor Red
    }
    
    Write-Host "`n✅ Stack Overflow scraper test completed!" -ForegroundColor Green
}
catch {
    Write-Host "`n❌ Stack Overflow scraper test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
}
