using IntentPlanningService.Configuration;
using IntentPlanningService.Models;
using IntentPlanningService.Services;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace IntentPlanningService.Controllers;

/// <summary>
/// API Controller for Intent Understanding and Workflow Planning
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class IntentPlanningController : ControllerBase
{
    private readonly ILogger<IntentPlanningController> _logger;
    private readonly IntentAnalysisService _intentAnalysisService;
    private readonly WorkflowPlanningService _workflowPlanningService;
    private readonly ToolDiscoveryService _toolDiscoveryService;
    private readonly RiskAssessmentService _riskAssessmentService;
    private readonly IntentPlanningConfiguration _config;

    public IntentPlanningController(
        ILogger<IntentPlanningController> logger,
        IntentAnalysisService intentAnalysisService,
        WorkflowPlanningService workflowPlanningService,
        ToolDiscoveryService toolDiscoveryService,
        RiskAssessmentService riskAssessmentService,
        IntentPlanningConfiguration config)
    {
        _logger = logger;
        _intentAnalysisService = intentAnalysisService;
        _workflowPlanningService = workflowPlanningService;
        _toolDiscoveryService = toolDiscoveryService;
        _riskAssessmentService = riskAssessmentService;
        _config = config;
    }

    /// <summary>
    /// Analyze user intent and generate executable workflow
    /// </summary>
    /// <param name="request">User request containing the input to analyze</param>
    /// <returns>Complete intent analysis result with executable workflow</returns>
    [HttpPost("analyze")]
    [ProducesResponseType(typeof(IntentAnalysisResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IntentAnalysisResult>> AnalyzeIntentAsync([FromBody] UserRequest request)
    {
        try
        {
            _logger.LogInformation("Received intent analysis request from user {UserId}: {UserInput}", 
                request.UserId, request.UserInput);

            // Validate request
            var validationResult = ValidateUserRequest(request);
            if (!validationResult.IsValid)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = string.Join("; ", validationResult.Errors),
                    Status = StatusCodes.Status400BadRequest
                });
            }

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Step 1: Analyze user intent
            var userGoal = await _intentAnalysisService.AnalyzeIntentAsync(request);
            
            if (userGoal.ExtractionConfidence < _config.IntentAnalysis.MinConfidenceThreshold)
            {
                _logger.LogWarning("Low confidence intent analysis for request {RequestId}: {Confidence}", 
                    request.RequestId, userGoal.ExtractionConfidence);
                
                return Ok(new IntentAnalysisResult
                {
                    RequestId = request.RequestId,
                    UserGoal = userGoal,
                    Success = false,
                    ErrorMessage = "Unable to understand intent with sufficient confidence",
                    ProcessingTimeMs = stopwatch.ElapsedMilliseconds
                });
            }

            // Step 2: Discover available tools
            var availableTools = await _toolDiscoveryService.DiscoverAvailableToolsAsync();
            
            if (!availableTools.Any())
            {
                _logger.LogWarning("No available tools found for workflow generation");
                
                return Ok(new IntentAnalysisResult
                {
                    RequestId = request.RequestId,
                    UserGoal = userGoal,
                    Success = false,
                    ErrorMessage = "No available tools found to execute the requested operation",
                    ProcessingTimeMs = stopwatch.ElapsedMilliseconds
                });
            }

            // Step 3: Generate executable workflow
            var workflow = await _workflowPlanningService.GenerateWorkflowAsync(userGoal, availableTools);
            
            if (!workflow.Steps.Any())
            {
                _logger.LogWarning("No workflow steps generated for request {RequestId}", request.RequestId);
                
                return Ok(new IntentAnalysisResult
                {
                    RequestId = request.RequestId,
                    UserGoal = userGoal,
                    Success = false,
                    ErrorMessage = "Unable to generate executable workflow for the requested operation",
                    ProcessingTimeMs = stopwatch.ElapsedMilliseconds
                });
            }

            // Step 4: Generate alternative workflows if enabled
            var alternativeWorkflows = new List<ExecutableWorkflow>();
            if (_config.WorkflowPlanning.EnableAlternativeWorkflows)
            {
                alternativeWorkflows = await _workflowPlanningService.GenerateAlternativeWorkflowsAsync(userGoal, availableTools);
            }

            stopwatch.Stop();

            var result = new IntentAnalysisResult
            {
                RequestId = request.RequestId,
                UserGoal = userGoal,
                PrimaryWorkflow = workflow,
                AlternativeWorkflows = alternativeWorkflows,
                AvailableTools = new List<AvailableTool>(), // Don't list all tools - user feedback: "why do we list thru all the powershell command tools"
                Success = true,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };

            _logger.LogInformation("Intent analysis completed for request {RequestId} in {ElapsedMs}ms with {StepCount} workflow steps", 
                request.RequestId, stopwatch.ElapsedMilliseconds, workflow.Steps.Count);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing intent for request {RequestId}", request.RequestId);
            
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal Server Error",
                Detail = "An error occurred while analyzing the intent",
                Status = StatusCodes.Status500InternalServerError
            });
        }
    }

    /// <summary>
    /// Get available tools from the Universal Tool Manager
    /// </summary>
    /// <param name="category">Optional category filter</param>
    /// <param name="capabilities">Optional capabilities filter</param>
    /// <returns>List of available tools</returns>
    [HttpGet("tools")]
    [ProducesResponseType(typeof(List<AvailableTool>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<AvailableTool>>> GetAvailableToolsAsync(
        [FromQuery] string? category = null,
        [FromQuery] string? capabilities = null)
    {
        try
        {
            var request = new ToolDiscoveryRequest();
            
            if (!string.IsNullOrEmpty(category))
                request.Category = category;
                
            if (!string.IsNullOrEmpty(capabilities))
                request.RequiredCapabilities = capabilities.Split(',').Select(c => c.Trim()).ToList();

            var tools = await _toolDiscoveryService.DiscoverAvailableToolsAsync(request);
            
            _logger.LogInformation("Retrieved {Count} available tools", tools.Count);
            
            return Ok(tools);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving available tools");
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal Server Error",
                Detail = "An error occurred while retrieving available tools"
            });
        }
    }

    /// <summary>
    /// Get a specific tool by ID
    /// </summary>
    /// <param name="toolId">The tool identifier</param>
    /// <returns>Tool information</returns>
    [HttpGet("tools/{toolId}")]
    [ProducesResponseType(typeof(AvailableTool), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<AvailableTool>> GetToolAsync([FromRoute] string toolId)
    {
        try
        {
            var tool = await _toolDiscoveryService.GetToolAsync(toolId);
            
            if (tool == null)
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Tool Not Found",
                    Detail = $"Tool with ID '{toolId}' was not found",
                    Status = StatusCodes.Status404NotFound
                });
            }
            
            return Ok(tool);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tool {ToolId}", toolId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal Server Error",
                Detail = "An error occurred while retrieving the tool"
            });
        }
    }

    /// <summary>
    /// Assess the risk level of a workflow
    /// </summary>
    /// <param name="workflow">The workflow to assess</param>
    /// <returns>Risk assessment result</returns>
    [HttpPost("assess-risk")]
    [ProducesResponseType(typeof(WorkflowRiskAssessment), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<WorkflowRiskAssessment>> AssessWorkflowRiskAsync([FromBody] ExecutableWorkflow workflow)
    {
        try
        {
            if (workflow == null || !workflow.Steps.Any())
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Workflow",
                    Detail = "Workflow must contain at least one step",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            var riskAssessment = await _riskAssessmentService.AssessWorkflowRiskAsync(workflow);
            
            _logger.LogInformation("Risk assessment completed for workflow {WorkflowId}: {RiskLevel}", 
                workflow.WorkflowId, riskAssessment.RiskLevel);
            
            return Ok(riskAssessment);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing workflow risk for {WorkflowId}", workflow.WorkflowId);
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal Server Error",
                Detail = "An error occurred while assessing workflow risk"
            });
        }
    }

    /// <summary>
    /// Check the health status of all tools
    /// </summary>
    /// <returns>Health status of all tools</returns>
    [HttpGet("health/tools")]
    [ProducesResponseType(typeof(Dictionary<string, string>), StatusCodes.Status200OK)]
    public async Task<ActionResult<Dictionary<string, string>>> CheckToolHealthAsync()
    {
        try
        {
            var healthStatus = await _toolDiscoveryService.CheckToolHealthAsync();
            
            _logger.LogInformation("Tool health check completed for {Count} tools", healthStatus.Count);
            
            return Ok(healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking tool health");
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal Server Error",
                Detail = "An error occurred while checking tool health"
            });
        }
    }

    /// <summary>
    /// Refresh the tool cache
    /// </summary>
    /// <returns>Success status</returns>
    [HttpPost("tools/refresh")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public async Task<ActionResult> RefreshToolCacheAsync()
    {
        try
        {
            await _toolDiscoveryService.RefreshToolCacheAsync();
            
            _logger.LogInformation("Tool cache refreshed successfully");
            
            return Ok(new { message = "Tool cache refreshed successfully", timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing tool cache");
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal Server Error",
                Detail = "An error occurred while refreshing tool cache"
            });
        }
    }

    #region Private Methods

    /// <summary>
    /// Validate the user request
    /// </summary>
    private RequestValidationResult ValidateUserRequest(UserRequest request)
    {
        var result = new RequestValidationResult { IsValid = true };

        if (string.IsNullOrWhiteSpace(request.UserInput))
        {
            result.IsValid = false;
            result.Errors.Add("User input is required");
        }

        if (string.IsNullOrWhiteSpace(request.UserId))
        {
            result.IsValid = false;
            result.Errors.Add("User ID is required");
        }

        if (request.UserInput?.Length > 5000)
        {
            result.IsValid = false;
            result.Errors.Add("User input exceeds maximum length of 5000 characters");
        }

        return result;
    }

    #endregion
}

/// <summary>
/// Represents the result of request validation
/// </summary>
public class RequestValidationResult
{
    public bool IsValid { get; set; } = true;
    public List<string> Errors { get; set; } = new();
}
