# Enhanced Multi-Tier PowerShell Knowledge Scraper
# Implements comprehensive tiered data collection strategy
param(
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "./ScrapedData/multitier_comprehensive_$(Get-Date -Format 'yyyyMMdd_HHmmss').json",
    [Parameter(Mandatory = $false)]
    [string[]]$Tiers = @("1", "2", "3"),
    [Parameter(Mandatory = $false)]
    [int]$MaxItemsPerSource = 50,
    [Parameter(Mandatory = $false)]
    [string]$GitHubToken = $env:GITHUB_TOKEN
)

Write-Host "Enhanced Multi-Tier PowerShell Knowledge Scraper" -ForegroundColor Cyan
Write-Host "Implementing comprehensive tiered data collection strategy" -ForegroundColor Gray
Write-Host "=" * 80 -ForegroundColor Gray

# Tier definitions with credibility scores
$tierDefinitions = @{
    "1" = @{
        name = "Microsoft Official Documentation"
        description = "High-quality structured baseline from Microsoft Learn and official docs"
        credibility = 0.95
        sources = @("microsoft-learn", "microsoft-docs", "powershell-gallery-official")
    }
    "2" = @{
        name = "Community & GitHub"
        description = "Real-world Q&A, scripts, and examples from GitHub and StackOverflow"
        credibility = 0.80
        sources = @("github-repos", "stackoverflow", "reddit-powershell")
    }
    "3" = @{
        name = "Expert Blogs & Articles"
        description = "Community experts, MVPs, and specialized PowerShell blogs"
        credibility = 0.85
        sources = @("expert-blogs", "community-sites", "rss-feeds")
    }
}

# GitHub configuration with priority organizations
$githubConfig = @{
    api_base = "https://api.github.com"
    headers = @{
        "Accept" = "application/vnd.github.v3+json"
        "User-Agent" = "PowerShell-Knowledge-Scraper"
    }
    priority_orgs = @(
        "microsoft", "microsoftdocs", "azure", "powershell", "microsoftgraph",
        "azure-samples", "microsoft-365", "officedev", "microsoftlearning",
        "dfinke", "lazywinadmin", "adamtheautomator", "powershell-gallery"
    )
    target_topics = @(
        "powershell", "powershell-script", "automation", "active-directory", 
        "powershell-module", "windows-powershell", "powershell-core", "devops",
        "system-administration", "microsoft", "azure", "office365", "exchange",
        "sharepoint", "teams", "graph-api", "azure-ad", "intune", "security"
    )
}

# Community sources configuration
$communitySources = @(
    @{
        name = "Adam the Automator"
        base_url = "https://adamtheautomator.com"
        rss_feed = "https://adamtheautomator.com/feed/"
        credibility = 0.9
        type = "expert_blog"
    },
    @{
        name = "PowerShell Explained (Kevin Marquette)"
        base_url = "https://powershellexplained.com"
        rss_feed = "https://powershellexplained.com/feed.xml"
        credibility = 0.95
        type = "expert_blog"
    },
    @{
        name = "Petri.com PowerShell"
        base_url = "https://petri.com"
        search_url = "https://petri.com/search?q=powershell"
        credibility = 0.85
        type = "tech_site"
    },
    @{
        name = "4sysops"
        base_url = "https://4sysops.com"
        search_url = "https://4sysops.com/?s=powershell"
        credibility = 0.8
        type = "tech_site"
    },
    @{
        name = "PowerShell.org"
        base_url = "https://powershell.org"
        credibility = 0.9
        type = "community"
    },
    @{
        name = "Microsoft DevBlogs PowerShell"
        base_url = "https://devblogs.microsoft.com/powershell/"
        rss_feed = "https://devblogs.microsoft.com/powershell/feed/"
        credibility = 0.95
        type = "official_blog"
    }
)

# Add GitHub token to headers if provided
if ($GitHubToken) {
    $githubConfig.headers["Authorization"] = "token $GitHubToken"
    Write-Host "Using GitHub token for enhanced API limits" -ForegroundColor Green
}

function Invoke-WebRequestWithRetry {
    param(
        [string]$Uri,
        [hashtable]$Headers = @{},
        [int]$MaxRetries = 3,
        [int]$DelaySeconds = 2
    )
    
    $attempt = 0
    while ($attempt -lt $MaxRetries) {
        try {
            $attempt++
            $response = Invoke-RestMethod -Uri $Uri -Headers $Headers -ErrorAction Stop
            return $response
        }
        catch {
            Write-Host "    Attempt $attempt failed: $($_.Exception.Message)" -ForegroundColor Yellow
            if ($attempt -eq $MaxRetries) {
                throw
            }
            Start-Sleep -Seconds ($DelaySeconds * $attempt)
        }
    }
}

function Get-Tier1MicrosoftContent {
    param([int]$MaxItems = 50)
    
    Write-Host "Tier 1: Collecting Microsoft Official Documentation..." -ForegroundColor Green
    
    $entries = @()
    
    # Microsoft Learn PowerShell documentation
    $learnUrls = @(
        "https://learn.microsoft.com/en-us/powershell/",
        "https://learn.microsoft.com/en-us/powershell/module/",
        "https://learn.microsoft.com/en-us/powershell/scripting/"
    )
    
    foreach ($url in $learnUrls) {
        try {
            Write-Host "  Processing: $url" -ForegroundColor Gray
            $response = Invoke-WebRequest -Uri $url -ErrorAction Stop
            
            # Extract links to specific cmdlet documentation
            $links = $response.Links | Where-Object { 
                $_.href -match "powershell/module/" -and 
                $_.href -match "\.(get-|set-|new-|remove-|add-)" 
            } | Select-Object -First 20
            
            foreach ($link in $links) {
                $fullUrl = if ($link.href.StartsWith("http")) { $link.href } else { "https://learn.microsoft.com$($link.href)" }
                
                try {
                    $cmdletResponse = Invoke-WebRequest -Uri $fullUrl -ErrorAction Stop
                    $content = $cmdletResponse.Content
                    
                    # Extract structured content
                    $title = if ($cmdletResponse.ParsedHtml.title) { $cmdletResponse.ParsedHtml.title } else { $link.innerText }
                    
                    $entries += @{
                        id = "tier1_microsoft_$($fullUrl.GetHashCode().ToString().Replace('-', 'n'))"
                        title = $title
                        content = $content
                        source = @{
                            url = $fullUrl
                            type = "microsoft_official"
                            tier = 1
                            credibility = 0.95
                        }
                        tier = 1
                        tags = @("tier1", "microsoft", "official", "documentation")
                        last_updated = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
                    }
                    
                    if ($entries.Count -ge $MaxItems) { break }
                }
                catch {
                    Write-Host "    Failed to process: $fullUrl" -ForegroundColor Red
                }
            }
            
            if ($entries.Count -ge $MaxItems) { break }
        }
        catch {
            Write-Host "  Failed to process: $url - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "  Collected $($entries.Count) Tier 1 entries" -ForegroundColor Green
    return $entries
}

function Get-Tier2GitHubContent {
    param([int]$MaxItems = 50)
    
    Write-Host "Tier 2: Collecting GitHub Repositories and Scripts..." -ForegroundColor Green
    
    $entries = @()
    
    # Search for repositories by organization
    foreach ($org in $githubConfig.priority_orgs) {
        if ($entries.Count -ge $MaxItems) { break }
        
        try {
            Write-Host "  Searching organization: $org" -ForegroundColor Gray
            $searchUrl = "$($githubConfig.api_base)/search/repositories?q=org:$org+language:powershell&sort=stars&order=desc&per_page=10"
            $response = Invoke-WebRequestWithRetry -Uri $searchUrl -Headers $githubConfig.headers
            
            foreach ($repo in $response.items) {
                if ($entries.Count -ge $MaxItems) { break }
                
                # Get repository files
                $files = Get-GitHubRepositoryFiles -RepoFullName $repo.full_name -MaxFiles 5
                
                foreach ($file in $files) {
                    $entries += @{
                        id = "tier2_github_$($repo.full_name.Replace('/', '_'))_$($file.name.Replace('.', '_'))"
                        title = "$($file.name) - $($repo.name)"
                        content = $file.content
                        source = @{
                            url = $file.html_url
                            type = "github"
                            tier = 2
                            credibility = [math]::Min(0.9, [math]::Log10($repo.stargazers_count + 1) / 4)
                            repository = $repo.full_name
                            stars = $repo.stargazers_count
                        }
                        tier = 2
                        tags = @("tier2", "github", "community", $file.type) + $repo.topics
                        metadata = @{
                            repository_description = $repo.description
                            file_path = $file.path
                            organization = $org
                        }
                        last_updated = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
                    }
                }
            }
        }
        catch {
            Write-Host "    Failed to search organization $org : $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "  Collected $($entries.Count) Tier 2 entries" -ForegroundColor Green
    return $entries
}

function Get-GitHubRepositoryFiles {
    param(
        [string]$RepoFullName,
        [int]$MaxFiles = 5
    )

    try {
        $searchUrl = "$($githubConfig.api_base)/search/code?q=repo:$RepoFullName+extension:ps1+OR+extension:psm1&per_page=$MaxFiles"
        $response = Invoke-WebRequestWithRetry -Uri $searchUrl -Headers $githubConfig.headers

        $files = @()
        foreach ($file in $response.items) {
            if ($file.name -match '\.(ps1|psm1)$') {
                # Get file content
                $contentResponse = Invoke-WebRequestWithRetry -Uri $file.download_url -Headers @{}

                $files += @{
                    name = $file.name
                    path = $file.path
                    content = $contentResponse
                    html_url = $file.html_url
                    type = if ($file.name -match '\.ps1$') { "script" } else { "module" }
                }
            }
        }

        return $files
    }
    catch {
        Write-Host "      Failed to get files for $RepoFullName : $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

function Get-Tier2StackOverflowContent {
    param([int]$MaxItems = 50)

    Write-Host "Tier 2: Collecting StackOverflow Q&A..." -ForegroundColor Green

    $entries = @()
    $baseUrl = "https://api.stackexchange.com/2.3/"
    $tags = @("powershell", "active-directory", "powershell-activedirectory", "azure-ad", "microsoft-graph")

    foreach ($tag in $tags) {
        if ($entries.Count -ge $MaxItems) { break }

        try {
            Write-Host "  Searching tag: $tag" -ForegroundColor Gray
            $searchUrl = "${baseUrl}questions?order=desc&sort=votes&tagged=$tag&site=stackoverflow&filter=withbody&pagesize=10"
            $response = Invoke-WebRequestWithRetry -Uri $searchUrl

            foreach ($question in $response.items) {
                if ($entries.Count -ge $MaxItems) { break }

                # Get answers for this question
                $answersUrl = "${baseUrl}questions/$($question.question_id)/answers?order=desc&sort=votes&site=stackoverflow&filter=withbody"
                $answersResponse = Invoke-WebRequestWithRetry -Uri $answersUrl

                # Build comprehensive content
                $content = "# Question: $($question.title)`n`n"
                $content += "**Score:** $($question.score) | **Views:** $($question.view_count)`n`n"
                $content += "## Problem Description`n`n$($question.body)`n`n"

                if ($answersResponse.items.Count -gt 0) {
                    $content += "## Solutions`n`n"
                    $answerIndex = 1
                    foreach ($answer in $answersResponse.items) {
                        $content += "### Solution $answerIndex"
                        if ($answer.is_accepted) { $content += " ✅ (Accepted)" }
                        $content += " - Score: $($answer.score)`n`n"
                        $content += "$($answer.body)`n`n"
                        $answerIndex++
                    }
                }

                $entries += @{
                    id = "tier2_stackoverflow_$($question.question_id)"
                    title = $question.title
                    content = $content
                    source = @{
                        url = $question.link
                        type = "stackoverflow"
                        tier = 2
                        credibility = [math]::Min(0.9, [math]::Max(0.5, $question.score / 50.0))
                    }
                    tier = 2
                    tags = @("tier2", "stackoverflow", "qa", $tag)
                    metadata = @{
                        question_score = $question.score
                        view_count = $question.view_count
                        answer_count = $answersResponse.items.Count
                        creation_date = $question.creation_date
                    }
                    last_updated = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
                }
            }
        }
        catch {
            Write-Host "    Failed to search StackOverflow tag $tag : $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    Write-Host "  Collected $($entries.Count) Tier 2 StackOverflow entries" -ForegroundColor Green
    return $entries
}

function Get-Tier3CommunityContent {
    param([int]$MaxItems = 50)

    Write-Host "Tier 3: Collecting Expert Blogs and Community Content..." -ForegroundColor Green

    $entries = @()

    foreach ($source in $communitySources) {
        if ($entries.Count -ge $MaxItems) { break }

        Write-Host "  Processing: $($source.name)" -ForegroundColor Gray

        if ($source.rss_feed) {
            $articles = Get-RSSFeedArticles -FeedUrl $source.rss_feed -SourceName $source.name -MaxArticles 10

            foreach ($article in $articles) {
                if ($entries.Count -ge $MaxItems) { break }

                $content = Get-ArticleContent -Url $article.url -Title $article.title
                if ($content) {
                    $entries += @{
                        id = "tier3_community_$($source.name.Replace(' ', '_').ToLower())_$($article.url.GetHashCode().ToString().Replace('-', 'n'))"
                        title = $article.title
                        content = $content
                        source = @{
                            url = $article.url
                            type = "community"
                            tier = 3
                            credibility = $source.credibility
                            site_name = $source.name
                        }
                        tier = 3
                        tags = @("tier3", "community", $source.type, $source.name.Replace(' ', '-').ToLower())
                        metadata = @{
                            published_date = $article.published_date
                            description = $article.description
                            source_type = $source.type
                        }
                        last_updated = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
                    }
                }
            }
        }
    }

    Write-Host "  Collected $($entries.Count) Tier 3 entries" -ForegroundColor Green
    return $entries
}

function Get-RSSFeedArticles {
    param(
        [string]$FeedUrl,
        [string]$SourceName,
        [int]$MaxArticles = 10
    )

    try {
        $response = Invoke-RestMethod -Uri $FeedUrl -ErrorAction Stop
        $articles = @()

        # Handle different RSS formats
        $items = if ($response.rss.channel.item) {
            $response.rss.channel.item
        } elseif ($response.feed.entry) {
            $response.feed.entry
        } else {
            $response.channel.item
        }

        $count = 0
        foreach ($item in $items) {
            if ($count -ge $MaxArticles) { break }

            # Filter for PowerShell-related content
            $title = if ($item.title) { $item.title } else { $item.title.'#text' }
            $description = if ($item.description) { $item.description } else { $item.summary }

            if ($title -match "powershell|ps1|cmdlet|automation|active.directory" -or
                $description -match "powershell|ps1|cmdlet|automation|active.directory") {

                $link = if ($item.link) { $item.link } else { $item.link.href }
                $pubDate = if ($item.pubDate) { $item.pubDate } else { $item.published }

                $articles += @{
                    title = $title
                    url = $link
                    description = $description
                    published_date = $pubDate
                    source = $SourceName
                }
                $count++
            }
        }

        return $articles
    }
    catch {
        Write-Host "    Failed to fetch RSS feed: $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

function Get-ArticleContent {
    param(
        [string]$Url,
        [string]$Title
    )

    try {
        $response = Invoke-WebRequest -Uri $Url -ErrorAction Stop -TimeoutSec 30
        $html = $response.Content

        # Extract main content using common patterns
        $content = ""
        $contentPatterns = @(
            '<article[^>]*>(.*?)</article>',
            '<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>',
            '<div[^>]*class="[^"]*post[^"]*"[^>]*>(.*?)</div>',
            '<main[^>]*>(.*?)</main>'
        )

        foreach ($pattern in $contentPatterns) {
            $matches = [regex]::Matches($html, $pattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            if ($matches.Count -gt 0) {
                $content = $matches[0].Groups[1].Value
                break
            }
        }

        # Clean HTML content
        $cleanContent = Clean-HtmlContent -HtmlContent $content

        # Validate content quality
        if ($cleanContent.Length -gt 500 -and $cleanContent -match "powershell|cmdlet|Get-|Set-|New-") {
            return $cleanContent
        }

        return $null
    }
    catch {
        Write-Host "      Failed to fetch article: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Clean-HtmlContent {
    param([string]$HtmlContent)

    if (-not $HtmlContent) { return "" }

    # Remove script and style tags
    $content = $HtmlContent -replace '<script[^>]*>.*?</script>', '' -replace '<style[^>]*>.*?</style>', ''

    # Remove HTML comments
    $content = $content -replace '<!--.*?-->', ''

    # Convert common HTML entities
    $content = $content -replace '&nbsp;', ' ' -replace '&amp;', '&' -replace '&lt;', '<' -replace '&gt;', '>' -replace '&quot;', '"'

    # Preserve code blocks but clean other HTML
    $codeBlocks = @()
    $codePattern = '<(?:pre|code)[^>]*>(.*?)</(?:pre|code)>'
    $matches = [regex]::Matches($content, $codePattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)

    for ($i = 0; $i -lt $matches.Count; $i++) {
        $placeholder = "CODEBLOCK_PLACEHOLDER_$i"
        $codeBlocks += $matches[$i].Groups[1].Value
        $content = $content -replace [regex]::Escape($matches[$i].Value), $placeholder
    }

    # Remove remaining HTML tags
    $content = $content -replace '<[^>]+>', ''

    # Restore code blocks
    for ($i = 0; $i -lt $codeBlocks.Count; $i++) {
        $placeholder = "CODEBLOCK_PLACEHOLDER_$i"
        $content = $content -replace $placeholder, "`n```powershell`n$($codeBlocks[$i])`n```"
    }

    # Clean up whitespace
    $content = $content -replace '\s+', ' ' -replace '\n\s*\n', "`n`n"
    $content = $content.Trim()

    return $content
}

# Main execution logic
try {
    $scrapedData = @{
        source = "multi_tier_comprehensive"
        description = "Comprehensive PowerShell knowledge from multiple tiers of sources"
        scraped_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        tier_definitions = $tierDefinitions
        entries = @()
        statistics = @{
            total_entries = 0
            tier1_entries = 0
            tier2_entries = 0
            tier3_entries = 0
            avg_credibility = 0
            processing_time_seconds = 0
        }
    }

    $startTime = Get-Date
    $allEntries = @()

    # Process each requested tier
    foreach ($tier in $Tiers) {
        Write-Host "`nProcessing Tier $tier..." -ForegroundColor Cyan

        switch ($tier) {
            "1" {
                $tier1Entries = Get-Tier1MicrosoftContent -MaxItems $MaxItemsPerSource
                $allEntries += $tier1Entries
                $scrapedData.statistics.tier1_entries = $tier1Entries.Count
            }
            "2" {
                # GitHub content
                $tier2GitHubEntries = Get-Tier2GitHubContent -MaxItems ([math]::Floor($MaxItemsPerSource / 2))
                $allEntries += $tier2GitHubEntries

                # StackOverflow content
                $tier2StackOverflowEntries = Get-Tier2StackOverflowContent -MaxItems ([math]::Floor($MaxItemsPerSource / 2))
                $allEntries += $tier2StackOverflowEntries

                $scrapedData.statistics.tier2_entries = $tier2GitHubEntries.Count + $tier2StackOverflowEntries.Count
            }
            "3" {
                $tier3Entries = Get-Tier3CommunityContent -MaxItems $MaxItemsPerSource
                $allEntries += $tier3Entries
                $scrapedData.statistics.tier3_entries = $tier3Entries.Count
            }
        }
    }

    # Calculate statistics
    $endTime = Get-Date
    $scrapedData.statistics.processing_time_seconds = [math]::Round(($endTime - $startTime).TotalSeconds, 2)
    $scrapedData.statistics.total_entries = $allEntries.Count

    if ($allEntries.Count -gt 0) {
        $avgCredibility = ($allEntries | ForEach-Object { $_.source.credibility } | Measure-Object -Average).Average
        $scrapedData.statistics.avg_credibility = [math]::Round($avgCredibility, 3)
    }

    # Add tier distribution
    $scrapedData.statistics.tier_distribution = @{
        tier1_percentage = if ($allEntries.Count -gt 0) { [math]::Round(($scrapedData.statistics.tier1_entries / $allEntries.Count) * 100, 1) } else { 0 }
        tier2_percentage = if ($allEntries.Count -gt 0) { [math]::Round(($scrapedData.statistics.tier2_entries / $allEntries.Count) * 100, 1) } else { 0 }
        tier3_percentage = if ($allEntries.Count -gt 0) { [math]::Round(($scrapedData.statistics.tier3_entries / $allEntries.Count) * 100, 1) } else { 0 }
    }

    $scrapedData.entries = $allEntries

    # Save the comprehensive dataset
    $scrapedData | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8

    # Display comprehensive results
    Write-Host "`n" + "=" * 80 -ForegroundColor Green
    Write-Host "Multi-Tier PowerShell Knowledge Scraping Completed!" -ForegroundColor Green
    Write-Host "=" * 80 -ForegroundColor Green

    Write-Host "`nOverall Statistics:" -ForegroundColor Cyan
    Write-Host "  Total entries collected: $($scrapedData.statistics.total_entries)" -ForegroundColor White
    Write-Host "  Processing time: $($scrapedData.statistics.processing_time_seconds) seconds" -ForegroundColor White
    Write-Host "  Average credibility score: $($scrapedData.statistics.avg_credibility)" -ForegroundColor White

    Write-Host "`nTier Breakdown:" -ForegroundColor Cyan
    Write-Host "  Tier 1 (Microsoft Official): $($scrapedData.statistics.tier1_entries) entries ($($scrapedData.statistics.tier_distribution.tier1_percentage)%)" -ForegroundColor White
    Write-Host "  Tier 2 (Community/GitHub): $($scrapedData.statistics.tier2_entries) entries ($($scrapedData.statistics.tier_distribution.tier2_percentage)%)" -ForegroundColor White
    Write-Host "  Tier 3 (Expert Blogs): $($scrapedData.statistics.tier3_entries) entries ($($scrapedData.statistics.tier_distribution.tier3_percentage)%)" -ForegroundColor White

    Write-Host "`nData Quality Insights:" -ForegroundColor Cyan
    $highCredibilityCount = ($allEntries | Where-Object { $_.source.credibility -ge 0.9 }).Count
    $mediumCredibilityCount = ($allEntries | Where-Object { $_.source.credibility -ge 0.7 -and $_.source.credibility -lt 0.9 }).Count
    $lowerCredibilityCount = ($allEntries | Where-Object { $_.source.credibility -lt 0.7 }).Count

    Write-Host "  High credibility (≥0.9): $highCredibilityCount entries" -ForegroundColor White
    Write-Host "  Medium credibility (0.7-0.9): $mediumCredibilityCount entries" -ForegroundColor White
    Write-Host "  Lower credibility (<0.7): $lowerCredibilityCount entries" -ForegroundColor White

    Write-Host "`nOutput saved to: $OutputPath" -ForegroundColor Green
    Write-Host "`nThis comprehensive dataset provides:" -ForegroundColor Yellow
    Write-Host "  • Tier 1: High-quality structured baseline from Microsoft" -ForegroundColor Gray
    Write-Host "  • Tier 2: Real-world Q&A and script examples from community" -ForegroundColor Gray
    Write-Host "  • Tier 3: Expert insights and specialized tutorials" -ForegroundColor Gray
    Write-Host "  • Dramatically improved conversational query understanding" -ForegroundColor Gray
}
catch {
    Write-Host "Multi-tier scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Full error: $($_.Exception)" -ForegroundColor Red
    exit 1
}
