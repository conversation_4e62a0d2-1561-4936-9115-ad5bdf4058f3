/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    Planner.cpp

Abstract:
    Implementation of AIMX Planner that creates execution plans for different types of requests.
    The Planner analyzes queries and generates detailed workflow plans that can be executed
    by the Orchestrator component.

Author:

    <PERSON> (SNAKE FIGHTER) (lindakup) 06/03/2025

--*/

#include "pch.hxx"
#include "Planner.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include "McpSvrMgr.h"
#include "McpToolManager.h"
#include "LLMInfer.h"
#include "ConversationManager.h"
#include "Orchestrator.h"
#include <cpprest/http_client.h>
#include "RagServiceManager.h"
#include "PshManager.h"
#include "Planner.cpp.tmh"

// Global map to track PlanningContext pointers for cancellation
std::unordered_map<GUID, PlanningContext*, GuidHash, GuidEqual> g_PlanningContextMap;
std::mutex g_PlanningContextMapMutex;

// Global map to track active planning operations (definition - declaration is in AimxCommon.h)
std::unordered_map<GUID, HANDLE, GuidHash, GuidEqual> g_PlanningThreadMap;
std::mutex g_PlanningThreadMapMutex;

HRESULT
Planner::StartPlanningAsync(
    _In_ const GUID& operationId,
    _In_ const std::wstring& query,
    _In_ AIMX_EXECUTION_MODE executionMode
    )
/*++

Routine Description:
    Starts asynchronous planning for a given operation. Creates a worker thread
    that will analyze the query and generate an execution plan.

Arguments:
    operationId   - The unique identifier for the operation
    query         - The query string to analyze and plan for
    executionMode - The execution mode (automated or interactive)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    HANDLE hThread = NULL;
    PlanningContext* pContext = nullptr;
    std::lock_guard<std::mutex> lock(g_PlanningContextMapMutex);

    TraceInfo(AimxPlanner, "Entry");

    // Allocate context for worker thread
    pContext = new (std::nothrow) PlanningContext();
    if (!pContext)
    {
        TraceErr(AimxPlanner, "Failed to allocate PlanningContext");
        hr = E_OUTOFMEMORY;
        goto Exit;
    }

    pContext->OperationId = operationId;
    pContext->Query = query;
    pContext->ExecutionMode = executionMode;

    // Store context for cancellation
    g_PlanningContextMap[operationId] = pContext;

    // Create worker thread for planning
    hThread = CreateThread(
        NULL,                           // Default security attributes
        0,                              // Default stack size
        PlanningWorkerThread,           // Thread function
        pContext,                       // Thread parameter
        0,                              // Default creation flags
        NULL                            // Don't need thread ID
        );

    if (!hThread)
    {
        DWORD dwError = GetLastError();
        hr = HRESULT_FROM_WIN32(dwError);
        TraceErr(AimxPlanner, "CreateThread failed: %!WINERROR!", dwError);
        goto Exit;
    }

    // Store thread handle for potential cancellation
    {
        std::lock_guard<std::mutex> lock(g_PlanningThreadMapMutex);
        g_PlanningThreadMap[operationId] = hThread;
    }

    pContext = nullptr; // Thread owns the context now
    hThread = NULL;     // Thread map owns the handle now

Exit:
    if (pContext)
    {
        delete pContext;
    }
    if (hThread)
    {
        CloseHandle(hThread);
    }

    TraceInfo(AimxPlanner, "Exit - StartPlanningAsync. hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
Planner::CreateSimplePlan(
    _In_ const GUID& operationId,
    _In_ const std::wstring& command
    )
/*++

Routine Description:
    Creates a simple execution plan for direct commands. This is a synchronous
    operation that generates a basic plan structure.

Arguments:
    operationId - The unique identifier for the operation
    command     - The command to create a plan for

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    std::shared_ptr<AIMX_OPERATION> operation;
    nlohmann::json executionPlan;
    std::string utf8Command;

    TraceInfo(AimxPlanner, "Entry");

    // Find operation in global map
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        auto it = g_OperationMap.find(operationId);
        if (it == g_OperationMap.end())
        {
            TraceErr(AimxPlanner, "Operation not found");
            hr = E_INVALIDARG;
            goto Exit;
        }
        operation = it->second;
    }


    // first convert command to UTF-8 suitable for JSON
    utf8Command = WideToUtf8(command);
    if (utf8Command.empty())
    {
        TraceErr(AimxPlanner, "Failed to convert command to UTF-8");
        hr = E_INVALIDARG;
        goto Exit;
    }

    executionPlan[AimxConstants::JsonFields::AIMX_JSON_KEY_PLAN_TYPE] = AimxConstants::PlanTypes::AIMX_PLAN_TYPE_DIRECT;
    executionPlan[AimxConstants::JsonFields::AIMX_STEPS] = nlohmann::json::array({
        {
            {AimxConstants::JsonFields::AIMX_STEP_ID, 1},
            {AimxConstants::JsonFields::AIMX_ACTION, AimxConstants::Actions::AIMX_ACTION_EXECUTE_COMMAND},
            {AimxConstants::JsonFields::AIMX_COMMAND, utf8Command},
            {AimxConstants::JsonFields::AIMX_TARGET, AimxConstants::Targets::AIMX_TARGET_EXTERNAL_SERVICE},
            {AimxConstants::JsonFields::AIMX_JSON_KEY_SERVICE, AimxConstants::Targets::AIMX_TARGET_AD_TOOLS_AGENT},
            {AimxConstants::JsonFields::AIMX_JSON_KEY_TIMEOUT, 30000}
        }
    });
    executionPlan[AimxConstants::JsonFields::AIMX_REQUIRED_SERVICES] = nlohmann::json::array({AimxConstants::Targets::AIMX_TARGET_AD_TOOLS_AGENT});
    executionPlan[AimxConstants::JsonFields::AIMX_JSON_KEY_RISK_LEVEL] = AimxConstants::RiskLevels::AIMX_RISK_LEVEL_LOW;

    // Update operation with the plan
    operation->ExecutionPlan = executionPlan;
    operation->Status = AIMX_STATUS_PLAN_READY;

Exit:
    TraceInfo(AimxPlanner, "Exit - CreateSimplePlan. hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
Planner::CancelPlanning(
    _In_ const GUID& operationId
    )
/*++

Routine Description:
    Cancels an ongoing planning operation by terminating the worker thread.

Arguments:
    operationId - The unique identifier for the operation to cancel

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    HANDLE hThread = NULL;
    PlanningContext* pContext = nullptr;
    std::shared_ptr<AIMX_OPERATION> operation;

    TraceInfo(AimxPlanner, "Entry");

    // Set cancelRequested flag
    {
        std::lock_guard<std::mutex> lock(g_PlanningContextMapMutex);
        auto it = g_PlanningContextMap.find(operationId);
        if (it != g_PlanningContextMap.end()) {
            pContext = it->second;
            pContext->cancelRequested = true;
        }
    }

    // Find and remove thread handle from map
    {
        std::lock_guard<std::mutex> lock(g_PlanningThreadMapMutex);
        auto it = g_PlanningThreadMap.find(operationId);
        if (it != g_PlanningThreadMap.end()) {
            hThread = it->second;
            g_PlanningThreadMap.erase(it);
        }
    }

    // Wait for the planning thread to exit
    if (hThread) {
        WaitForSingleObject(hThread, INFINITE);
        CloseHandle(hThread);
    }

    // Update operation status
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        auto it = g_OperationMap.find(operationId);
        if (it != g_OperationMap.end()) {
            operation = it->second;
            operation->Status = AIMX_STATUS_CANCELLED;
            GetSystemTimeAsFileTime(&operation->CompletionTime);
        }
    }

    TraceInfo(AimxPlanner, "Exit. hr: %!HRESULT!", hr);
    return hr;
}

DWORD WINAPI
Planner::PlanningWorkerThread(
    _In_ LPVOID lpParameter
)
/*++

Routine Description:
    Worker thread function that performs the actual planning work asynchronously.

Arguments:
    lpParameter - Pointer to PlanningContext structure

Return Value:
    0 on success, or an error code on failure.

--*/
{
    HRESULT hr = S_OK;
    PlanningContext* pContext = static_cast<PlanningContext*>(lpParameter);
    std::shared_ptr<AIMX_OPERATION> operation;
    nlohmann::json planningSteps;
    nlohmann::json executionPlan;

    TraceInfo(AimxPlanner, "Entry");

    if (!pContext)
    {
        TraceErr(AimxPlanner, "Invalid context parameter");
        return ERROR_INVALID_PARAMETER;
    }

    // Find operation in global map
    {
        std::lock_guard<std::mutex> lock(g_OperationMapMutex);
        auto it = g_OperationMap.find(pContext->OperationId);
        if (it == g_OperationMap.end())
        {
            TraceErr(AimxPlanner, "Operation not found in worker thread");
            delete pContext;
            return ERROR_NOT_FOUND;
        }
        operation = it->second;
    }

    // Send initial progress update
    ConversationSessionManager::NotifyOperationProgress(
        pContext->OperationId,
        L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_ANALYSIS) + L"] " + std::wstring(AimxConstants::Messages::AIMX_MSG_ANALYZING_REQUEST)
    );

    // Planning / Execution processes
    try
    {
        // Step 0: Try PowerShell execution first (before MCP analysis)        
        hr = TryPowerShellExecution(pContext->OperationId, pContext->Query);
        if (SUCCEEDED(hr))
        {
            TraceInfo(AimxPlanner, "PowerShell execution completed successfully, skipping MCP analysis");
            operation->Status = AIMX_STATUS_COMPLETED;
            ConversationSessionManager::NotifyOperationCompleted(
                pContext->OperationId,
                L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_EXECUTION_COMPLETED) + L"] PowerShell execution Succeeded"
            );
            // we can skip the rest of the steps and exit.
            goto Exit;
        }

        // Step 1: Analyze the query (only if PowerShell execution was not successful)
        hr = AnalyzeQuery(pContext->OperationId, pContext->Query, planningSteps);
        if (FAILED(hr))
        {
            TraceErr(AimxPlanner, "AnalyzeQuery failed: %!HRESULT!", hr);
            operation->Status = AIMX_STATUS_FAILED;
            ConversationSessionManager::NotifyOperationFailed(
                pContext->OperationId,
                L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_ANALYSIS) + L"] " + std::wstring(AimxConstants::Messages::AIMX_MSG_FAILED_ANALYZE_REQUEST)
            );
            goto Exit;
        }

        // Send progress update after analysis
        ConversationSessionManager::NotifyOperationProgress(
            pContext->OperationId,
            L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_CREATION) + L"] " + std::wstring(AimxConstants::Messages::AIMX_MSG_CREATING_EXECUTION_PLAN)
        );

        // Check if operation was cancelled
        if (pContext->cancelRequested || operation->Status == AIMX_STATUS_CANCELLED)
        {
            TraceInfo(AimxPlanner, "Planning operation was cancelled");
            goto Exit;
        }

        // Step 2: Generate execution plan
        hr = GenerateExecutionPlan(pContext->Query, planningSteps, executionPlan);
        if (FAILED(hr))
        {
            TraceErr(AimxPlanner, "GenerateExecutionPlan failed: %!HRESULT!", hr);
            operation->Status = AIMX_STATUS_FAILED;
            ConversationSessionManager::NotifyOperationFailed(
                pContext->OperationId,
                L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_CREATION) + L"] " + std::wstring(AimxConstants::Messages::AIMX_MSG_FAILED_CREATE_PLAN)
            );
            goto Exit;
        }

        // Check if operation was cancelled again
        if (pContext->cancelRequested || operation->Status == AIMX_STATUS_CANCELLED)
        {
            TraceInfo(AimxPlanner, "Planning operation was cancelled");
            goto Exit;
        }

        // Step 3: Validate execution plan
        hr = ValidateExecutionPlan(executionPlan);
        if (FAILED(hr))
        {
            TraceErr(AimxPlanner, "ValidateExecutionPlan failed: %!HRESULT!", hr);
            operation->Status = AIMX_STATUS_FAILED;
            ConversationSessionManager::NotifyOperationFailed(
                pContext->OperationId,
                L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_VALIDATION) + L"] " + std::wstring(AimxConstants::Messages::AIMX_MSG_FAILED_VALIDATE_PLAN)
            );
            goto Exit;
        }

        // Update operation with completed plan
        operation->ExecutionPlan = executionPlan;
        operation->Status = AIMX_STATUS_PLAN_READY;

        // Send completion notification
        ConversationSessionManager::NotifyOperationProgress(
            pContext->OperationId,
            L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_READY) + L"] " + std::wstring(AimxConstants::Messages::AIMX_MSG_PLAN_CREATED_SUCCESSFULLY)
        );

        TraceInfo(AimxPlanner, "Planning completed successfully");

        // step 4: exection.
        // For automated execution mode, automatically trigger execution
        if (pContext->ExecutionMode == AIMX_MODE_AUTOMATED)
        {
            TraceInfo(AimxPlanner, "Automated mode detected, triggering execution automatically");
            HRESULT execHr = Orchestrator::ExecuteOperationAsync(pContext->OperationId);
            if (FAILED(execHr))
            {
                TraceErr(AimxPlanner, "Failed to trigger automatic execution: %!HRESULT!", execHr);
                operation->Status = AIMX_STATUS_FAILED;
                ConversationSessionManager::NotifyOperationFailed(
                    pContext->OperationId,
                    L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_READY) + L"] Failed to trigger automatic execution: " + std::wstring(AimxConstants::Messages::AIMX_MSG_TOOL_EXECUTION_FAILED)
                );
            }
            else
            {
                TraceInfo(AimxPlanner, "Successfully triggered automatic execution");
            }
        }
        else
        {
            TraceInfo(AimxPlanner, "Interactive mode detected, waiting for manual execution trigger");
        }
    }
    catch (...)
    {
        TraceErr(AimxPlanner, "Exception in planning worker thread");
        operation->Status = AIMX_STATUS_FAILED;
        ConversationSessionManager::NotifyOperationFailed(
            pContext->OperationId,
            L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_START) + L"] " + std::wstring(AimxConstants::Messages::AIMX_MSG_PLANNING_FAILED_UNEXPECTED)
        );
        hr = E_FAIL;
    }

Exit:
    // Remove thread handle from map
    {
        std::lock_guard<std::mutex> lock(g_PlanningThreadMapMutex);
        g_PlanningThreadMap.erase(pContext->OperationId);
    }

    // Remove context from map before deleting
    if (pContext)
    {
        std::lock_guard<std::mutex> lock(g_PlanningContextMapMutex);
        g_PlanningContextMap.erase(pContext->OperationId);
    }
    delete pContext;

    TraceInfo(AimxPlanner, "Exit - PlanningWorkerThread. hr: %!HRESULT!", hr);
    return SUCCEEDED(hr) ? 0 : hr;

}

HRESULT
Planner::AnalyzeQuery(
    _In_ const GUID& operationId,
    _In_ const std::wstring& query,
    _Out_ nlohmann::json& planningSteps
    )
/*++

Routine Description:
    Analyzes the input query to determine what steps are needed for execution.
    If PowerShell execution is already complete, skips analysis and returns success.
    Otherwise, calls the MCP server for natural language processing and intent recognition.

Arguments:
    operationId - The operation ID for tracking
    query - The query string to analyze
    planningSteps - Receives the analysis results

    steps are in the format as follows
    steps:
        stepId: 1
        action: execute_tool
        target: ad_tools_agent
        toolName: GetUserDetails
        parameters: {"username": "john_doe"}

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    LLM_ANALYSIS_RESULT analysisResult = {};
    std::vector<MCP_SERVER_INFO> enabledServers;
    std::vector<MCP_TOOL_INFO> availableTools;

    planningSteps.clear();
    if (query.empty())
    {
        TraceErr(AimxPlanner, "Empty query provided for analysis");
        return E_INVALIDARG;
    }

    TraceInfo(AimxPlanner, "Analyzing query with LLM: %ws", query.c_str());

    // Get enabled MCP servers and their tools
    hr = McpSvrMgr::GetEnabledMcpServers(enabledServers);
    if (FAILED(hr))
    {
        TraceErr(AimxPlanner, "Failed to get enabled MCP servers: %!HRESULT!", hr);
        return hr;
    }

    // Get conversation session for feedback
    std::shared_ptr<ConversationSession> conversationSession;
    ConversationSessionManager::GetSessionByOperationId(operationId, conversationSession);

    if (!conversationSession)
    {
        TraceWarn(AimxPlanner, "No conversation session found for operation: %!GUID!", &operationId);
        return CreateFallbackPlan(query, planningSteps);
    }

    // Stage 1: Use RAG semantic search to get top 3 relevant tools
    conversationSession->SendMessage(AIMX_MSG_PROGRESS_UPDATE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_DISCOVERY) + L"] Searching for relevant tools using semantic similarity...");

    std::vector<RAG_TOOL_SEARCH_RESULT> ragResults;
    hr = RagServiceManager::SearchTools(query, 3, ragResults);
    if (FAILED(hr))
    {
        TraceWarn(AimxPlanner, "RAG tool search failed: %!HRESULT!, falling back to all tools", hr);
        conversationSession->SendMessage(AIMX_MSG_PROGRESS_UPDATE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_DISCOVERY) + L"] RAG search failed, using all available tools...");

        // Fallback: collect all available tools from enabled servers
        for (const auto& server : enabledServers)
        {
            if (server.isEnabled)
            {
                availableTools.insert(availableTools.end(),
                                    server.availableTools.begin(),
                                    server.availableTools.end());
            }
        }
    }
    else
    {
        TraceInfo(AimxPlanner, "RAG search found %d relevant tools", static_cast<int>(ragResults.size()));
        conversationSession->SendMessage(AIMX_MSG_PROGRESS_UPDATE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_DISCOVERY) + L"] Found " + std::to_wstring(ragResults.size()) + L" relevant tools from semantic search");

        // Convert RAG results to MCP_TOOL_INFO
        for (const auto& ragResult : ragResults)
        {
            TraceInfo(AimxPlanner, "Processing RAG result: toolId='%ws', toolName='%ws', score=%.3f",
                     ragResult.toolId.c_str(), ragResult.toolName.c_str(), ragResult.score);

            // Parse the tool GUID from the RAG result
            std::wstring toolGuidStr = ragResult.toolId;

            // Add curly braces if not present for GUID parsing. ugh..
            if (toolGuidStr.length() == 36 && toolGuidStr.find(L'{') == std::wstring::npos)
            {
                toolGuidStr = L"{" + toolGuidStr + L"}";
            }

            try
            {
                // Convert string to GUID
                GUID toolGuid;
                HRESULT hr = CLSIDFromString(toolGuidStr.c_str(), &toolGuid);
                if (SUCCEEDED(hr))
                {
                    // Use the new tool-centric lookup
                    MCP_TOOL_INFO toolInfo;
                    hr = McpSvrMgr::FindToolByGuid(toolGuid, toolInfo);
                    if (SUCCEEDED(hr))
                    {
                        availableTools.push_back(toolInfo);
                        TraceInfo(AimxPlanner, "Added RAG-selected tool: %ws from server %ws (score: %.3f)",
                                 toolInfo.toolName.c_str(), toolInfo.serverName.c_str(), ragResult.score);
                    }
                    else
                    {
                        TraceWarn(AimxPlanner, "Tool not found in registry for RAG result: %ws (toolGuid: %ws)",
                                 ragResult.toolName.c_str(), toolGuidStr.c_str());
                    }
                }
                else
                {
                    TraceWarn(AimxPlanner, "Invalid tool GUID format in RAG result: %ws", toolGuidStr.c_str());
                }
            }
            catch (const std::exception& e)
            {
                TraceErr(AimxPlanner, "Exception processing RAG result: %s", e.what());
            }
        }
    }

    if (availableTools.empty())
    {
        TraceWarn(AimxPlanner, "No available tools found from MCP servers");
        conversationSession->SendMessage(AIMX_MSG_ERROR_MESSAGE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_DISCOVERY) + L"] No tools available for analysis");
        return CreateFallbackPlan(query, planningSteps);
    }

    TraceInfo(AimxPlanner, "Using %Iu tools for LLM analysis", availableTools.size());

    // Stage 2: Use LLM to analyze the query with the filtered tools
    conversationSession->SendMessage(AIMX_MSG_PROGRESS_UPDATE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_ANALYSIS) + L"] Analyzing query with LLM using " + std::to_wstring(availableTools.size()) + L" selected tools...");

    hr = LLMInfer::AnalyzeUserQuery(query, availableTools, analysisResult, conversationSession);
    if (FAILED(hr))
    {
        TraceErr(AimxPlanner, "LLM query analysis failed: %!HRESULT!", hr);
        if (conversationSession)
        {
            conversationSession->SendMessage(AIMX_MSG_ERROR_MESSAGE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_ANALYSIS) + L"] LLM analysis failed, using fallback plan");
        }
        return CreateFallbackPlan(query, planningSteps);
    }

    // Convert LLM analysis result to execution plan
    if (conversationSession)
    {
        conversationSession->SendMessage(AIMX_MSG_PROGRESS_UPDATE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_CREATION) + L"] Converting LLM analysis into detailed execution plan...");
    }

    hr = ConvertAnalysisToExecutionPlan(analysisResult, enabledServers, planningSteps, conversationSession);
    if (FAILED(hr))
    {
        TraceErr(AimxPlanner, "Failed to convert analysis to execution plan: %!HRESULT!", hr);
        if (conversationSession)
        {
            conversationSession->SendMessage(AIMX_MSG_ERROR_MESSAGE, L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_CREATION) + L"] Failed to create execution plan from LLM analysis");
        }
        return hr;
    }

    if (conversationSession)
    {
        size_t stepCount = planningSteps[AimxConstants::JsonFields::AIMX_STEPS].size();
        std::wstring planMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_READY) + L"] Execution plan created with " + std::to_wstring(stepCount) + AimxConstants::PromptFormatting::AIMX_STEPS_SUFFIX + L":";
        conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, planMsg);

        // Show detailed execution plan steps
        for (size_t i = 0; i < stepCount && i < 10; ++i) // Limit to first 10 steps
        {
            const auto& step = planningSteps[AimxConstants::JsonFields::AIMX_STEPS][i];
            std::wstring stepMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_READY) + L"] "
                + AimxConstants::PromptFormatting::AIMX_STEP_PREFIX
                + std::to_wstring(i + 1)
                + AimxConstants::PromptFormatting::AIMX_COLON_SEPARATOR;

            if (step.contains(AimxConstants::JsonFields::AIMX_ACTION))
            {
                stepMsg += L"Action=" + Utf8ToWide(step[AimxConstants::JsonFields::AIMX_ACTION].get<std::string>());
            }

            if (step.contains(AimxConstants::JsonFields::AIMX_TARGET))
            {
                stepMsg += L", Target=" + Utf8ToWide(step[AimxConstants::JsonFields::AIMX_TARGET].get<std::string>());
            }

            if (step.contains(AimxConstants::JsonFields::AIMX_TOOL_NAME))
            {
                stepMsg += L", Tool=" + Utf8ToWide(step[AimxConstants::JsonFields::AIMX_TOOL_NAME].get<std::string>());
            }

            if (step.contains(AimxConstants::JsonFields::AIMX_PARAMETERS))
            {
                std::string params = step[AimxConstants::JsonFields::AIMX_PARAMETERS].dump();
                stepMsg += L", Params=" + Utf8ToWide(params);
            }

            conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, stepMsg);
        }

        if (stepCount > 10)
        {
            std::wstring moreMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_READY) + L"] " + AimxConstants::PromptFormatting::AIMX_MORE_STEPS_PREFIX + std::to_wstring(stepCount - 10) + AimxConstants::PromptFormatting::AIMX_MORE_STEPS_SUFFIX;
            conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, moreMsg);
        }
    }

    TraceInfo(AimxPlanner, "Exit - AnalyzeQuery. hr: %!HRESULT!", hr);
    TraceInfo(AimxPlanner, "Planning steps generated: %s", planningSteps.dump().c_str());
    return hr;
}

HRESULT
Planner::TryPowerShellExecution(
    _In_ const GUID& operationId,
    _In_ const std::wstring& query
    )
/*++

Routine Description:
    Step 0: Try PowerShell execution first before MCP analysis.
    Searches for PowerShell tools, sends to LLM for analysis and execution.

Arguments:
    operationId - The operation ID for tracking
    query - The query string to analyze

Return Value:
    S_OK on success (regardless of PowerShell execution result), or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxPlanner, "Step 0: Trying PowerShell execution for query: %ws", query.c_str());

    // Get conversation session for PowerShell execution
    std::shared_ptr<ConversationSession> conversationSession;
    ConversationSessionManager::GetSessionByOperationId(operationId, conversationSession);

    if (!conversationSession)
    {
        TraceErr(AimxPlanner, "No conversation session found for PowerShell execution");
        return E_FAIL; // Not a failure, just continue to MCP
    }

    // Try PowerShell tools first as the default toolset
    conversationSession->SendMessage(AIMX_MSG_PROGRESS_UPDATE,
        L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_DISCOVERY) + L"] " +
        L"Searching for PowerShell tools using semantic similarity...");

    std::vector<POWERSHELL_COMMAND_SEARCH_RESULT> pshResults;
    HRESULT pshSearchHr = PshManager::SearchPowerShellCommands(query, 3, pshResults);

    if (FAILED(pshSearchHr) || pshResults.empty())
    {
        TraceErr(AimxPlanner, "No PowerShell tools found or search failed, continuing to MCP analysis");
        return E_FAIL;
    }

    // Send PowerShell tools to LLM for analysis and execution
    LLM_ANALYSIS_RESULT pshAnalysisResult = {};
    HRESULT pshAnalysisHr = LLMInfer::AnalyzePowerShellQuery(query, pshResults, pshAnalysisResult, conversationSession);

    if (FAILED(pshAnalysisHr))
    {
        TraceErr(AimxPlanner, "PowerShell analysis failed, continuing to MCP analysis");
        return E_FAIL;
    }

    // Check if PowerShell execution was successful
    if (!pshAnalysisResult.requiredTools.empty() &&
        pshAnalysisResult.requiredTools[0].serverName == L"PowerShellDirect" &&
        pshAnalysisResult.requiredTools[0].parameters.contains("success") &&
        pshAnalysisResult.requiredTools[0].parameters["success"].get<bool>())
    {
        TraceInfo(AimxPlanner, "PowerShell execution completed successfully");

        // Extract and send final result
        const auto& pshTool = pshAnalysisResult.requiredTools[0];
        std::string resultStr = pshTool.parameters["result"].dump();
        std::wstring finalResult = Utf8ToWide(resultStr);

        conversationSession->SendMessage(AIMX_MSG_COMPLETION,
            L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_COMPLETION_FINAL) + L"] PowerShell execution completed successfully");
        conversationSession->SendMessage(AIMX_MSG_FINAL_RESULT,
            L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_COMPLETION_FINAL) + L"] " + finalResult);

    }
    else
    {
        TraceInfo(AimxPlanner, "PowerShell execution failed or no results, continuing to MCP analysis");
        conversationSession->SendMessage(AIMX_MSG_PROGRESS_UPDATE,
            L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_DISCOVERY) + L"] PowerShell execution failed, trying MCP tools...");
    }

    return S_OK;
}

HRESULT
Planner::GenerateExecutionPlan(
    _In_ const std::wstring& query,
    _In_ const nlohmann::json& planningSteps,
    _Out_ nlohmann::json& executionPlan
    )
/*++

Routine Description:
    Generates a detailed execution plan based on the query analysis.

Arguments:
    query         - The original query string
    planningSteps - The analysis results from AnalyzeQuery
    executionPlan - Receives the generated execution plan

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    std::set<std::string> requiredServiceNames;
    std::string utf8Query;

    TraceInfo(AimxPlanner, "Entry. Generating execution plan for query: %ws", query.c_str());
    TraceInfo(AimxPlanner, "Planning steps: %s", planningSteps.dump().c_str());

    // Validate planningSteps
    if (planningSteps.empty() || !planningSteps.contains(AimxConstants::JsonFields::AIMX_STEPS) || !planningSteps[AimxConstants::JsonFields::AIMX_STEPS].is_array()) {
        TraceErr(AimxPlanner, "No planning steps provided for execution plan generation");
        hr = E_INVALIDARG;
        goto Exit;
    }

    //convert the query to a format suitable for JSON
    utf8Query = WideToUtf8(query);
    if (utf8Query.empty()) {
        TraceErr(AimxPlanner, "Failed to convert query to UTF-8");
        hr = E_INVALIDARG;
        goto Exit;
    }

    // Build the execution plan as a JSON object
    executionPlan = nlohmann::json();
    executionPlan[AimxConstants::JsonFields::AIMX_JSON_KEY_PLAN_TYPE] = AimxConstants::PlanTypes::AIMX_PLAN_TYPE_CHATBOT_QUERY;
    executionPlan[AimxConstants::JsonFields::AIMX_JSON_KEY_ORIGINAL_QUERY] = utf8Query;
    executionPlan[AimxConstants::JsonFields::AIMX_STEPS] = planningSteps[AimxConstants::JsonFields::AIMX_STEPS];

    // Collect required services from toolName fields
    for (const auto& step : planningSteps[AimxConstants::JsonFields::AIMX_STEPS]) {
        if (step.contains(AimxConstants::JsonFields::AIMX_TOOL_NAME) && step[AimxConstants::JsonFields::AIMX_TOOL_NAME].is_string()) {
            requiredServiceNames.insert(step[AimxConstants::JsonFields::AIMX_TOOL_NAME].get<std::string>());
        }
    }
    executionPlan[AimxConstants::JsonFields::AIMX_REQUIRED_SERVICES] = nlohmann::json::array();
    for (const auto& service : requiredServiceNames) {
        executionPlan[AimxConstants::JsonFields::AIMX_REQUIRED_SERVICES].push_back(service);
    }
    executionPlan[AimxConstants::JsonFields::AIMX_REQUIRES_APPROVAL] = false;

    TraceInfo(AimxPlanner, "Generated Execution Plan: %s", executionPlan.dump().c_str());

Exit:
    TraceInfo(AimxPlanner, "Exit - GenerateExecutionPlan. hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
Planner::ValidateExecutionPlan(
    _In_ const nlohmann::json& executionPlan
    )
/*++

Routine Description:
    Validates the generated execution plan for correctness and safety.

Arguments:
    executionPlan - The execution plan to validate

Return Value:
    S_OK if plan is valid, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;

    TraceInfo(AimxPlanner, "Entry");

    // Basic validation - check if plan is not empty
    if (executionPlan.is_null())
    {
        TraceErr(AimxPlanner, "Execution plan is empty");
        hr = E_INVALIDARG;
        goto Exit;
    }

    // Check if plan contains required elements
    if (!executionPlan.contains(AimxConstants::JsonFields::AIMX_JSON_KEY_PLAN_TYPE))
    {
        TraceErr(AimxPlanner, "Execution plan missing planType");
        hr = E_INVALIDARG;
        goto Exit;
    }

    if (!executionPlan.contains(AimxConstants::JsonFields::AIMX_STEPS))
    {
        TraceErr(AimxPlanner, "Execution plan missing steps");
        hr = E_INVALIDARG;
        goto Exit;
    }

    // Additional validation would be performed here
    // This is a placeholder implementation

    TraceInfo(AimxPlanner, "Execution plan validation passed");

Exit:
    TraceInfo(AimxPlanner, "Exit - ValidateExecutionPlan. hr: %!HRESULT!", hr);
    return hr;
}

HRESULT
Planner::CreateFallbackPlan(
    _In_ const std::wstring& query,
    _Out_ nlohmann::json& planningSteps
    )
/*++

Routine Description:
    Creates a fallback execution plan when no MCP tools are available
    or LLM analysis fails. This plan will use direct LLM processing.

Arguments:
    query         - The original user query
    planningSteps - Receives the fallback execution plan

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxPlanner, "Creating fallback plan for query: %ws", query.c_str());

    planningSteps[AimxConstants::JsonFields::AIMX_STEPS] = nlohmann::json::array();

    nlohmann::json step;
    step[AimxConstants::JsonFields::AIMX_STEP_ID] = 1;
    step[AimxConstants::JsonFields::AIMX_ACTION] = ToString(AimxAction::ProcessNaturalLanguage);
    step[AimxConstants::JsonFields::AIMX_TARGET] = ToString(AimxTarget::AiLlmAgent);
    step[AimxConstants::JsonFields::AIMX_JSON_KEY_QUERY] = WideToUtf8(query);

    planningSteps[AimxConstants::JsonFields::AIMX_STEPS].push_back(step);

    TraceInfo(AimxPlanner, "Fallback plan created successfully");
    return S_OK;
}

HRESULT
Planner::ConvertAnalysisToExecutionPlan(
    _In_ const LLM_ANALYSIS_RESULT& analysisResult,
    _In_ const std::vector<MCP_SERVER_INFO>& enabledServers,
    _Out_ nlohmann::json& planningSteps,
    _In_opt_ std::shared_ptr<ConversationSession> conversationSession
    )
/*++

Routine Description:
    Converts LLM analysis result into a structured execution plan
    with proper tool mappings and parameters.

Arguments:
    analysisResult - The result from LLM query analysis
    enabledServers - List of enabled MCP servers for tool mapping
    planningSteps  - Receives the generated execution plan

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxPlanner, "Converting LLM analysis to execution plan");

    if (conversationSession)
    {
        std::wstring analysisMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_CREATION) + L"] Creating execution plan from LLM analysis. Found " +
                                  std::to_wstring(analysisResult.requiredTools.size()) + L" required tools.";
        conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, analysisMsg);

        if (analysisResult.overallConfidence > 0)
        {
            std::wstring confidenceMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_CREATION) + L"] LLM Analysis Confidence: " + std::to_wstring(analysisResult.overallConfidence);
            conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, confidenceMsg);
        }

        std::wstring serversMsg = L"[" + std::wstring(AimxConstants::MessageStages::AIMX_STAGE_PLANNING_DISCOVERY) + L"] Available MCP servers: " + std::to_wstring(enabledServers.size());
        for (const auto& server : enabledServers)
        {
            std::wstring serverNameWide = server.serverName.c_str();
            serversMsg += L" [" + serverNameWide + L": " + std::to_wstring(server.availableTools.size()) + L" tools]";
        }
        conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, serversMsg);
    }

    planningSteps[AimxConstants::JsonFields::AIMX_STEPS] = nlohmann::json::array();

    if (analysisResult.requiredTools.empty())
    {
        TraceWarn(AimxPlanner, "No tools required by LLM analysis, creating fallback plan");
        if (conversationSession)
        {
            conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, L"No specific tools required. Creating direct LLM response plan.");
        }
        return CreateFallbackPlan(L"", planningSteps);
    }

    int stepId = 1;

    if (conversationSession)
    {
        std::wstring processingMsg = L"Processing " + std::to_wstring(analysisResult.requiredTools.size()) + L" required tools:";
        conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, processingMsg);
    }

    for (const auto& toolReq : analysisResult.requiredTools)
    {
        if (conversationSession)
        {
            std::wstring displayName = McpToolManager::CreateDisplayName(toolReq.serverName, toolReq.toolName);
            std::wstring toolMsg = L"  Analyzing tool: " + displayName;
            if (toolReq.confidence > 0)
            {
                toolMsg += L" [Confidence: " + std::to_wstring(toolReq.confidence) + L"]";
            }
            if (!toolReq.reasoning.empty())
            {
                toolMsg += L" - " + toolReq.reasoning;
            }
            conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, toolMsg);
        }

        // Find the server that contains this tool using helper function
        std::wstring foundServerName;
        MCP_TOOL_INFO foundTool;

        // Check if the tool name contains a server prefix (e.g., "Hello/HelloFromMcpTool")
        // If so, parse it to extract the actual tool name
        std::wstring actualToolName = toolReq.toolName;
        std::wstring parsedServerName;
        std::wstring parsedToolName;

        if (McpToolManager::ParseToolId(toolReq.toolName, parsedServerName, parsedToolName))
        {
            // Successfully parsed server/tool format, use the parsed tool name
            actualToolName = parsedToolName;
            TraceInfo(AimxPlanner, "Parsed tool identifier '%ws' into server '%ws' and tool '%ws'",
                     toolReq.toolName.c_str(), parsedServerName.c_str(), parsedToolName.c_str());
        }

        bool toolFound = McpToolManager::FindToolByName(actualToolName, enabledServers, foundServerName, foundTool);

        if (!toolFound)
        {
            TraceWarn(AimxPlanner, "Tool not found in enabled servers: %ws (actual tool name: %ws)",
                     toolReq.toolName.c_str(), actualToolName.c_str());
            if (conversationSession)
            {
                std::wstring errorMsg = L"ERROR: Tool '" + actualToolName + L"' not found in available servers";
                if (actualToolName != toolReq.toolName)
                {
                    errorMsg += L" (parsed from '" + toolReq.toolName + L"')";
                }
                conversationSession->SendMessage(AIMX_MSG_ERROR_MESSAGE, errorMsg);
            }
            continue;
        }

        if (conversationSession)
        {
            std::wstring foundDisplayName = McpToolManager::CreateDisplayName(foundServerName, actualToolName);
            std::wstring foundMsg = L"  Found " + foundDisplayName;
            conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, foundMsg);
        }

        // Create execution step
        nlohmann::json step;
        step[AimxConstants::JsonFields::AIMX_STEP_ID] = stepId++;
        step[AimxConstants::JsonFields::AIMX_ACTION] = ToString(AimxAction::ExecuteTool);
        step[AimxConstants::JsonFields::AIMX_TARGET] = WideToUtf8(foundServerName);
        step[AimxConstants::JsonFields::AIMX_TOOL_NAME] = WideToUtf8(actualToolName);

        // Convert parameters to JSON
        if (!toolReq.parameters.empty())
        {
            step[AimxConstants::JsonFields::AIMX_PARAMETERS] = toolReq.parameters;
        }

        planningSteps[AimxConstants::JsonFields::AIMX_STEPS].push_back(step);

        if (conversationSession)
        {
            std::wstring stepMsg = L"  Step " + std::to_wstring(stepId) + L": Execute " + actualToolName + L" on " + foundServerName;
            if (!toolReq.parameters.empty())
            {
                std::string params = toolReq.parameters.dump();
                stepMsg += L" with parameters: " + Utf8ToWide(params);
            }
            conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, stepMsg);
        }
    }

    // Add final step for response generation if we have tool results
    if (!planningSteps[AimxConstants::JsonFields::AIMX_STEPS].empty())
    {
        nlohmann::json responseStep;
        responseStep[AimxConstants::JsonFields::AIMX_STEP_ID] = stepId;
        responseStep[AimxConstants::JsonFields::AIMX_ACTION] = ToString(AimxAction::GenerateResponse);
        responseStep[AimxConstants::JsonFields::AIMX_TARGET] = ToString(AimxTarget::AiLlmAgent);
        planningSteps[AimxConstants::JsonFields::AIMX_STEPS].push_back(responseStep);

        if (conversationSession)
        {
            std::wstring finalStepMsg = L"  Step " + std::to_wstring(stepId) + L": Generate final response using LLM";
            conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, finalStepMsg);
        }
    }

    if (conversationSession)
    {
        size_t totalSteps = planningSteps[AimxConstants::JsonFields::AIMX_STEPS].size();
        std::wstring summaryMsg = L"Execution plan completed with " + std::to_wstring(totalSteps) + L" steps ready for execution";
        conversationSession->SendMessage(AIMX_MSG_EXECUTION_PLAN, summaryMsg);
    }

    TraceInfo(AimxPlanner, "Execution plan created with %Iu steps",
              planningSteps[AimxConstants::JsonFields::AIMX_STEPS].size());
    return S_OK;
}
