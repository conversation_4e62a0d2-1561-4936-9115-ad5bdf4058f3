﻿{
    "ScrapedAt":  "2025-07-28T09:59:41Z",
    "BatchId":  "20250728_095941",
    "Patterns":  [
                     {
                         "RelevanceScore":  0.99280736181906026,
                         "CreatedAt":  "2025-07-28T09:59:41Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_general_ad_eca3a43a",
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T09:59:41Z",
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub: samratashok/nishang",
                         "CodeTemplate":  "",
                         "CommonMistakes":  [

                                            ],
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  0.75,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "general_ad",
                                      "read",
                                      "activedirectory",
                                      "hacking",
                                      "infosec",
                                      "nishang",
                                      "penetration-testing",
                                      "powershell",
                                      "red-team",
                                      "redteam",
                                      "security",
                                      "github",
                                      "repository",
                                      "powershell"
                                  ],
                         "Abstract":  "# samratashok/nishang\n\n**Description:** Nishang - Offensive PowerShell for red team, penetration testing and offensive security. \n\n**Stars:** 9358\n**Language:** PowerShell\n**URL:** https://github.com/samratashok/nishang\n\n**Topics:** activedirectory, hacking, infosec, nishang, penetration-testing, powershell, red-team, redteam, security\n\n...",
                         "Sources":  [
                                         {
                                             "SourceType":  "github",
                                             "CredibilityScore":  0.75,
                                             "PublishedAt":  "2025-07-28T09:59:41Z",
                                             "Url":  "https://github.com/samratashok/nishang",
                                             "ScrapedAt":  "2025-07-28T09:59:41Z",
                                             "Id":  "8137c730-03a5-4c78-9f6a-3daf25995591",
                                             "Author":  "samratashok",
                                             "Title":  "GitHub: samratashok/nishang"
                                         }
                                     ],
                         "Content":  "# samratashok/nishang\n\n**Description:** Nishang - Offensive PowerShell for red team, penetration testing and offensive security. \n\n**Stars:** 9358\n**Language:** PowerShell\n**URL:** https://github.com/samratashok/nishang\n\n**Topics:** activedirectory, hacking, infosec, nishang, penetration-testing, powershell, red-team, redteam, security\n\n"
                     },
                     {
                         "RelevanceScore":  0.7551938720483895,
                         "CreatedAt":  "2025-07-28T09:59:41Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_group_management_6751c63e",
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T09:59:41Z",
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub: EvotecIT/GPOZaurr",
                         "CodeTemplate":  "",
                         "CommonMistakes":  [

                                            ],
                         "Domain":  "group_management",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  0.75,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "group_management",
                                      "read",
                                      "activedirectory",
                                      "gpo",
                                      "group-policy",
                                      "hacktoberfest",
                                      "powershell",
                                      "github",
                                      "repository",
                                      "powershell"
                                  ],
                         "Abstract":  "# EvotecIT/GPOZaurr\n\n**Description:** Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them. \n\n**Stars:** 1048\n**Language:** PowerShell\n**URL:** https://github.com/EvotecIT/GPOZaurr\n\n**Topics:** activedirectory, gpo, group-policy, hacktoberfest, powershell\n\n...",
                         "Sources":  [
                                         {
                                             "SourceType":  "github",
                                             "CredibilityScore":  0.75,
                                             "PublishedAt":  "2025-07-28T09:59:41Z",
                                             "Url":  "https://github.com/EvotecIT/GPOZaurr",
                                             "ScrapedAt":  "2025-07-28T09:59:41Z",
                                             "Id":  "71b1e93a-b5be-428f-ba1e-73472f639087",
                                             "Author":  "EvotecIT",
                                             "Title":  "GitHub: EvotecIT/GPOZaurr"
                                         }
                                     ],
                         "Content":  "# EvotecIT/GPOZaurr\n\n**Description:** Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them. \n\n**Stars:** 1048\n**Language:** PowerShell\n**URL:** https://github.com/EvotecIT/GPOZaurr\n\n**Topics:** activedirectory, gpo, group-policy, hacktoberfest, powershell\n\n"
                     },
                     {
                         "RelevanceScore":  0.74235397808849535,
                         "CreatedAt":  "2025-07-28T09:59:41Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_general_ad_953b5fe7",
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T09:59:41Z",
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub: samratashok/ADModule",
                         "CodeTemplate":  "",
                         "CommonMistakes":  [

                                            ],
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  0.75,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "general_ad",
                                      "read",
                                      "github",
                                      "repository",
                                      "powershell"
                                  ],
                         "Abstract":  "# samratashok/ADModule\n\n**Description:** Microsoft signed ActiveDirectory PowerShell module\n\n**Stars:** 931\n**Language:** PowerShell\n**URL:** https://github.com/samratashok/ADModule\n\n...",
                         "Sources":  [
                                         {
                                             "SourceType":  "github",
                                             "CredibilityScore":  0.75,
                                             "PublishedAt":  "2025-07-28T09:59:41Z",
                                             "Url":  "https://github.com/samratashok/ADModule",
                                             "ScrapedAt":  "2025-07-28T09:59:41Z",
                                             "Id":  "9046d624-0218-4e1f-a31f-7bfc3b4bb495",
                                             "Author":  "samratashok",
                                             "Title":  "GitHub: samratashok/ADModule"
                                         }
                                     ],
                         "Content":  "# samratashok/ADModule\n\n**Description:** Microsoft signed ActiveDirectory PowerShell module\n\n**Stars:** 931\n**Language:** PowerShell\n**URL:** https://github.com/samratashok/ADModule\n\n"
                     }
                 ],
    "SourceType":  "github",
    "TotalPatterns":  3
}
