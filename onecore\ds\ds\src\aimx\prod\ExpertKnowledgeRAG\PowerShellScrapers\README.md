# Expert Knowledge RAG - PowerShell Scraping System

A comprehensive PowerShell-based web scraping system designed to extract expert knowledge patterns from various sources for building a PowerShell Active Directory knowledge base.

## 🎯 **Overview**

This system scrapes and processes content from multiple sources to build a comprehensive knowledge base of PowerShell Active Directory expertise:

- **Microsoft Docs**: Official cmdlet documentation and examples
- **Stack Overflow**: Community Q&A with real-world solutions
- **GitHub**: Open-source scripts and modules
- **Expert Blogs**: Professional insights and best practices

## 📁 **Project Structure**

```
PowerShellScrapers/
├── Core/
│   └── ScrapingFramework.psm1      # Core scraping framework
├── Scrapers/
│   ├── MicrosoftDocsScraper.ps1    # Microsoft documentation scraper
│   ├── StackOverflowScraper.ps1    # Stack Overflow Q&A scraper
│   └── GitHubScraper.ps1           # GitHub repository scraper
├── ScrapingConfig.json             # Configuration file
├── Start-ExpertKnowledgeScraping.ps1  # Master orchestrator
├── Test-ScrapingFramework.ps1      # Comprehensive test suite
└── README.md                       # This file
```

## 🚀 **Quick Start**

### 1. **Prerequisites**
- PowerShell 5.1 or later
- Internet connection
- Optional: GitHub Personal Access Token for higher API limits

### 2. **Configuration**
Edit `ScrapingConfig.json` to customize:
- Output directories
- Rate limiting settings
- Quality filters
- Source priorities

### 3. **Run Complete Scraping**
```powershell
# Run all scrapers
.\Start-ExpertKnowledgeScraping.ps1

# Run specific scrapers only
.\Start-ExpertKnowledgeScraping.ps1 -ScrapersToRun @("MicrosoftDocs", "StackOverflow")

# Run with GitHub token for higher limits
.\Start-ExpertKnowledgeScraping.ps1 -GitHubToken "your_github_token_here"
```

### 4. **Run Individual Scrapers**
```powershell
# Microsoft Docs only
.\Scrapers\MicrosoftDocsScraper.ps1

# Stack Overflow with limits
.\Scrapers\StackOverflowScraper.ps1 -MaxQuestions 100 -MinScore 10

# GitHub with token
.\Scrapers\GitHubScraper.ps1 -GitHubToken "your_token" -MaxRepositories 50
```

### 5. **Test the System**
```powershell
# Run comprehensive tests
.\Test-ScrapingFramework.ps1

# Test framework only
.\Test-ScrapingFramework.ps1 -TestFrameworkOnly

# Test scrapers only
.\Test-ScrapingFramework.ps1 -TestScrapersOnly
```

## 📊 **Output Format**

The system generates JSON files with structured knowledge patterns:

```json
{
  "BatchId": "20241228_143022",
  "SourceType": "microsoft_docs",
  "ScrapedAt": "2024-12-28T14:30:22Z",
  "TotalPatterns": 145,
  "Patterns": [
    {
      "Id": "pattern_user_management_A1B2C3D4",
      "Domain": "user_management",
      "Operation": "read",
      "PatternType": "cmdlet_usage",
      "Title": "Microsoft Docs: Get-ADUser",
      "Abstract": "Retrieves one or more Active Directory users...",
      "Content": "Full documentation content...",
      "CodeTemplate": "PowerShell code examples...",
      "RequiredParameters": "[\"Identity\", \"Filter\"]",
      "BestPractices": ["Use -Filter for queries", "Include -Properties for additional data"],
      "Sources": [
        {
          "SourceType": "microsoft_docs",
          "Url": "https://docs.microsoft.com/...",
          "Author": "Microsoft",
          "CredibilityScore": 0.95
        }
      ],
      "Tags": ["powershell", "active-directory", "user", "read"],
      "CredibilityScore": 0.95,
      "CreatedAt": "2024-12-28T14:30:22Z"
    }
  ]
}
```

## 🔧 **Configuration Options**

### **ScrapingConfig.json Key Settings**

```json
{
  "ScrapingConfiguration": {
    "OutputDirectory": "./ScrapedData",
    "MaxConcurrentJobs": 5,
    "DelayBetweenRequests": 1000,
    "TimeoutSeconds": 30
  },
  "QualityFilters": {
    "MinimumCodeLength": 10,
    "RequiredKeywords": ["PowerShell", "Active Directory"],
    "MinimumCredibilityScore": 0.5
  }
}
```

## 📈 **Expected Results**

### **Typical Scraping Yields**
- **Microsoft Docs**: ~150 high-quality cmdlet documentation patterns
- **Stack Overflow**: ~300 community Q&A patterns
- **GitHub**: ~200 real-world script patterns
- **Total**: ~650+ expert knowledge patterns

### **Quality Metrics**
- **Average Credibility**: 0.75-0.85
- **Code Coverage**: 90%+ patterns include executable code
- **Domain Coverage**: All major AD management areas

## 🧪 **Testing**

The test suite validates:
- ✅ Framework initialization
- ✅ Web request handling with retries
- ✅ Content extraction (code blocks, commands)
- ✅ Quality assessment
- ✅ Pattern creation and saving
- ✅ Individual scraper functionality
- ✅ Full orchestration workflow

## 🔍 **Troubleshooting**

### **Common Issues**

1. **Rate Limiting**
   - Increase delays in config
   - Use GitHub token for higher limits
   - Run scrapers individually with pauses

2. **Network Timeouts**
   - Increase timeout values
   - Check internet connection
   - Retry failed requests

3. **No Patterns Extracted**
   - Check quality filter settings
   - Verify source URLs are accessible
   - Review log files for errors

4. **Permission Errors**
   - Ensure write access to output directory
   - Run PowerShell as administrator if needed

### **Log Files**
Check `./Logs/scraping_YYYYMMDD.log` for detailed execution logs.

## 🎯 **Next Steps**

After successful scraping:

1. **Review Results**: Check output JSON files for quality and completeness
2. **Database Import**: Load patterns into your knowledge database
3. **Vector Embeddings**: Generate embeddings for semantic search
4. **Integration**: Connect to your LLM pipeline for expert knowledge injection

## 📝 **Customization**

### **Adding New Scrapers**
1. Create new scraper in `Scrapers/` directory
2. Implement required functions following existing patterns
3. Add configuration to `ScrapingConfig.json`
4. Update orchestrator to include new scraper

### **Custom Pattern Types**
Extend pattern types in the framework:
- `workflow`: Multi-step processes
- `cmdlet_usage`: Single cmdlet examples
- `best_practice`: Expert recommendations
- `common_mistake`: Things to avoid
- `performance_tip`: Optimization advice

## 🤝 **Contributing**

To contribute improvements:
1. Test changes with `Test-ScrapingFramework.ps1`
2. Ensure all tests pass
3. Update documentation
4. Follow PowerShell best practices

## 📄 **License**

This project is part of the AIMX Expert Knowledge RAG system.

---

**🎉 Happy Scraping! Build the ultimate PowerShell AD knowledge base!**
