﻿{
    "ScrapedAt":  "2025-07-28T09:55:46Z",
    "BatchId":  "20250728_095546",
    "Patterns":  [
                     {
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T09:55:46Z",
                         "PatternType":  "cmdlet_usage",
                         "Id":  "pattern_general_ad_5f1a1f0f",
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T09:55:46Z",
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow: How to get all groups that a user is a member of?",
                         "CodeTemplate":  "G",
                         "CommonMistakes":  [

                                            ],
                         "Domain":  "general_ad",
                         "RequiredParameters":  "\"Get-ADGroupMember\\u003c/code\\u003e\\u003c/a\\u003e cmdlet returns members of a specific group. Is there a cmdlet or property to get all the groups that a particular user is a member of?\\u003c/p\\u003e\"",
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "general_ad",
                                      "read",
                                      "windows",
                                      "powershell",
                                      "cmd",
                                      "active-directory",
                                      "powershell-2.0"
                                  ],
                         "Abstract":  "\u003cp\u003ePowerShell\u0027s \u003ca href=\"https://technet.microsoft.com/en-us/library/ee617193.aspx\" rel=\"noreferrer\"\u003e\u003ccode\u003eGet-ADGroupMember\u003c/code\u003e\u003c/a\u003e cmdlet returns members of a specific group. Is there a cmdlet or property to get all the groups that a particular user is a member of?\u003c/p\u003e\n...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T09:55:46Z",
                                             "Url":  "https://stackoverflow.com/questions/5072996/how-to-get-all-groups-that-a-user-is-a-member-of",
                                             "ScrapedAt":  "2025-07-28T09:55:46Z",
                                             "Id":  "0d406d97-e6d1-461f-8a39-615cafd414fa",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow: How to get all groups that a user is a member of?"
                                         }
                                     ],
                         "Content":  "\u003cp\u003ePowerShell\u0027s \u003ca href=\"https://technet.microsoft.com/en-us/library/ee617193.aspx\" rel=\"noreferrer\"\u003e\u003ccode\u003eGet-ADGroupMember\u003c/code\u003e\u003c/a\u003e cmdlet returns members of a specific group. Is there a cmdlet or property to get all the groups that a particular user is a member of?\u003c/p\u003e\n"
                     },
                     {
                         "RelevanceScore":  0.68,
                         "CreatedAt":  "2025-07-28T09:55:46Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_general_ad_6b57a5d5",
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T09:55:46Z",
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow: Powershell script to see currently logged in users (domain and machine) + status (active, idle, away)",
                         "CodeTemplate":  "Get-WmiObject -Class win32_computersystem",
                         "CommonMistakes":  [

                                            ],
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "general_ad",
                                      "read",
                                      "powershell",
                                      "active-directory",
                                      "powershell-2.0",
                                      "powershell-3.0",
                                      "windows-server-2012"
                                  ],
                         "Abstract":  "\u003cp\u003eI am searching for a simple command to see logged on users on server.\nI know this one :\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-WmiObject -Class win32_computersystem\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003ebut this will not provide me the info I need.\nIt returns :\ndomain\nManufactureer\nModel\nName (Machine name)\nPrimaryOwnerName\nTotalPhysicalMemory\u003c/p\u003e\n\n\u003cp\u003eI run Powershell 3.0 on a Windows 2012 server.\u003c/p\u003e\n\n\u003cp\u003eAlso \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-WmiObject Win32_LoggedOnUser -ComputerName $Computer | Select Antecedent -Unique\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003egi...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T09:55:46Z",
                                             "Url":  "https://stackoverflow.com/questions/23219718/powershell-script-to-see-currently-logged-in-users-domain-and-machine-status",
                                             "ScrapedAt":  "2025-07-28T09:55:46Z",
                                             "Id":  "e6d7320b-e07e-47b1-b5bb-b354877dc98e",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow: Powershell script to see currently logged in users (domain and machine) + status (active, idle, away)"
                                         }
                                     ],
                         "Content":  "\u003cp\u003eI am searching for a simple command to see logged on users on server.\nI know this one :\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-WmiObject -Class win32_computersystem\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003ebut this will not provide me the info I need.\nIt returns :\ndomain\nManufactureer\nModel\nName (Machine name)\nPrimaryOwnerName\nTotalPhysicalMemory\u003c/p\u003e\n\n\u003cp\u003eI run Powershell 3.0 on a Windows 2012 server.\u003c/p\u003e\n\n\u003cp\u003eAlso \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-WmiObject Win32_LoggedOnUser -ComputerName $Computer | Select Antecedent -Unique\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003egives me not the exact answers I need.\nI would love to see as well the idle time, or if they are active or away.\u003c/p\u003e\n"
                     },
                     {
                         "RelevanceScore":  0.67,
                         "CreatedAt":  "2025-07-28T09:55:46Z",
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_general_ad_f8ecf41a",
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T09:55:46Z",
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow: Powershell: A positional parameter cannot be found that accepts argument \u0026quot;xxx\u0026quot;",
                         "CodeTemplate":  "I",
                         "CommonMistakes":  [

                                            ],
                         "Domain":  "general_ad",
                         "RequiredParameters":  "[\"Get-ADUser -SearchBase \\u0026quot;ou=Testing,ou=Users,dc=my,dc=domain\\u0026quot; -Filter * -Properties *\",\"Set-ADUser $user -userPrincipalName = $newname\"]",
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "general_ad",
                                      "read",
                                      "powershell",
                                      "active-directory"
                                  ],
                         "Abstract":  "\u003cp\u003eI am trying to understand what this error actually means. So far a search of similar help requests for this error range from missing parameters, missing pipes, use of single or multi-lines, and also concatenation issues but none of the answers seem to give a definitive reason. So I assume the issue is code format (which makes it a lot harder to track down).\u003c/p\u003e\n\u003cp\u003eThis is my script which I am writing to rename active directory users per target OU from whatever format they are now into a first...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T09:55:46Z",
                                             "Url":  "https://stackoverflow.com/questions/35433151/powershell-a-positional-parameter-cannot-be-found-that-accepts-argument-xxx",
                                             "ScrapedAt":  "2025-07-28T09:55:46Z",
                                             "Id":  "65b092dd-7349-49ef-88cc-ec3f3a559388",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow: Powershell: A positional parameter cannot be found that accepts argument \u0026quot;xxx\u0026quot;"
                                         }
                                     ],
                         "Content":  "\u003cp\u003eI am trying to understand what this error actually means. So far a search of similar help requests for this error range from missing parameters, missing pipes, use of single or multi-lines, and also concatenation issues but none of the answers seem to give a definitive reason. So I assume the issue is code format (which makes it a lot harder to track down).\u003c/p\u003e\n\u003cp\u003eThis is my script which I am writing to rename active directory users per target OU from whatever format they are now into a firstname.surname format.\u003c/p\u003e\n\u003cp\u003eI have created a test OU in AD with some users who will trigger errors and some that will not. However, the users that should not give me an error are giving me the \u0026quot;a positional parameter cannot be found that accepts argument \u0026quot;firstname.surname\u0026quot;\u003c/p\u003e\n\u003cp\u003eI cannot see what is wrong with the script but hopefully, someone can give me some pointers.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eImport-Module ActiveDirectory\n\n$users = $null\n\n$users = Get-ADUser -SearchBase \u0026quot;ou=Testing,ou=Users,dc=my,dc=domain\u0026quot; -Filter * -Properties *\nforeach ($user in $users) {\n    Write-Host \u0026quot;Processing... $($user)\u0026quot;\n    $newname = $null\n\n    # Check first/last name is set\n    if (!$user.givenName -or !$user.Surname) {\n        Write-Host \u0026quot;$($user) does not have first name or last name set. Please correct, skipping user.\u0026quot;\n        continue\n    } else {\n        $newname = (\u0026quot;$($user.givenName).$($user.Surname)\u0026quot;)\n\n        #Check if new username already exists\n        if (dsquery user -samid $newname) {\n            Write-Host \u0026quot;$($user) requires altered username with initial.\u0026quot;\n\n            if (!$user.Initials) {\n                Write-Host \u0026quot;$($user) does not have any initials set. Please correct, skipping user.\u0026quot;\n                continue\n            }\n\n            $newname = (\u0026quot;$($user.givenName)$($user.Initials).$($user.Surname)\u0026quot;)\n\n            #Check if altered new username already exists\n            if (dsquery user -samid $newname) {\n                Write-Host \u0026quot;$($user) requires manual change. Please correct, skipping user.\u0026quot;\n                continue\n            }\n        }\n\n        try {\n            #Change UPN\n            Set-ADUser $user -userPrincipalName = $newname\n            #Change DN\n            Rename-ADObject -identity $user -Newname $newname\n        } catch {\n            Write-Host \u0026quot;Error when renaming $($user). Error is: $($_.Exception.Message). User requires manual change. Please correct, skipping user.\u0026quot;\n            continue\n        }\n    }\n}\n\u003c/code\u003e\u003c/pre\u003e\n"
                     },
                     {
                         "RelevanceScore":  0.5,
                         "CreatedAt":  "2025-07-28T09:55:46Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_general_ad_847b35a2",
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T09:55:46Z",
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow: Powershell v3.0 pipe issue",
                         "CodeTemplate":  "gc .\\domains.txt | Get-ADDomain",
                         "CommonMistakes":  [

                                            ],
                         "Domain":  "general_ad",
                         "RequiredParameters":  "[\"Get-ADDomain\",\"Get-ADDomain : A referral was returned from the server\",\"Get-ADDomain \\u0026lt;Domain name here\\u0026gt;\",\"Get-ADDomain $_ }\",\"Get-ADDomain } -PSHost\"]",
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "general_ad",
                                      "read",
                                      "powershell",
                                      "active-directory",
                                      "powershell-3.0"
                                  ],
                         "Abstract":  "\u003cp\u003eI\u0027m having trouble with this command:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003egc .\\domains.txt | Get-ADDomain\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eAs the name implies, domains.txt contains a list of Active Directory to query (all domains are in the same forest).\u003c/p\u003e\n\n\u003cp\u003eIf I run it on my Windows 8 machine everything works fine and I get the expected results, instead on a Windows 2008 R2 SP1 member server (not a DC) with WMF 3.0 I get result only from the first domain in the list and for the others:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-ADDomain : A ref...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T09:55:46Z",
                                             "Url":  "https://stackoverflow.com/questions/13192447/powershell-v3-0-pipe-issue",
                                             "ScrapedAt":  "2025-07-28T09:55:46Z",
                                             "Id":  "071cc4dd-2463-4abd-a9c3-1d8b18eafbeb",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow: Powershell v3.0 pipe issue"
                                         }
                                     ],
                         "Content":  "\u003cp\u003eI\u0027m having trouble with this command:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003egc .\\domains.txt | Get-ADDomain\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eAs the name implies, domains.txt contains a list of Active Directory to query (all domains are in the same forest).\u003c/p\u003e\n\n\u003cp\u003eIf I run it on my Windows 8 machine everything works fine and I get the expected results, instead on a Windows 2008 R2 SP1 member server (not a DC) with WMF 3.0 I get result only from the first domain in the list and for the others:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-ADDomain : A referral was returned from the server\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eIf I query a domain in the list with:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-ADDomain \u0026lt;Domain name here\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eit works fine.\u003c/p\u003e\n\n\u003ch1\u003eMy Workstation\u003c/h1\u003e\n\n\u003cp\u003eMicrosoft Windows 8 Enterprise (6.2.9200) x64\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ePS D:\\Tools\\Powershell\u0026gt; $PSVersionTable\n\nName                           Value\n----                           -----\nPSVersion                      3.0\nWSManStackVersion              3.0\nSerializationVersion           *******\nCLRVersion                     4.0.30319.18010\nBuildVersion                   6.2.9200.16384\nPSCompatibleVersions           {1.0, 2.0, 3.0}\nPSRemotingProtocolVersion      2.2\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003ch1\u003eServer\u003c/h1\u003e\n\n\u003cp\u003eMicrosoft Windows Server 2008 R2 Standard SP1 (6.1.7601) x64\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ePS C:\\Tools\\Powershell\u0026gt; $PSVersionTable\n\nName                           Value\n----                           -----\nWSManStackVersion              3.0\nPSCompatibleVersions           {1.0, 2.0, 3.0}\nSerializationVersion           *******\nBuildVersion                   6.2.9200.16398\nPSVersion                      3.0\nCLRVersion                     4.0.30319.269\nPSRemotingProtocolVersion      2.2\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003ch1\u003eUpdate\u003c/h1\u003e\n\n\u003cp\u003eIf i run on the server:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003egc .\\domains.txt | %{ Get-ADDomain $_ }\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eit runs fine\u003c/p\u003e\n\n\u003ch1\u003eTRACE\u003c/h1\u003e\n\n\u003cpre\u003e\u003ccode\u003etrace-command -Name ParameterBinding { \"DOMAIN_1\",\"DOMAIN_2\" | Get-ADDomain } -PSHost\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eServer: \u003ca href=\"http://pastebin.com/sRVJHaCU\"\u003ehttp://pastebin.com/sRVJHaCU\u003c/a\u003e\u003c/p\u003e\n\n\u003cp\u003eWorkstation: \u003ca href=\"http://pastebin.com/kj3JV6nV\"\u003ehttp://pastebin.com/kj3JV6nV\u003c/a\u003e\u003c/p\u003e\n\n\u003cp\u003eThanks in advance\u003c/p\u003e\n"
                     },
                     {
                         "RelevanceScore":  0.38,
                         "CreatedAt":  "2025-07-28T09:55:46Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_general_ad_ce729d23",
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T09:55:46Z",
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow: How can I verify if an AD account is locked?",
                         "CodeTemplate":  "",
                         "CommonMistakes":  [

                                            ],
                         "Domain":  "general_ad",
                         "RequiredParameters":  "[\"Get-ADUser\\u003c/code\\u003e\\u003c/a\\u003e does not return this parameter :\\u003c/p\\u003e\",\"Get-ADUser GlenJohn -Properties *\"]",
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  0.8,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "general_ad",
                                      "read",
                                      "powershell",
                                      "active-directory"
                                  ],
                         "Abstract":  "\u003cp\u003eI want to know if it is possible to verify if a specific AD account is locked. \u003c/p\u003e\n\n\u003cp\u003eThe command \u003ca href=\"http://technet.microsoft.com/en-ca/library/ee617241.aspx\" rel=\"noreferrer\"\u003e\u003ccode\u003eGet-ADUser\u003c/code\u003e\u003c/a\u003e does not return this parameter :\u003c/p\u003e\n\n\u003cpre\u003e -------------------------- EXAMPLE 3 --------------------------\n\n Command Prompt: C:\\PS\u003e\n Get-ADUser GlenJohn -Properties * \n\n\n  - Surname : John \n  - Name : Glen John\n  - UserPrincipalName : jglen\n  - GivenName : Glen\n  - Enabled : False\n  ...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T09:55:46Z",
                                             "Url":  "https://stackoverflow.com/questions/********/how-can-i-verify-if-an-ad-account-is-locked",
                                             "ScrapedAt":  "2025-07-28T09:55:46Z",
                                             "Id":  "a8263b28-4081-4dd7-9041-31d0de9fa43e",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow: How can I verify if an AD account is locked?"
                                         }
                                     ],
                         "Content":  "\u003cp\u003eI want to know if it is possible to verify if a specific AD account is locked. \u003c/p\u003e\n\n\u003cp\u003eThe command \u003ca href=\"http://technet.microsoft.com/en-ca/library/ee617241.aspx\" rel=\"noreferrer\"\u003e\u003ccode\u003eGet-ADUser\u003c/code\u003e\u003c/a\u003e does not return this parameter :\u003c/p\u003e\n\n\u003cpre\u003e -------------------------- EXAMPLE 3 --------------------------\n\n Command Prompt: C:\\PS\u003e\n Get-ADUser GlenJohn -Properties * \n\n\n  - Surname : John \n  - Name : Glen John\n  - UserPrincipalName : jglen\n  - GivenName : Glen\n  - Enabled : False\n  - SamAccountName : GlenJohn\n  - ObjectClass :\n  - user SID :S-1-5-21-**********-**********-**********-3544\n  - ObjectGUID :e1418d64-096c-4cb0-b903-ebb66562d99d\n  - DistinguishedName : CN=Glen John,OU=NorthAmerica,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM\n\n Description :\n -----------\n\n Get all properties of the user with samAccountName \u0027GlenJohn\u0027.\n\n --------------------------END EXAMPLE --------------------------\u003c/pre\u003e\n\n\u003cp\u003eIs there an other way to get this information ?\u003c/p\u003e\n"
                     }
                 ],
    "SourceType":  "stackoverflow",
    "TotalPatterns":  5
}
