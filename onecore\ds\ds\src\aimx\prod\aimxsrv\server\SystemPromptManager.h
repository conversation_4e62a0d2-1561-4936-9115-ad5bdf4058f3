/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    SystemPromptManager.h

Abstract:

    Header file for the System Prompt Manager component that handles system prompt
    generation for LLM inference operations. Incorporates Cline system prompt format
    for standardized MCP tool and server communication. Provides context-aware
    prompt templates for different LLM inference scenarios.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 07/13/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <shared_mutex>
#include "AimxCommon.h"
#include "nlohmann/json.hpp"
#include "PshManager.h"
#include "McpSvrMgr.h"

// System prompt template types
enum class SYSTEM_PROMPT_TYPE
{
    TOOL_ANALYSIS = 0,
    RESPONSE_GENERATION,
    PARAMETER_REFINEMENT,
    RISK_ASSESSMENT,
    CONVERSATION_CONTINUATION,
    POWERSHELL_AD_ASSISTANT
};

// System prompt object incorporating Cline format
class SystemPromptManager
{
public:
    // Initialize with MCP server information
    HRESULT Initialize(
        _In_ const std::vector<MCP_SERVER_INFO>& availableServers
        );

    // Get system prompt for specific type
    HRESULT GetSystemPrompt(
        _In_ SYSTEM_PROMPT_TYPE promptType,
        _In_ const nlohmann::json& context,
        _Out_ std::wstring& systemPrompt
        );

    // Update available tools and servers
    HRESULT UpdateAvailableTools(
        _In_ const std::vector<MCP_TOOL_INFO>& tools
        );

    // Get user prompt for response generation
    HRESULT GetResponseGenerationUserPrompt(
        _In_ const std::wstring& originalQuery,
        _In_ const std::wstring& toolResults,
        _Out_ std::wstring& userPrompt
        );

    // Get user prompt for PowerShell tool analysis
    HRESULT GetPowerShellToolAnalysisUserPrompt(
        _In_ const std::wstring& userQuery,
        _In_ const std::vector<POWERSHELL_COMMAND_SEARCH_RESULT>& powerShellCommands,
        _Out_ std::wstring& userPrompt
        );

    // Get formatted tool definitions for LLM
    HRESULT GetFormattedToolDefinitions(
        _Out_ std::wstring& toolDefinitions
        );

    // Get PowerShell AD Assistant system prompt
    std::wstring GetPowerShellSystemPrompt();

private:
    // Core system prompt templates based on Cline format
    std::wstring GetBaseSystemPrompt();
    std::wstring GetToolAnalysisPrompt();
    std::wstring GetResponseGenerationPrompt();
    std::wstring GetParameterRefinementPrompt();
    std::wstring GetRiskAssessmentPrompt();

    // Tool formatting methods
    std::wstring FormatToolsForLLM(
        _In_ const std::vector<MCP_TOOL_INFO>& tools
        );

    // Context-specific prompt generation
    std::wstring BuildContextualPrompt(
        _In_ SYSTEM_PROMPT_TYPE promptType,
        _In_ const nlohmann::json& context
        );

    std::vector<MCP_TOOL_INFO> m_availableTools;
    std::vector<MCP_SERVER_INFO> m_availableServers;
    mutable std::shared_mutex m_toolsMutex;
};
