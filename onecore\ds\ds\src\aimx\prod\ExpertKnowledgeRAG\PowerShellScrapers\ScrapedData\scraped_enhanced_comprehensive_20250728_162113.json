﻿{
    "ScrapedAt":  "2025-07-28T16:21:13Z",
    "BatchId":  "20250728_162113",
    "Patterns":  [
                     {
                         "Syntax":  [
                                        "Remove-ADReplicationSiteLinkBridge\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType \u003cADAuthType\u003e]\n    [-Credential \u003cPSCredential\u003e]\n    [-Identity] \u003cADReplicationSiteLinkBridge\u003e\n    [-Server \u003cString\u003e]\n    [\u003cCommonParameters\u003e]"
                                    ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:20:36Z",
                         "Synopsis":  "",
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_general_ad_f81d83b3",
                         "Operation":  "delete",
                         "Notes":  "",
                         "UpdatedAt":  "2025-07-28T16:20:36Z",
                         "Parameters":  [
                                            "Confirm",
                                            "Credential",
                                            "Identity",
                                            "Server"
                                        ],
                         "BestPractices":  [

                                           ],
                         "Title":  "Microsoft Docs: Remove-ADReplicationSiteLinkBridge",
                         "CodeTemplate":  "Remove-ADReplicationSiteLinkBridge\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType \u003cADAuthType\u003e]\n    [-Credential \u003cPSCredential\u003e]\n    [-Identity] \u003cADReplicationSiteLinkBridge\u003e\n    [-Server \u003cString\u003e]\n    [\u003cCommonParameters\u003e]",
                         "CommonMistakes":  [

                                            ],
                         "Examples":  [
                                          "Remove-ADReplicationSiteLinkBridge\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType \u003cADAuthType\u003e]\n    [-Credential \u003cPSCredential\u003e]\n    [-Identity] \u003cADReplicationSiteLinkBridge\u003e\n    [-Server \u003cString\u003e]\n    [\u003cCommonParameters\u003e]",
                                          "PS C:\\\u003e Remove-ADReplicationSiteLinkBridge -Identity \"NorthAmerica-Asia\"",
                                          "PS C:\\\u003e Get-ADReplicationSiteLinkBridge -Filter \"SiteLinksIncluded -eq \u0027Europe-Asia\u0027\" | Remove-ADReplicationSiteLinkBridge"
                                      ],
                         "Description":  "",
                         "Domain":  "general_ad",
                         "RequiredParameters":  [

                                                ],
                         "RelatedLinks":  [

                                          ],
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "general_ad",
                                      "delete"
                                  ],
                         "Abstract":  " \u003c!DOCTYPE html\u003e\n\t\t\u003chtml\n\t\t\tclass=\"layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus\"\n\t\t\tlang=\"en-us\"\n\t\t\tdir=\"ltr\"\n\t\t\tdata-authenticated=\"false\"\n\t\t\tdata-auth-status-determined=\"false\"\n\t\t\tdata-target=\"docs\"\n\t\t\tx-ms-format-detection=\"none\"\n\t\t\u003e\n\t\t\t\n\t\t\u003chead\u003e\n\t\t\t\u003ctitle\u003eRemove-ADReplicationSiteLinkBridge (ActiveDirectory) | Microsoft Learn\u003c/title\u003e\n\t\t\t\u003cmeta charset=\"utf-8\" /\u003e\n\t\t\t\u003cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" /\u003e\n\t\t\t\u003cmeta nam...",
                         "Sources":  [
                                         {
                                             "SourceType":  "microsoft_docs",
                                             "CredibilityScore":  0.95,
                                             "PublishedAt":  "2025-07-28T16:20:36Z",
                                             "Url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-adreplicationsitelinkbridge",
                                             "ScrapedAt":  "2025-07-28T16:20:36Z",
                                             "Id":  "380d88a9-b66c-4831-87bf-cb4048e03e24",
                                             "Author":  "Microsoft",
                                             "Title":  "Microsoft Docs: Remove-ADReplicationSiteLinkBridge"
                                         }
                                     ],
                         "Content":  " \u003c!DOCTYPE html\u003e\n\t\t\u003chtml\n\t\t\tclass=\"layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus\"\n\t\t\tlang=\"en-us\"\n\t\t\tdir=\"ltr\"\n\t\t\tdata-authenticated=\"false\"\n\t\t\tdata-auth-status-determined=\"false\"\n\t\t\tdata-target=\"docs\"\n\t\t\tx-ms-format-detection=\"none\"\n\t\t\u003e\n\t\t\t\n\t\t\u003chead\u003e\n\t\t\t\u003ctitle\u003eRemove-ADReplicationSiteLinkBridge (ActiveDirectory) | Microsoft Learn\u003c/title\u003e\n\t\t\t\u003cmeta charset=\"utf-8\" /\u003e\n\t\t\t\u003cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" /\u003e\n\t\t\t\u003cmeta name=\"color-scheme\" content=\"light dark\" /\u003e\n\n\t\t\t\u003cmeta name=\"description\" content=\"Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.\" /\u003e\n\t\t\t\u003clink rel=\"canonical\" href=\"https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-adreplicationsitelinkbridge?view=windowsserver2025-ps\" /\u003e \n\n\t\t\t\u003c!-- Non-customizable open graph and sharing-related metadata --\u003e\n\t\t\t\u003cmeta name=\"twitter:card\" content=\"summary_large_image\" /\u003e\n\t\t\t\u003cmeta name=\"twitter:site\" content=\"@MicrosoftLearn\" /\u003e\n\t\t\t\u003cmeta property=\"og:type\" content=\"website\" /\u003e\n\t\t\t\u003cmeta property=\"og:image:alt\" content=\"Microsoft Learn\" /\u003e\n\t\t\t\u003cmeta property=\"og:image\" content=\"https://learn.microsoft.com/en-us/media/open-graph-image.png\" /\u003e\n\t\t\t\u003c!-- Page specific open graph and sharing-related metadata --\u003e\n\t\t\t\u003cmeta property=\"og:title\" content=\"Remove-ADReplicationSiteLinkBridge (ActiveDirectory)\" /\u003e\n\t\t\t\u003cmeta property=\"og:url\" content=\"https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-adreplicationsitelinkbridge?view=windowsserver2025-ps\" /\u003e\n\t\t\t\u003cmeta property=\"og:description\" content=\"Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.\" /\u003e\n\t\t\t\u003cmeta name=\"platform_id\" content=\"be567795-4669-b2d1-0030-d4b1d21762fe\" /\u003e \n\t\t\t\u003cmeta name=\"locale\" content=\"en-us\" /\u003e\n\t\t\t \u003cmeta name=\"adobe-target\" content=\"true\" /\u003e\n\t\t\t\u003cmeta name=\"uhfHeaderId\" content=\"MSDocsHeader-M365-IT\" /\u003e\n\n\t\t\t\u003cmeta name=\"page_type\" content=\"powershell\" /\u003e\n\n\t\t\t\u003c!--page specific meta tags--\u003e\n\t\t\t\n\n\t\t\t\u003c!-- custom meta tags --\u003e\n\t\t\t\n\t\t\u003cmeta name=\"uid\" content=\"ActiveDirectory.Remove-ADReplicationSiteLinkBridge\" /\u003e\n\t\n\t\t\u003cmeta name=\"module\" content=\"ActiveDirectory\" /\u003e\n\t\n\t\t\u003cmeta name=\"schema\" content=\"PowerShellCmdlet1\" /\u003e\n\t\n\t\t\u003cmeta name=\"ROBOTS\" content=\"INDEX, FOLLOW\" /\u003e\n\t\n\t\t\u003cmeta name=\"apiPlatform\" content=\"powershell\" /\u003e\n\t\n\t\t\u003cmeta name=\"archive_url\" content=\"https://learn.microsoft.com/previous-versions/powershell/windows/get-started\" /\u003e\n\t\n\t\t\u003cmeta name=\"author\" content=\"robinharwood\" /\u003e\n\t\n\t\t\u003cmeta name=\"breadcrumb_path\" content=\"/powershell/windows/bread/toc.json\" /\u003e\n\t\n\t\t\u003cmeta name=\"feedback_product_url\" content=\"https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332\" /\u003e\n\t\n\t\t\u003cmeta name=\"feedback_system\" content=\"Standard\" /\u003e\n\t\n\t\t\u003cmeta name=\"manager\" content=\"tedhudek\" /\u003e\n\t\n\t\t\u003cmeta name=\"ms.author\" content=\"roharwoo\" /\u003e\n\t\n\t\t\u003cmeta name=\"ms.devlang\" content=\"powershell\" /\u003e\n\t\n\t\t\u003cmeta name=\"ms.service\" content=\"windows-11\" /\u003e\n\t\n\t\t\u003cmeta name=\"ms.topic\" content=\"reference\" /\u003e\n\t\n\t\t\u003cmeta name=\"products\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/56936876-97d9-45cc-ad1b-9d63320447c8\" /\u003e\n\t\n\t\t\u003cmeta name=\"products\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/56754133-c3c3-4a9f-af19-71bdbe19fccf\" /\u003e\n\t\n\t\t\u003cmeta name=\"document type\" content=\"cmdlet\" /\u003e\n\t\n\t\t\u003cmeta name=\"external help file\" content=\"Microsoft.ActiveDirectory.Management.dll-Help.xml\" /\u003e\n\t\n\t\t\u003cmeta name=\"HelpUri\" content=\"https://learn.microsoft.com/powershell/module/activedirectory/remove-adreplicationsitelinkbridge?view=windowsserver2025-ps\u0026amp;wt.mc_id=ps-gethelp\" /\u003e\n\t\n\t\t\u003cmeta name=\"Module Name\" content=\"ActiveDirectory\" /\u003e\n\t\n\t\t\u003cmeta name=\"ms.date\" content=\"2016-12-27T00:00:00Z\" /\u003e\n\t\n\t\t\u003cmeta name=\"PlatyPS schema version\" content=\"2024-05-01T00:00:00Z\" /\u003e\n\t\n\t\t\u003cmeta name=\"document_id\" content=\"64e7fbb4-2ca3-1556-9cc9-82aa188b9bf9\" /\u003e\n\t\n\t\t\u003cmeta name=\"document_version_independent_id\" content=\"39d9cfbb-3caf-cbba-1960-ef56866a57ff\" /\u003e\n\t\n\t\t\u003cmeta name=\"updated_at\" content=\"2025-05-14T22:44:00Z\" /\u003e\n\t\n\t\t\u003cmeta name=\"original_content_git_url\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\" /\u003e\n\t\n\t\t\u003cmeta name=\"gitcommit\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/0ef3f225d29e26d1cf3119f37dfff70bb6165746/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\" /\u003e\n\t\n\t\t\u003cmeta name=\"git_commit_id\" content=\"0ef3f225d29e26d1cf3119f37dfff70bb6165746\" /\u003e\n\t\n\t\t\u003cmeta name=\"monikers\" content=\"windowsserver2025-ps\" /\u003e\n\t\n\t\t\u003cmeta name=\"default_moniker\" content=\"windowsserver2025-ps\" /\u003e\n\t\n\t\t\u003cmeta name=\"site_name\" content=\"Docs\" /\u003e\n\t\n\t\t\u003cmeta name=\"depot_name\" content=\"TechNet.windows-powershell\" /\u003e\n\t\n\t\t\u003cmeta name=\"in_right_rail\" content=\"h2h3\" /\u003e\n\t\n\t\t\u003cmeta name=\"page_kind\" content=\"command\" /\u003e\n\t\n\t\t\u003cmeta name=\"toc_rel\" content=\"../windowsserver2025-ps/toc.json\" /\u003e\n\t\n\t\t\u003cmeta name=\"feedback_help_link_type\" content=\"\" /\u003e\n\t\n\t\t\u003cmeta name=\"feedback_help_link_url\" content=\"\" /\u003e\n\t\n\t\t\u003cmeta name=\"config_moniker_range\" content=\"WindowsServer2025-ps\" /\u003e\n\t\n\t\t\u003cmeta name=\"asset_id\" content=\"module/activedirectory/remove-adreplicationsitelinkbridge\" /\u003e\n\t\n\t\t\u003cmeta name=\"moniker_range_name\" content=\"ffb05b7b47577225af7c7b6a20151268\" /\u003e\n\t\n\t\t\u003cmeta name=\"item_type\" content=\"Content\" /\u003e\n\t\n\t\t\u003cmeta name=\"source_path\" content=\"docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\" /\u003e\n\t\n\t\t\u003cmeta name=\"github_feedback_content_git_url\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\" /\u003e\n\t \n\t\t\u003cmeta name=\"cmProducts\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/bcbcbad5-4208-4783-8035-8481272c98b8\" data-source=\"generated\" /\u003e\n\t\n\t\t\u003cmeta name=\"spProducts\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/43b2e5aa-8a6d-4de2-a252-692232e5edc8\" data-source=\"generated\" /\u003e\n\t\n\n\t\t\t\u003c!-- assets and js globals --\u003e\n\t\t\t\n\t\t\t\u003clink rel=\"stylesheet\" href=\"/static/assets/0.4.03126.7002-3880ccdd/styles/site-ltr.css\" /\u003e\n\t\t\t\u003clink rel=\"preconnect\" href=\"//mscom.demdex.net\" crossorigin /\u003e\n\t\t\t\t\t\t\u003clink rel=\"dns-prefetch\" href=\"//target.microsoft.com\" /\u003e\n\t\t\t\t\t\t\u003clink rel=\"dns-prefetch\" href=\"//microsoftmscompoc.tt.omtrdc.net\" /\u003e\n\t\t\t\t\t\t\u003clink\n\t\t\t\t\t\t\trel=\"preload\"\n\t\t\t\t\t\t\tas=\"script\"\n\t\t\t\t\t\t\thref=\"/static/third-party/adobe-target/at-js/2.9.0/at.js\"\n\t\t\t\t\t\t\tintegrity=\"sha384-1/viVM50hgc33O2gOgkWz3EjiD/Fy/ld1dKYXJRUyjNYVEjSUGcSN+iPiQF7e4cu\"\n\t\t\t\t\t\t\tcrossorigin=\"anonymous\"\n\t\t\t\t\t\t\tid=\"adobe-target-script\"\n\t\t\t\t\t\t\ttype=\"application/javascript\"\n\t\t\t\t\t\t/\u003e\n\t\t\t\u003cscript src=\"https://wcpstatic.microsoft.com/mscc/lib/v2/wcp-consent.js\"\u003e\u003c/script\u003e\n\t\t\t\u003cscript src=\"https://js.monitor.azure.com/scripts/c/ms.jsll-4.min.js\"\u003e\u003c/script\u003e\n\t\t\t\u003cscript src=\"/_themes/docs.theme/master/en-us/_themes/global/deprecation.js\"\u003e\u003c/script\u003e\n\n\t\t\t\u003c!-- msdocs global object --\u003e\n\t\t\t\u003cscript id=\"msdocs-script\"\u003e\n\t\tvar msDocs = {\n  \"environment\": {\n    \"accessLevel\": \"online\",\n    \"azurePortalHostname\": \"portal.azure.com\",\n    \"reviewFeatures\": false,\n    \"supportLevel\": \"production\",\n    \"systemContent\": true,\n    \"siteName\": \"learn\",\n    \"legacyHosting\": false\n  },\n  \"data\": {\n    \"contentLocale\": \"en-us\",\n    \"contentDir\": \"ltr\",\n    \"userLocale\": \"en-us\",\n    \"userDir\": \"ltr\",\n    \"pageTemplate\": \"Reference\",\n    \"brand\": \"\",\n    \"context\": {},\n    \"standardFeedback\": true,\n    \"showFeedbackReport\": false,\n    \"feedbackHelpLinkType\": \"\",\n    \"feedbackHelpLinkUrl\": \"\",\n    \"feedbackSystem\": \"Standard\",\n    \"feedbackGitHubRepo\": \"\",\n    \"feedbackProductUrl\": \"https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332\",\n    \"extendBreadcrumb\": true,\n    \"isEditDisplayable\": true,\n    \"isPrivateUnauthorized\": false,\n    \"hideViewSource\": false,\n    \"isPermissioned\": false,\n    \"hasRecommendations\": false,\n    \"contributors\": []\n  },\n  \"functions\": {}\n};;\n\t\u003c/script\u003e\n\n\t\t\t\u003c!-- base scripts, msdocs global should be before this --\u003e\n\t\t\t\u003cscript src=\"/static/assets/0.4.03126.7002-3880ccdd/scripts/en-us/index-docs.js\"\u003e\u003c/script\u003e\n\t\t\t\n\n\t\t\t\u003c!-- json-ld --\u003e\n\t\t\t\n\t\t\u003c/head\u003e\n\t\n\t\t\t\u003cbody\n\t\t\t\tid=\"body\"\n\t\t\t\tdata-bi-name=\"body\"\n\t\t\t\tclass=\"layout-body \"\n\t\t\t\tlang=\"en-us\"\n\t\t\t\tdir=\"ltr\"\n\t\t\t\u003e\n\t\t\t\t\u003cheader class=\"layout-body-header\"\u003e\n\t\t\u003cdiv class=\"header-holder has-default-focus\"\u003e\n\t\t\t\n\t\t\u003ca\n\t\t\thref=\"#main\"\n\t\t\t\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\t\n\t\t\u003e\n\t\t\tSkip to main content\n\t\t\u003c/a\u003e\n\t\n\t\t\u003ca\n\t\t\thref=\"#side-doc-outline\"\n\t\t\t\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\t\n\t\t\u003e\n\t\t\tSkip to in-page navigation\n\t\t\u003c/a\u003e\n\t\n\t\t\u003ca\n\t\t\thref=\"#\"\n\t\t\tdata-skip-to-ask-learn\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\thidden\n\t\t\u003e\n\t\t\tSkip to Ask Learn chat experience\n\t\t\u003c/a\u003e\n\t\n\n\t\t\t\u003cdiv hidden id=\"cookie-consent-holder\" data-test-id=\"cookie-consent-container\"\u003e\u003c/div\u003e\n\t\t\t\u003c!-- Unsupported browser warning --\u003e\n\t\t\t\u003cdiv\n\t\t\t\tid=\"unsupported-browser\"\n\t\t\t\tstyle=\"background-color: white; color: black; padding: 16px; border-bottom: 1px solid grey;\"\n\t\t\t\thidden\n\t\t\t\u003e\n\t\t\t\t\u003cdiv style=\"max-width: 800px; margin: 0 auto;\"\u003e\n\t\t\t\t\t\u003cp style=\"font-size: 24px\"\u003eThis browser is no longer supported.\u003c/p\u003e\n\t\t\t\t\t\u003cp style=\"font-size: 16px; margin-top: 16px;\"\u003e\n\t\t\t\t\t\tUpgrade to Microsoft Edge to take advantage of the latest features, security updates, and technical support.\n\t\t\t\t\t\u003c/p\u003e\n\t\t\t\t\t\u003cdiv style=\"margin-top: 12px;\"\u003e\n\t\t\t\t\t\t\u003ca\n\t\t\t\t\t\t\thref=\"https://go.microsoft.com/fwlink/p/?LinkID=2092881 \"\n\t\t\t\t\t\t\tstyle=\"background-color: #0078d4; border: 1px solid #0078d4; color: white; padding: 6px 12px; border-radius: 2px; display: inline-block;\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\tDownload Microsoft Edge\n\t\t\t\t\t\t\u003c/a\u003e\n\t\t\t\t\t\t\u003ca\n\t\t\t\t\t\t\thref=\"https://learn.microsoft.com/en-us/lifecycle/faq/internet-explorer-microsoft-edge\"\n\t\t\t\t\t\t\tstyle=\"background-color: white; padding: 6px 12px; border: 1px solid #505050; color: #171717; border-radius: 2px; display: inline-block;\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\tMore info about Internet Explorer and Microsoft Edge\n\t\t\t\t\t\t\u003c/a\u003e\n\t\t\t\t\t\u003c/div\u003e\n\t\t\t\t\u003c/div\u003e\n\t\t\t\u003c/div\u003e\n\t\t\t\u003c!-- site header --\u003e\n\t\t\t\u003cheader\n\t\t\t\tid=\"ms--site-header\"\n\t\t\t\tdata-test-id=\"site-header-wrapper\"\n\t\t\t\trole=\"banner\"\n\t\t\t\titemscope=\"itemscope\"\n\t\t\t\titemtype=\"http://schema.org/Organization\"\n\t\t\t\u003e\n\t\t\t\t\u003cdiv\n\t\t\t\t\tid=\"ms--mobile-nav\"\n\t\t\t\t\tclass=\"site-header display-none-tablet padding-inline-none gap-none\"\n\t\t\t\t\tdata-bi-name=\"mobile-header\"\n\t\t\t\t\tdata-test-id=\"mobile-header\"\n\t\t\t\t\u003e\u003c/div\u003e\n\t\t\t\t\u003cdiv\n\t\t\t\t\tid=\"ms--primary-nav\"\n\t\t\t\t\tclass=\"site-header display-none display-flex-tablet\"\n\t\t\t\t\tdata-bi-name=\"L1-header\"\n\t\t\t\t\tdata-test-id=\"primary-header\"\n\t\t\t\t\u003e\u003c/div\u003e\n\t\t\t\t\u003cdiv\n\t\t\t\t\tid=\"ms--secondary-nav\"\n\t\t\t\t\tclass=\"site-header display-none display-flex-tablet\"\n\t\t\t\t\tdata-bi-name=\"L2-header\"\n\t\t\t\t\tdata-test-id=\"secondary-header\"\n\t\t\t\t\u003e\u003c/div\u003e\n\t\t\t\u003c/header\u003e\n\t\t\t\n\t\t\u003c!-- banner --\u003e\n\t\t\u003cdiv data-banner\u003e\n\t\t\t\u003cdiv id=\"disclaimer-holder\"\u003e\u003c/div\u003e\n\t\t\t\n\t\t\u003c/div\u003e\n\t\t\u003c!-- banner end --\u003e\n\t\n\t\t\u003c/div\u003e\n\t\u003c/header\u003e\n\t\t\t\t \u003csection\n\t\t\t\t\tid=\"layout-body-menu\"\n\t\t\t\t\tclass=\"layout-body-menu display-flex\"\n\t\t\t\t\tdata-bi-name=\"menu\"\n\t\t\t  \u003e\n\t\t\t\t\t\u003cdiv\n\t\tid=\"left-container\"\n\t\tclass=\"left-container display-none display-block-tablet padding-inline-sm padding-bottom-sm width-full\"\n\t\u003e\n\t\t\u003cnav\n\t\t\tid=\"affixed-left-container\"\n\t\t\tclass=\"margin-top-sm-tablet position-sticky display-flex flex-direction-column\"\n\t\t\taria-label=\"Primary\"\n\t\t\u003e\u003c/nav\u003e\n\t\u003c/div\u003e\n\t\t\t  \u003c/section\u003e\n\n\t\t\t\t\u003cmain\n\t\t\t\t\tid=\"main\"\n\t\t\t\t\trole=\"main\"\n\t\t\t\t\tclass=\"layout-body-main \"\n\t\t\t\t\tdata-bi-name=\"content\"\n\t\t\t\t\tlang=\"en-us\"\n\t\t\t\t\tdir=\"ltr\"\n\t\t\t\t\u003e\n\t\t\t\t\t\n\t\t\t\u003cdiv\n\t\tid=\"ms--content-header\"\n\t\tclass=\"content-header default-focus border-bottom-none\"\n\t\tdata-bi-name=\"content-header\"\n\t\u003e\n\t\t\u003cdiv class=\"content-header-controls margin-xxs margin-inline-sm-tablet\"\u003e\n\t\t\t\u003cbutton\n\t\t\t\ttype=\"button\"\n\t\t\t\tclass=\"contents-button button button-sm margin-right-xxs\"\n\t\t\t\tdata-bi-name=\"contents-expand\"\n\t\t\t\taria-haspopup=\"true\"\n\t\t\t\tdata-contents-button\n\t\t\t\u003e\n\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\u003cspan class=\"docon docon-menu\"\u003e\u003c/span\u003e\u003c/span\u003e\n\t\t\t\t\u003cspan class=\"contents-expand-title\"\u003e Table of contents \u003c/span\u003e\n\t\t\t\u003c/button\u003e\n\t\t\t\u003cbutton\n\t\t\t\ttype=\"button\"\n\t\t\t\tclass=\"ap-collapse-behavior ap-expanded button button-sm\"\n\t\t\t\tdata-bi-name=\"ap-collapse\"\n\t\t\t\taria-controls=\"action-panel\"\n\t\t\t\u003e\n\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\u003cspan class=\"docon docon-exit-mode\"\u003e\u003c/span\u003e\u003c/span\u003e\n\t\t\t\t\u003cspan\u003eExit editor mode\u003c/span\u003e\n\t\t\t\u003c/button\u003e\n\t\t\u003c/div\u003e\n\t\u003c/div\u003e\n\t\t\t\u003cdiv data-main-column class=\"padding-sm padding-top-none padding-top-sm-tablet\"\u003e\n\t\t\t\t\u003cdiv\u003e\n\t\t\t\t\t\n\t\t\u003cdiv id=\"article-header\" class=\"background-color-body margin-bottom-xs display-none-print\"\u003e\n\t\t\t\u003cdiv class=\"display-flex align-items-center justify-content-space-between\"\u003e\n\t\t\t\t\n\t\t\u003cdetails\n\t\t\tid=\"article-header-breadcrumbs-overflow-popover\"\n\t\t\tclass=\"popover\"\n\t\t\tdata-for=\"article-header-breadcrumbs\"\n\t\t\u003e\n\t\t\t\u003csummary\n\t\t\t\tclass=\"button button-clear button-primary button-sm inner-focus\"\n\t\t\t\taria-label=\"All breadcrumbs\"\n\t\t\t\u003e\n\t\t\t\t\u003cspan class=\"icon\"\u003e\n\t\t\t\t\t\u003cspan class=\"docon docon-more\"\u003e\u003c/span\u003e\n\t\t\t\t\u003c/span\u003e\n\t\t\t\u003c/summary\u003e\n\t\t\t\u003cdiv id=\"article-header-breadcrumbs-overflow\" class=\"popover-content padding-none\"\u003e\u003c/div\u003e\n\t\t\u003c/details\u003e\n\n\t\t\u003cbread-crumbs\n\t\t\tid=\"article-header-breadcrumbs\"\n\t\t\tdata-test-id=\"article-header-breadcrumbs\"\n\t\t\tclass=\"overflow-hidden flex-grow-1 margin-right-sm margin-right-md-tablet margin-right-lg-desktop margin-left-negative-xxs padding-left-xxs\"\n\t\t\u003e\u003c/bread-crumbs\u003e\n\t \n\t\t\u003cdiv\n\t\t\tid=\"article-header-page-actions\"\n\t\t\tclass=\"opacity-none margin-left-auto display-flex flex-wrap-no-wrap align-items-stretch\"\n\t\t\u003e\n\t\t\t\n\t\t\u003cbutton\n\t\t\tclass=\"button button-sm border-none inner-focus display-none-tablet flex-shrink-0 \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-modal-entry-mobile\"\n\t\t\tdata-ask-learn-modal-entry\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\taria-label=\"Ask Learn\"\n\t\t\thidden\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon font-size-lg\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t\t\u003cbutton\n\t\t\tclass=\"button button-sm display-none display-inline-flex-tablet display-none-desktop flex-shrink-0 margin-right-xxs \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-modal-entry-tablet\"\n\t\t\tdata-ask-learn-modal-entry\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\thidden\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon font-size-lg\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan\u003eAsk Learn\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t\t\u003cbutton\n\t\t\tclass=\"button button-sm display-none flex-shrink-0 display-inline-flex-desktop margin-right-xxs\t \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-flyout-entry\"\n\t\t\tdata-ask-learn-flyout-entry\n\t\t\tdata-flyout-button=\"toggle\"\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\taria-controls=\"ask-learn-flyout\"\n\t\t\thidden\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon font-size-lg\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan\u003eAsk Learn\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t \n\t\t\u003cbutton\n\t\t\ttype=\"button\"\n\t\t\tid=\"ms--focus-mode-button\"\n\t\t\tdata-focus-mode\n\t\t\tdata-bi-name=\"focus-mode-entry\"\n\t\t\tclass=\"button button-sm flex-shrink-0 margin-right-xxs display-none display-inline-flex-desktop\"\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon font-size-lg\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-glasses\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan\u003eFocus mode\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t \n\n\t\t\t\u003cdetails class=\"popover popover-right\" id=\"article-header-page-actions-overflow\"\u003e\n\t\t\t\t\u003csummary\n\t\t\t\t\tclass=\"justify-content-flex-start button button-clear button-sm button-primary inner-focus\"\n\t\t\t\t\taria-label=\"More actions\"\n\t\t\t\t\ttitle=\"More actions\"\n\t\t\t\t\u003e\n\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-more-vertical\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003cdiv class=\"popover-content\"\u003e\n\t\t\t\t\t\n\t\t\u003cbutton\n\t\t\tdata-page-action-item=\"overflow-mobile\"\n\t\t\ttype=\"button\"\n\t\t\tclass=\"button-block button-sm has-inner-focus button button-clear display-none-tablet justify-content-flex-start text-align-left\"\n\t\t\tdata-bi-name=\"contents-expand\"\n\t\t\tdata-contents-button\n\t\t\tdata-popover-close\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-editor-list-bullet\" aria-hidden=\"true\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan class=\"contents-expand-title\"\u003eTable of contents\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t \n\t\t\u003ca\n\t\t\tid=\"lang-link-overflow\"\n\t\t\tclass=\"button-sm has-inner-focus button button-clear button-block justify-content-flex-start text-align-left\"\n\t\t\tdata-bi-name=\"language-toggle\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-read-in-link\n\t\t\thref=\"#\"\n\t\t\thidden\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\" data-read-in-link-icon\u003e\n\t\t\t\t\u003cspan class=\"docon docon-locale-globe\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan data-read-in-link-text\u003eRead in English\u003c/span\u003e\n\t\t\u003c/a\u003e\n\t \n\t\t\u003cbutton\n\t\t\ttype=\"button\"\n\t\t\tclass=\"collection button button-clear button-sm button-block justify-content-flex-start text-align-left inner-focus\"\n\t\t\tdata-list-type=\"collection\"\n\t\t\tdata-bi-name=\"collection\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-popover-close\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-circle-addition\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan class=\"collection-status\"\u003eAdd\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t\n\t\t\t\t\t\n\t\t\u003cbutton\n\t\t\ttype=\"button\"\n\t\t\tclass=\"collection button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus\"\n\t\t\tdata-list-type=\"plan\"\n\t\t\tdata-bi-name=\"plan\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-popover-close\n\t\t\thidden\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-circle-addition\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan class=\"plan-status\"\u003eAdd to plan\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t  \n\t\t\u003ca\n\t\t\tdata-contenteditbtn\n\t\t\tclass=\"button button-clear button-block button-sm inner-focus justify-content-flex-start text-align-left text-decoration-none\"\n\t\t\tdata-bi-name=\"edit\"\n\t\t\t\n\t\t\thref=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\"\n\t\t\tdata-original_content_git_url=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\"\n\t\t\tdata-original_content_git_url_template=\"{repo}/blob/{branch}/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\"\n\t\t\tdata-pr_repo=\"\"\n\t\t\tdata-pr_branch=\"\"\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-edit-outline\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan\u003eEdit\u003c/span\u003e\n\t\t\u003c/a\u003e\n\t\n\t\t\t\t\t\n\t\t\u003chr class=\"margin-block-xxs\" /\u003e\n\t\t\u003ch4 class=\"font-size-sm padding-left-xxs\"\u003eShare via\u003c/h4\u003e\n\t\t\n\t\t\t\t\t\u003ca\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-facebook\"\n\t\t\t\t\t\tdata-bi-name=\"facebook\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon color-primary\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-facebook-share\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eFacebook\u003c/span\u003e\n\t\t\t\t\t\u003c/a\u003e\n\n\t\t\t\t\t\u003ca\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-twitter\"\n\t\t\t\t\t\tdata-bi-name=\"twitter\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon color-text\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-xlogo-share\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003ex.com\u003c/span\u003e\n\t\t\t\t\t\u003c/a\u003e\n\n\t\t\t\t\t\u003ca\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-linkedin\"\n\t\t\t\t\t\tdata-bi-name=\"linkedin\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon color-primary\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-linked-in-logo\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eLinkedIn\u003c/span\u003e\n\t\t\t\t\t\u003c/a\u003e\n\t\t\t\t\t\u003ca\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-email\"\n\t\t\t\t\t\tdata-bi-name=\"email\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon color-primary\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-mail-message\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eEmail\u003c/span\u003e\n\t\t\t\t\t\u003c/a\u003e\n\t\t\t  \n\t \n\t\t\u003chr class=\"margin-block-xxs\" /\u003e\n\t\t\u003cbutton\n\t\t\tclass=\"button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus\"\n\t\t\ttype=\"button\"\n\t\t\tdata-bi-name=\"print\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-popover-close\n\t\t\tdata-print-page\n\t\t\tdata-check-hidden=\"true\"\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon color-primary\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-print\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan\u003ePrint\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t\n\t\t\t\t\u003c/div\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003c/div\u003e\n\t\n\t\t\t\u003c/div\u003e\n\t\t\u003c/div\u003e\n\t\n\t\t\t\t\t\u003c!-- azure disclaimer --\u003e\n\t\t\t\t\t\n\t\t\t\t\t\u003c!-- privateUnauthorizedTemplate is hidden by default --\u003e\n\t\t\t\t\t\n\t\t\u003cdiv unauthorized-private-section data-bi-name=\"permission-content-unauthorized-private\" hidden\u003e\n\t\t\t\u003chr class=\"hr margin-top-xs margin-bottom-sm\" /\u003e\n\t\t\t\u003cdiv class=\"notification notification-info\"\u003e\n\t\t\t\t\u003cdiv class=\"notification-content\"\u003e\n\t\t\t\t\t\u003cp class=\"margin-top-none notification-title\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-exclamation-circle-solid\" aria-hidden=\"true\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eNote\u003c/span\u003e\n\t\t\t\t\t\u003c/p\u003e\n\t\t\t\t\t\u003cp class=\"margin-top-none authentication-determined not-authenticated\"\u003e\n\t\t\t\t\t\tAccess to this page requires authorization. You can try \u003ca class=\"docs-sign-in\" href=\"#\" data-bi-name=\"permission-content-sign-in\"\u003esigning in\u003c/a\u003e or \u003ca  class=\"docs-change-directory\" data-bi-name=\"permisson-content-change-directory\"\u003echanging directories\u003c/a\u003e.\n\t\t\t\t\t\u003c/p\u003e\n\t\t\t\t\t\u003cp class=\"margin-top-none authentication-determined authenticated\"\u003e\n\t\t\t\t\t\tAccess to this page requires authorization. You can try \u003ca class=\"docs-change-directory\" data-bi-name=\"permisson-content-change-directory\"\u003echanging directories\u003c/a\u003e.\n\t\t\t\t\t\u003c/p\u003e\n\t\t\t\t\u003c/div\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/div\u003e\n\t\n\t\t\t\t\t\u003cdiv class=\"content\"\u003e\u003c/div\u003e\n\t\t\t\t\t \n\t\t\t\t\t\u003cdiv class=\"content\"\u003e\u003ch1 data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\" class=\"margin-bottom-xs\"\u003eRemove-ADReplication\u003cwbr\u003eSite\u003cwbr\u003eLink\u003cwbr\u003eBridge\u003c/h1\u003e\n\n\t\u003cdiv class=\"margin-block-xxs\"\u003e\n\t\t\u003cul class=\"metadata page-metadata align-items-center\" data-bi-name=\"page info\"\u003e\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\u003c/ul\u003e\n\t\u003c/div\u003e\n\n\u003cdiv class=\"metadata\" data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003e\n\t\t\u003cdl class=\"attributeList\"\u003e\n\t\t\t\u003cdt\u003eModule:\u003c/dt\u003e\n\t\t\t\u003cdd\u003e\u003ca href=\"./?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eActiveDirectory Module\u003c/a\u003e\u003c/dd\u003e\n\t\t\u003c/dl\u003e\n\u003c/div\u003e\n\n\u003cnav id=\"center-doc-outline\" class=\"doc-outline is-hidden-desktop display-none-print margin-bottom-sm\" data-bi-name=\"intopic toc\" aria-label=\"\"\u003e\n  \u003ch2 class=\"title is-6 margin-block-xs\"\u003e\u003c/h2\u003e\n\u003c/nav\u003e\n\n\n\t\u003cdiv class=\"margin-block-sm\" data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003e\n\t\t\u003cp\u003eDeletes a replication site link bridge from Active Directory.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\n\t\u003ch2 id=\"syntax\" data-chunk-ids=\"default\"\u003eSyntax\u003c/h2\u003e\n\t\u003ch3 id=\"default\" data-chunk-ids=\"default\"\u003e\n\t\tDefault (Default)\n\t\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"default\"\u003e\n\t\t\u003cpre\u003e\u003ccode class=\"lang-Syntax\"\u003eRemove-ADReplicationSiteLinkBridge\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType \u0026lt;ADAuthType\u0026gt;]\n    [-Credential \u0026lt;PSCredential\u0026gt;]\n    [-Identity] \u0026lt;ADReplicationSiteLinkBridge\u0026gt;\n    [-Server \u0026lt;String\u0026gt;]\n    [\u0026lt;CommonParameters\u0026gt;]\n\u003c/code\u003e\u003c/pre\u003e\n\n\t\u003c/div\u003e\n\n\n\t\u003ch2 id=\"description\" data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003eDescription\u003c/h2\u003e\n\t\u003cdiv data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003e\n\t\t\u003cp\u003eThe \u003cstrong\u003eRemove-ADReplicationSiteLinkBridge\u003c/strong\u003e cmdlet deletes a replication site link bridge from Active Directory.\nA site link bridge connects two or more site links and enables transitivity between site links.\nEach site link in a bridge must have a site in common with another site link in the bridge.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\n\t\u003ch2 id=\"examples\" data-chunk-ids=\"example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges\"\u003eExamples\u003c/h2\u003e\n\t\u003ch3 id=\"example-1-remove-a-site-link-bridge\" data-chunk-ids=\"example-1-remove-a-site-link-bridge\"\u003eExample 1: Remove a site link bridge\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"example-1-remove-a-site-link-bridge\"\u003e\n\t\t\u003cpre\u003e\u003ccode\u003ePS C:\\\u0026gt; Remove-ADReplicationSiteLinkBridge -Identity \"NorthAmerica-Asia\"\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis command removes the site link bridge named NorthAmerica-Asia.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\t\u003ch3 id=\"example-2-remove-a-filtered-list-of-site-link-bridges\" data-chunk-ids=\"example-2-remove-a-filtered-list-of-site-link-bridges\"\u003eExample 2: Remove a filtered list of site link bridges\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"example-2-remove-a-filtered-list-of-site-link-bridges\"\u003e\n\t\t\u003cpre\u003e\u003ccode\u003ePS C:\\\u0026gt; Get-ADReplicationSiteLinkBridge -Filter \"SiteLinksIncluded -eq \u0027Europe-Asia\u0027\" | Remove-ADReplicationSiteLinkBridge\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis command gets the site link bridges that include Europe-Asia and removes them.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\n\t\u003ch2 id=\"parameters\" data-chunk-ids=\"authtype,confirm,credential,identity,server,whatif\"\u003eParameters\u003c/h2\u003e\n\t\t\u003ch3 id=\"-authtype\" data-chunk-ids=\"authtype\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Auth\u003cwbr\u003eType\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the authentication method to use.\nThe acceptable values for this parameter are:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eNegotiate or 0\u003c/li\u003e\n\u003cli\u003eBasic or 1\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eThe default authentication method is Negotiate.\u003c/p\u003e\n\u003cp\u003eA Secure Sockets Layer (SSL) connection is required for the Basic authentication method.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"authtype-properties\" data-chunk-ids=\"authtype\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"authtype\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eADAuthType\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eAccepted values:\u003c/td\u003e\u003ctd\u003eNegotiate, Basic\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"authtype-sets\" data-chunk-ids=\"authtype\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"authtype\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-confirm\" data-chunk-ids=\"confirm\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Confirm\u003c/h3\u003e\n\t\t\u003cp\u003ePrompts you for confirmation before running the cmdlet.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"confirm-properties\" data-chunk-ids=\"confirm\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"confirm\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eSwitchParameter\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eAliases:\u003c/td\u003e\u003ctd\u003ecf\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"confirm-sets\" data-chunk-ids=\"confirm\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"confirm\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-credential\" data-chunk-ids=\"credential\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Credential\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the user account credentials to use to perform this task.\nThe default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive.\nIf the cmdlet is run from such a provider drive, the account associated with the drive is the default.\u003c/p\u003e\n\u003cp\u003eTo specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a \u003cstrong\u003ePSCredential\u003c/strong\u003e object.\nIf you specify a user name for this parameter, the cmdlet prompts for a password.\u003c/p\u003e\n\u003cp\u003eYou can also create a \u003cstrong\u003ePSCredential\u003c/strong\u003e object by using a script or by using the \u003cstrong\u003eGet-Credential\u003c/strong\u003e cmdlet.\nYou can then set the \u003cem\u003eCredential\u003c/em\u003e parameter to the \u003cstrong\u003ePSCredential\u003c/strong\u003e object.\u003c/p\u003e\n\u003cp\u003eIf the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"credential-properties\" data-chunk-ids=\"credential\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"credential\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003ePSCredential\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"credential-sets\" data-chunk-ids=\"credential\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"credential\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-identity\" data-chunk-ids=\"identity\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Identity\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies an Active Directory object by providing one of the following property values.\nThe identifier in parentheses is the LDAP display name for the attribute.\nThe acceptable values for this parameter are:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eA distinguished name\u003c/li\u003e\n\u003cli\u003eA GUID (objectGUID)\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eThe cmdlet searches the default naming context or partition to find the object.\nIf two or more objects are found, the cmdlet returns a non-terminating error.\u003c/p\u003e\n\u003cp\u003eThis parameter can also get this object through the pipeline or you can set this parameter to an object instance.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"identity-properties\" data-chunk-ids=\"identity\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"identity\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eADReplicationSiteLinkBridge\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"identity-sets\" data-chunk-ids=\"identity\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"identity\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003e0\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-server\" data-chunk-ids=\"server\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Server\u003c/h3\u003e\n\t\t\u003cp\u003eThe default authentication method is Negotiate.\u003c/p\u003e\n\u003cp\u003eA Secure Sockets Layer (SSL) connection is required for the Basic authentication method.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"server-properties\" data-chunk-ids=\"server\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"server\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"server-sets\" data-chunk-ids=\"server\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"server\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-whatif\" data-chunk-ids=\"whatif\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-What\u003cwbr\u003eIf\u003c/h3\u003e\n\t\t\u003cp\u003eShows what would happen if the cmdlet runs.\nThe cmdlet is not run.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"whatif-properties\" data-chunk-ids=\"whatif\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"whatif\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eSwitchParameter\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eAliases:\u003c/td\u003e\u003ctd\u003ewi\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"whatif-sets\" data-chunk-ids=\"whatif\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"whatif\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"common-parameters\" data-no-chunk=\"\"\u003eCommonParameters\u003c/h3\u003e\n\t\t\u003cdiv data-no-chunk=\"\"\u003e\n\t\t\t\u003cp\u003eThis cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\n\u003ca href=\"https://go.microsoft.com/fwlink/?LinkID=113216\" data-linktype=\"external\"\u003eabout_CommonParameters\u003c/a\u003e.\u003c/p\u003e\n\n\t\t\u003c/div\u003e\n\n\t\u003ch2 id=\"inputs\" data-chunk-ids=\"inputs\"\u003eInputs\u003c/h2\u003e\n\t\t\t\u003ch3 id=\"input-1\" data-chunk-ids=\"inputs\" class=\"break-text font-size-xl\"\u003e\u003cspan class=\"no-loc xref\"\u003eNone or Microsoft.ActiveDirectory.Management.ADReplicationSiteLinkBridge\u003c/span\u003e\n\u003c/h3\u003e\n\t\t\t\u003cdiv data-chunk-ids=\"inputs\"\u003e\n\t\t\t\t\u003cp\u003eA site link bridge object is received by the \u003cem\u003eIdentity\u003c/em\u003e parameter.\u003c/p\u003e\n\n\t\t\t\u003c/div\u003e\n\n\t\u003ch2 id=\"outputs\" data-chunk-ids=\"outputs\"\u003eOutputs\u003c/h2\u003e\n\t\t\t\u003ch3 id=\"output-1\" data-chunk-ids=\"outputs\" class=\"break-text font-size-xl\"\u003e\u003cspan class=\"no-loc xref\"\u003eNone\u003c/span\u003e\n\u003c/h3\u003e\n\t\t\t\u003cdiv data-chunk-ids=\"outputs\"\u003e\n\t\t\t\t\n\t\t\t\u003c/div\u003e\n\n\t\u003ch2 id=\"notes\" data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003eNotes\u003c/h2\u003e\n\t\u003cdiv data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003e\n\t\t\u003cul\u003e\n\u003cli\u003eBy default, this cmdlet has the \u003cem\u003eConfirm\u003c/em\u003e parameter set, which prompts you to confirm before a removal of the specified object type can occur. To bypass prompting for confirmation before removal, you can specify \u003ccode\u003e-Confirm:$False\u003c/code\u003e when using this cmdlet.\u003c/li\u003e\n\u003c/ul\u003e\n\n\t\u003c/div\u003e\n\n\t\u003ch2 id=\"related-links\" data-no-chunk=\"\"\u003eRelated Links\u003c/h2\u003e\n\t\u003cul data-no-chunk=\"\"\u003e\n\t\t\t\u003cli\u003e\u003ca href=\"get-adreplicationsitelinkbridge?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eGet-ADReplicationSiteLinkBridge\u003c/a\u003e\u003c/li\u003e\n\t\t\t\u003cli\u003e\u003ca href=\"new-adreplicationsitelinkbridge?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eNew-ADReplicationSiteLinkBridge\u003c/a\u003e\u003c/li\u003e\n\t\t\t\u003cli\u003e\u003ca href=\"set-adreplicationsitelinkbridge?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eSet-ADReplicationSiteLinkBridge\u003c/a\u003e\u003c/li\u003e\n\t\u003c/ul\u003e\n\u003c/div\u003e\n\t\t\t\t\t\n\t\t\u003cdiv\n\t\t\tid=\"ms--inline-notifications\"\n\t\t\tclass=\"margin-block-xs\"\n\t\t\tdata-bi-name=\"inline-notification\"\n\t\t\u003e\u003c/div\u003e\n\t \n\t\t\u003cdiv\n\t\t\tid=\"assertive-live-region\"\n\t\t\trole=\"alert\"\n\t\t\taria-live=\"assertive\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t\u003e\u003c/div\u003e\n\t\t\u003cdiv\n\t\t\tid=\"polite-live-region\"\n\t\t\trole=\"status\"\n\t\t\taria-live=\"polite\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t\u003e\u003c/div\u003e\n\t\n\t\t\t\t\t\n\t\t\u003c!-- feedback section --\u003e\n\t\t\u003csection\n\t\t\tid=\"site-user-feedback-footer\"\n\t\t\tclass=\"font-size-sm margin-top-md display-none-print display-none-desktop\"\n\t\t\tdata-test-id=\"site-user-feedback-footer\"\n\t\t\tdata-bi-name=\"site-feedback-section\"\n\t\t\u003e\n\t\t\t\u003chr class=\"hr\" /\u003e\n\t\t\t\u003ch2 id=\"ms--feedback\" class=\"title is-3\"\u003eFeedback\u003c/h2\u003e\n\t\t\t\u003cdiv class=\"display-flex flex-wrap-wrap align-items-center\"\u003e\n\t\t\t\t\u003cp class=\"font-weight-semibold margin-xxs margin-left-none\"\u003e\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\u003c/p\u003e\n\t\t\t\t\u003cdiv class=\"buttons\"\u003e\n\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-yes\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-like\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eYes\u003c/span\u003e\n\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-no\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-dislike\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eNo\u003c/span\u003e\n\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\u003c/div\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/section\u003e\n\t\t\u003c!-- end feedback section --\u003e\n\t\n\t\t\t\t\u003c/div\u003e\n\t\t\t\t\n\t\t\t\u003c/div\u003e\n\t\t\t\n\t\t\u003cdiv\n\t\t\tid=\"action-panel\"\n\t\t\trole=\"region\"\n\t\t\taria-label=\"Action Panel\"\n\t\t\tclass=\"action-panel\"\n\t\t\ttabindex=\"-1\"\n\t\t\u003e\u003c/div\u003e\n\t\n\t\t\n\t\t\t\t\u003c/main\u003e\n\t\t\t\t\u003caside\n\t\t\t\t\tid=\"layout-body-aside\"\n\t\t\t\t\tclass=\"layout-body-aside \"\n\t\t\t\t\tdata-bi-name=\"aside\"\n\t\t\t  \u003e\n\t\t\t\t\t\n\t\t\u003cdiv\n\t\t\tid=\"ms--additional-resources\"\n\t\t\tclass=\"right-container padding-sm display-none display-block-desktop height-full\"\n\t\t\tdata-bi-name=\"pageactions\"\n\t\t\trole=\"complementary\"\n\t\t\taria-label=\"Additional resources\"\n\t\t\u003e\n\t\t\t\u003cdiv id=\"affixed-right-container\" data-bi-name=\"right-column\"\u003e\n\t\t\t\t\n\t\t\u003cnav\n\t\t\tid=\"side-doc-outline\"\n\t\t\tclass=\"doc-outline border-bottom padding-bottom-xs margin-bottom-xs\"\n\t\t\tdata-bi-name=\"intopic toc\"\n\t\t\taria-label=\"In this article\"\n\t\t\u003e\n\t\t\t\u003ch3\u003eIn this article\u003c/h3\u003e\n\t\t\u003c/nav\u003e\n\t\n\t\t\t\t\u003c!-- Feedback --\u003e\n\t\t\t\t\n\t\t\u003csection\n\t\t\tid=\"ms--site-user-feedback-right-rail\"\n\t\t\tclass=\"font-size-sm display-none-print\"\n\t\t\tdata-test-id=\"site-user-feedback-right-rail\"\n\t\t\tdata-bi-name=\"site-feedback-right-rail\"\n\t\t\u003e\n\t\t\t\u003cp class=\"font-weight-semibold margin-bottom-xs\"\u003eWas this page helpful?\u003c/p\u003e\n\t\t\t\u003cdiv class=\"buttons\"\u003e\n\t\t\t\t\u003cbutton\n\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-yes\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\u003e\n\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-like\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\u003cspan\u003eYes\u003c/span\u003e\n\t\t\t\t\u003c/button\u003e\n\t\t\t\t\u003cbutton\n\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-no\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\u003e\n\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-dislike\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\u003cspan\u003eNo\u003c/span\u003e\n\t\t\t\t\u003c/button\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/section\u003e\n\t\n\t\t\t\u003c/div\u003e\n\t\t\u003c/div\u003e\n\t\n\t\t\t  \u003c/aside\u003e \u003csection\n\t\t\t\t\tid=\"layout-body-flyout\"\n\t\t\t\t\tclass=\"layout-body-flyout \"\n\t\t\t\t\tdata-bi-name=\"flyout\"\n\t\t\t  \u003e\n\t\t\t\t\t \u003cdiv\n\tclass=\"height-full border-left background-color-body-medium\"\n\tid=\"ask-learn-flyout\"\n\u003e\u003c/div\u003e\n\t\t\t  \u003c/section\u003e \u003cdiv class=\"layout-body-footer \" data-bi-name=\"layout-footer\"\u003e\n\t\t\u003cfooter\n\t\t\tid=\"footer\"\n\t\t\tdata-test-id=\"footer\"\n\t\t\tdata-bi-name=\"footer\"\n\t\t\tclass=\"footer-layout has-padding has-default-focus border-top  uhf-container\"\n\t\t\trole=\"contentinfo\"\n\t\t\u003e\n\t\t\t\u003cdiv class=\"display-flex gap-xs flex-wrap-wrap is-full-height padding-right-lg-desktop\"\u003e\n\t\t\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"#\"\n\t\t\tdata-bi-name=\"select-locale\"\n\t\t\tclass=\"locale-selector-link flex-shrink-0 button button-sm button-clear external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003e\u003cspan class=\"icon\" aria-hidden=\"true\"\n\t\t\t\t\u003e\u003cspan class=\"docon docon-world\"\u003e\u003c/span\u003e\u003c/span\n\t\t\t\u003e\u003cspan class=\"local-selector-link-text\"\u003een-us\u003c/span\u003e\u003c/a\n\t\t\u003e\n\t\n\t\t\t\t\u003cdiv class=\"ccpa-privacy-link\" data-ccpa-privacy-link hidden\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://aka.ms/yourcaliforniaprivacychoices\"\n\t\t\tdata-bi-name=\"your-privacy-choices\"\n\t\t\tclass=\"button button-sm button-clear flex-shrink-0 external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003e\n\t\t\u003csvg\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\tviewBox=\"0 0 30 14\"\n\t\t\txml:space=\"preserve\"\n\t\t\theight=\"16\"\n\t\t\twidth=\"43\"\n\t\t\taria-hidden=\"true\"\n\t\t\tfocusable=\"false\"\n\t\t\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#fff\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7zm-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#06f\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M24.6 4c.******* 0 .8L22.5 7l2.2 2.2c.******* 0 .8-.2.2-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0-.2-.2-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0z\"\n\t\t\t\tstyle=\"fill:#fff\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M12.7 4.1c.*******.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0z\"\n\t\t\t\tstyle=\"fill:#06f\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\u003c/svg\u003e\n\t\n\t\t\t\u003cspan\u003eYour Privacy Choices\u003c/span\u003e\u003c/a\n\t\t\u003e\n\t\n\t\u003c/div\u003e\n\t\t\t\t\u003cdiv class=\"flex-shrink-0\"\u003e\n\t\t\u003cdiv class=\"dropdown has-caret-up\"\u003e\n\t\t\t\u003cbutton\n\t\t\t\tdata-test-id=\"theme-selector-button\"\n\t\t\t\tclass=\"dropdown-trigger button button-clear button-sm has-inner-focus theme-dropdown-trigger\"\n\t\t\t\taria-controls=\"{{ themeMenuId }}\"\n\t\t\t\taria-expanded=\"false\"\n\t\t\t\ttitle=\"Theme\"\n\t\t\t\tdata-bi-name=\"theme\"\n\t\t\t\u003e\n\t\t\t\t\u003cspan class=\"icon\"\u003e\n\t\t\t\t\t\u003cspan class=\"docon docon-sun\" aria-hidden=\"true\"\u003e\u003c/span\u003e\n\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003cspan\u003eTheme\u003c/span\u003e\n\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\u003c/span\u003e\n\t\t\t\u003c/button\u003e\n\t\t\t\u003cdiv class=\"dropdown-menu\" id=\"{{ themeMenuId }}\" role=\"menu\"\u003e\n\t\t\t\t\u003cul class=\"theme-selector padding-xxs\" data-test-id=\"theme-dropdown-menu\"\u003e\n\t\t\t\t\t\u003cli class=\"theme display-block\"\u003e\n\t\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"light\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"theme-light margin-right-xxs\"\u003e\n\t\t\t\t\t\t\t\t\u003cspan\n\t\t\t\t\t\t\t\t\tclass=\"theme-selector-icon border display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\t\t\u003csvg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\"\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect width=\"22\" height=\"14\" class=\"has-fill-body-background\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\u003c/svg\u003e\n\t\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003cspan role=\"menuitem\"\u003e Light \u003c/span\u003e\n\t\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003c/li\u003e\n\t\t\t\t\t\u003cli class=\"theme display-block\"\u003e\n\t\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"dark\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"theme-dark margin-right-xxs\"\u003e\n\t\t\t\t\t\t\t\t\u003cspan\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\t\t\u003csvg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\"\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect width=\"22\" height=\"14\" class=\"has-fill-body-background\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\u003c/svg\u003e\n\t\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003cspan role=\"menuitem\"\u003e Dark \u003c/span\u003e\n\t\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003c/li\u003e\n\t\t\t\t\t\u003cli class=\"theme display-block\"\u003e\n\t\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"high-contrast\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"theme-high-contrast margin-right-xxs\"\u003e\n\t\t\t\t\t\t\t\t\u003cspan\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\t\t\u003csvg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\"\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect width=\"22\" height=\"14\" class=\"has-fill-body-background\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\u003c/svg\u003e\n\t\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003cspan role=\"menuitem\"\u003e High contrast \u003c/span\u003e\n\t\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003c/li\u003e\n\t\t\t\t\u003c/ul\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/div\u003e\n\t\u003c/div\u003e\n\t\t\t\u003c/div\u003e\n\t\t\t\u003cul class=\"links\" data-bi-name=\"footerlinks\"\u003e\n\t\t\t\t\u003cli class=\"manage-cookies-holder\" hidden=\"\"\u003e\u003c/li\u003e\n\t\t\t\t\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/principles-for-ai-generated-content\"\n\t\t\tdata-bi-name=\"aiDisclaimer\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eAI Disclaimer\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/previous-versions/\"\n\t\t\tdata-bi-name=\"archivelink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003ePrevious Versions\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e \u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://techcommunity.microsoft.com/t5/microsoft-learn-blog/bg-p/MicrosoftLearnBlog\"\n\t\t\tdata-bi-name=\"bloglink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eBlog\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e \u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/contribute\"\n\t\t\tdata-bi-name=\"contributorGuide\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eContribute\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://go.microsoft.com/fwlink/?LinkId=521839\"\n\t\t\tdata-bi-name=\"privacy\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003ePrivacy\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/legal/termsofuse\"\n\t\t\tdata-bi-name=\"termsofuse\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eTerms of Use\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://www.microsoft.com/legal/intellectualproperty/Trademarks/\"\n\t\t\tdata-bi-name=\"trademarks\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eTrademarks\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\n\t\t\t\t\u003cli\u003e\u0026copy; Microsoft 2025\u003c/li\u003e\n\t\t\t\u003c/ul\u003e\n\t\t\u003c/footer\u003e\n\t\u003c/footer\u003e\n\t\t\t\u003c/body\u003e\n\t\t\u003c/html\u003e"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "Management_Install.ps1",
                                               "Set-ExecutionPolicy -ExecutionPolicy Unrestricted",
                                               "Get-ExecutionPolicy",
                                               "Unrestricted",
                                               "C:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\\Management_Install.ps1",
                                               "get-help about_signing",
                                               ".\\Management_Install.ps1",
                                               "Set-ExecutionPolicy RemoteSigned",
                                               "Set-ExecutionPolicy Restricted",
                                               "Access to the registry key\n\u0027HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\ShellIds\\Microsoft.PowerShell\u0027 is denied. \nTo change the execution policy for the default (LocalMachine) scope, \n  start Windows PowerShell with the \u0026quot;Run as administrator\u0026quot; option. \nTo change the execution policy for the current user, \n  run \u0026quot;Set-ExecutionPolicy -Scope CurrentUser\u0026quot;.",
                                               "Set-ExecutionPolicy RemoteSigned -Scope CurrentUser",
                                               "-ExecutionPolicy Bypass",
                                               "powershell -ExecutionPolicy Bypass -File script.ps1",
                                               "C:\\Windows\\SysWOW64\\cmd.exe",
                                               "powershell Set-ExecutionPolicy RemoteSigned",
                                               "C:\\Windows\\system32\\cmd.exe",
                                               "echo %PROCESSOR_ARCHITECTURE%",
                                               "[Environment]::Is64BitProcess",
                                               "Get-ExecutionPolicy",
                                               "Set-ExecutionPolicy",
                                               "RemoteSigned",
                                               "Unrestricted",
                                               "LocalMachine",
                                               "CurrentUser",
                                               "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser",
                                               "%UserProfile%\\My Documents\\WindowsPowerShell\\Microsoft.PowerShellISE_profile.ps1",
                                               "ExecutionPolicy",
                                               "Get-ExecutionPolicy",
                                               "Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted -Force\n\nSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Bypass -Force",
                                               "Unrestricted",
                                               "Set-ExecutionPolicy Unrestricted",
                                               "CurrentUser",
                                               "RemoteSigned",
                                               "Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;",
                                               "Unrestricted",
                                               "Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;Unrestricted\u0026quot;",
                                               "Help Get-ExecutionPolicy -Full\nHelp Set-ExecutionPolicy -Full",
                                               "Set-ExecutionPolicy RemoteSigned",
                                               "Set-ExecutionPolicy RemoteSigned",
                                               "set-ExecutionPolicy RemoteSigned -Scope CurrentUser",
                                               "Get-ExecutionPolicy",
                                               "ExecutionPolicy",
                                               "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser",
                                               "Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted",
                                               "Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted",
                                               "powershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;",
                                               "powershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;AllSigned\u0026quot;",
                                               "powershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;",
                                               "*.Format.ps1xml",
                                               "*.Types.ps1xml",
                                               "Get-ChildItem",
                                               "-ExecutionPolicy",
                                               "powershell.exe",
                                               "RemoteSigned",
                                               "LocalMachine",
                                               "Set-ExecutionPolicy",
                                               "Unrestricted",
                                               "Set-ExecutionPolicy -Scope CurrentUser RemoteSigned -Force",
                                               "-ExecutionPolicy \u0026lt;policy\u0026gt;",
                                               "Set-ExecutionPolicy -Scope Process ...",
                                               "pwsh.exe -ExecutionPolicy RemoteSigned -File someScript.ps1",
                                               "Get-ExecutionPolicy",
                                               "MachinePolicy",
                                               "CurrentUser",
                                               "powershell Get-Content .\\test.ps1 | Invoke-Expression",
                                               "Set-ExecutionPolicy",
                                               "Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned",
                                               "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\",
                                               "Set-ExecutionPolicy -Scope CurrentUser Unrestricted",
                                               "Set-ExecutionPolicy RemoteSigned -Scope Process",
                                               "Set-ExecutionPolicy RemoteSigned",
                                               "powershell Set-ExecutionPolicy -Scope CurrentUser\n\ncmdlet Set-ExecutionPolicy at command pipeline position 1\nSupply values for the following parameters:\nExecutionPolicy: `RemoteSigned`",
                                               "Get-ExecutionPolicy",
                                               "Set-ExecutionPolicy Unrestricted"
                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:20:40Z",
                         "ViewCount":  5174856,
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_general_ad_10f93e23",
                         "QuestionScore":  2987,
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T16:20:40Z",
                         "TotalScore":  9106,
                         "AnswerCount":  30,
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow Q\u0026A: PowerShell says \u0026quot;execution of scripts is disabled on this system.\u0026quot;",
                         "CodeTemplate":  "Set-ExecutionPolicy RemoteSigned",
                         "CommonMistakes":  [
                                                "Contains warnings about common mistakes and errors"
                                            ],
                         "HasAcceptedAnswer":  true,
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "windows-server-2008-r2"
                                  ],
                         "Abstract":  "# Question: PowerShell says \u0026quot;execution of scripts is disabled on this system.\u0026quot;\n\n**Score:** 2987 | **Views:** 5174856 | **Tags:** powershell, windows-server-2008-r2\n\n**URL:** https://stackoverflow.com/questions/4037939/powershell-says-execution-of-scripts-is-disabled-on-this-system\n\n## Question Body\n\n\u003cp\u003eI am trying to run a \u003ccode\u003ecmd\u003c/code\u003e file that calls a PowerShell script from \u003ccode\u003ecmd.exe\u003c/code\u003e, but I am getting this error:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003e\u003ccode\u003eManagement_Install.ps1\u003c/code\u003e ...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow_qa",
                                             "CredibilityScore":  0.85,
                                             "PublishedAt":  "2025-07-28T16:20:40Z",
                                             "Url":  "https://stackoverflow.com/questions/4037939/powershell-says-execution-of-scripts-is-disabled-on-this-system",
                                             "ScrapedAt":  "2025-07-28T16:20:40Z",
                                             "Id":  "e536fedd-7693-4812-9ea8-5c73f17d9050",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow Q\u0026A: PowerShell says \u0026quot;execution of scripts is disabled on this system.\u0026quot;"
                                         }
                                     ],
                         "Content":  "# Question: PowerShell says \u0026quot;execution of scripts is disabled on this system.\u0026quot;\n\n**Score:** 2987 | **Views:** 5174856 | **Tags:** powershell, windows-server-2008-r2\n\n**URL:** https://stackoverflow.com/questions/4037939/powershell-says-execution-of-scripts-is-disabled-on-this-system\n\n## Question Body\n\n\u003cp\u003eI am trying to run a \u003ccode\u003ecmd\u003c/code\u003e file that calls a PowerShell script from \u003ccode\u003ecmd.exe\u003c/code\u003e, but I am getting this error:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003e\u003ccode\u003eManagement_Install.ps1\u003c/code\u003e cannot be loaded because the execution of scripts is disabled on this system.\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eI ran this command:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -ExecutionPolicy Unrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eWhen I run \u003ccode\u003eGet-ExecutionPolicy\u003c/code\u003e from PowerShell, it returns \u003ccode\u003eUnrestricted\u003c/code\u003e.\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eGet-ExecutionPolicy\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eOutput:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eUnrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\u003chr /\u003e\n\u003cblockquote\u003e\n\u003cp\u003ecd \u0026quot;C:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\u0026quot;\npowershell .\\Management_Install.ps1 1\u003c/p\u003e\n\u003cp\u003eWARNING: Running x86 PowerShell...\u003c/p\u003e\n\u003cp\u003eFile \u003ccode\u003eC:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\\Management_Install.ps1\u003c/code\u003e cannot be loaded because the execution of scripts is disabled on this system. Please see \u0026quot;\u003ccode\u003eget-help about_signing\u003c/code\u003e\u0026quot; for more details.\u003c/p\u003e\n\u003cp\u003eAt line:1 char:25\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003e.\\Management_Install.ps1\u003c/code\u003e \u0026lt;\u0026lt;\u0026lt;\u0026lt;  1\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003eCategoryInfo          : NotSpecified: (:) [], PSSecurityException\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eFullyQualifiedErrorId : RuntimeException\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eC:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\u0026gt; PAUSE\u003c/p\u003e\n\u003cp\u003ePress any key to continue . . .\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003chr /\u003e\n\u003cp\u003eThe system is \u003ca href=\"https://en.wikipedia.org/wiki/Windows_Server_2008\" rel=\"noreferrer\"\u003eWindows Server 2008\u003c/a\u003e R2.\u003c/p\u003e\n\u003cp\u003eWhat am I doing wrong?\u003c/p\u003e\n\n\n## Answers (30 total)\n\n### Answer 1 (Score: 3631) âœ… ACCEPTED ANSWER\n\n\u003cp\u003eIf you\u0027re using \u003ca href=\"https://en.wikipedia.org/wiki/Windows_Server_2008\" rel=\"noreferrer\"\u003eWindows Server 2008\u003c/a\u003e R2 then there is an \u003cem\u003ex64\u003c/em\u003e and \u003cem\u003ex86\u003c/em\u003e version of PowerShell both of which have to have their execution policies set. Did you set the execution policy on both hosts?\u003c/p\u003e\n\u003cp\u003eAs an \u003cem\u003eAdministrator\u003c/em\u003e, you can set the execution policy by typing this into your PowerShell window:\u003c/p\u003e\n\u003cpre class=\"lang-None prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy RemoteSigned\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eFor more information, see \u003cem\u003e\u003ca href=\"https://learn.microsoft.com/powershell/module/microsoft.powershell.security/set-executionpolicy\" rel=\"noreferrer\"\u003eUsing the Set-ExecutionPolicy Cmdlet\u003c/a\u003e\u003c/em\u003e.\u003c/p\u003e\n\u003cp\u003eWhen you are done, you can set the policy back to its default value with:\u003c/p\u003e\n\u003cpre class=\"lang-None prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy Restricted\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou may see an error:\u003c/p\u003e\n\u003cpre class=\"lang-None prettyprint-override\"\u003e\u003ccode\u003eAccess to the registry key\n\u0027HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\ShellIds\\Microsoft.PowerShell\u0027 is denied. \nTo change the execution policy for the default (LocalMachine) scope, \n  start Windows PowerShell with the \u0026quot;Run as administrator\u0026quot; option. \nTo change the execution policy for the current user, \n  run \u0026quot;Set-ExecutionPolicy -Scope CurrentUser\u0026quot;.\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eSo you may need to run the command like this (as seen in comments):\u003c/p\u003e\n\u003cpre class=\"lang-None prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy RemoteSigned -Scope CurrentUser\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 2 (Score: 1186)\n\n\u003cp\u003eYou can bypass this policy for a single file by adding \u003ccode\u003e-ExecutionPolicy Bypass\u003c/code\u003e when running PowerShell\u003c/p\u003e\n\u003cpre class=\"lang-None prettyprint-override\"\u003e\u003ccode\u003epowershell -ExecutionPolicy Bypass -File script.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 3 (Score: 412)\n\n\u003cp\u003eI had a similar issue and noted that the default \u003ccode\u003ecmd\u003c/code\u003e on \u003ca href=\"https://en.wikipedia.org/wiki/Windows_Server_2012\" rel=\"noreferrer\"\u003eWindows Server 2012\u003c/a\u003e, was running the x64 one.\u003c/p\u003e\n\u003cp\u003eFor \u003cstrong\u003eWindows 11\u003c/strong\u003e, \u003cstrong\u003eWindows 10\u003c/strong\u003e, \u003cstrong\u003eWindows 7\u003c/strong\u003e, \u003cstrong\u003eWindows 8\u003c/strong\u003e, \u003cstrong\u003eWindows Server 2008 R2\u003c/strong\u003e or \u003cstrong\u003eWindows Server 2012\u003c/strong\u003e, run the following commands as \u003cstrong\u003eAdministrator\u003c/strong\u003e:\u003c/p\u003e\n\u003cp\u003e\u003cem\u003ex86\u003c/em\u003e (32 bit)\u003cbr/\u003e\nOpen \u003ccode\u003eC:\\Windows\\SysWOW64\\cmd.exe\u003c/code\u003e\u003cbr/\u003e\nRun the command \u003ccode\u003epowershell Set-ExecutionPolicy RemoteSigned\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003e\u003cem\u003ex64\u003c/em\u003e (64 bit)\u003cbr/\u003e\nOpen \u003ccode\u003eC:\\Windows\\system32\\cmd.exe\u003c/code\u003e\u003cbr/\u003e\nRun the command \u003ccode\u003epowershell Set-ExecutionPolicy RemoteSigned\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eYou can check mode using\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eIn CMD: \u003ccode\u003eecho %PROCESSOR_ARCHITECTURE%\u003c/code\u003e\u003c/li\u003e\n\u003cli\u003eIn Powershell: \u003ccode\u003e[Environment]::Is64BitProcess\u003c/code\u003e\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003e\u003cstrong\u003eReferences:\u003c/strong\u003e\u003cbr/\u003e\n\u003ca href=\"https://technet.microsoft.com/library/hh847748.aspx\" rel=\"noreferrer\"\u003eMSDN - Windows PowerShell execution policies\u003c/a\u003e\u003cbr/\u003e\n\u003ca href=\"http://www.samlogic.net/articles/32-64-bit-windows-folder-x86-syswow64.htm\" rel=\"noreferrer\"\u003eWindows - 32bit vs 64bit directory explanation\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 4 (Score: 280)\n\n\u003cp\u003eMost of the existing answers explain the \u003cem\u003eHow\u003c/em\u003e, but very few explain the \u003cem\u003eWhy\u003c/em\u003e. And before you go around executing code from strangers on the Internet, especially code that disables security measures, you should understand exactly what you\u0027re doing. So here\u0027s a little more detail on this problem.\u003c/p\u003e\n\u003cp\u003eFrom the TechNet \u003ca href=\"http://technet.microsoft.com/library/hh847748.aspx\" rel=\"noreferrer\"\u003eAbout Execution Policies Page\u003c/a\u003e:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003eWindows PowerShell execution policies let you determine the conditions under which Windows PowerShell loads configuration files and runs scripts.\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eThe benefits of which, as enumerated by \u003ca href=\"http://www.darkoperator.com/blog/2013/3/5/powershell-basics-execution-policy-part-1.html\" rel=\"noreferrer\"\u003ePowerShell Basics - Execution Policy and Code Signing\u003c/a\u003e, are:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cstrong\u003eControl of Execution\u003c/strong\u003e - Control the level of trust for executing scripts.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003eCommand Highjack\u003c/strong\u003e - Prevent injection of commands in my path.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003eIdentity\u003c/strong\u003e - Is the script created and signed by a developer I trust and/or a signed with a certificate from a Certificate Authority I trust.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003eIntegrity\u003c/strong\u003e - Scripts cannot be modified by malware or malicious user.\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eTo check your current execution policy, you can run \u003ca href=\"http://technet.microsoft.com/library/hh849821.aspx\" rel=\"noreferrer\"\u003e\u003ccode\u003eGet-ExecutionPolicy\u003c/code\u003e\u003c/a\u003e. But you\u0027re probably here because you want to change it.\u003c/p\u003e\n\u003cp\u003eTo do so you\u0027ll run the \u003ca href=\"http://technet.microsoft.com/library/hh849812.aspx\" rel=\"noreferrer\"\u003e\u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e\u003c/a\u003e cmdlet.\u003c/p\u003e\n\u003cp\u003eYou\u0027ll have two major decisions to make when updating the execution policy.\u003c/p\u003e\n\u003ch3\u003eExecution Policy Type:\u003c/h3\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eRestricted\u003c/code\u003e\u003c/strong\u003e\u003csup\u003e†\u003c/sup\u003e - No Script either local, remote or downloaded can be executed on the system.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eAllSigned\u003c/code\u003e\u003c/strong\u003e - All script that are ran require to be digitally signed.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eRemoteSigned\u003c/code\u003e\u003c/strong\u003e - All remote scripts (UNC) or downloaded need to be signed.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eUnrestricted\u003c/code\u003e\u003c/strong\u003e - No signature for any type of script is required.\u003c/li\u003e\n\u003c/ul\u003e\n\u003ch3\u003eScope of new Change\u003c/h3\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eLocalMachine\u003c/code\u003e\u003c/strong\u003e\u003csup\u003e†\u003c/sup\u003e - The execution policy affects all users of the computer.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eCurrentUser\u003c/code\u003e\u003c/strong\u003e - The execution policy affects only the current user.\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003e\u003ccode\u003eProcess\u003c/code\u003e\u003c/strong\u003e - The execution policy affects only the current Windows PowerShell process.\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003e\u003csup\u003e† = Default\u003c/sup\u003e\u003c/p\u003e\n\u003cp\u003e\u003cem\u003eFor example\u003c/em\u003e: if you wanted to change the policy to RemoteSigned for just the CurrentUser, you\u0027d run the following command:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cstrong\u003eNote\u003c/strong\u003e: In order to change the Execution policy, you must be running \u003cstrong\u003ePowerShell As Administrator\u003c/strong\u003e.\nIf you are in regular mode and try to change the execution policy, you\u0027ll get the following error:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003eAccess to the registry key \u0027HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\ShellIds\\Microsoft.PowerShell\u0027 is denied. To change the execution policy for the default (LocalMachine) scope, start Windows PowerShell with the \u0026quot;Run as administrator\u0026quot; option.\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eIf you want to tighten up the internal restrictions on your own scripts that have not been downloaded from the Internet (or at least don\u0027t contain the UNC metadata), you can force the policy to only run signed scripts.  To sign your own scripts, you can follow the instructions on Scott Hanselman\u0027s article on \u003ca href=\"http://www.hanselman.com/blog/SigningPowerShellScripts.aspx\" rel=\"noreferrer\"\u003eSigning PowerShell Scripts\u003c/a\u003e.\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003eNote\u003c/strong\u003e: Most people are likely to get this error whenever they open PowerShell because the first thing PowerShell tries to do when it launches is execute your user profile script that sets up your environment however you like it.\u003c/p\u003e\n\u003cp\u003eThe file is typically located in:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e%UserProfile%\\My Documents\\WindowsPowerShell\\Microsoft.PowerShellISE_profile.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou can find the exact location by running the PowerShell variable\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e$profile\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eIf there\u0027s nothing that you care about in the profile, and don\u0027t want to fuss with your security settings, you can just delete it and PowerShell won\u0027t find anything that it cannot execute.\u003c/p\u003e\n\n\n### Answer 5 (Score: 80)\n\n\u003cp\u003eWe can get the status of current \u003ccode\u003eExecutionPolicy\u003c/code\u003e by the command below:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eGet-ExecutionPolicy\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eBy default it is \u003cstrong\u003eRestricted\u003c/strong\u003e. To allow the execution of PowerShell scripts we need to set this ExecutionPolicy either as \u003cstrong\u003eUnrestricted\u003c/strong\u003e or \u003cstrong\u003eBypass\u003c/strong\u003e.\u003c/p\u003e\n\u003cp\u003eWe can set the policy for Current User as \u003ccode\u003eBypass\u003c/code\u003e by using any of the below PowerShell commands:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted -Force\n\nSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Bypass -Force\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cstrong\u003eUnrestricted\u003c/strong\u003e policy loads all configuration files and runs all scripts. If you run an unsigned script that was downloaded from the Internet, you are prompted for permission before it runs.\u003c/p\u003e\n\u003cp\u003eWhereas in \u003cstrong\u003eBypass\u003c/strong\u003e policy, nothing is blocked and there are no warnings or prompts during script execution. Bypass \u003ccode\u003eExecutionPolicy\u003c/code\u003e is more relaxed than \u003ccode\u003eUnrestricted\u003c/code\u003e.\u003c/p\u003e\n\n\n### Answer 6 (Score: 68)\n\n\u003cp\u003eAlso running this command before the script also solves the issue:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy Unrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 7 (Score: 59)\n\n\u003cp\u003eIf you are in an environment where you are not an administrator, you can set the Execution Policy just for you (\u003ccode\u003eCurrentUser\u003c/code\u003e), and it will not require administrator.\u003c/p\u003e\n\u003cp\u003eYou can set it to \u003ccode\u003eRemoteSigned\u003c/code\u003e:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eor \u003ccode\u003eUnrestricted\u003c/code\u003e:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;Unrestricted\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou can read all about Getting and Setting Execution policy in the help entries:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eHelp Get-ExecutionPolicy -Full\nHelp Set-ExecutionPolicy -Full\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 8 (Score: 58)\n\n\u003cp\u003eIn Windows 7:\u003c/p\u003e\n\u003cp\u003eGo to Start Menu and search for \u0026quot;Windows PowerShell ISE\u0026quot;.\u003c/p\u003e\n\u003cp\u003eRight click the x86 version and choose \u0026quot;Run as administrator\u0026quot;.\u003c/p\u003e\n\u003cp\u003eIn the top part, paste \u003ccode\u003eSet-ExecutionPolicy RemoteSigned\u003c/code\u003e; run the script. Choose \u0026quot;Yes\u0026quot;.\u003c/p\u003e\n\u003cp\u003eRepeat these steps for the 64-bit version of Powershell ISE too (the non x86 version).\u003c/p\u003e\n\n\n### Answer 9 (Score: 43)\n\n\u003cp\u003eRemoteSigned: all scripts you created yourself will be run, and all scripts downloaded from the Internet will need to be signed by a trusted publisher.\u003c/p\u003e\n\u003cp\u003eOK, change the policy by simply typing:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy RemoteSigned\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 10 (Score: 36)\n\n\u003cp\u003eFirst, you need to open the PowerShell window and run this command.\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eset-ExecutionPolicy RemoteSigned -Scope CurrentUser\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eThen it will ask you to confirm. Type \u003ckbd\u003eY\u003c/kbd\u003e and press \u003ckbd\u003eEnter\u003c/kbd\u003e.\u003c/p\u003e\n\u003cp\u003eWhen you run this command, you can see that your system has set all policies for the current user as remotely. It will take a few seconds to complete this process.\u003c/p\u003e\n\u003cp\u003eThe image will be shown like below:\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/XyFUJ.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/XyFUJ.png\" alt=\"Enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003cp\u003eTo check if the execution policy has set. Type:\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eGet-ExecutionPolicy\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eIf it was set, the output would be like this:\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/0wTAV.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/0wTAV.png\" alt=\"Enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 11 (Score: 33)\n\n\u003cp\u003eI\u0027m using \u003cstrong\u003eWindows 10\u003c/strong\u003e and was unable to run any command. The only command that gave me some clues was this: \u003c/p\u003e\n\n\u003cp\u003e[x64]\u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003col\u003e\n  \u003cli\u003eOpen C:\\Windows\\SysWOW64\\cmd.exe \u003cem\u003e[as administrator]\u003c/em\u003e\u003c/li\u003e\n  \u003cli\u003eRun the command\u003e \u003cstrong\u003epowershell Set-ExecutionPolicy Unrestricted\u003c/strong\u003e\u003c/li\u003e\n  \u003c/ol\u003e\n\u003c/blockquote\u003e\n\n\u003cp\u003eBut this didn\u0027t work. It was limited. Probably new security policies for Windows10. I had this error: \u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003cp\u003eSet-ExecutionPolicy: Windows PowerShell updated your execution policy successfully, but the setting is overridden by a policy defined at a more specific scope. Due to the override, your shell will retain its current effective execution policy of...\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\u003cp\u003eSo I found another way (\u003cstrong\u003esolution\u003c/strong\u003e):\u003c/p\u003e\n\n\u003col\u003e\n\u003cli\u003eOpen Run Command/Console (\u003ckbd\u003eWin\u003c/kbd\u003e + \u003ckbd\u003eR\u003c/kbd\u003e) \u003c/li\u003e\n\u003cli\u003eType: \u003cstrong\u003egpedit.msc\u003c/strong\u003e (\u003ca href=\"http://en.wikipedia.org/wiki/Group_Policy\" rel=\"noreferrer\"\u003eGroup Policy\u003c/a\u003e Editor)\u003c/li\u003e\n\u003cli\u003eBrowse to \u003cem\u003eLocal Computer Policy\u003c/em\u003e -\u003e \u003cem\u003eComputer Configuration\u003c/em\u003e -\u003e \u003cem\u003eAdministrative Templates\u003c/em\u003e -\u003e \u003cem\u003eWindows Components\u003c/em\u003e -\u003e \u003cem\u003eWindows Powershell\u003c/em\u003e. \u003c/li\u003e\n\u003cli\u003eEnable \"\u003cstrong\u003eTurn on Script Execution\u003c/strong\u003e\"\u003c/li\u003e\n\u003cli\u003eSet the policy as needed. I set mine to \"\u003cstrong\u003eAllow all scripts\u003c/strong\u003e\".\u003c/li\u003e\n\u003c/ol\u003e\n\n\u003cp\u003eNow open PowerShell and enjoy ;)\u003c/p\u003e\n\n\n### Answer 12 (Score: 30)\n\n\u003cp\u003eOpen a Windows PowerShell command window and run the below query to change \u003ccode\u003eExecutionPolicy\u003c/code\u003e:\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003e\u003ccode\u003eSet-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser\u003c/code\u003e\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003eIf it asks for confirming changes, press \u003ckbd\u003eY\u003c/kbd\u003e and hit \u003ckbd\u003eEnter\u003c/kbd\u003e.\u003c/p\u003e\n\n\n### Answer 13 (Score: 25)\n\n\u003cpre\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis worked for me\u003c/p\u003e\n\n\n### Answer 14 (Score: 22)\n\n\u003cp\u003eYou should run this command:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 15 (Score: 19)\n\n\u003cp\u003e\u003ckbd\u003eWin\u003c/kbd\u003e + \u003ckbd\u003eR\u003c/kbd\u003e and type copy paste command and press \u003ckbd\u003eOK\u003c/kbd\u003e:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eAnd execute your script.\u003c/p\u003e\n\u003cp\u003eThen revert changes like:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;AllSigned\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 16 (Score: 14)\n\n\u003cp\u003eOpen the command prompt in Windows.\nIf the problem is only with PowerShell, use the following command:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell Set-ExecutionPolicy -Scope \u0026quot;CurrentUser\u0026quot; -ExecutionPolicy \u0026quot;RemoteSigned\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 17 (Score: 14)\n\n\n\u003cp\u003eThere\u0027s great information in the existing answers, but let me attempt a \u003cstrong\u003esystematic overview\u003c/strong\u003e:\u003c/p\u003e\n\u003ch3\u003eContext\u003c/h3\u003e\n\u003cp\u003ePowerShell\u0027s effective \u003cstrong\u003e\u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Execution_Policies\" rel=\"nofollow noreferrer\"\u003eexecution policy\u003c/a\u003e applies\u003c/strong\u003e:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003eto \u003cstrong\u003ePowerShell code stored in \u003cem\u003efiles\u003c/em\u003e\u003c/strong\u003e, which means:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eregular script files (\u003ccode\u003e*.ps1\u003c/code\u003e)\u003c/li\u003e\n\u003cli\u003escript \u003cem\u003emodule\u003c/em\u003e files (\u003ccode\u003e*.psm1\u003c/code\u003e) (modules implemented in PowerShell)\u003c/li\u003e\n\u003cli\u003emodules that are bundled with \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Format.ps1xml\" rel=\"nofollow noreferrer\"\u003eformatting\u003c/a\u003e and \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Types.ps1xml\" rel=\"nofollow noreferrer\"\u003etype-extension\u003c/a\u003e files (\u003ccode\u003e*.Format.ps1xml\u003c/code\u003e and \u003ccode\u003e*.Types.ps1xml\u003c/code\u003e) - even if those files happen \u003cem\u003enot\u003c/em\u003e to contain embedded PowerShell script blocks.\u003c/li\u003e\n\u003cli\u003eIt does \u003cem\u003enot\u003c/em\u003e apply to:\n\u003cul\u003e\n\u003cli\u003ecalls to (binary) \u003cem\u003ecmdlets\u003c/em\u003e (e.g., \u003ccode\u003eGet-ChildItem\u003c/code\u003e), \u003cem\u003eexcept\u003c/em\u003e for third-party binary cmdlets that come with modules that encompass formatting and type-extension files, as discussed above.\u003c/li\u003e\n\u003cli\u003ecommands submitted interactively or passed to the PowerShell \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_powershell_exe\" rel=\"nofollow noreferrer\"\u003eCLI\u003c/a\u003e via the\u003cbr /\u003e\n\u003ccode\u003e-Command\u003c/code\u003e parameter (unless these commands directly or indirectly call script files as defined above).\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003cstrong\u003eon Windows only\u003c/strong\u003e (that is, on \u003cem\u003eUnix-like\u003c/em\u003e platforms (Linux, macOS) execution policies do not apply and \u003cem\u003eno\u003c/em\u003e restrictions are placed on executing PowerShell code)\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eIn \u003cstrong\u003e\u003cem\u003eworkstation\u003c/em\u003e editions of Windows\u003c/strong\u003e, \u003cstrong\u003escript-file execution is \u003cem\u003edisabled\u003c/em\u003e by default\u003c/strong\u003e (policy \u003cstrong\u003e\u003ccode\u003eRestricted\u003c/code\u003e\u003c/strong\u003e), requiring either a \u003cem\u003epersistent\u003c/em\u003e modification of the policy to enable it, or a \u003cem\u003ecurrent-process-only\u003c/em\u003e modification such as via the \u003ccode\u003e-ExecutionPolicy\u003c/code\u003e parameter when calling the PowerShell \u003cem\u003eCLI\u003c/em\u003e, \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_powershell_exe\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003epowershell.exe\u003c/code\u003e\u003c/a\u003e (\u003cem\u003eWindows PowerShell\u003c/em\u003e edition) / \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_pwsh\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003epwsh.exe\u003c/code\u003e\u003c/a\u003e (\u003cem\u003ePowerShell (Core) 7\u003c/em\u003e edition).\u003cbr /\u003e\nIn \u003cstrong\u003erecent \u003cem\u003eserver\u003c/em\u003e editions of Windows\u003c/strong\u003e, the \u003cstrong\u003edefault policy is \u003ccode\u003eRemoteSigned\u003c/code\u003e\u003c/strong\u003e, meaning that while locally stored scripts (including on file shares) may be executed, downloaded-from-the-web scripts only execute if they\u0027re \u003cem\u003esigned\u003c/em\u003e.\u003c/p\u003e\n\u003cp\u003eExecution \u003cstrong\u003epolicies are maintained \u003cem\u003eseparately\u003c/em\u003e\u003c/strong\u003e:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003efor the two \u003cstrong\u003ePowerShell \u003cem\u003eeditions\u003c/em\u003e\u003c/strong\u003e:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003ethe legacy, \u003cem\u003eWindows-only\u003c/em\u003e, ships-with-Windows \u003cstrong\u003e\u003cem\u003eWindows PowerShell\u003c/em\u003e\u003c/strong\u003e edition (whose latest and last version is v5.1.x)\u003c/li\u003e\n\u003cli\u003ethe modern, \u003cem\u003ecross-platform\u003c/em\u003e, install-on-demand \u003cstrong\u003e\u003ca href=\"https://github.com/PowerShell/PowerShell/blob/master/README.md\" rel=\"nofollow noreferrer\"\u003e\u003cem\u003ePowerShell (Core) 7\u003c/em\u003e\u003c/a\u003e\u003c/strong\u003e edition.\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003efor the \u003cstrong\u003e\u003cem\u003e32-bit and 64-bit\u003c/em\u003e versions of \u003cem\u003eWindows PowerShell\u003c/em\u003e\u003c/strong\u003e (both of which are preinstalled)\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eNote: If you were to install 32-bit and 64-bit versions of \u003cem\u003ePowerShell (Core)\u003c/em\u003e side by side (which would be unusual), only the \u003ccode\u003eLocalMachine\u003c/code\u003e scope would be bitness-specific.\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eFor a given edition / bitness combination of PowerShell, the \u003cstrong\u003eexecution policies can be set in \u003cem\u003emultiple scopes\u003c/em\u003e\u003c/strong\u003e, but \u003cstrong\u003ethere\u0027s only ever \u003cem\u003eone effective\u003c/em\u003e policy\u003c/strong\u003e, based on precedence rules - see below.\u003c/p\u003e\n\u003chr /\u003e\n\u003ch3\u003eDetails\u003c/h3\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003e\u003cstrong\u003eIn PowerShell \u003cem\u003eon Windows\u003c/em\u003e, script-file execution is \u003cem\u003edisabled\u003c/em\u003e by default\u003c/strong\u003e in \u003cstrong\u003e\u003cem\u003eworkstation\u003c/em\u003e editions\u003c/strong\u003e of Windows (on Unix, execution policies do not apply); that is, the default execution policy in workstation editions of Windows is \u003cstrong\u003e\u003ccode\u003eRestricted\u003c/code\u003e\u003c/strong\u003e, whereas in \u003cstrong\u003e\u003cem\u003eserver\u003c/em\u003e editions\u003c/strong\u003e, it is \u003cstrong\u003e\u003ccode\u003eRemoteSigned\u003c/code\u003e\u003c/strong\u003e; see the conceptual \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Execution_Policies\" rel=\"nofollow noreferrer\"\u003eabout_Execution_Policies\u003c/a\u003e help topic for a description of all available policies.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eTo \u003cstrong\u003eset a (local) policy that permits script execution\u003c/strong\u003e, use \u003cstrong\u003e\u003ca href=\"https://learn.microsoft.com/powershell/module/microsoft.powershell.security/set-executionpolicy\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e\u003c/a\u003e\u003c/strong\u003e with a policy of \u003ccode\u003eAllSigned\u003c/code\u003e, \u003ccode\u003eRemoteSigned\u003c/code\u003e, \u003ccode\u003eUnrestricted\u003c/code\u003e, or \u003ccode\u003eBypass\u003c/code\u003e, in descending order of security. There are \u003cstrong\u003ethree scopes\u003c/strong\u003e that \u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e can target, using the \u003cstrong\u003e\u003ccode\u003e-Scope\u003c/code\u003e parameter\u003c/strong\u003e (see below); \u003cstrong\u003echanging the \u003ccode\u003eLocalMachine\u003c/code\u003e scope\u0027s policy requires \u003cem\u003eelevation\u003c/em\u003e (running as admin)\u003c/strong\u003e.\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003e\u003cstrong\u003eA frequently used policy that provides a \u003cem\u003ebalance between security and convenience\u003c/em\u003e is \u003ccode\u003eRemoteSigned\u003c/code\u003e\u003c/strong\u003e, which allows \u003cem\u003elocal\u003c/em\u003e scripts - including from \u003cem\u003enetwork shares\u003c/em\u003e - to execute \u003cem\u003ewithout containing a \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Signing\" rel=\"nofollow noreferrer\"\u003esignature\u003c/a\u003e\u003c/em\u003e, while requiring scripts downloaded \u003cem\u003efrom the internet\u003c/em\u003e to be signed (assuming that the downloading mechanism marks such as scripts as internet-originated, which web browsers do by default). For instance, \u003cstrong\u003eto set the current user\u0027s execution policy to \u003ccode\u003eRemoteSigned\u003c/code\u003e\u003c/strong\u003e, run the following (\u003ccode\u003e-Force\u003c/code\u003e bypasses the confirmation prompt that is shown by default):\u003c/p\u003e\n\u003cpre class=\"lang-bash prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser RemoteSigned -Force\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eTo \u003cem\u003eunset\u003c/em\u003e a previously set policy in a given scope, use the \u003ccode\u003eUndefined\u003c/code\u003e policy.\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eThe \u003cstrong\u003ePowerShell \u003cem\u003eCLI\u003c/em\u003e\u003c/strong\u003e (\u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_powershell_exe\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003epowershell.exe\u003c/code\u003e\u003c/a\u003e for \u003cem\u003eWindows PowerShell\u003c/em\u003e, \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Pwsh\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003epwsh.exe\u003c/code\u003e\u003c/a\u003e for \u003cem\u003ePowerShell (Core) 7\u003c/em\u003e) accepts a process-specific \u003ccode\u003e-ExecutionPolicy \u0026lt;policy\u0026gt;\u003c/code\u003e argument too, which is often used for \u003cstrong\u003e\u003cem\u003ead-hoc\u003c/em\u003e policy overrides\u003c/strong\u003e (only for the process being created, the equivalent of \u003ccode\u003eSet-ExecutionPolicy -Scope Process ...\u003c/code\u003e); e.g.:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epwsh.exe -ExecutionPolicy RemoteSigned -File someScript.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003cstrong\u003eImportant\u003c/strong\u003e:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003eExecution policies can also be set via \u003cstrong\u003e\u003ca href=\"https://en.wikipedia.org/wiki/Group_Policy\" rel=\"nofollow noreferrer\"\u003eGroup Policy Objects (GPOs)\u003c/a\u003e\u003c/strong\u003e, in which case they \u003cstrong\u003ecan \u003cem\u003enot\u003c/em\u003e be changed or overridden with \u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e or via the CLI\u0027s \u003ccode\u003e-ExecutionPolicy\u003c/code\u003e parameter\u003c/strong\u003e: see \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_Group_Policy_Settings\" rel=\"nofollow noreferrer\"\u003eabout_Group_Policy_Settings\u003c/a\u003e\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eExecution policies can be set in \u003cstrong\u003evarious scopes\u003c/strong\u003e, and which one is in effect is determined by their \u003cstrong\u003eprecedence\u003c/strong\u003e (run \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/Microsoft.PowerShell.Security/Get-ExecutionPolicy\" rel=\"nofollow noreferrer\"\u003e\u003ccode\u003eGet-ExecutionPolicy\u003c/code\u003e\u003c/a\u003e\u003ccode\u003e -List\u003c/code\u003e to see all scopes and their respective policies), in \u003cstrong\u003edescending order\u003c/strong\u003e:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003e\u003ccode\u003eMachinePolicy\u003c/code\u003e (via GPO; cannot be overridden locally)\u003csup\u003e[1]\u003c/sup\u003e\u003c/li\u003e\n\u003cli\u003e\u003ccode\u003eUserPolicy\u003c/code\u003e (via GPO; cannot be overridden locally)\u003csup\u003e[1]\u003c/sup\u003e\u003c/li\u003e\n\u003cli\u003e\u003ccode\u003eProcess\u003c/code\u003e (current process only; typically set ad-hoc via the CLI)\u003c/li\u003e\n\u003cli\u003e\u003ccode\u003eCurrentUser\u003c/code\u003e (as set by \u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e)\u003c/li\u003e\n\u003cli\u003e\u003ccode\u003eLocalMachine\u003c/code\u003e (as set by \u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e, with admin rights)\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003c/li\u003e\n\u003c/ul\u003e\n\u003chr /\u003e\n\u003cp\u003e\u003csup\u003e[1] This applies to \u003cem\u003edomain\u003c/em\u003e-wide GPOs. \u003cem\u003eLocal\u003c/em\u003e GPOs \u003cem\u003ecan\u003c/em\u003e  be modified locally, namely via \u003ccode\u003egpedit.msc\u003c/code\u003e or directly via the registry, which in the case of the \u003cem\u003emachine\u003c/em\u003e policy requires administrative privileges.\u003c/sup\u003e\u003c/p\u003e\n\n\n### Answer 18 (Score: 11)\n\n\u003cp\u003eSetting the execution policy is environment-specific. If you are trying to execute a script from the running x86 \u003ca href=\"https://technet.microsoft.com/en-us/library/dd315244.aspx\" rel=\"noreferrer\"\u003eISE\u003c/a\u003e you have to use the x86 PowerShell to set the execution policy. Likewise, if you are running the 64-bit ISE you have to set the policy with the 64-bit PowerShell.\u003c/p\u003e\n\n\n### Answer 19 (Score: 11)\n\n\u003col\u003e\n\u003cli\u003eOpen Run Command/Console ( Win + R )\nType: \u003cstrong\u003egpedit. msc\u003c/strong\u003e (Group Policy Editor)\u003c/li\u003e\n\u003cli\u003eBrowse to \u003cstrong\u003eLocal Computer Policy\u003c/strong\u003e -\u003e \u003cstrong\u003eComputer Configuration\u003c/strong\u003e -\u003e \u003cstrong\u003eAdministrative Templates\u003c/strong\u003e -\u003e \u003cstrong\u003eWindows Components\u003c/strong\u003e -\u003e \u003cstrong\u003eWindows Powershell.\u003c/strong\u003e\u003c/li\u003e\n\u003cli\u003e\u003cstrong\u003eEnable\u003c/strong\u003e \"\u003cstrong\u003eTurn on Script Execution\u003c/strong\u003e\"\nSet the policy as needed. \u003cstrong\u003eI set mine to \"Allow all scripts\"\u003c/strong\u003e.\u003c/li\u003e\n\u003c/ol\u003e\n\n\u003cp\u003eNow run the run command what ever you are using.. Trust this the app will runs.. Enjoy :)\u003c/p\u003e\n\n\n### Answer 20 (Score: 10)\n\n\u003cp\u003eYou can also bypass this by using the following command:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell Get-Content .\\test.ps1 | Invoke-Expression\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou can also read this article by Scott Sutherland that explains 15 different ways to bypass the PowerShell \u003ccode\u003eSet-ExecutionPolicy\u003c/code\u003e if you don\u0027t have administrator privileges:\u003c/p\u003e\n\u003cp\u003e\u003cem\u003e\u003ca href=\"https://blog.netspi.com/15-ways-to-bypass-the-powershell-execution-policy/\" rel=\"nofollow noreferrer\"\u003e15 Ways to Bypass the PowerShell Execution Policy\u003c/a\u003e\u003c/em\u003e\u003c/p\u003e\n\n\n### Answer 21 (Score: 10)\n\n\u003cp\u003eyou may try this and select \"All\" Option\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 22 (Score: 10)\n\n\u003cp\u003eI have also faced a similar issue. Try this.\u003c/p\u003e\n\u003cp\u003eAs I\u0027m using Windows, I followed the steps as given below.\nOpen a command prompt as an administrator and then go to this path:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eC:\\Users\\<USER>\\AppData\\Roaming\\npm\\\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eLook for the file ng.ps1 in this folder (directory)\nand then delete it (\u003cem\u003edel ng.ps1\u003c/em\u003e).\u003c/p\u003e\n\u003cp\u003eYou can also clear npm cache after this though it should work without this step as well.\u003c/p\u003e\n\n\n### Answer 23 (Score: 9)\n\n\u003cp\u003eIf you have Git installed, just use \u003ca href=\"https://superuser.com/questions/1053633/what-is-git-bash-for-windows-anyway\"\u003eGit Bash\u003c/a\u003e.\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/59WhL.png\" rel=\"nofollow noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/59WhL.png\" alt=\"Enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 24 (Score: 8)\n\n\u003cp\u003eFor Windows 11...\u003c/p\u003e\n\u003cp\u003eIt is indeed very easy. Just open the settings application.\nNavigate to \u003cem\u003ePrivacy and Security\u003c/em\u003e:\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/q76IF.png\" rel=\"nofollow noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/q76IF.png\" alt=\"Privacy and security image\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003cp\u003eClick on \u003cem\u003eFor Developers\u003c/em\u003e and scroll to the bottom and find the PowerShell option under which check the checkbox stating \u0026quot;Change the execution policy ... remote scripts\u0026quot;.\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/Snz6j.png\" rel=\"nofollow noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/Snz6j.png\" alt=\"Developer options image\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 25 (Score: 8)\n\n\u003cp\u003eIn VS code just run this command:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eSet-ExecutionPolicy -Scope CurrentUser Unrestricted\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 26 (Score: 7)\n\n\u003cp\u003eIn the PowerShell \u003ca href=\"https://technet.microsoft.com/en-us/library/dd315244.aspx\" rel=\"nofollow noreferrer\"\u003eISE\u003c/a\u003e editor I found running the following line first allowed scripts.\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy RemoteSigned -Scope Process\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 27 (Score: 7)\n\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003eSet-ExecutionPolicy RemoteSigned\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eExecuting this command in administrator mode in PowerShell will solve the problem.\u003c/p\u003e\n\n\n### Answer 28 (Score: 7)\n\n\u003cp\u003eIn Window 10:\u003c/p\u003e\n\u003cp\u003eIf you are not administrator, you can use this:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell Set-ExecutionPolicy -Scope CurrentUser\n\ncmdlet Set-ExecutionPolicy at command pipeline position 1\nSupply values for the following parameters:\nExecutionPolicy: `RemoteSigned`\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eIt solved my problem like a charm!\u003c/p\u003e\n\n\n### Answer 29 (Score: 6)\n\n\u003cp\u003eIn powershell\u003c/p\u003e\n\u003cp\u003eTo check the current execution policy, use the following command:\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eGet-ExecutionPolicy\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eTo change the execution policy to Unrestricted, which allows running any script without digital signatures, use the following command:\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eSet-ExecutionPolicy Unrestricted\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eThis solution worked for me, but be careful of the security risks involved.\u003c/p\u003e\n\n\n### Answer 30 (Score: 5)\n\n\u003col\u003e\n\u003cli\u003eOpen PowerShell as Administrator and run \u003cstrong\u003eSet-ExecutionPolicy -Scope CurrentUser\u003c/strong\u003e\u003c/li\u003e\n\u003cli\u003eProvide \u003cstrong\u003eRemoteSigned\u003c/strong\u003e and press Enter\u003c/li\u003e\n\u003cli\u003eRun \u003cstrong\u003eSet-ExecutionPolicy -Scope CurrentUser\u003c/strong\u003e\u003c/li\u003e\n\u003cli\u003eProvide \u003cstrong\u003eUnrestricted\u003c/strong\u003e and press Enter\u003c/li\u003e\n\u003c/ol\u003e\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "$PSVersionTable.PSVersion",
                                               "$Host.Version",
                                               "(Get-Host).Version",
                                               "PS C:\\\u0026gt; $PSVersionTable.PSVersion\n\nMajor  Minor  Build  Revision\n-----  -----  -----  --------\n4      0      -1     -1",
                                               "$PSVersionTable",
                                               "get-host\n\nName             : ConsoleHost\nVersion          : 2.0\nInstanceId       : d730016e-2875-4b57-9cd6-d32c8b71e18a\nUI               : System.Management.Automation.Internal.Host.InternalHostUserInterface\nCurrentCulture   : en-GB\nCurrentUICulture : en-US\nPrivateData      : Microsoft.PowerShell.ConsoleHost+ConsoleColorProxy\nIsRunspacePushed : False\nRunspace         : System.Management.Automation.Runspaces.LocalRunspace\n\n$PSVersionTable\n\nName                           Value\n----                           -----\nCLRVersion                     2.0.50727.4200\nBuildVersion                   6.0.6002.18111\nPSVersion                      2.0\nWSManStackVersion              2.0\nPSCompatibleVersions           {1.0, 2.0}\nSerializationVersion           *******\nPSRemotingProtocolVersion      2.1",
                                               "$psversiontable",
                                               "1 \u0026gt;  $psversiontable\n\nName                           Value                                           \n----                           -----                                           \nCLRVersion                     2.0.50727.4927                                  \nBuildVersion                   6.1.7600.16385                                  \nPSVersion                      2.0                                             \nWSManStackVersion              2.0                                             \nPSCompatibleVersions           {1.0, 2.0}                                      \nSerializationVersion           *******                                         \nPSRemotingProtocolVersion      2.1",
                                               "HKEY_LOCAL_MACHINE\\Software\\Microsoft\\PowerShell\\1\\Install",
                                               "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\3",
                                               "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\PowerShellEngine\\PowerShellVersion",
                                               "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\3\\PowerShellEngine\\PowerShellVersion",
                                               "$isV2 = test-path variable:\\psversiontable",
                                               "function Get-PSVersion {\n    if (test-path variable:psversiontable) {$psversiontable.psversion} else {[version]\"*******\"}\n}",
                                               "powershell -Command \"$PSVersionTable.PSVersion\"",
                                               "powershell -command \"(Get-Variable PSVersionTable -ValueOnly).PSVersion\"",
                                               "Get-Host | Select-Object Version",
                                               "Version\n-------\n3.0",
                                               "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1",
                                               "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\PowerShellEngine",
                                               "HKLM\\Software\\Microsoft\\PowerShellCore\\InstalledVersions\\\u0026lt;GUID\u0026gt;",
                                               "\u0026lt;GUID\u0026gt;",
                                               "SemanticVersion",
                                               "Path                                                                                           Name              Type Data\n----                                                                                           ----              ---- ----\nHKLM:\\SOFTWARE\\Microsoft\\PowerShellCore\\InstalledVersions\\31ab5147-9a97-4452-8443-d9709f0516e1 SemanticVersion String 7.1.3",
                                               "Get-ItemPropertyValue",
                                               "PS\u0026gt; Get-ItemPropertyValue -Path \u0026quot;HKLM:\\SOFTWARE\\Microsoft\\PowerShellCore\\InstalledVersions\\*\u0026quot; -Name \u0026quot;SemanticVersion\u0026quot;\n\n7.1.3",
                                               "C:\\Users\\<USER>\u0026gt;powershell\n\nWindows PowerShell\nCopyright (C) 2009 Microsoft Corporation. All rights reserved.\n\nPS C:\\Users\\<USER>\u0026gt;",
                                               "$PSVersionTable.PSVersion",
                                               "PS C:\\Users\\<USER>\u0026gt; $PSVersionTable.PSVersion\n\nMajor  Minor  Build  Revision\n-----  -----  -----  --------\n2      0      -1     -1\n\nPS C:\\Users\\<USER>\u0026gt;",
                                               "$host.version",
                                               "$psversiontable.psversion",
                                               "Get-Variable",
                                               "Get-Variable | where {$_.Name -Like \u0027*version*\u0027} | %{$_[0].Value}",
                                               "$psVersion = $PSVersionTable.PSVersion\nIf ($psVersion)\n{\n    #PowerShell Version Mapping\n    $psVersionMappings = @()\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.1.14393.0\u0027;FriendlyName=\u0027Windows PowerShell 5.1 Preview\u0027;ApplicableOS=\u0027Windows 10 Anniversary Update\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.1.14300.1000\u0027;FriendlyName=\u0027Windows PowerShell 5.1 Preview\u0027;ApplicableOS=\u0027Windows Server 2016 Technical Preview 5\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.494\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1607\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.122\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1603\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.117\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM 1602\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.63\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1602\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.51\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM 1512\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10514.6\u0027;FriendlyName=\u0027Windows PowerShell 5 Production Preview 1508\u0027;ApplicableOS=\u0027Windows Server 2012 R2\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10018.0\u0027;FriendlyName=\u0027Windows PowerShell 5 Preview 1502\u0027;ApplicableOS=\u0027Windows Server 2012 R2\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.9883.0\u0027;FriendlyName=\u0027Windows PowerShell 5 Preview November 2014\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows 8.1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00274.0\u0027;FriendlyName=\u0027Windows PowerShell 4 RTM\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00273.0\u0027;FriendlyName=\u0027Windows PowerShell 3 RTM\u0027;ApplicableOS=\u0027Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00272.0\u0027;FriendlyName=\u0027Windows PowerShell 2 RTM\u0027;ApplicableOS=\u0027Windows Server 2008 R2 SP1 and Windows 7\u0027}\n    foreach ($psVersionMapping in $psVersionMappings)\n    {\n        If ($psVersion -ge $psVersionMapping.Name) {\n            @{CurrentVersion=$psVersion;FriendlyName=$psVersionMapping.FriendlyName;ApplicableOS=$psVersionMapping.ApplicableOS}\n            Break\n        }\n    }\n}\nElse{\n    @{CurrentVersion=\u00271.0\u0027;FriendlyName=\u0027Windows PowerShell 1 RTM\u0027;ApplicableOS=\u0027Windows Server 2008, Windows Server 2003, Windows Vista, Windows XP\u0027}\n}",
                                               "HKLM\\Software\\Microsoft\\PowerShell\\1 Install ( = 1 )",
                                               "HKLM\\Software\\Microsoft\\PowerShell\\1 PID (=89393-100-0001260-00301) -- For RC2\nHKLM\\Software\\Microsoft\\PowerShell\\1 PID (=89393-100-0001260-04309) -- For RTM",
                                               "@echo off\necho Checking powershell version...\ndel \"%temp%\\PSVers.txt\" 2\u0026gt;nul\npowershell -command \"[string]$PSVersionTable.PSVersion.Major +\u0027.\u0027+ [string]$PSVersionTable.PSVersion.Minor | Out-File ([string](cat env:\\temp) + \u0027\\PSVers.txt\u0027)\" 2\u0026gt;nul\nif errorlevel 1 (\n echo Powershell is not installed. Please install it from download.Microsoft.com; thanks.\n) else (\n echo You have installed Powershell version:\n type \"%temp%\\PSVers.txt\"\n del \"%temp%\\PSVers.txt\" 2\u0026gt;nul\n)\ntimeout 15",
                                               "if ($PSVersionTable.PSVersion.Major -eq 5) {\n    #Execute code available in PowerShell 5, like Compress\n    Write-Host \"You are running PowerShell version 5\"\n}\nelse {\n    #Use a different process\n    Write-Host \"This is version $PSVersionTable.PSVersion.Major\"\n}",
                                               "$PSVersionTable.PSVersion.Major",
                                               "powershell \"exit $PSVersionTable.PSVersion.Major\"\nif %errorlevel% GEQ 5 (\n    echo Do some fancy stuff that only powershell v5 or higher supports\n) else (\n    echo Functionality not support by current powershell version.\n)",
                                               "powershell \"exit $PSVersionTable.PSVersion.Major\"\nif %errorlevel% GEQ 5 (\n    rem Unzip archive automatically\n    powershell Expand-Archive Compressed.zip\n) else (\n    rem Make the user unzip, because lazy\n    echo Please unzip Compressed.zip prior to continuing...\n    pause\n)",
                                               "version 7.1.0",
                                               "$PSVersionTable | Select-Object PSVersion",
                                               "PSVersion\n---------\n7.1.0",
                                               "version 5.1",
                                               "$PSVersionTable.PSVersion",
                                               "Major  Minor  Build  Revision\n-----  -----  -----  --------\n5      1      18362  1171",
                                               "pwsh --version",
                                               "PowerShell 7.2.5",
                                               "$psver = $PSVersionTable.PSVersion.Major",
                                               "$psver = (Get-Host).Version.Major",
                                               "$PSVer = (Get-Host).Version.Major\n$sortSwitch = \u0026quot;-Descending\u0026quot;\nif ($PSVer -gt 5) {$sortSwitch = \u0026quot;-r\u0026quot;}\n$pathSep = [IO.Path]::DirectorySeparatorChar\n\n$pattern = \u0026quot;???? something.zip\u0026quot;\n$files = (Get-ChildItem $pattern -Name | sort $sortSwitch)\nforeach ($file in $files) {\n    # Do stuff\n}",
                                               "reg query \u0026quot;HKLM\\SOFTWARE\\Microsoft\\PowerShell\\1\u0026quot; /v Install \u0026gt;nul 2\u0026gt;\u0026amp;1\nif %ERRORLEVEL% EQU 0 (\n  :: Default to PowerShell 5 if both are installed\n  set PSEXE=powershell\n) else (\n  set PSEXE=pwsh\n)\necho Using %PSEXE%\n%PSEXE% -ExecutionPolicy bypass -command \u0026quot;\u0026amp; { ... ; exit $LASTEXITCODE }\u0026quot;",
                                               "$PSVersionTable",
                                               "$version = if (Test-Path Variable:PSVersionTable)\n{\n    [version]$PSVersionTable.PSVersion\n}\nelse\n{\n    [version]\u0026quot;1.0\u0026quot;\n}",
                                               "Get-Host | select {$_.Version}",
                                               "@echo off\nfor /f \"tokens=2 delims=:\" %%a in (\u0027powershell -Command Get-Host ^| findstr /c:Version\u0027) do (echo %%a)",
                                               "5.1.18362.752"
                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:20:41Z",
                         "ViewCount":  3218401,
                         "PatternType":  "best_practice",
                         "Id":  "pattern_general_ad_46c32ee7",
                         "QuestionScore":  2908,
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T16:20:41Z",
                         "TotalScore":  7659,
                         "AnswerCount":  24,
                         "BestPractices":  [
                                               "Contains recommended practices and approaches"
                                           ],
                         "Title":  "Stack Overflow Q\u0026A: Determine installed PowerShell version",
                         "CodeTemplate":  "$PSVersionTable.PSVersion",
                         "CommonMistakes":  [
                                                "Contains warnings about common mistakes and errors"
                                            ],
                         "HasAcceptedAnswer":  true,
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "version"
                                  ],
                         "Abstract":  "# Question: Determine installed PowerShell version\n\n**Score:** 2908 | **Views:** 3218401 | **Tags:** powershell, version\n\n**URL:** https://stackoverflow.com/questions/1825585/determine-installed-powershell-version\n\n## Question Body\n\n\u003cp\u003eHow can I determine what version of PowerShell is installed on a computer, and indeed if it is installed at all?\u003c/p\u003e\n\n\n## Answers (24 total)\n\n### Answer 1 (Score: 3864) âœ… ACCEPTED ANSWER\n\n\u003cp\u003eUse \u003ccode\u003e$PSVersionTable.PSVersion\u003c/code\u003e to determine the engine vers...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow_qa",
                                             "CredibilityScore":  0.85,
                                             "PublishedAt":  "2025-07-28T16:20:41Z",
                                             "Url":  "https://stackoverflow.com/questions/1825585/determine-installed-powershell-version",
                                             "ScrapedAt":  "2025-07-28T16:20:41Z",
                                             "Id":  "29a721d3-78f3-4c41-9c9e-67c95ef928f0",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow Q\u0026A: Determine installed PowerShell version"
                                         }
                                     ],
                         "Content":  "# Question: Determine installed PowerShell version\n\n**Score:** 2908 | **Views:** 3218401 | **Tags:** powershell, version\n\n**URL:** https://stackoverflow.com/questions/1825585/determine-installed-powershell-version\n\n## Question Body\n\n\u003cp\u003eHow can I determine what version of PowerShell is installed on a computer, and indeed if it is installed at all?\u003c/p\u003e\n\n\n## Answers (24 total)\n\n### Answer 1 (Score: 3864) âœ… ACCEPTED ANSWER\n\n\u003cp\u003eUse \u003ccode\u003e$PSVersionTable.PSVersion\u003c/code\u003e to determine the engine version. If the variable does not exist, it is safe to assume the engine is version \u003ccode\u003e1.0\u003c/code\u003e.\u003c/p\u003e\n\n\u003cp\u003eNote that \u003ccode\u003e$Host.Version\u003c/code\u003e and \u003ccode\u003e(Get-Host).Version\u003c/code\u003e are not reliable - they reflect\nthe version of the host only, not the engine. PowerGUI,\nPowerShellPLUS, etc. are all hosting applications, and\nthey will set the host\u0027s version to reflect their product\nversion\u0026nbsp;\u0026mdash; which is entirely correct, but not what you\u0027re looking for.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ePS C:\\\u0026gt; $PSVersionTable.PSVersion\n\nMajor  Minor  Build  Revision\n-----  -----  -----  --------\n4      0      -1     -1\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 2 (Score: 459)\n\n\u003cp\u003eI would use either \u003cstrong\u003e\u003ca href=\"https://technet.microsoft.com/en-us/library/hh849946.aspx\" rel=\"noreferrer\"\u003eGet-Host\u003c/a\u003e\u003c/strong\u003e or \u003cstrong\u003e$PSVersionTable\u003c/strong\u003e.  As Andy Schneider points out, \u003ccode\u003e$PSVersionTable\u003c/code\u003e doesn\u0027t work in version 1; it was introduced in version 2.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eget-host\n\nName             : ConsoleHost\nVersion          : 2.0\nInstanceId       : d730016e-2875-4b57-9cd6-d32c8b71e18a\nUI               : System.Management.Automation.Internal.Host.InternalHostUserInterface\nCurrentCulture   : en-GB\nCurrentUICulture : en-US\nPrivateData      : Microsoft.PowerShell.ConsoleHost+ConsoleColorProxy\nIsRunspacePushed : False\nRunspace         : System.Management.Automation.Runspaces.LocalRunspace\n\n$PSVersionTable\n\nName                           Value\n----                           -----\nCLRVersion                     2.0.50727.4200\nBuildVersion                   6.0.6002.18111\nPSVersion                      2.0\nWSManStackVersion              2.0\nPSCompatibleVersions           {1.0, 2.0}\nSerializationVersion           *******\nPSRemotingProtocolVersion      2.1\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 3 (Score: 106)\n\n\u003cp\u003eYou can look at the built in variable, \u003ccode\u003e$psversiontable\u003c/code\u003e. If it doesn\u0027t exist, you have V1. If it does exist, it will give you all the info you need.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e1 \u0026gt;  $psversiontable\n\nName                           Value                                           \n----                           -----                                           \nCLRVersion                     2.0.50727.4927                                  \nBuildVersion                   6.1.7600.16385                                  \nPSVersion                      2.0                                             \nWSManStackVersion              2.0                                             \nPSCompatibleVersions           {1.0, 2.0}                                      \nSerializationVersion           *******                                         \nPSRemotingProtocolVersion      2.1    \n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 4 (Score: 100)\n\n\u003cp\u003eTo determine if PowerShell is installed, you can check the registry for the existence of \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKEY_LOCAL_MACHINE\\Software\\Microsoft\\PowerShell\\1\\Install\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eand\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\3\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eand, if it exists, whether the value is 1 (for installed), as detailed in the blog post \u003cem\u003e\u003ca href=\"http://myitforum.com/cs2/blogs/yli628/archive/2007/08/16/check-if-powershell-installed-and-version.aspx\" rel=\"noreferrer\"\u003eCheck if PowerShell installed and version\u003c/a\u003e\u003c/em\u003e.\u003c/p\u003e\n\n\u003cp\u003eTo determine the version of PowerShell that is installed, you can check the registry keys \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\PowerShellEngine\\PowerShellVersion\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eand\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\3\\PowerShellEngine\\PowerShellVersion\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eTo determine the version of PowerShell that is installed from a .ps1 script, you can use the following one-liner, as detailed on PowerShell.com in \u003cem\u003e\u003ca href=\"http://powershell.com/cs/blogs/tips/archive/2009/09/11/which-powershell-version-am-i-running.aspx\" rel=\"noreferrer\"\u003eWhich PowerShell Version Am I Running\u003c/a\u003e\u003c/em\u003e.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e$isV2 = test-path variable:\\psversiontable\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eThe same site also gives a function to return the version:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003efunction Get-PSVersion {\n    if (test-path variable:psversiontable) {$psversiontable.psversion} else {[version]\"*******\"}\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 5 (Score: 61)\n\n\u003cp\u003eYou can directly check the version with one line only by invoking PowerShell \u003cem\u003eexternally\u003c/em\u003e, such as from Command Prompt\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003epowershell -Command \"$PSVersionTable.PSVersion\"\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eAccording to \u003ca href=\"https://stackoverflow.com/users/9226723/psaul\"\u003e@psaul\u003c/a\u003e you \u003cstrong\u003ecan\u003c/strong\u003e actually have one command that is agnostic from where it came (CMD, PowerShell or Pwsh). Thank you for that.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003epowershell -command \"(Get-Variable PSVersionTable -ValueOnly).PSVersion\"\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eI\u0027ve tested and it worked flawlessly on both CMD and PowerShell.\u003c/p\u003e\n\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/WN2dY.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/WN2dY.png\" alt=\"Image\"\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 6 (Score: 49)\n\n\u003cp\u003eYou can verify that Windows PowerShell version installed by completing the following check:\u003c/p\u003e\n\n\u003col\u003e\n\u003cli\u003eClick Start, click All Programs, click Accessories, click Windows PowerShell, and then click Windows PowerShell.\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eIn the Windows PowerShell console, type the following command at the command prompt and then press ENTER:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-Host | Select-Object Version\n\u003c/code\u003e\u003c/pre\u003e\u003c/li\u003e\n\u003c/ol\u003e\n\n\u003cp\u003eYou will see output that looks like this:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eVersion\n-------\n3.0\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003e\u003ca href=\"http://www.myerrorsandmysolutions.com/how-to-verify-the-windows-powershell-version-installed/\" rel=\"noreferrer\"\u003ehttp://www.myerrorsandmysolutions.com/how-to-verify-the-windows-powershell-version-installed/\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 7 (Score: 24)\n\n\u003cp\u003e\u003ca href=\"https://blogs.msdn.com/b/powershell/archive/2009/06/25/detection-logic-poweshell-installation.aspx?Redirected=true\"\u003eMicrosoft\u0027s recommended forward compatible method for checking if PowerShell is installed and determining the installed version\u003c/a\u003e is to look at two specific registry keys. I\u0027ve reproduced the details here in case the link breaks.\u003c/p\u003e\n\n\u003cp\u003eAccording to the linked page:\u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003cp\u003eDepending on any other registry key(s), or version of PowerShell.exe or the location of PowerShell.exe is not guaranteed to work in the long term.\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\u003cp\u003eTo check if \u003cstrong\u003eany version\u003c/strong\u003e of PowerShell is installed, check for the following value in the registry:\u003c/p\u003e\n\n\u003cul\u003e\n\u003cli\u003eKey Location: \u003ccode\u003eHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\u003c/code\u003e\u003c/li\u003e\n\u003cli\u003eValue Name: Install\u003c/li\u003e\n\u003cli\u003eValue Type: REG_DWORD\u003c/li\u003e\n\u003cli\u003eValue Data: 0x00000001 (1\u003c/li\u003e\n\u003c/ul\u003e\n\n\u003cp\u003eTo check \u003cstrong\u003ewhether version 1.0 or 2.0\u003c/strong\u003e of PowerShell is installed, check for the following value in the registry:\u003c/p\u003e\n\n\u003cul\u003e\n\u003cli\u003eKey Location: \u003ccode\u003eHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\PowerShellEngine\u003c/code\u003e\u003c/li\u003e\n\u003cli\u003eValue Name: PowerShellVersion\u003c/li\u003e\n\u003cli\u003eValue Type: REG_SZ\u003c/li\u003e\n\u003cli\u003eValue Data: \u0026lt;1.0 | 2.0\u003e\u003c/li\u003e\n\u003c/ul\u003e\n\n\n### Answer 8 (Score: 17)\n\n\u003ch1\u003ePowerShell 7\u003c/h1\u003e\n\u003cp\u003eThe accepted answer is only appropriate if one version of PowerShell is installed on a computer. With the advent of PowerShell 7, this scenario becomes increasingly unlikely.\u003c/p\u003e\n\u003cp\u003eMicrosoft\u0027s \u003ca href=\"https://learn.microsoft.com/en-us/powershell/scripting/install/installing-powershell-core-on-windows#registry-keys-created-during-installation\" rel=\"noreferrer\"\u003edocumentation\u003c/a\u003e states that additional registry keys are created when PowerShell 7 is installed:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003eBeginning in PowerShell 7.1, the [installer] package creates registry keys\nthat store the installation location and version of PowerShell. These\nvalues are located in\n\u003ccode\u003eHKLM\\Software\\Microsoft\\PowerShellCore\\InstalledVersions\\\u0026lt;GUID\u0026gt;\u003c/code\u003e. The\nvalue of \u003ccode\u003e\u0026lt;GUID\u0026gt;\u003c/code\u003e is unique for each build type (release or preview),\nmajor version, and architecture.\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eExploring the registry in the aforementioned location reveals the following registry value: \u003ccode\u003eSemanticVersion\u003c/code\u003e. This value contains the information we seek.\u003c/p\u003e\n\u003cp\u003eOn my computer it appears like the following:\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003ePath                                                                                           Name              Type Data\n----                                                                                           ----              ---- ----\nHKLM:\\SOFTWARE\\Microsoft\\PowerShellCore\\InstalledVersions\\31ab5147-9a97-4452-8443-d9709f0516e1 SemanticVersion String 7.1.3\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/uRpmX.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/uRpmX.png\" alt=\"Image displaying the specified key in the Windows Registry Editor\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003cp\u003eAs you can see, the version of PowerShell 7 installed on my computer is 7.1.3. If PowerShell 7 is \u003cstrong\u003enot\u003c/strong\u003e installed on the target computer, the key in its entirety should not exist.\u003c/p\u003e\n\u003cp\u003eAs mentioned in the Microsoft documentation, the registry path will be slightly different dependent on installed PowerShell version.\u003c/p\u003e\n\u003cp\u003ePart of the key path changing could pose a challenge in some scenarios, but for those interested in a command line-based solution, PowerShell itself can handle this problem easily.\u003c/p\u003e\n\u003cp\u003eThe PowerShell cmdlet used to query the data in this registry value is the \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.management/get-itempropertyvalue\" rel=\"noreferrer\"\u003e\u003ccode\u003eGet-ItemPropertyValue\u003c/code\u003e cmdlet\u003c/a\u003e. Observe its use and output as follows (note the asterisk \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_wildcards\" rel=\"noreferrer\"\u003ewildcard\u003c/a\u003e character used in place of the part of the key path that is likely to change):\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003ePS\u0026gt; Get-ItemPropertyValue -Path \u0026quot;HKLM:\\SOFTWARE\\Microsoft\\PowerShellCore\\InstalledVersions\\*\u0026quot; -Name \u0026quot;SemanticVersion\u0026quot;\n\n7.1.3\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eJust a simple one-liner.\u003c/p\u003e\n\n\n### Answer 9 (Score: 11)\n\n\u003cp\u003eI found the easiest way to check if installed was to:\u003c/p\u003e\n\n\u003cul\u003e\n\u003cli\u003erun a command prompt (Start, Run, \u003ccode\u003ecmd\u003c/code\u003e, then OK)\u003c/li\u003e\n\u003cli\u003etype \u003ccode\u003epowershell\u003c/code\u003e then hit return.  You should then get the PowerShell \u003ccode\u003ePS\u003c/code\u003e prompt:\u003c/li\u003e\n\u003c/ul\u003e\n\n\u003cp\u003e\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eC:\\Users\\<USER>\u0026gt;powershell\n\nWindows PowerShell\nCopyright (C) 2009 Microsoft Corporation. All rights reserved.\n\nPS C:\\Users\\<USER>\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eYou can then check the version from the PowerShell prompt by typing \u003ccode\u003e$PSVersionTable.PSVersion\u003c/code\u003e:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ePS C:\\Users\\<USER>\u0026gt; $PSVersionTable.PSVersion\n\nMajor  Minor  Build  Revision\n-----  -----  -----  --------\n2      0      -1     -1\n\nPS C:\\Users\\<USER>\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eType \u003ccode\u003eexit\u003c/code\u003e if you want to go back to the command prompt (\u003ccode\u003eexit\u003c/code\u003e again if you want to also close the command prompt).\u003c/p\u003e\n\n\u003cp\u003eTo run scripts, see \u003ca href=\"http://ss64.com/ps/syntax-run.html\" rel=\"noreferrer\"\u003ehttp://ss64.com/ps/syntax-run.html\u003c/a\u003e.\u003c/p\u003e\n\n\n### Answer 10 (Score: 9)\n\n\u003cp\u003e\u003ccode\u003e$host.version\u003c/code\u003e is just plain wrong/unreliable. This gives you the version of the hosting executable (powershell.exe, powergui.exe, powershell_ise.exe, powershellplus.exe etc) and \u003cstrong\u003enot\u003c/strong\u003e the version of the engine itself.\u003c/p\u003e\n\n\u003cp\u003eThe engine version is contained in \u003ccode\u003e$psversiontable.psversion\u003c/code\u003e. For PowerShell 1.0, this variable does not exist, so obviously if this variable is not available it is entirely safe to assume the engine is 1.0, obviously.\u003c/p\u003e\n\n\n### Answer 11 (Score: 9)\n\n\u003cp\u003eThe easiest way to forget this page and never return to it is to learn the \u003ccode\u003eGet-Variable\u003c/code\u003e: \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-Variable | where {$_.Name -Like \u0027*version*\u0027} | %{$_[0].Value}\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eThere is no need to remember every variable. Just \u003ccode\u003eGet-Variable\u003c/code\u003e is enough (and \"There should be something about version\"). \u003c/p\u003e\n\n\n### Answer 12 (Score: 8)\n\n\u003cp\u003eUse:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e$psVersion = $PSVersionTable.PSVersion\nIf ($psVersion)\n{\n    #PowerShell Version Mapping\n    $psVersionMappings = @()\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.1.14393.0\u0027;FriendlyName=\u0027Windows PowerShell 5.1 Preview\u0027;ApplicableOS=\u0027Windows 10 Anniversary Update\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.1.14300.1000\u0027;FriendlyName=\u0027Windows PowerShell 5.1 Preview\u0027;ApplicableOS=\u0027Windows Server 2016 Technical Preview 5\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.494\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1607\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.122\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1603\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.117\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM 1602\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.63\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1602\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.51\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM 1512\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10514.6\u0027;FriendlyName=\u0027Windows PowerShell 5 Production Preview 1508\u0027;ApplicableOS=\u0027Windows Server 2012 R2\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10018.0\u0027;FriendlyName=\u0027Windows PowerShell 5 Preview 1502\u0027;ApplicableOS=\u0027Windows Server 2012 R2\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.9883.0\u0027;FriendlyName=\u0027Windows PowerShell 5 Preview November 2014\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows 8.1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00274.0\u0027;FriendlyName=\u0027Windows PowerShell 4 RTM\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00273.0\u0027;FriendlyName=\u0027Windows PowerShell 3 RTM\u0027;ApplicableOS=\u0027Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8, and Windows 7 SP1\u0027}\n    $psVersionMappings += New-Object PSObject -Property @{Name=\u00272.0\u0027;FriendlyName=\u0027Windows PowerShell 2 RTM\u0027;ApplicableOS=\u0027Windows Server 2008 R2 SP1 and Windows 7\u0027}\n    foreach ($psVersionMapping in $psVersionMappings)\n    {\n        If ($psVersion -ge $psVersionMapping.Name) {\n            @{CurrentVersion=$psVersion;FriendlyName=$psVersionMapping.FriendlyName;ApplicableOS=$psVersionMapping.ApplicableOS}\n            Break\n        }\n    }\n}\nElse{\n    @{CurrentVersion=\u00271.0\u0027;FriendlyName=\u0027Windows PowerShell 1 RTM\u0027;ApplicableOS=\u0027Windows Server 2008, Windows Server 2003, Windows Vista, Windows XP\u0027}\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eYou can download the detailed script from \u003cem\u003e\u003ca href=\"https://gallery.technet.microsoft.com/How-to-determine-installed-32de832c\" rel=\"noreferrer\"\u003eHow to determine installed PowerShell version\u003c/a\u003e\u003c/em\u003e.\u003c/p\u003e\n\n\n### Answer 13 (Score: 7)\n\n\u003cp\u003eTo check if PowerShell is installed use:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKLM\\Software\\Microsoft\\PowerShell\\1 Install ( = 1 )\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eTo check if RC2 or RTM is installed use:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eHKLM\\Software\\Microsoft\\PowerShell\\1 PID (=89393-100-0001260-00301) -- For RC2\nHKLM\\Software\\Microsoft\\PowerShell\\1 PID (=89393-100-0001260-04309) -- For RTM\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eSource: \u003ca href=\"http://www.myitforum.com/articles/40/view.asp?id=10618\" rel=\"nofollow noreferrer\"\u003ethis website\u003c/a\u003e.\u003c/p\u003e\n\n\n### Answer 14 (Score: 7)\n\n\u003cp\u003eSince the most helpful answer didn\u0027t address the \u003cem\u003eif exists\u003c/em\u003e portion, I thought I\u0027d give one take on it via a quick-and-dirty solution. It relies on PowerShell being in the path \u003ca href=\"http://en.wikipedia.org/wiki/Environment_variable\" rel=\"noreferrer\"\u003eenvironment variable\u003c/a\u003e which is likely what you want. (Hat tip to the top answer as I didn\u0027t know that.) Paste this into a \u003cem\u003etext\u003c/em\u003e file and name it\u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003cp\u003eTest Powershell Version.cmd\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\u003cp\u003eor similar.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e@echo off\necho Checking powershell version...\ndel \"%temp%\\PSVers.txt\" 2\u0026gt;nul\npowershell -command \"[string]$PSVersionTable.PSVersion.Major +\u0027.\u0027+ [string]$PSVersionTable.PSVersion.Minor | Out-File ([string](cat env:\\temp) + \u0027\\PSVers.txt\u0027)\" 2\u0026gt;nul\nif errorlevel 1 (\n echo Powershell is not installed. Please install it from download.Microsoft.com; thanks.\n) else (\n echo You have installed Powershell version:\n type \"%temp%\\PSVers.txt\"\n del \"%temp%\\PSVers.txt\" 2\u0026gt;nul\n)\ntimeout 15\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 15 (Score: 6)\n\n\u003cp\u003eI needed to check the version of PowerShell and then run the appropriate code. Some of our servers run v5, and others v4. This means that some functions, like compress, may or may not be available.\u003c/p\u003e\n\n\u003cp\u003eThis is my solution: \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eif ($PSVersionTable.PSVersion.Major -eq 5) {\n    #Execute code available in PowerShell 5, like Compress\n    Write-Host \"You are running PowerShell version 5\"\n}\nelse {\n    #Use a different process\n    Write-Host \"This is version $PSVersionTable.PSVersion.Major\"\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 16 (Score: 5)\n\n\u003cp\u003eThe below  cmdlet will return the PowerShell version.\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e$PSVersionTable.PSVersion.Major\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 17 (Score: 4)\n\n\u003cp\u003eThis is the top search result for \"Batch file get powershell version\", so I\u0027d like to provide a basic example of how to do conditional flow in a batch file depending on the powershell version\u003c/p\u003e\n\n\u003cp\u003e\u003cstrong\u003eGeneric example\u003c/strong\u003e\u003c/p\u003e\n\n\n\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell \"exit $PSVersionTable.PSVersion.Major\"\nif %errorlevel% GEQ 5 (\n    echo Do some fancy stuff that only powershell v5 or higher supports\n) else (\n    echo Functionality not support by current powershell version.\n)\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003e\u003cstrong\u003eReal world example\u003c/strong\u003e\u003c/p\u003e\n\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003epowershell \"exit $PSVersionTable.PSVersion.Major\"\nif %errorlevel% GEQ 5 (\n    rem Unzip archive automatically\n    powershell Expand-Archive Compressed.zip\n) else (\n    rem Make the user unzip, because lazy\n    echo Please unzip Compressed.zip prior to continuing...\n    pause\n)\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 18 (Score: 4)\n\n\u003cp\u003eI tried this on \u003ccode\u003eversion 7.1.0\u003c/code\u003e and it worked:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$PSVersionTable | Select-Object PSVersion\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cem\u003e\u003cstrong\u003eOutput\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePSVersion\n---------\n7.1.0\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eIt doesn\u0027t work on \u003ccode\u003eversion 5.1\u003c/code\u003e though, so rather go for this on versions below 7:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$PSVersionTable.PSVersion\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cem\u003e\u003cstrong\u003eOutput\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eMajor  Minor  Build  Revision\n-----  -----  -----  --------\n5      1      18362  1171\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cem\u003e\u003cstrong\u003eEDIT\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cp\u003eAs of PowerShell 7.2.5, you can now do:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003epwsh -v\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eOr\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003epwsh --version\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cem\u003e\u003cstrong\u003eOutput\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePowerShell 7.2.5\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 19 (Score: 3)\n\n\u003cp\u003eI used the following, which works on PS 7 and PS 5:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$psver = $PSVersionTable.PSVersion.Major\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eor:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$psver = (Get-Host).Version.Major\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThen I can use logic depending on which version is running.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$PSVer = (Get-Host).Version.Major\n$sortSwitch = \u0026quot;-Descending\u0026quot;\nif ($PSVer -gt 5) {$sortSwitch = \u0026quot;-r\u0026quot;}\n$pathSep = [IO.Path]::DirectorySeparatorChar\n\n$pattern = \u0026quot;???? something.zip\u0026quot;\n$files = (Get-ChildItem $pattern -Name | sort $sortSwitch)\nforeach ($file in $files) {\n    # Do stuff\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 20 (Score: 1)\n\n\u003cp\u003eVery old question but still relevant, it\u0027s just that the nature of the problem is different now in 2023.  Finding the version is easy, but first we have to launch the right executable.  For that, we\u0027re basically back to looking in the registry.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ereg query \u0026quot;HKLM\\SOFTWARE\\Microsoft\\PowerShell\\1\u0026quot; /v Install \u0026gt;nul 2\u0026gt;\u0026amp;1\nif %ERRORLEVEL% EQU 0 (\n  :: Default to PowerShell 5 if both are installed\n  set PSEXE=powershell\n) else (\n  set PSEXE=pwsh\n)\necho Using %PSEXE%\n%PSEXE% -ExecutionPolicy bypass -command \u0026quot;\u0026amp; { ... ; exit $LASTEXITCODE }\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThere are other hints you can get by inspecting environment variables, but I think testing the registry for \u0027Windows\u0027 PowerShell is the safest.\u003c/p\u003e\n\n\n### Answer 21 (Score: 1)\n\n\u003cp\u003ePowerShell versions 2-7 contain a variable named \u003ccode\u003e$PSVersionTable\u003c/code\u003e. It seems horrible, but you have to test for version 1:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e$version = if (Test-Path Variable:PSVersionTable)\n{\n    [version]$PSVersionTable.PSVersion\n}\nelse\n{\n    [version]\u0026quot;1.0\u0026quot;\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 22 (Score: 0)\n\n\u003cp\u003eYou can also call the \"host\" command from the PowerShell commandline. It should give you the value of the \u003ccode\u003e$host\u003c/code\u003e variable.\u003c/p\u003e\n\n\n### Answer 23 (Score: -2)\n\n\u003cp\u003eExtending the answer with a select operator:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-Host | select {$_.Version}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 24 (Score: -2)\n\n\u003cp\u003eI have made a small batch script that can determine PowerShell version:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e@echo off\nfor /f \"tokens=2 delims=:\" %%a in (\u0027powershell -Command Get-Host ^| findstr /c:Version\u0027) do (echo %%a)\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eThis simply extracts the version of PowerShell using \u003ccode\u003eGet-Host\u003c/code\u003e and searches the string \u003ccode\u003eVersion\u003c/code\u003e\u003c/p\u003e\n\n\u003cp\u003eWhen the line with the version is found, it uses the \u003ccode\u003efor\u003c/code\u003e command to extract the version. In this case we are saying that the delimiter is a colon and search next the first colon, resulting in my case \u003ccode\u003e5.1.18362.752\u003c/code\u003e.\u003c/p\u003e\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "\u0026lt;PORT\u0026gt;",
                                               "netstat -ano | findstr :\u0026lt;PORT\u0026gt;",
                                               "\u0026lt;PORT\u0026gt;",
                                               "taskkill /PID \u0026lt;PID\u0026gt; /F",
                                               "npx kill-port 8080",
                                               "npx kill-port 8080",
                                               "netstat -ano|findstr \u0026quot;PID :8080\u0026quot;",
                                               "taskkill /PID 18264 /f",
                                               "netstat -aon | findstr 3000",
                                               "taskkill /f /pid 1234",
                                               "sudo lsof -t -i:3000",
                                               "sudo kill -9 1234",
                                               "netstat -ano | findstr :yourPortNumber",
                                               "tskill typeyourPIDhere",
                                               "netstat -ano | findstr :8080",
                                               "taskkill /PID typeyourPIDhere /F",
                                               "netstat -ano | findstr :8080",
                                               "stop-process 82932",
                                               "kill $(lsof -t -i :PORT_NUMBER)",
                                               "for /f \"tokens=5\" %a in (\u0027netstat -aon ^| find \":8080\" ^| find \"LISTENING\"\u0027) do taskkill /f /pid %a",
                                               "for /f \"tokens=5\" %%a in (\u0027netstat -aon ^| find \":8080\" ^| find \"LISTENING\"\u0027) do taskkill /f /pid %%a",
                                               "npx kill-port 8080",
                                               "npx: installed 3 in 13.796s\nProcess on port 8080 killed",
                                               "netstat -ano|findstr \u0026quot;PID :8888\u0026quot;",
                                               "taskkill /pid 8912 /f",
                                               "SUCCESS: The process with PID 8860 has been terminated.",
                                               "Get-Process -Id (Get-NetTCPConnection -LocalPort \u0026quot;8080\u0026quot;).OwningProcess | Stop-Process",
                                               "1. netstat -ano | findstr :2181\n       TCP    0.0.0.0:2181           0.0.0.0:0              LISTENING       8876\n       TCP    [::]:2181              [::]:0                 LISTENING       8876\n\n     2.taskkill //PID 8876 //F\n       SUCCESS: The process with PID 8876 has been terminated.",
                                               "set /P port=\u0026quot;Enter port : \u0026quot;\necho showing process running with port %port%\n\nnetstat -ano|findstr \u0026quot;PID :%port%\u0026quot;\n\nset /P pid=\u0026quot;Enter PID to kill : \u0026quot;\n\ntaskkill /pid %pid% /f\n\nset /P exit=\u0026quot;Press any key to exit...\u0026quot;",
                                               "netstat -ano | findstr :\u0026lt;8080\u0026gt;",
                                               "taskkill /PID \u0026lt;Enter the copied PID Number\u0026gt; /F",
                                               "netstat -ano | findstr :PORT",
                                               "PS C:\\Users\\<USER>\u0026gt; netstat -ano | findstr :4445\n  TCP    0.0.0.0:4445           0.0.0.0:0              LISTENING       7368\n  TCP    [::]:4445              [::]:0                 LISTENING       7368\nPS C:\\Users\\<USER>\u0026gt; kill 7368\nPS C:\\Users\\<USER>\u0026gt; netstat -ano | findstr :4445\nPS C:\\Users\\<USER>\u0026gt;",
                                               "function killport([parameter(mandatory)] [string] $uport){\n    if($upid = (Get-NetTCPConnection -LocalPort $uport -ErrorAction Ignore).OwningProcess){kill $upid}\n}",
                                               "killport 8080",
                                               "kill $(Get-NetTCPConnection -LocalPort 8761 -ErrorAction Ignore).OwningProcess",
                                               "@ECHO OFF                                                                              \nFOR /F \"tokens=5\" %%T IN (\u0027netstat -a -n -o ^| findstr \"9797\" \u0027) DO (\nSET /A ProcessId=%%T) \u0026amp;GOTO SkipLine                                                   \n:SkipLine                                                                              \necho ProcessId to kill = %ProcessId%\ntaskkill /f /pid %ProcessId%\nPAUSE",
                                               "from psutil import process_iter\nfrom signal import SIGTERM # or SIGKILL\n\nfor proc in process_iter():\n    for conns in proc.connections(kind=\u0027inet\u0027):\n        if conns.laddr.port == 8080:\n            proc.send_signal(SIGTERM) # or SIGKILL\n            continue",
                                               "netstat -vanp tcp | grep 8888",
                                               "tcp4     0      0    127.0.0.1.8888   *.*    LISTEN      131072 131072  76061    0\ntcp46    0      0    *.8888           *.*    LISTEN      131072 131072  50523    0",
                                               "sudo kill -9 76061 50523",
                                               "netstat -ano | findstr :PORT\nkill PI",
                                               "tskill `netstat -ano | grep LISTENING | findstr :8080 | sed -r \u0027s/(\\s+[^\\s]+){4}(.*)/\\1/\u0027`",
                                               "function killport() {\n        tskill `netstat -ano | findstr LISTENING | findstr :$1 | sed -r \u0027s/^(\\s+[^\\s]+){4}(\\d*)$/\\1/\u0027`\n}",
                                               "killport 8080",
                                               "node killPort.js 8080",
                                               "killPort.js",
                                               "const { exec } = require(\u0027child_process\u0027);\nconst fs = require(`fs`);\n\nconst port = process.argv.length \u0026gt; 2 ? process.argv[2] : ``;\nif (!port || isNaN(port)) console.log(`port is required as an argument and has to be a number`);\nelse {\n    exec(`netstat -ano | findstr :${port}`, (err, stdout, stderr) =\u0026gt; {\n        if (!stdout) console.log(`nobody listens on port ${port}`);\n        else {\n            const res = stdout.split(`\\n`).map(s =\u0026gt; s.trim());\n            const pid = res.map(s =\u0026gt; s.split(` `).pop()).filter(s =\u0026gt; s).pop();\n            console.log(`Listener of ${port} is found, its pid is ${pid}, killing it...`);\n            exec(`taskkill /PID ${pid} /F`, (err, stdout, stderr) =\u0026gt; {\n                if (!stdout) console.log(`we tried to kill it, but not sure about the result, please run me again`);\n                else console.log(stdout);\n            })\n        }\n    });\n}",
                                               "PIDS=$(cmd.exe /c netstat -ano | cmd.exe /c findstr :$1 | awk \u0027{print $5}\u0027)\nfor pid in $PIDS\ndo\n    cmd.exe /c taskkill /PID $pid /F\ndone"
                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:20:42Z",
                         "ViewCount":  3942248,
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_general_ad_f4d7731d",
                         "QuestionScore":  1383,
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T16:20:42Z",
                         "TotalScore":  6011,
                         "AnswerCount":  28,
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow Q\u0026A: How can I close some specific port on Linux?",
                         "CodeTemplate":  "netstat -ano | findstr :\u0026lt;PORT\u0026gt;",
                         "CommonMistakes":  [
                                                "Contains warnings about common mistakes and errors"
                                            ],
                         "HasAcceptedAnswer":  true,
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "linux",
                                      "windows",
                                      "powershell",
                                      "ubuntu"
                                  ],
                         "Abstract":  "# Question: How can I close some specific port on Linux?\n\n**Score:** 1383 | **Views:** 3942248 | **Tags:** linux, windows, powershell, ubuntu\n\n**URL:** https://stackoverflow.com/questions/39632667/how-can-i-close-some-specific-port-on-linux\n\n## Question Body\n\n\u003cp\u003eHow can I remove the port that some application/process has used it but didn\u0027t close the port?\u003c/p\u003e\n\u003cp\u003eFor example: when I run ssh on my ubuntu and tunnel the ssh connection  on some port \u003ccode\u003e\u0026lt;PORT\u0026gt;\u003c/code\u003e on my system if my netwo...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow_qa",
                                             "CredibilityScore":  0.85,
                                             "PublishedAt":  "2025-07-28T16:20:42Z",
                                             "Url":  "https://stackoverflow.com/questions/39632667/how-can-i-close-some-specific-port-on-linux",
                                             "ScrapedAt":  "2025-07-28T16:20:42Z",
                                             "Id":  "0aafd7a5-2a77-483b-97f2-30e73e8acb65",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow Q\u0026A: How can I close some specific port on Linux?"
                                         }
                                     ],
                         "Content":  "# Question: How can I close some specific port on Linux?\n\n**Score:** 1383 | **Views:** 3942248 | **Tags:** linux, windows, powershell, ubuntu\n\n**URL:** https://stackoverflow.com/questions/39632667/how-can-i-close-some-specific-port-on-linux\n\n## Question Body\n\n\u003cp\u003eHow can I remove the port that some application/process has used it but didn\u0027t close the port?\u003c/p\u003e\n\u003cp\u003eFor example: when I run ssh on my ubuntu and tunnel the ssh connection  on some port \u003ccode\u003e\u0026lt;PORT\u0026gt;\u003c/code\u003e on my system if my network interrupts and disconnect the port is still open but the ssh connection is lost, How to close the \u003ccode\u003e\u0026lt;PORT\u0026gt;\u003c/code\u003e?\u003c/p\u003e\n\n\n## Answers (28 total)\n\n### Answer 1 (Score: 2862) âœ… ACCEPTED ANSWER\n\n\u003cp\u003e\u003cstrong\u003eStep 1:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003eOpen up cmd.exe (note: you \u003cem\u003emay\u003c/em\u003e need to run it as an administrator, but this isn\u0027t always necessary), then run the below command:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003e\u003ccode\u003enetstat -ano | findstr :\u0026lt;PORT\u0026gt;\u003c/code\u003e\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003e(Replace \u003ccode\u003e\u0026lt;PORT\u0026gt;\u003c/code\u003e with the port number you want, but keep the colon)\u003c/p\u003e\n\u003cp\u003e\u003cimg src=\"https://i.sstatic.net/lEpCZ.png\" alt=\"\" /\u003e\u003c/p\u003e\n\u003cp\u003eThe area circled in red shows the PID (process identifier). Locate the PID of the process that\u0027s using the port you want.\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003eStep 2:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003eNext, run the following command:\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003e\u003ccode\u003etaskkill /PID \u0026lt;PID\u0026gt; /F\u003c/code\u003e\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003e(No colon this time)\u003c/p\u003e\n\u003cp\u003e\u003cimg src=\"https://i.sstatic.net/8k64x.png\" alt=\"\" /\u003e\u003c/p\u003e\n\u003cp\u003eLastly, you can check whether the operation succeeded or not by re-running the command in \u0026quot;Step 1\u0026quot;. If it was successful you shouldn\u0027t see any more search results for that port number.\u003c/p\u003e\n\n\n### Answer 2 (Score: 607)\n\n\u003cp\u003eI know that is really old question, but found pretty easy to remember, fast command to kill apps that are using port.\u003c/p\u003e\n\u003cp\u003eRequirements: npm@5.2.0^ version\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enpx kill-port 8080\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou can also read more about kill-port here: \u003ca href=\"https://www.npmjs.com/package/kill-port\" rel=\"noreferrer\"\u003ehttps://www.npmjs.com/package/kill-port\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 3 (Score: 229)\n\n\u003cp\u003eThere are two ways to kill the processes\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003eOption 01 - Simplest and easiest\u003c/strong\u003e\n\u003cbr\u003e\u003cbr\u003e\n\u003cem\u003eRequirement : npm@5.2.0^ version\u003c/em\u003e\n\u003cbr\u003e\n\u003cbr\u003e\nOpen the Command prompt as Administrator and give the following command with the port (Here the port is 8080)\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enpx kill-port 8080\n\u003c/code\u003e\u003c/pre\u003e\n\u003chr /\u003e\n\u003cp\u003e\u003cstrong\u003eOption 02 - Most commonly used\u003c/strong\u003e\n\u003cbr\u003e\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eStep 01\u003cbr\u003e\u003cbr\u003e\nOpen Windows command prompt as Administrator\u003c/li\u003e\n\u003cli\u003eStep 02\u003cbr\u003e\u003cbr\u003e\nFind the PID of the port you want to kill with the below command: Here port is 8080\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -ano|findstr \u0026quot;PID :8080\u0026quot;\n\n\u003c/code\u003e\u003c/pre\u003e\n\u003cblockquote\u003e\n\u003cp\u003eTCP 0.0.0.0:8080 0.0.0.0:0 LISTENING 18264\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cul\u003e\n\u003cli\u003eStep 03\u003cbr\u003e\u003cbr\u003e\nKill the PID you received above with the below command (In my case PID is 18264)\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003etaskkill /PID 18264 /f\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 4 (Score: 219)\n\n\u003cp\u003e\u003cstrong\u003eWith Windows 10/11 default tools:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003e✔ Step one:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003e\u003cem\u003eOpen Windows PowerShell \u003cstrong\u003eas Administrator\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003e✔ Step two:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003e\u003cem\u003eFind the ProcessID for the port you need to kill (e.g. 3000)\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -aon | findstr 3000\n\u003c/code\u003e\u003c/pre\u003e\n\u003cblockquote\u003e\n\u003cp\u003eTCP    0.0.0.0:3000  \u003cstrong\u003eLISTEN   1234\u003c/strong\u003e\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003e\u003cstrong\u003e✔ Step three:\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003e\u003cem\u003eKill the zombie process:\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etaskkill /f /pid 1234\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003ewhere \u0026quot;\u003cstrong\u003e1234\u003c/strong\u003e\u0026quot; is your ProcessID (aka PID)\u003c/p\u003e\n\u003cp\u003e*\u003cem\u003e\u003cstrong\u003eExtra tip if you use Windows Subsystem for Linux (Ubuntu WSL):\u003c/strong\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003e✔ Step one:\u003c/strong\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003esudo lsof -t -i:3000\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cstrong\u003e✔ Step two:\u003c/strong\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003esudo kill -9 1234\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 5 (Score: 191)\n\n\u003cp\u003e\u003cem\u003eStep 1 (same is in accepted \u003ca href=\"https://stackoverflow.com/a/39633428/5292302\"\u003eanswer\u003c/a\u003e written by \u003ca href=\"https://stackoverflow.com/users/3626371/kavinduwije\"\u003eKavinduWije\u003c/a\u003e):\u003c/em\u003e\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -ano | findstr :yourPortNumber\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/blockquote\u003e\n\u003cp\u003e\u003cem\u003eChange in Step 2 to:\u003c/em\u003e\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etskill typeyourPIDhere \n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003e\u003cem\u003eNote\u003c/em\u003e: \u003ccode\u003etaskkill\u003c/code\u003e is not working in some git bash terminal\u003c/p\u003e\n\n\n### Answer 6 (Score: 116)\n\n\u003cp\u003e\u003cstrong\u003eIf you are using GitBash\u003c/strong\u003e\u003c/p\u003e\n\n\u003cp\u003e\u003cem\u003eStep one:\u003c/em\u003e\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003enetstat -ano | findstr :8080\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003e\u003cem\u003eStep two:\u003c/em\u003e \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003etaskkill /PID typeyourPIDhere /F \n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003e(\u003ccode\u003e/F\u003c/code\u003e forcefully terminates the process)\u003c/p\u003e\n\n\n### Answer 7 (Score: 91)\n\n\u003cp\u003eThe simplest solution — the only one I can ever remember:\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003eIn Windows Powershell\u003c/strong\u003e\u003c/p\u003e\n\u003cp\u003eSay we want to stop a process on port \u003cstrong\u003e8080\u003c/strong\u003e\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003eGet the process:\u003c/li\u003e\n\u003c/ol\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -ano | findstr :8080\n\u003c/code\u003e\u003c/pre\u003e\n\u003col start=\"2\"\u003e\n\u003cli\u003eStop the process\u003c/li\u003e\n\u003c/ol\u003e\n\u003cpre\u003e\u003ccode\u003estop-process 82932\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 8 (Score: 63)\n\n\u003cp\u003eIf you already know the port number, it will probably suffice to send a software termination signal to the process (SIGTERM):\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ekill $(lsof -t -i :PORT_NUMBER)\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 9 (Score: 46)\n\n\u003cp\u003eFor use in command line:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003efor /f \"tokens=5\" %a in (\u0027netstat -aon ^| find \":8080\" ^| find \"LISTENING\"\u0027) do taskkill /f /pid %a\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eFor use in bat-file:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003efor /f \"tokens=5\" %%a in (\u0027netstat -aon ^| find \":8080\" ^| find \"LISTENING\"\u0027) do taskkill /f /pid %%a\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 10 (Score: 43)\n\n\u003cp\u003eSimple CMD is working me. Easy to remember\u003c/p\u003e\n\u003cp\u003efind the port number which you want kill and run the below cmd\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enpx kill-port 8080\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eAfter complete the Port get stopped and getting this message\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enpx: installed 3 in 13.796s\nProcess on port 8080 killed\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 11 (Score: 34)\n\n\u003cp\u003eIn \u003cstrong\u003eWindows PowerShell\u003c/strong\u003e version 1 or later to stop a process on port 3000 type:\u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003cp\u003eStop-Process (,(netstat -ano | findstr :3000).split() | foreach {$\u003cem\u003e[$\u003c/em\u003e.length-1]}) -Force\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\u003chr\u003e\n\n\u003cp\u003eAs suggested by @morganpdx here`s a more PowerShell-ish, better version:\u003c/p\u003e\n\n\u003cblockquote\u003e\n  \u003cp\u003eStop-Process -Id (Get-NetTCPConnection -LocalPort 3000).OwningProcess -Force\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\n### Answer 12 (Score: 23)\n\n\u003cp\u003eOpen command prompt and issue below command\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -ano|findstr \u0026quot;PID :8888\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eOutput will show the process id occupying the port\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/pLq08.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/pLq08.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003cp\u003eIssue below command to kill the PID\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etaskkill /pid 8912 /f\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eYou will receive the output as below\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eSUCCESS: The process with PID 8860 has been terminated.\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 13 (Score: 20)\n\n\u003cp\u003eIf you can use PowerShell on Windows you just need :\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eGet-Process -Id (Get-NetTCPConnection -LocalPort \u0026quot;8080\u0026quot;).OwningProcess | Stop-Process\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 14 (Score: 17)\n\n\u003cp\u003eFor Windows users, you can use the \u003ca href=\"https://www.nirsoft.net/utils/cports.html#DownloadLinks\" rel=\"noreferrer\"\u003eCurrPorts\u003c/a\u003e tool to kill ports under usage easily:\u003c/p\u003e\n\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/wSqrm.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/wSqrm.png\" alt=\"Enter image description here\"\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 15 (Score: 11)\n\n\u003cp\u003eI was running zookeeper on Windows and wasn\u0027t able to stop \u003ca href=\"https://en.wikipedia.org/wiki/Apache_ZooKeeper\" rel=\"nofollow noreferrer\"\u003eZooKeeper\u003c/a\u003e running at 2181 port using zookeeper-stop.sh, so tried this double slash \"//\" method to taskkill. It worked\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e     1. netstat -ano | findstr :2181\n       TCP    0.0.0.0:2181           0.0.0.0:0              LISTENING       8876\n       TCP    [::]:2181              [::]:0                 LISTENING       8876\n\n     2.taskkill //PID 8876 //F\n       SUCCESS: The process with PID 8876 has been terminated.\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 16 (Score: 11)\n\n\u003ch2\u003eLet\u0027s Automate!\u003c/h2\u003e\n\u003cp\u003eIf you fall into this issue much often like me, make an .bat file and run it to end process.\u003c/p\u003e\n\u003ch2\u003ecreate a bat file \u0026quot;killport.bat\u0026quot;\u003c/h2\u003e\n\u003cpre\u003e\u003ccode\u003eset /P port=\u0026quot;Enter port : \u0026quot;\necho showing process running with port %port%\n\nnetstat -ano|findstr \u0026quot;PID :%port%\u0026quot;\n\nset /P pid=\u0026quot;Enter PID to kill : \u0026quot;\n\ntaskkill /pid %pid% /f\n\nset /P exit=\u0026quot;Press any key to exit...\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eRun this file by double clicking and\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003eEnter port number (then it will list process with PID)\u003c/li\u003e\n\u003cli\u003eEnter PID to kill\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eDone\u003c/p\u003e\n\u003ch1\u003e(Optional)\u003c/h1\u003e\n\u003cp\u003eSet to path environment so that you can access this file from anywhere.\u003c/p\u003e\n\u003cp\u003eMost probably you will know how to add an new path to env. But here\u0027s how if you don\u0027t\u003c/p\u003e\n\u003ch2\u003eStep 1 of 4\u003c/h2\u003e\n\u003cp\u003eSearch ENV on start menu\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/Bry76.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/Bry76.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003ch2\u003eStep 2 of 4\u003c/h2\u003e\n\u003cp\u003eSelect Environment Variables\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/AxVDy.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/AxVDy.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003ch2\u003eStep 3 of 4\u003c/h2\u003e\n\u003cp\u003eSelect \u0027path\u0027 and click Edit button\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/3c0sS.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/3c0sS.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003ch2\u003eStep 4 of 4\u003c/h2\u003e\n\u003cp\u003eClick \u0027New\u0027 add the path where .bat file is stored. Since I saved it on \u0027../Documents/bats\u0027 folder I am adding this path. Your path will depend on where you save this file.\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/wSdzm.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/wSdzm.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003ch3\u003eOpen CMD and test. Remember you filename will the word to run this file. Since I saved the .bat file as \u0027killport.bat\u0027 =\u0026gt; \u0027killport\u0027 is the word to run it.\u003c/h3\u003e\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/zaLxh.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/zaLxh.png\" alt=\"enter image description here\" /\u003e\u003c/a\u003e\nif do enjoy!\nelse share how you done it here\u003c/p\u003e\n\n\n### Answer 17 (Score: 11)\n\n\u003cp\u003eRun cmd as administrator. Then type this code in there.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -ano | findstr :\u0026lt;8080\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThen you can see the PID run on your port. Then copy that PID number. ( PID is a unique number that helps identify a hardware product or a registered software product.)  And type below code line and press enter.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etaskkill /PID \u0026lt;Enter the copied PID Number\u0026gt; /F\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 18 (Score: 9)\n\n\u003cp\u003eIf you\u0027re using \u003ca href=\"https://devblogs.microsoft.com/commandline/windows-terminal-1-0/\" rel=\"nofollow noreferrer\"\u003eWindows Terminal\u003c/a\u003e then the killing process might be little less tedious.\nI\u0027ve been using windows terminal and \u003ccode\u003ekill PID\u003c/code\u003e works fine for me to kill processes on the port as the new Windows Terminal supports certain bash commands. For example: \u003ccode\u003ekill 13300\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eSo, the complete process will look like this-\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eOpen Windows Terminal\u003c/li\u003e\n\u003cli\u003eType the following command to show processes running on the port you\u0027re looking to kill processes.\n\u003ccode\u003enetstat -ano | findstr :PORT\u003c/code\u003e\u003c/li\u003e\n\u003cli\u003eType following to kill the process.\n\u003ccode\u003ekill PID\u003c/code\u003e\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eFor Example:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePS C:\\Users\\<USER>\u0026gt; netstat -ano | findstr :4445\n  TCP    0.0.0.0:4445           0.0.0.0:0              LISTENING       7368\n  TCP    [::]:4445              [::]:0                 LISTENING       7368\nPS C:\\Users\\<USER>\u0026gt; kill 7368\nPS C:\\Users\\<USER>\u0026gt; netstat -ano | findstr :4445\nPS C:\\Users\\<USER>\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eSee when I typed the first command to list processes on the port it returned empty. That means all processes are killed now.\u003c/p\u003e\n\u003cp\u003eUpdate: \u003ccode\u003ekill\u003c/code\u003e is an alias for \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.management/stop-process?view=powershell-7.1\" rel=\"nofollow noreferrer\"\u003eStop-Process\u003c/a\u003e. Thanks, @FSCKur for letting us know.\u003c/p\u003e\n\n\n### Answer 19 (Score: 8)\n\n\u003cp\u003eIf you use powershell 7+ this worked for me. Just add this function in your $PROFILE file.\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003efunction killport([parameter(mandatory)] [string] $uport){\n    if($upid = (Get-NetTCPConnection -LocalPort $uport -ErrorAction Ignore).OwningProcess){kill $upid}\n}\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003ethen simply use \u003ccode\u003ekillport 8080\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eor if you prefer just the command you can try this:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ekill $(Get-NetTCPConnection -LocalPort 8761 -ErrorAction Ignore).OwningProcess\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 20 (Score: 6)\n\n\u003cp\u003eYou can do by run a bat file:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e@ECHO OFF                                                                              \nFOR /F \"tokens=5\" %%T IN (\u0027netstat -a -n -o ^| findstr \"9797\" \u0027) DO (\nSET /A ProcessId=%%T) \u0026amp;GOTO SkipLine                                                   \n:SkipLine                                                                              \necho ProcessId to kill = %ProcessId%\ntaskkill /f /pid %ProcessId%\nPAUSE\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 21 (Score: 4)\n\n\u003cp\u003eIn case you want to do it using Python: check \u003cem\u003e\u003ca href=\"https://stackoverflow.com/questions/20691258/is-possible-in-python-kill-process-which-is-running-on-specific-port-for-exampl\"\u003eIs it possible in python to kill process that is listening on specific port, for example 8080?\u003c/a\u003e\u003c/em\u003e\u003c/p\u003e\n\n\u003cp\u003eThe answer from \u003ca href=\"https://stackoverflow.com/users/905256/smunk\"\u003eSmunk\u003c/a\u003e works nicely. I repeat his code here:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003efrom psutil import process_iter\nfrom signal import SIGTERM # or SIGKILL\n\nfor proc in process_iter():\n    for conns in proc.connections(kind=\u0027inet\u0027):\n        if conns.laddr.port == 8080:\n            proc.send_signal(SIGTERM) # or SIGKILL\n            continue\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 22 (Score: 3)\n\n\u003cp\u003ethe first step\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003enetstat -vanp tcp | grep 8888\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eexample\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etcp4     0      0    127.0.0.1.8888   *.*    LISTEN      131072 131072  76061    0\ntcp46    0      0    *.8888           *.*    LISTEN      131072 131072  50523    0\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003ethe second step: find your PIDs and kill them\u003c/p\u003e\n\u003cp\u003ein my case\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003esudo kill -9 76061 50523\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 23 (Score: 3)\n\n\u003cpre\u003e\u003ccode\u003enetstat -ano | findstr :PORT\nkill PI\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 24 (Score: 2)\n\n\u003cp\u003eOne line solution using \u003cstrong\u003eGitBash\u003c/strong\u003e:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e tskill `netstat -ano | grep LISTENING | findstr :8080 | sed -r \u0027s/(\\s+[^\\s]+){4}(.*)/\\1/\u0027`\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eReplace \u003cstrong\u003e8080\u003c/strong\u003e with the port your server is listening to.\u003c/p\u003e\n\n\u003cp\u003eIf you need to use it often, try adding to your \u003ccode\u003e~/.bashrc\u003c/code\u003e the function:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003efunction killport() {\n        tskill `netstat -ano | findstr LISTENING | findstr :$1 | sed -r \u0027s/^(\\s+[^\\s]+){4}(\\d*)$/\\1/\u0027`\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eand simply run \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ekillport 8080\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 25 (Score: 1)\n\n\u003cp\u003eI wrote a tiny node js script for this. Just run it like this:\n\u003ccode\u003enode killPort.js 8080\u003c/code\u003e or whatever port you need to kill. Save the following to a \u003ccode\u003ekillPort.js\u003c/code\u003e file:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003econst { exec } = require(\u0027child_process\u0027);\nconst fs = require(`fs`);\n\nconst port = process.argv.length \u0026gt; 2 ? process.argv[2] : ``;\nif (!port || isNaN(port)) console.log(`port is required as an argument and has to be a number`);\nelse {\n    exec(`netstat -ano | findstr :${port}`, (err, stdout, stderr) =\u0026gt; {\n        if (!stdout) console.log(`nobody listens on port ${port}`);\n        else {\n            const res = stdout.split(`\\n`).map(s =\u0026gt; s.trim());\n            const pid = res.map(s =\u0026gt; s.split(` `).pop()).filter(s =\u0026gt; s).pop();\n            console.log(`Listener of ${port} is found, its pid is ${pid}, killing it...`);\n            exec(`taskkill /PID ${pid} /F`, (err, stdout, stderr) =\u0026gt; {\n                if (!stdout) console.log(`we tried to kill it, but not sure about the result, please run me again`);\n                else console.log(stdout);\n            })\n        }\n    });\n}\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 26 (Score: 1)\n\n\u003cp\u003eI am using GitBash and I error like below when ran\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003etaskkill //PID XXXX\u003c/p\u003e\n\u003cp\u003eERROR: The process with PID 7420 could not be terminated. Reason: This\nprocess can only be terminated forcefully (with /F option).\u003c/p\u003e\n\u003c/blockquote\u003e\n\u003cp\u003eSo used //F like below and worked fine\u003c/p\u003e\n\u003cblockquote\u003e\n\u003cp\u003etaskkill //F //PID  XXXX\u003c/p\u003e\n\u003c/blockquote\u003e\n\n\n### Answer 27 (Score: 0)\n\n\u003cp\u003eHere is a script to do it in WSL2\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePIDS=$(cmd.exe /c netstat -ano | cmd.exe /c findstr :$1 | awk \u0027{print $5}\u0027)\nfor pid in $PIDS\ndo\n    cmd.exe /c taskkill /PID $pid /F\ndone\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 28 (Score: -3)\n\n\u003cp\u003eWe can avoid this by simple restarting IIS, using the below command:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eIISRESET\n\u003c/code\u003e\u003c/pre\u003e\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "# This is a comment in PowerShell",
                                               "\u0026lt;# #\u0026gt;",
                                               "#REQUIRES -Version 2.0\n\n\u0026lt;#\n.SYNOPSIS\n    A brief description of the function or script. This keyword can be used\n    only once in each topic.\n.DESCRIPTION\n    A detailed description of the function or script. This keyword can be\n    used only once in each topic.\n.NOTES\n    File Name      : xxxx.ps1\n    Author         : J.P. Blanc (<EMAIL>)\n    Prerequisite   : PowerShell V2 over Vista and upper.\n    Copyright 2011 - Jean Paul Blanc/Silogix\n.LINK\n    Script posted over:\n    http://silogix.fr\n.EXAMPLE\n    Example 1\n.EXAMPLE\n    Example 2\n#\u0026gt;\nFunction blabla\n{}",
                                               "# This is a comment in PowerShell",
                                               "# Comment Here",
                                               "\u0026lt;# \n  Multi \n  Line \n#\u0026gt;",
                                               "Get-Content -Path \u0026lt;# configuration file #\u0026gt; C:\\config.ini",
                                               "Space + TAB",
                                               "# Single line comment in PowerShell\n\n\u0026lt;#\n--------------------------------------\nMulti-line comment in PowerShell V2+\n--------------------------------------\n#\u0026gt;",
                                               "# Get all Windows Service processes \u0026lt;-- one line comment, it starts with \u0027#\u0027\nGet-Process -Name *host*\n\nGet-Process -Name *host* ## You could put as many ### as you want, it does not matter\n\nGet-Process -Name *host* # | Stop-Service # Everything from the first # until end of the line is treated as comment\n\nStop-Service -DisplayName Windows*Update # -WhatIf # You can use it to comment out cmdlet switches",
                                               "\u0026lt;#\nEveryting between \u0027\u0026lt; #\u0027 and \u0027# \u0026gt;\u0027 is\ntreated as a comment. A typical use case is for help, see below.\n\n# You could also have a single line comment inside the multi line comment block.\n# Or two... :)\n\n#\u0026gt;\n\n\u0026lt;#\n.SYNOPSIS\n    A brief description of the function or script.\n    This keyword can be used only once in each topic.\n\n.DESCRIPTION\n    A detailed description of the function or script.\n    This keyword can be used only once in each topic.\n\n.NOTES\n    Some additional notes. This keyword can be used only once in each topic.\n    This keyword can be used only once in each topic.\n\n.LINK\n    A link used when Get-Help with a switch -OnLine is used.\n    This keyword can be used only once in each topic.\n\n.EXAMPLE\n    Example 1\n    You can use this keyword as many as you want.\n\n.EXAMPLE\n    Example 2\n    You can use this keyword as many as you want.\n#\u0026gt;",
                                               "\u0026lt;#\nNope, these are not allowed in PowerShell.\n\n\u0026lt;# This will break your first multiline comment block... #\u0026gt;\n...and this will throw a syntax error.\n#\u0026gt;",
                                               "\u0026lt;#\nThe multi line comment opening/close\ncan be also used to comment some nested code\nor as an explanation for multi chained operations..\n#\u0026gt;\nGet-Service | \u0026lt;# Step explanation #\u0026gt;\nWhere-Object { $_.Status -eq [ServiceProcess.ServiceControllerStatus]::Stopped } |\n\u0026lt;# Format-Table -Property DisplayName, Status -AutoSize |#\u0026gt;\nOut-File -FilePath Services.txt -Encoding Unicode",
                                               "# Some well written script\nexit\nWriting something after exit is possible but not recommended.\nIt isn\u0027t a comment.\nEspecially in Visual Studio Code, these words baffle PSScriptAnalyzer.\nYou could actively break your session in VS Code.",
                                               "# Comment here",
                                               "#requires -runasadmin",
                                               "(Some basic code) # Use \"#\" after a line and use:\n\n \u0026lt;#\n    for more lines\n    ...\n    ...\n    ...\n    ..\n    .\n #\u0026gt;",
                                               "....\nexit \n\nHi\nHello\nWe are comments\nAnd not executed"
                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:20:43Z",
                         "ViewCount":  1052890,
                         "PatternType":  "best_practice",
                         "Id":  "pattern_general_ad_dae85193",
                         "QuestionScore":  1115,
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T16:20:43Z",
                         "TotalScore":  2883,
                         "AnswerCount":  10,
                         "BestPractices":  [
                                               "Contains recommended practices and approaches"
                                           ],
                         "Title":  "Stack Overflow Q\u0026A: How do you comment out code in PowerShell?",
                         "CodeTemplate":  "# This is a comment in PowerShell",
                         "CommonMistakes":  [
                                                "Contains warnings about common mistakes and errors"
                                            ],
                         "HasAcceptedAnswer":  true,
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "syntax",
                                      "powershell-2.0",
                                      "comments"
                                  ],
                         "Abstract":  "# Question: How do you comment out code in PowerShell?\n\n**Score:** 1115 | **Views:** 1052890 | **Tags:** powershell, syntax, powershell-2.0, comments\n\n**URL:** https://stackoverflow.com/questions/7342597/how-do-you-comment-out-code-in-powershell\n\n## Question Body\n\n\u003cp\u003eHow do you comment out code in \u003cstrong\u003ePowerShell\u003c/strong\u003e (1.0 or 2.0)?\u003c/p\u003e\n\n\n## Answers (10 total)\n\n### Answer 1 (Score: 1469) âœ… ACCEPTED ANSWER\n\n\u003cp\u003eIn PowerShell V1 there\u0027s only \u003ccode\u003e#\u003c/code\u003e to make the text after it a commen...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow_qa",
                                             "CredibilityScore":  0.85,
                                             "PublishedAt":  "2025-07-28T16:20:43Z",
                                             "Url":  "https://stackoverflow.com/questions/7342597/how-do-you-comment-out-code-in-powershell",
                                             "ScrapedAt":  "2025-07-28T16:20:43Z",
                                             "Id":  "5ac4c3af-2c2b-45a4-b08d-584c73295db3",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow Q\u0026A: How do you comment out code in PowerShell?"
                                         }
                                     ],
                         "Content":  "# Question: How do you comment out code in PowerShell?\n\n**Score:** 1115 | **Views:** 1052890 | **Tags:** powershell, syntax, powershell-2.0, comments\n\n**URL:** https://stackoverflow.com/questions/7342597/how-do-you-comment-out-code-in-powershell\n\n## Question Body\n\n\u003cp\u003eHow do you comment out code in \u003cstrong\u003ePowerShell\u003c/strong\u003e (1.0 or 2.0)?\u003c/p\u003e\n\n\n## Answers (10 total)\n\n### Answer 1 (Score: 1469) âœ… ACCEPTED ANSWER\n\n\u003cp\u003eIn PowerShell V1 there\u0027s only \u003ccode\u003e#\u003c/code\u003e to make the text after it a comment.\u003c/p\u003e\n\u003cpre class=\"lang-powershell prettyprint-override\"\u003e\u003ccode\u003e# This is a comment in PowerShell\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eIn PowerShell V2 \u003ccode\u003e\u0026lt;# #\u0026gt;\u003c/code\u003e can be used for block comments and more specifically for help comments.\u003c/p\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e#REQUIRES -Version 2.0\n\n\u0026lt;#\n.SYNOPSIS\n    A brief description of the function or script. This keyword can be used\n    only once in each topic.\n.DESCRIPTION\n    A detailed description of the function or script. This keyword can be\n    used only once in each topic.\n.NOTES\n    File Name      : xxxx.ps1\n    Author         : J.P. Blanc (<EMAIL>)\n    Prerequisite   : PowerShell V2 over Vista and upper.\n    Copyright 2011 - Jean Paul Blanc/Silogix\n.LINK\n    Script posted over:\n    http://silogix.fr\n.EXAMPLE\n    Example 1\n.EXAMPLE\n    Example 2\n#\u0026gt;\nFunction blabla\n{}\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eFor more explanation about \u003ccode\u003e.SYNOPSIS\u003c/code\u003e and \u003ccode\u003e.*\u003c/code\u003e see \u003ca href=\"https://learn.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_comment_based_help?view=powershell-7.1\" rel=\"noreferrer\"\u003eabout_Comment_Based_Help\u003c/a\u003e.\u003c/p\u003e\n\u003cp\u003eRemark: These function comments are used by the \u003ccode\u003eGet-Help\u003c/code\u003e CmdLet and can be put before the keyword \u003ccode\u003eFunction\u003c/code\u003e, or inside the \u003ccode\u003e{}\u003c/code\u003e before or after the code itself.\u003c/p\u003e\n\n\n### Answer 2 (Score: 119)\n\n\u003cp\u003eYou use the hash mark like this:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e# This is a comment in PowerShell\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eWikipedia has a good page for keeping track of how to do comments in several popular languages:\u003c/p\u003e\n\u003cp\u003e\u003cem\u003e\u003ca href=\"http://en.wikipedia.org/wiki/Comparison_of_programming_languages_(syntax)#Comments\" rel=\"noreferrer\"\u003eComments\u003c/a\u003e\u003c/em\u003e\u003c/p\u003e\n\n\n### Answer 3 (Score: 65)\n\n\u003cp\u003eSingle line comments start with a \u003ca href=\"https://en.wikipedia.org/wiki/Number_sign\"\u003ehash symbol\u003c/a\u003e, everything to the right of the \u003ccode\u003e#\u003c/code\u003e will be ignored:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e# Comment Here\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eIn PowerShell 2.0 and above multi-line block comments can be used:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e\u0026lt;# \n  Multi \n  Line \n#\u0026gt; \n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eYou could use block comments to embed comment text within a command:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003eGet-Content -Path \u0026lt;# configuration file #\u0026gt; C:\\config.ini\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003e\u003cstrong\u003eNote:\u003c/strong\u003e Because PowerShell supports \u003ca href=\"https://en.wikipedia.org/wiki/Command-line_completion\"\u003eTab Completion\u003c/a\u003e you need to be careful about copying and pasting \u003ccode\u003eSpace + TAB\u003c/code\u003e before comments.\u003c/p\u003e\n\n\n### Answer 4 (Score: 42)\n\n\u003cp\u003eIt\u0027s the \u003ccode\u003e#\u003c/code\u003e.\u003c/p\u003e\n\u003cp\u003eSee \u003cem\u003e\u003ca href=\"http://www.neolisk.com/techblog/powershell-specialcharactersandtokens\" rel=\"nofollow noreferrer\"\u003ePowerShell - Special Characters And Tokens\u003c/a\u003e\u003c/em\u003e for special characters.\u003c/p\u003e\n\n\n### Answer 5 (Score: 24)\n\n\u003cp\u003eHere\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e# Single line comment in PowerShell\n\n\u0026lt;#\n--------------------------------------\nMulti-line comment in PowerShell V2+\n--------------------------------------\n#\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 6 (Score: 24)\n\n\u003cp\u003eWithin PowerShell ISE you can hit \u003ckbd\u003eCtrl\u003c/kbd\u003e+\u003ckbd\u003eJ\u003c/kbd\u003e to open the \u003cem\u003eStart Snipping\u003c/em\u003e  menu and select \u003cem\u003eComment block\u003c/em\u003e:\u003c/p\u003e\n\n\u003cp\u003e\u003ca href=\"https://i.sstatic.net/caJt2.png\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/caJt2.png\" alt=\"enter image description here\"\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 7 (Score: 10)\n\n\u003cp\u003eI\u0027m a little bit late to this party but seems that nobody actually wrote all use cases. So...\u003c/p\u003e\n\u003cp\u003eOnly supported version of PowerShell these days (\u003cem\u003efall of 2020 and beyond\u003c/em\u003e) are:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eWindows PowerShell 5.1.x\u003c/li\u003e\n\u003cli\u003ePowerShell 7.0.x.\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eYou don\u0027t want to or you shouldn\u0027t work with different versions of PowerShell.\u003c/p\u003e\n\u003cp\u003e\u003cstrong\u003eBoth versions\u003c/strong\u003e (\u003cem\u003eor any another version which you could come around WPS 3.0-5.0, PS Core 6.x.x on some outdated stations\u003c/em\u003e) \u003cstrong\u003eshare the same comment functionality.\u003c/strong\u003e\u003c/p\u003e\n\u003ch2\u003eOne line comments\u003c/h2\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e# Get all Windows Service processes \u0026lt;-- one line comment, it starts with \u0027#\u0027\nGet-Process -Name *host*\n\nGet-Process -Name *host* ## You could put as many ### as you want, it does not matter\n\nGet-Process -Name *host* # | Stop-Service # Everything from the first # until end of the line is treated as comment\n\nStop-Service -DisplayName Windows*Update # -WhatIf # You can use it to comment out cmdlet switches\n\u003c/code\u003e\u003c/pre\u003e\n\u003ch2\u003eMulti line comments\u003c/h2\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e\u0026lt;#\nEveryting between \u0027\u0026lt; #\u0027 and \u0027# \u0026gt;\u0027 is\ntreated as a comment. A typical use case is for help, see below.\n\n# You could also have a single line comment inside the multi line comment block.\n# Or two... :)\n\n#\u0026gt;\n\n\u0026lt;#\n.SYNOPSIS\n    A brief description of the function or script.\n    This keyword can be used only once in each topic.\n\n.DESCRIPTION\n    A detailed description of the function or script.\n    This keyword can be used only once in each topic.\n\n.NOTES\n    Some additional notes. This keyword can be used only once in each topic.\n    This keyword can be used only once in each topic.\n\n.LINK\n    A link used when Get-Help with a switch -OnLine is used.\n    This keyword can be used only once in each topic.\n\n.EXAMPLE\n    Example 1\n    You can use this keyword as many as you want.\n\n.EXAMPLE\n    Example 2\n    You can use this keyword as many as you want.\n#\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\u003ch2\u003eNested multi line comments\u003c/h2\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e\u0026lt;#\nNope, these are not allowed in PowerShell.\n\n\u0026lt;# This will break your first multiline comment block... #\u0026gt;\n...and this will throw a syntax error.\n#\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\u003ch2\u003eIn code nested multi line comments\u003c/h2\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e\u0026lt;#\nThe multi line comment opening/close\ncan be also used to comment some nested code\nor as an explanation for multi chained operations..\n#\u0026gt;\nGet-Service | \u0026lt;# Step explanation #\u0026gt;\nWhere-Object { $_.Status -eq [ServiceProcess.ServiceControllerStatus]::Stopped } |\n\u0026lt;# Format-Table -Property DisplayName, Status -AutoSize |#\u0026gt;\nOut-File -FilePath Services.txt -Encoding Unicode\n\u003c/code\u003e\u003c/pre\u003e\n\u003ch2\u003eEdge case scenario\u003c/h2\u003e\n\u003cpre class=\"lang-none prettyprint-override\"\u003e\u003ccode\u003e# Some well written script\nexit\nWriting something after exit is possible but not recommended.\nIt isn\u0027t a comment.\nEspecially in Visual Studio Code, these words baffle PSScriptAnalyzer.\nYou could actively break your session in VS Code.\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 8 (Score: 10)\n\n\u003cp\u003eUse a hashtag followed by a white space(!) for this:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e # Comment here\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eDo not forget the white space here! Otherwise it can interfere with internal commands.\u003c/p\u003e\n\u003cp\u003eE.g., this is \u003cem\u003enot\u003c/em\u003e a comment:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e#requires -runasadmin\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 9 (Score: 3)\n\n\u003cp\u003eYou can make:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e (Some basic code) # Use \"#\" after a line and use:\n\n \u0026lt;#\n    for more lines\n    ...\n    ...\n    ...\n    ..\n    .\n #\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 10 (Score: 2)\n\n\u003cp\u003eThere is a special way of inserting comments add the end of script:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e....\nexit \n\nHi\nHello\nWe are comments\nAnd not executed \n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eAnything after \u003ccode\u003eexit\u003c/code\u003e is not executed, and behave quite like comments.\u003c/p\u003e\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "powershell.exe \u0027C:\\my_path\\yada_yada\\run_import_script.ps1\u0027",
                                               "PS\u0026gt; cd C:\\my_path\\yada_yada\\ (enter)",
                                               "PS\u0026gt; .\\run_import_script.ps1 (enter)",
                                               "powershell -noexit \u0026quot;\u0026amp; \u0026quot;\u0026quot;C:\\my_path\\yada_yada\\run_import_script.ps1\u0026quot;\u0026quot;\u0026quot; (enter)",
                                               "Powershell.exe -File C:\\my_path\\yada_yada\\run_import_script.ps1",
                                               "powershell [-noexit] -executionpolicy bypass -File \u0026lt;Filename\u0026gt;",
                                               "powershell -executionpolicy bypass -File .\\Test.ps1",
                                               "powershell.exe -noexit \"\u0026amp; \u0027c:\\Data\\ScheduledScripts\\ShutdownVM.ps1\u0027\"",
                                               "powershell -command - \u0026lt; c:\\mypath\\myscript.ps1",
                                               "set-executionpolicy unrestricted",
                                               "set-ExecutionPolicy default",
                                               "./myscript.ps1",
                                               "@echo off\ncolor 1F\necho.\n\nC:\\Windows\\system32\\WindowsPowerShell\\v1.0\\powershell.exe -ExecutionPolicy Bypass -File \"PrepareEnvironment.ps1\"\n\n:EOF\necho Waiting seconds\ntimeout /t 10 /nobreak \u0026gt; NUL",
                                               "Program/Script",
                                               "Powershell.exe",
                                               "-File \"C:\\xxx.ps1\"",
                                               "$\u0026gt; . c:\\program file\\prog.ps1",
                                               "$\u0026gt; add or entry_func or main",
                                               "C:\\my_path\\yada_yada\\run_import_script.ps1",
                                               "powershell.exe \u0026quot;\u0026amp; \u0027C:\\my_path\\yada_yada\\run_import_script.ps1\u0027\u0026quot;",
                                               "powershell.exe .\\test.ps1\npowershell.exe -File .\\test.ps1\npowershell.exe -C \u0026quot;\u0026amp; \u0027.\\test.ps1\u0027\u0026quot;",
                                               "\u0026quot;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe\u0026quot; \u0026quot;-Command\u0026quot; \u0026quot;if((Get-ExecutionPolicy ) -ne \u0027AllSigned\u0027) { Set-ExecutionPolicy -Scope Process Bypass }; \u0026amp; \u0027C:\\Users\\<USER>\\Desktop\\MYSCRIPT.ps1\u0027\u0026quot;",
                                               "type \u0026quot;script_path\u0026quot; | powershell.exe -c -",
                                               "# \u0026amp; cls \u0026amp; @powershell \u0026quot;gc \u0027%~0\u0027 | iex\u0026quot; \u0026amp; exit",
                                               "@powershell \u0026quot;gc \u0027%~0\u0027 | iex\u0026quot;"
                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:20:44Z",
                         "ViewCount":  3497461,
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_general_ad_6fc5bad5",
                         "QuestionScore":  1091,
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-28T16:20:44Z",
                         "TotalScore":  3124,
                         "AnswerCount":  18,
                         "BestPractices":  [

                                           ],
                         "Title":  "Stack Overflow Q\u0026A: How to run a PowerShell script",
                         "CodeTemplate":  "PS\u0026gt; cd C:\\my_path\\yada_yada\\ (enter)",
                         "CommonMistakes":  [
                                                "Contains warnings about common mistakes and errors"
                                            ],
                         "HasAcceptedAnswer":  true,
                         "Domain":  "general_ad",
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "windows",
                                      "powershell",
                                      "scripting"
                                  ],
                         "Abstract":  "# Question: How to run a PowerShell script\n\n**Score:** 1091 | **Views:** 3497461 | **Tags:** windows, powershell, scripting\n\n**URL:** https://stackoverflow.com/questions/2035193/how-to-run-a-powershell-script\n\n## Question Body\n\n\u003cp\u003eHow do I run a PowerShell script?\u003c/p\u003e\n\n\u003cul\u003e\n\u003cli\u003eI have a script named myscript.ps1\u003c/li\u003e\n\u003cli\u003eI have all the necessary frameworks installed\u003c/li\u003e\n\u003cli\u003eI set that \u003ca href=\"https://stackoverflow.com/questions/10635/why-dont-my-powershell-scripts-run\"\u003eexecution policy\u003c/a\u003e thi...",
                         "Sources":  [
                                         {
                                             "SourceType":  "stackoverflow_qa",
                                             "CredibilityScore":  0.85,
                                             "PublishedAt":  "2025-07-28T16:20:44Z",
                                             "Url":  "https://stackoverflow.com/questions/2035193/how-to-run-a-powershell-script",
                                             "ScrapedAt":  "2025-07-28T16:20:44Z",
                                             "Id":  "058edd2a-04f0-48ca-bbdc-95b8d308eca7",
                                             "Author":  "Stack Overflow Community",
                                             "Title":  "Stack Overflow Q\u0026A: How to run a PowerShell script"
                                         }
                                     ],
                         "Content":  "# Question: How to run a PowerShell script\n\n**Score:** 1091 | **Views:** 3497461 | **Tags:** windows, powershell, scripting\n\n**URL:** https://stackoverflow.com/questions/2035193/how-to-run-a-powershell-script\n\n## Question Body\n\n\u003cp\u003eHow do I run a PowerShell script?\u003c/p\u003e\n\n\u003cul\u003e\n\u003cli\u003eI have a script named myscript.ps1\u003c/li\u003e\n\u003cli\u003eI have all the necessary frameworks installed\u003c/li\u003e\n\u003cli\u003eI set that \u003ca href=\"https://stackoverflow.com/questions/10635/why-dont-my-powershell-scripts-run\"\u003eexecution policy\u003c/a\u003e thing\u003c/li\u003e\n\u003cli\u003eI have followed the instructions on \u003ca href=\"http://technet.microsoft.com/en-us/library/ee176949.aspx\" rel=\"noreferrer\"\u003ethis MSDN help page\u003c/a\u003e\nand am trying to run it like so:\n\u003ccode\u003epowershell.exe \u0027C:\\my_path\\yada_yada\\run_import_script.ps1\u0027\u003c/code\u003e (with or without \u003ccode\u003e--noexit\u003c/code\u003e)\u003c/li\u003e\n\u003c/ul\u003e\n\n\u003cp\u003ewhich returns exactly nothing, except that the file name is output.\u003c/p\u003e\n\n\u003cp\u003eNo error, no message, nothing. Oh, when I add \u003ccode\u003e-noexit\u003c/code\u003e, the same thing happens, but I remain within PowerShell and have to exit manually.\u003c/p\u003e\n\n\u003cp\u003eThe .ps1 file is supposed to run a program and return the error level dependent on that program\u0027s output. But I\u0027m quite sure I\u0027m not even getting there yet.\u003c/p\u003e\n\n\u003cp\u003eWhat am I doing wrong?\u003c/p\u003e\n\n\n## Answers (18 total)\n\n### Answer 1 (Score: 1102) âœ… ACCEPTED ANSWER\n\n\u003cp\u003ePrerequisites:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eYou need to be able to run PowerShell as an administrator\u003c/li\u003e\n\u003cli\u003eYou need to set your PowerShell execution policy to a permissive value or be able to bypass it\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eSteps:\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003e\u003cp\u003eLaunch Windows PowerShell as an Administrator, and wait for the \u003ccode\u003ePS\u0026gt;\u003c/code\u003e prompt to appear\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eNavigate within PowerShell to the directory where the script lives:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePS\u0026gt; cd C:\\my_path\\yada_yada\\ (enter)\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eExecute the script:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003ePS\u0026gt; .\\run_import_script.ps1 (enter)\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eOr: you can run the PowerShell script from the Command Prompt (\u003ccode\u003ecmd.exe\u003c/code\u003e) like this:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003epowershell -noexit \u0026quot;\u0026amp; \u0026quot;\u0026quot;C:\\my_path\\yada_yada\\run_import_script.ps1\u0026quot;\u0026quot;\u0026quot; (enter)\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eaccording to \u003ca href=\"http://poshoholic.com/2007/09/27/invoking-a-powershell-script-from-cmdexe-or-start-run/\" rel=\"noreferrer\"\u003e\u003cem\u003eInvoking a PowerShell script from cmd.exe (or Start | Run)\u003c/em\u003e\u003c/a\u003e by Kirk Munro.\u003c/p\u003e\n\u003cp\u003eOr you could even \u003ca href=\"http://www.codeproject.com/KB/threads/AsyncPowerShell.aspx\" rel=\"noreferrer\"\u003erun your PowerShell script asynchronously from your C# application\u003c/a\u003e.\u003c/p\u003e\n\n\n### Answer 2 (Score: 340)\n\n\u003cp\u003eIf you are on PowerShell 2.0, use PowerShell.exe\u0027s \u003ccode\u003e-File\u003c/code\u003e parameter to invoke a script from another environment, like cmd.exe. For example:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003ePowershell.exe -File C:\\my_path\\yada_yada\\run_import_script.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 3 (Score: 246)\n\n\u003cp\u003eIf you want to run a script without modifying the default script execution policy, you can use the \u003cem\u003ebypass\u003c/em\u003e switch when launching \u003cstrong\u003eWindows PowerShell\u003c/strong\u003e.  \u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003epowershell [-noexit] -executionpolicy bypass -File \u0026lt;Filename\u0026gt;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 4 (Score: 138)\n\n\u003cp\u003eIn a command prompt type:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003epowershell -executionpolicy bypass -File .\\Test.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eNOTE:  Here \u003ccode\u003eTest.ps1\u003c/code\u003e is the PowerShell script.\u003c/p\u003e\n\n\n### Answer 5 (Score: 42)\n\n\u003cp\u003eI\u0027ve had the same problem, and I tried and tried... Finally I used:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003epowershell.exe -noexit \"\u0026amp; \u0027c:\\Data\\ScheduledScripts\\ShutdownVM.ps1\u0027\"\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eAnd put this line in a batch-file, and this works.\u003c/p\u003e\n\n\n### Answer 6 (Score: 30)\n\n\u003cp\u003eIf you only have \u003cstrong\u003ePowerShell 1.0\u003c/strong\u003e, this seems to do the trick well enough:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003epowershell -command - \u0026lt; c:\\mypath\\myscript.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eIt pipes the script file to the PowerShell command line.\u003c/p\u003e\n\n\n### Answer 7 (Score: 28)\n\n\u003col\u003e\n\u003cli\u003eOpen PowerShell in administrator mode\u003c/li\u003e\n\u003cli\u003eRun: \u003ccode\u003eset-executionpolicy unrestricted\u003c/code\u003e\u003c/li\u003e\n\u003cli\u003eOpen a regular PowerShell window and run your script.\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eI found this solution following the link that was given as part of the error message: \u003cem\u003e\u003ca href=\"https://go.microsoft.com/fwlink/?LinkID=135170\" rel=\"noreferrer\"\u003eAbout Execution Policies\u003c/a\u003e\u003c/em\u003e\u003c/p\u003e\n\u003cp\u003eMake sure to run \u003ccode\u003eset-ExecutionPolicy default\u003c/code\u003e once you\u0027re done, or you will be exposed to security risks.\u003c/p\u003e\n\n\n### Answer 8 (Score: 27)\n\n\u003cp\u003ePretty easy. Right click the .ps1 file in Windows and in the shell menu click on \u003cem\u003eRun with PowerShell\u003c/em\u003e.\u003c/p\u003e\n\n\n### Answer 9 (Score: 16)\n\n\u003cp\u003eIf your script is named with the \u003ccode\u003e.ps1\u003c/code\u003e extension and you\u0027re in a PowerShell window, you just run \u003ccode\u003e./myscript.ps1\u003c/code\u003e (assuming the file is in your working directory).\u003c/p\u003e\n\n\u003cp\u003eThis is true for me anyway on Windows 10 with PowerShell version 5.1 anyway, and I don\u0027t think I\u0027ve done anything to make it possible.\u003c/p\u003e\n\n\n### Answer 10 (Score: 13)\n\n\u003cp\u003eUsing cmd (BAT) file:\u003c/p\u003e\n\n\u003cpre\u003e\u003ccode\u003e@echo off\ncolor 1F\necho.\n\nC:\\Windows\\system32\\WindowsPowerShell\\v1.0\\powershell.exe -ExecutionPolicy Bypass -File \"PrepareEnvironment.ps1\"\n\n:EOF\necho Waiting seconds\ntimeout /t 10 /nobreak \u0026gt; NUL\n\u003c/code\u003e\u003c/pre\u003e\n\n\u003cp\u003eIf you need \u003cstrong\u003erun as administrator\u003c/strong\u003e:\u003c/p\u003e\n\n\u003col\u003e\n\u003cli\u003eMake a shortcut pointed to the command prompt (I named it\nAdministrative Command Prompt)\u003c/li\u003e\n\u003cli\u003eOpen the shortcut\u0027s properties and go to the Compatibility tab\u003c/li\u003e\n\u003cli\u003eUnder the Privilege Level section, make sure the checkbox next to \"Run this program as an administrator\" is checked\u003c/li\u003e\n\u003c/ol\u003e\n\n\n### Answer 11 (Score: 13)\n\n\u003cp\u003eIn case you want to run a PowerShell script with Windows Task Scheduler, please follow the steps below:\u003c/p\u003e\n\n\u003col\u003e\n\u003cli\u003e\u003cp\u003eCreate a task\u003c/p\u003e\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eSet \u003ccode\u003eProgram/Script\u003c/code\u003e to \u003ccode\u003ePowershell.exe\u003c/code\u003e\u003c/p\u003e\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eSet \u003ccode\u003eArguments\u003c/code\u003e to \u003ccode\u003e-File \"C:\\xxx.ps1\"\u003c/code\u003e\u003c/p\u003e\u003c/li\u003e\n\u003c/ol\u003e\n\n\u003cp\u003eIt\u0027s from another answer, \u003cem\u003e\u003ca href=\"https://stackoverflow.com/questions/23953926/how-to-execute-a-powershell-script-automatically-using-windows-task-scheduler/23954618#23954618\"\u003eHow do I execute a PowerShell script automatically using Windows task scheduler?\u003c/a\u003e\u003c/em\u003e.\u003c/p\u003e\n\n\n### Answer 12 (Score: 12)\n\n\u003cp\u003eAn easy way is to use \u003ca href=\"https://technet.microsoft.com/en-us/library/dd315244.aspx\" rel=\"noreferrer\"\u003ePowerShell ISE\u003c/a\u003e, open script, run and invoke your script, function...\u003c/p\u003e\n\n\u003cp\u003e\u003ca href=\"https://technet.microsoft.com/en-us/library/dd315244.aspx\" rel=\"noreferrer\"\u003e\u003cimg src=\"https://i.sstatic.net/h2jFM.png\" alt=\"Enter image description here\"\u003e\u003c/a\u003e\u003c/p\u003e\n\n\n### Answer 13 (Score: 9)\n\n\u003cul\u003e\n\u003cli\u003e\u003cp\u003eGive the path of the script, that is, path setting by cmd:\u003c/p\u003e\n\n\u003cp\u003e\u003ccode\u003e$\u0026gt; . c:\\program file\\prog.ps1\u003c/code\u003e\u003c/p\u003e\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eRun the entry point function of PowerShell:\u003c/p\u003e\n\n\u003cp\u003eFor example, \u003ccode\u003e$\u0026gt; add or entry_func or main\u003c/code\u003e\u003c/p\u003e\u003c/li\u003e\n\u003c/ul\u003e\n\n\n### Answer 14 (Score: 5)\n\n\u003cp\u003eWith the appropriate execution policy, you should just be able to call the file directly and Windows will associate it with PowerShell\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003eC:\\my_path\\yada_yada\\run_import_script.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThat does not do so well with arguments. The real answer to your question is that you are missing the \u003ccode\u003e\u0026amp;\u003c/code\u003e to say \u0026quot;execute this\u0026quot;\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003epowershell.exe \u0026quot;\u0026amp; \u0027C:\\my_path\\yada_yada\\run_import_script.ps1\u0027\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003chr /\u003e\n\u003cp\u003eFor \u003ca href=\"https://stackoverflow.com/users/648265/ivan-pozdeev\"\u003e@ivan_pozdeev\u003c/a\u003e\u003c/p\u003e\n\u003ch2\u003eTo Run Any Windows Script\u003c/h2\u003e\n\u003ch4\u003eBatch\u003c/h4\u003e\n\u003cul\u003e\n\u003cli\u003eTo run from File Explorer: double-click test.bat\u003c/li\u003e\n\u003cli\u003eTo run from cmd.exe or *.bat or *.cmd:\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003e.\\test.bat\n\u003c/code\u003e\u003c/pre\u003e\n\u003cul\u003e\n\u003cli\u003eTo run from PowerShell:\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003e.\\test.bat\n\u003c/code\u003e\u003c/pre\u003e\n\u003cul\u003e\n\u003cli\u003eAdditional Registry edits\u003c/li\u003e\n\u003cli\u003eMany other ways\u003c/li\u003e\n\u003c/ul\u003e\n\u003ch4\u003ePowerShell\u003c/h4\u003e\n\u003cul\u003e\n\u003cli\u003eTo run from File Explorer: right-click for context menu, then click \u0026quot;Run with PowerShell\u0026quot;\u003c/li\u003e\n\u003cli\u003eTo run from cmd.exe or *.bat or *.cmd:\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003epowershell.exe .\\test.ps1\npowershell.exe -File .\\test.ps1\npowershell.exe -C \u0026quot;\u0026amp; \u0027.\\test.ps1\u0027\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\u003cul\u003e\n\u003cli\u003eTo run from PowerShell:\u003c/li\u003e\n\u003c/ul\u003e\n\u003cpre\u003e\u003ccode\u003e.\\test.ps1\n\u003c/code\u003e\u003c/pre\u003e\n\u003cul\u003e\n\u003cli\u003eAdditional Registry edits\u003c/li\u003e\n\u003cli\u003eMany other ways\u003c/li\u003e\n\u003c/ul\u003e\n\n\n### Answer 15 (Score: 5)\n\n\u003cp\u003eI\u0027ve just found the method what Microsoft do when we right click on a \u003ccode\u003eps1\u003c/code\u003e script and click on \u0026quot;Run with PowerShell\u0026quot; :\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e\u0026quot;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe\u0026quot; \u0026quot;-Command\u0026quot; \u0026quot;if((Get-ExecutionPolicy ) -ne \u0027AllSigned\u0027) { Set-ExecutionPolicy -Scope Process Bypass }; \u0026amp; \u0027C:\\Users\\<USER>\\Desktop\\MYSCRIPT.ps1\u0027\u0026quot;\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 16 (Score: 4)\n\n\u003cp\u003eYou can run from cmd like this:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003etype \u0026quot;script_path\u0026quot; | powershell.exe -c -\n\u003c/code\u003e\u003c/pre\u003e\n\n\n### Answer 17 (Score: 3)\n\n\u003cp\u003eUse the \u003ccode\u003e-File\u003c/code\u003e parameter in front of the filename. The quotes make PowerShell think it is a string of commands.\u003c/p\u003e\n\n\n### Answer 18 (Score: 0)\n\n\u003cp\u003eTo create a PowerShell script with an easy way to bypass restrictions without unnecessary files and clicks, you can use the following method:\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003e\u003cp\u003eСreate a new file \u0026quot;your_script**.cmd\u0026quot;.**\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eOpen the file for editing and paste in the first line:\u003c/p\u003e\n\u003cpre\u003e\u003ccode\u003e# \u0026amp; cls \u0026amp; @powershell \u0026quot;gc \u0027%~0\u0027 | iex\u0026quot; \u0026amp; exit\n\u003c/code\u003e\u003c/pre\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eStarting from the second line, paste your PowerShell script and save.\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eDone! Just run your script by double clicking.\u003c/p\u003e\n\u003cp\u003eHow does it work??\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003e\u003cp\u003eThe script is initially run in cmd, and the \u003ccode\u003e#\u003c/code\u003e symbol simply causes a do-nothing error.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003e\u0026amp;\u003c/code\u003e allows to execute one command after another.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003ecls\u003c/code\u003e simply clears the screen.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003e@powershell \u0026quot;gc \u0027%~0\u0027 | iex\u0026quot;\u003c/code\u003e is complex, so I will analyze it in more detail:\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003e@\u003c/code\u003e hides the executed command (to keep the screen clean).\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003epowershell\u003c/code\u003e starts an PowerShell and runs the command specified in quotation marks.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003egc \u0027%~0\u0027\u003c/code\u003e is the first command that runs in PowerShell. It reads the content of the current file, as \u0027%~0\u0027 is replaced by the current script location.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003e|\u003c/code\u003e allows redirecting the output of one command to the input of another.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003eiex\u003c/code\u003e executes all the commands it receives.\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ol\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003e\u003ccode\u003eexit\u003c/code\u003e terminates the execution of the cmd script after completion of PowerShell execution.\u003c/p\u003e\n\u003c/li\u003e\n\u003cli\u003e\u003cp\u003eAfter the current file is run in powershell, the first line will be ignored, since \u003ccode\u003e#\u003c/code\u003e in Powershell means comment.\u003c/p\u003e\n\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eIdea was taken from the site: \u003ca href=\"https://www.netspi.com/blog/technical-blog/network-pentesting/15-ways-to-bypass-the-powershell-execution-policy/\" rel=\"nofollow noreferrer\"\u003enetspi.com \u0026quot;15 ways to bypass the powershell execution policy\u0026quot;\u003c/a\u003e\u003c/p\u003e\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [

                                           ],
                         "RelevanceScore":  0.8996065061142513,
                         "CreatedAt":  "2025-07-28T16:20:58Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_user_management_c2c64a61",
                         "Operation":  "read",
                         "Stars":  1143,
                         "UpdatedAt":  "2025-07-28T16:20:58Z",
                         "HasReadme":  true,
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub Repository: ********/adPEAS",
                         "LastUpdated":  "2025-07-24T19:31:50Z",
                         "CodeTemplate":  "Import-Module .\\adPEAS.ps1\n\n. .\\adPEAS.ps1\n\ngc -raw .\\adPEAS.ps1 | iex\n\nIEX (New-Object Net.WebClient).DownloadString(\u0027https://raw.githubusercontent.com/********/adPEAS/main/adPEAS.ps1\u0027)\n\nInvoke-adPEAS\n\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Outputfile \u0027C:\\temp\\adPEAS_outputfile\u0027 -NoColor\n\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027\n\n$SecPassword = ConvertTo-SecureString \u0027Passw0rd1!\u0027 -AsPlainText -Force\n$Cred = New-Object System.Management.Automation.PSCredential(\u0027contoso\\johndoe\u0027, $SecPassword)\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Cred $Cred\n\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027 -Username \u0027contoso\\johndoe\u0027 -Password \u0027Passw0rd1!\u0027 -Force\n\nInvoke-adPEAS -Module Domain\n\nInvoke-adPEAS -Module Rights\n\nInvoke-adPEAS -Module GPO\n\nInvoke-adPEAS -Module ADCS\n\nInvoke-adPEAS -Module Creds\n\nInvoke-adPEAS -Module Delegation\n\nInvoke-adPEAS -Module Accounts\n\nInvoke-adPEAS -Module Computer\n\nInvoke-adPEAS -Module Bloodhound\n\nInvoke-adPEAS -Module Bloodhound -BloodHoundLegacy\n\nInvoke-adPEAS -Module Bloodhound -Scope All",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  135,
                         "PowerShellFiles":  2,
                         "Domain":  "user_management",
                         "RepoSize":  79150,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "user_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: ********/adPEAS\n\n**Stars:** 1143 | **Language:** PowerShell | **Forks:** 135\n\n**Description:** Powershell tool to automate Active Directory enumeration.\n\n**URL:** https://github.com/********/adPEAS\n\n**Clone URL:** https://github.com/********/adPEAS.git\n\n## README\n\n# adPEAS\n\n![](https://github.com/********/adPEAS/raw/main/images/adPEAS_large.jpg)\n\nadPEAS is a Powershell tool to automate Active Directory enumeration.\nIn fact, adPEAS is like a wrapper for different other cool projects...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:20:58Z",
                                             "Url":  "https://github.com/********/adPEAS",
                                             "ScrapedAt":  "2025-07-28T16:20:58Z",
                                             "Id":  "2913849b-96a6-49a0-92ac-d85f91bd3dbd",
                                             "Author":  "********",
                                             "Title":  "GitHub Repository: ********/adPEAS"
                                         }
                                     ],
                         "Topics":  [

                                    ],
                         "Content":  "# Repository: ********/adPEAS\n\n**Stars:** 1143 | **Language:** PowerShell | **Forks:** 135\n\n**Description:** Powershell tool to automate Active Directory enumeration.\n\n**URL:** https://github.com/********/adPEAS\n\n**Clone URL:** https://github.com/********/adPEAS.git\n\n## README\n\n# adPEAS\n\n![](https://github.com/********/adPEAS/raw/main/images/adPEAS_large.jpg)\n\nadPEAS is a Powershell tool to automate Active Directory enumeration.\nIn fact, adPEAS is like a wrapper for different other cool projects like PowerView, PoshADCS, BloodHound stuff and some own written lines of code.\n\nAs said, adPEAS is a wrapper for other tools. They are almost all written in pure Powershell but some of them are included as C# code in a compressed binary blob.\n\nadPEAS-Light is a version without Bloodhound and it is more likely that it will not be blocked by an AV solution.\n\n# How It Works\n\nadPEAS can be run simply by starting the script via _invoke-adPEAS_ if it is started on a domain joined computer.\nIf the system you are running adPEAS from is not domain joined or you want to enumerate another domain, use a certain domain controller to connect to, use different credentials or just to enumerate for credential exposure only, you can do it by using defined parameters.\n\n## adPEAS Modules\n\nadPEAS consists of the following enumeration modules:\n* Domain - Searching for basic Active Directory information, like Domain Controllers, Sites und Subnets, Trusts and Password/Kerberos policy\n* Rights - Searching for specific Active Directory rights and permissions, like LAPS, DCSync and adding computer to domain\n* GPO -  Searching for basic GPO related things, like local group membership on domain computer\n* ADCS - Searching for basic Active Directory Certificate Services information, like CA Name, CA Server and vulnerable Templates\n* Creds - Searching for different kind of credential exposure, like ASREPRoast, Kerberoasting, GroupPolicies, Netlogon scripts, LAPS, gMSA, certain legacy attributes, e.g. UnixPassword, etc.\n* Delegation - Searching for delegation issues, like \u0027Constrained Delegation\u0027, \u0027Unconstrained Delegation\u0027 and \u0027Resource Based Constrained Delegation\u0027, for computer and user accounts\n* Accounts - Searching for non-disabled high privileged user accounts in predefined groups and account issues like e.g. old passwords\n* Computer - Enumerating Domain Controllers, Certificate Services, Exchange Server and outdated OS versions like Windows Server 2008R2, etc.\n* BloodHound - Enumerating Active Directory with the SharpHound collector for BloodHound Community Edition or BloodHound-Legacy\n\n# Some How To Use Examples\n## Simple usage with generic program parameters\nFirst you have to load adPEAS in Powershell...\n```\nImport-Module .\\adPEAS.ps1\n```\nor\n```\n. .\\adPEAS.ps1\n```\nor\n```\ngc -raw .\\adPEAS.ps1 | iex\n```\nor\n```\nIEX (New-Object Net.WebClient).DownloadString(\u0027https://raw.githubusercontent.com/********/adPEAS/main/adPEAS.ps1\u0027)\n```\n\nStart adPEAS with all enumeration modules and enumerate the domain the logged-on user and computer is connected to.\n```\nInvoke-adPEAS\n```\n\nStart adPEAS with all enumeration modules and enumerate the domain \u0027contoso.com\u0027. In addition it writes all output without any ANSI color codes to a file.\n```\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Outputfile \u0027C:\\temp\\adPEAS_outputfile\u0027 -NoColor\n```\n\nStart adPEAS with all enumeration modules, enumerate the domain \u0027contoso.com\u0027 and use the domain controller \u0027dc1.contoso.com\u0027 for almost all enumeration requests.\n```\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027\n```\n\nStart adPEAS with all enumeration modules, enumerate the domain \u0027contoso.com\u0027 and use the passed PSCredential object during enumeration.\n```\n$SecPassword = ConvertTo-SecureString \u0027Passw0rd1!\u0027 -AsPlainText -Force\n$Cred = New-Object System.Management.Automation.PSCredential(\u0027contoso\\johndoe\u0027, $SecPassword)\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Cred $Cred\n```\n\nStart adPEAS with all enumeration modules, enumerate the domain \u0027contoso.com\u0027 by using the domain controller \u0027dc1.contoso.com\u0027 and use the username \u0027contoso\\johndoe\u0027 with password \u0027Passw0rd1!\u0027 during enumeration. If, due to DNS issues Active Directory detection fails, the switch -Force forces adPEAS to ignore those issues and try to get still as much information as possible.\n```\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027 -Username \u0027contoso\\johndoe\u0027 -Password \u0027Passw0rd1!\u0027 -Force\n```\n\n## Usage with a single enumeration module\n### All modules below can be combined with all generic program parameters explained above.\n\nEnumerates basic Active Directory information, like Domain Controllers, Password Policy, Sites and Subnets and Trusts.\n```\nInvoke-adPEAS -Module Domain\n```\n\nEnumerates specific Active Directory rights and permissions, like LAPS, DCSync and adding computer to domain.\n```\nInvoke-adPEAS -Module Rights\n```\n\nEnumerates basic GPO information, like set local group membership on domain computer.\n```\nInvoke-adPEAS -Module GPO\n```\n\nEnumerates basic Active Directory Certificate Services information, like CA Name, CA Server and common Template vulnerabilities.\n```\nInvoke-adPEAS -Module ADCS\n```\n\nEnumerates credential exposure issues, like ASREPRoast, Kerberoasting, Linux/Unix password attributes, gMSA, LAPS (if your account has the rights to read it), Group Policies, Netlogon scripts.\n```\nInvoke-adPEAS -Module Creds\n```\n\nEnumerates delegation issues, like \u0027Unconstrained Delegation\u0027, \u0027Constrained Delegation\u0027, \u0027Resource Based Constrained Delegation\u0027 for user and computer objects.\n```\nInvoke-adPEAS -Module Delegation\n```\n\nEnumerates users in high privileged groups which are NOT disabled, like Administrators, Domain Admins, Enterprise Admins, Group Policy Creators, DNS Admins, Account Operators, Server Operators, Printer Operators, Backup Operators, Hyper-V Admins, Remote Management Users und CERT Publishers.\n```\nInvoke-adPEAS -Module Accounts\n```\n\nEnumerates installed Domain Controllers, Active Directory Certificate Services, Exchange Server and outdated OS versions like Windows Server 2008R2.\n```\nInvoke-adPEAS -Module Computer\n```\n\nStarts Bloodhound enumeration for BloodHound Community Edition (\u003e= version 5.0) with the scope DCOnly. Output ZIP files are stored in the same directory adPEAS is started from.\n```\nInvoke-adPEAS -Module Bloodhound\n```\n\nStarts Bloodhound enumeration for BloodHound-Legacy (up to version 4.3.1) with the scope DCOnly. Output ZIP files are stored in the same directory adPEAS is started from.\n```\nInvoke-adPEAS -Module Bloodhound -BloodHoundLegacy\n```\n\nStarts Bloodhound enumeration with the scope All. With this option the SharpHound collector will contact each member computer of the domain. Output ZIP files are stored in the same directory adPEAS is started from.\n```\nInvoke-adPEAS -Module Bloodhound -Scope All\n```\n\n## Special thanks go to...\n* Will Schroeder @harmjoy, for his great PowerView\n* Charlie Clark @exploitph, for his ongoing work on PowerView\n* Christoph Falta @cfalta, for his inspiring work on PoshADCS\n* Dirk-jan @_dirkjan, for his great AD and Windows research\n* SpecterOps, for their fantastic BloodHound\n* and all the people who inspired me on my journey...\n\n## PowerShell Files (2 files)\n\n"
                     },
                     {
                         "AllCodeBlocks":  [

                                           ],
                         "RelevanceScore":  0.74103762920994987,
                         "CreatedAt":  "2025-07-28T16:21:00Z",
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_user_management_a5a67a6a",
                         "Operation":  "read",
                         "Stars":  505,
                         "UpdatedAt":  "2025-07-28T16:21:00Z",
                         "HasReadme":  true,
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub Repository: ANSSI-FR/ADTimeline",
                         "LastUpdated":  "2025-07-20T15:22:59Z",
                         "CodeTemplate":  "PS\u003e import-csv timeline_%DOMAINFQDN%.csv -delimiter \";\"\nPS\u003e import-clixml ADobjects_%DOMAINFQDN%.xml\nPS\u003e import-clixml gcADobjects_%DOMAINFQDN%.xml\n\nPS\u003e./ADTimeline -customgroups \"VIP-group1,ESX-Admins,Tier1-admins\"\n\nPS\u003e$customgroups = get-content customgroups.txt\nPS\u003e./ADTimeline -customgroups $customgroups\n\n$groupscustom = (\"VIP-group1\",\"ESX-Admis\",\"Tier1-admins\")\n\nDOS\nPS\u003e .\\ADTimeline.ps1 -server \u003cGLOBAL CATALOG FQDN\u003e\n\nDOS\nC:\\Windows\\System32\u003e dsamain.exe -dbpath \u003cNTDS.DIT path\u003e -ldapport 3266 -allownonadminaccess\n\nDOS\nPS\u003e .\\ADTimeline.ps1 -server \"127.0.0.1:3266\"\n\nDOS\nPS\u003e.\\ADTimeline -nofwdSMTPaltRecipient\n\npowershell\nPS\u003e import-csv timeline_%DOMAINFQDN%.csv -delimiter \";\"\nPS\u003e import-clixml ADobjects_%DOMAINFQDN%.xml\nPS\u003e import-clixml gcADobjects_%DOMAINFQDN%.xml\n\npowershell\nPS\u003e./ADTimeline -customgroups \"VIP-group1,ESX-Admins,Tier1-admins\"\n\npowershell\nPS\u003e$customgroups = get-content customgroups.txt\nPS\u003e./ADTimeline -customgroups $customgroups\n\npowershell\n$groupscustom = (\"VIP-group1\",\"ESX-Admis\",\"Tier1-admins\")\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[ search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as TargetUserSid | fields TargetUserSid ] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullName , values(LogonType) as logonTypes, values(IpAddress) as IpAdresses, values(Computer) as Computers, dc(Computer) as CountComputers, dc(IpAddress) as CountIpAdresses by TargetUserSid\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4688\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SamAccountName as TargetUserName | fields TargetUserName] \nOR [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as SubjectUserSid | fields SubjectUserSid] \n|  stats values(CommandLine) by Computer, TargetUserName,SubjectUserName\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"*PowerShell*\"  \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | dedup SamAccountName | return 1000 $SamAccountName]\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4769\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt) earliest = 1 latest = now() | rename SID as ServiceSid | fields ServiceSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName  \n|  eval time=strftime(_time,\"%Y-%m-%d %H:%M:%S\") \n|  stats list(ServiceName) as Services, dc(ServiceName) as nbServices, list(time) as time by IpAddress, TicketEncryptionType \n| sort -nbServices\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4688\" \n[search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SamAccountName as TargetUserName | fields TargetUserName ] \nOR  [search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SID as SubjectUserSid | fields SubjectUserSid ] \n|  stats values(CommandLine) as cmdlines, values(TargetUserName) as TargetSubjectNames, values(SubjectUserName) as SubjectUserNames by Computer\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects   (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now() | rename SID as TargetUserSid  | fields TargetUserSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") earliest = 1 latest = now() |  eval eval_asrep_bit = floor(userAccountControl / pow(2, 22)) %2 | search eval_asrep_bit = 1 |  rename SID as TargetUserSid  | fields TargetUserSid]  \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\" earliest = 1 latest = now() | eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0 |   rename dNSHostName as Computer | fields Computer] \n|  search *   [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | eval canbedelagated = round(((userAccountControl / pow(2, 20)) %2), 0) | search canbedelagated = 0 | rename SID as TargetUserSid  | fields TargetUserSid ] \n|  strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName \n| table  _time, Computer, TargetFullName, IpAddress, LogonType, LogonProcessName\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" TargetUserName = \"*$\" NOT TargetUserSid=\"S-1-5-18\"\n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\"  earliest = 1 latest = now() |  eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0  |  rename dNSHostName as Computer | fields Computer]\n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(LogonType), values(IpAddress), values(LogonProcessName) count by Computer, TargetFullName",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  65,
                         "PowerShellFiles":  null,
                         "Domain":  "user_management",
                         "RepoSize":  1041,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "user_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: ANSSI-FR/ADTimeline\n\n**Stars:** 505 | **Language:** PowerShell | **Forks:** 65\n\n**Description:** Timeline of Active Directory changes with replication metadata\n\n**URL:** https://github.com/ANSSI-FR/ADTimeline\n\n**Clone URL:** https://github.com/ANSSI-FR/ADTimeline.git\n\n**Topics:** active-directory, dfir, forensics, powershell, splunk, timeline, windows\n\n## README\n\n![ADTimeline](./logo.png)\n---\n# Table of contents:\n1. [The ADTimeline PowerShell script](#thescript)\n    1. [Description...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:21:00Z",
                                             "Url":  "https://github.com/ANSSI-FR/ADTimeline",
                                             "ScrapedAt":  "2025-07-28T16:21:00Z",
                                             "Id":  "9f432579-9f39-456f-967b-f5e4b0160cf9",
                                             "Author":  "ANSSI-FR",
                                             "Title":  "GitHub Repository: ANSSI-FR/ADTimeline"
                                         }
                                     ],
                         "Topics":  [
                                        "active-directory",
                                        "dfir",
                                        "forensics",
                                        "powershell",
                                        "splunk",
                                        "timeline",
                                        "windows"
                                    ],
                         "Content":  "# Repository: ANSSI-FR/ADTimeline\n\n**Stars:** 505 | **Language:** PowerShell | **Forks:** 65\n\n**Description:** Timeline of Active Directory changes with replication metadata\n\n**URL:** https://github.com/ANSSI-FR/ADTimeline\n\n**Clone URL:** https://github.com/ANSSI-FR/ADTimeline.git\n\n**Topics:** active-directory, dfir, forensics, powershell, splunk, timeline, windows\n\n## README\n\n![ADTimeline](./logo.png)\n---\n# Table of contents:\n1. [The ADTimeline PowerShell script](#thescript)\n    1. [Description](#description)\n    2. [Prerequisites](#prerequisites)\n    3. [Usage](#usage)\n    4. [Files generated](#files)\n    5. [Custom groups](#groups)\n2. [The ADTimeline App for Splunk](#theapp)\n    1. [Description](#descriptionsplk)\n    2. [Sourcetypes](#sourcetype)\n    3. [AD General information dashboards](#infradashboards)\n    4. [AD threat hunting dashboards](#threathuntdashboards)\n    5. [Enhance your traditional event logs threat hunting with ADTimeline](#threathuntevtx)\n\n# The ADTimeline PowerShell script:  \u003ca name=\"thescript\"\u003e\u003c/a\u003e\n\n## Description: \u003ca name=\"description\"\u003e\u003c/a\u003e\n\nThe ADTimeline script generates a timeline based on Active Directory replication metadata for objects considered of interest.  \nReplication metadata gives you the time at which each replicated attribute for a given object was last changed. As a result the timeline of modifications is partial. For each modification of a replicated attribute a version number is incremented.  \nADTimeline was first presented at the [CoRI\u0026IN 2019](https://www.cecyf.fr/coriin/coriin-2019/) (Conférence sur la réponse aux incidents et l’investigation numérique). Slides of the presentation, in french language,  are available [here](https://cyber.gouv.fr/publications/investigation-numerique-sur-lannuaire-active-directory-avec-les-metadonnees-de). It was also presented at the [Amsterdam 2019 FIRST Technical Colloquium](https://www.first.org/events/colloquia/amsterdam2019/program#pActive-Directory-forensics-with-replication-metadata-ADTimeline-tool), slides in english are available [here](https://cyber.gouv.fr/en/actualites/adtimeline-active-directory-forensics-replication-metadata-first-technical-colloquium).\n\nObjects considered of interest retrieved by the script include:\n\n- Schema and configuration partition root objects.\n- Domain root and objects located directly under the root.\n- Objects having an ACE on the domain root.\n- Domain roots located in the AD forest.\n- Domain trusts.\n- Deleted users (i.e. tombstoned).\n- Objects protected by the SDProp process (i.e. AdminCount equals 1).\n- The Guest account.\n- The AdminSDHolder object.\n- Objects having an ACE on the AdminSDHolder object.\n- Class Schema objects.\n- Existing and deleted Group Policy objects.\n- DPAPI secrets.\n- Domain controllers (Computer objects, ntdsdsa and server objects).\n- DNS zones.\n- WMI filters.\n- Accounts with suspicious SIDHistory (scope is forest wide).\n- Sites.\n- Organizational Units.\n- Objects with Kerberos delegation enabled.\n- Extended rights.\n- Schema attributes with particular SearchFlags (Do not audit or confidential).\n- Kerberoastable user accounts (SPN value).\n- AS-REP roastable accounts (UserAccountControl value).\n- Authentication policy silos.\n- CertificationAuthority and pKIEnrollmentService objects.\n- Cross Reference containers.\n- Exchange RBAC roles and accounts assigned to a role.\n- Exchange mail flow configuration objects.\n- Exchange mailbox databases objects.\n- Exchange Mailbox Replication Service objects\n- Deleted objects under the configuration partition.\n- Dynamic objects.\n- The directory service and RID manager objects.\n- The Pre Windows 2000 compatible access, Cert publishers, GPO creator owners and DNS Admins groups.\n- ADFS DKM containers.\n- Service connection point objects considered of interest.\n- Custom groups which have to be manually defined.\n- User objects with mail forwarder enabled (msExchGenericForwardingAddress and altRecipient attributes).\n\n## Prerequisites: \u003ca name=\"prerequisites\"\u003e\u003c/a\u003e\n\n- The account launching the script should be able to read objects in the tombstone (Deleted Objects Container) and some parts of the Exchange settings located in the configuration partition (View-Only Organization management). Delegation can be tricky to setup (especially for reading the tombstone). That is why we advise you to run the script with a domain admin account. If you launch the script as a standard user, it will process the timeline without the objects mentioned.\n- Computer should run Windows NT 6.1 or later with PowerShell 2.0 or later and have the Active Directory Powershell module installed (part of RSAT-AD-Tools).\n- If you enabled PowerShell Constrained Language Mode the script might fail (calling $error.clear()). Consider whitelisting the script via your device guard policy.\n- If you are using offline mode install the ADLDS role on a Windows Server edition in order to use dsamain.exe and mount the NTDS database.\n\n    The version of the Windows Server you install the role on should be the same as the version of the Windows Server which the ntds.dit came from. If you do not know that version and you have the SOFTWARE hive available, you can look at the CurrentVersion key.\n    \n    If you can not mount the ntds.dit file with dsamain.exe, this might be because the NTDS dump is corrupted. In that case, you can follow [advice from cert-cwatch](https://github.com/ANSSI-FR/ADTimeline/issues/17#issuecomment-1984049537).\n\n## Usage: \u003ca name=\"usage\"\u003e\u003c/a\u003e\n\nIn online mode no argument is mandatory and the closest global catalog is used for processing. If no global catalog is found run the script with the server argument :\n```DOS\nPS\u003e .\\ADTimeline.ps1 -server \u003cGLOBAL CATALOG FQDN\u003e\n```\nIn offline mode: Replay if necessary transaction logs of the NTDS database, mount it on your analysis machine (ADLDS + RSAT-AD-Tools installed) and use 3266 as LDAP port.\n```DOS\nC:\\Windows\\System32\u003e dsamain.exe -dbpath \u003cNTDS.DIT path\u003e -ldapport 3266 -allownonadminaccess\n```\nIf necessary use the allowupgrade switch.\n\nLaunch the script targetting localhost on port 3266:\n```DOS\nPS\u003e .\\ADTimeline.ps1 -server \"127.0.0.1:3266\"\n```\n\nIf you encounter performance issues when running against a large MSExchange organization with forwarders massively used, use the nofwdSMTP parameter:\n```DOS\nPS\u003e.\\ADTimeline -nofwdSMTPaltRecipient\n```\n## Files generated \u003ca name=\"files\"\u003e\u003c/a\u003e\n\nOutput files are generated in the current directory:\n\n- timeline_%DOMAINFQDN%.csv: The timeline generated with the AD replication metadata of objects retrieved.\n- logfile_%DOMAINFQDN%.log: Script log file. You will also find various information on the domain.\n- ADobjects_%DOMAINFQDN%.xml: Objects of interest retrieved via LDAP.\n- gcADobjects_%DOMAINFQDN%.xml: Objects of interest retrieved via the Global Catalog.\n\n\nTo import files for analysis with powershell. \n```powershell\nPS\u003e import-csv timeline_%DOMAINFQDN%.csv -delimiter \";\"\nPS\u003e import-clixml ADobjects_%DOMAINFQDN%.xml\nPS\u003e import-clixml gcADobjects_%DOMAINFQDN%.xml\n```\nThe analysis with the ADTimeline for Splunk is a better solution.\n\n## Custom groups \u003ca name=\"groups\"\u003e\u003c/a\u003e\n\nIf you want to include custom AD groups in the timeline (for example virtualization admin groups, network admins, VIP groups...) use the *Customgroups* parameter.\n\n*Customgroups* parameter can be a string with multiple group comma separated (no space):\n```powershell\nPS\u003e./ADTimeline -customgroups \"VIP-group1,ESX-Admins,Tier1-admins\"\n```\n*Customgroups* parameter can also be an array, in case you import the list from a file (one group per line):\n```powershell\nPS\u003e$customgroups = get-content customgroups.txt\nPS\u003e./ADTimeline -customgroups $customgroups\n```\nIf you do not want to use a parameter you can also uncomment and edit the following array at the  begining of the script:\n```powershell\n$groupscustom = (\"VIP-group1\",\"ESX-Admis\",\"Tier1-admins\")\n```\n\n# The ADTimeline App for Splunk: \u003ca name=\"theapp\"\u003e\u003c/a\u003e\n\n## Description: \u003ca name=\"descriptionsplk\"\u003e\u003c/a\u003e\n\nThe ADTimeline application for Splunk processes and analyses the Active Directory data collected by the ADTimeline PowerShell script. The app was presented at the 32nd annual FIRST Conference, a recording of the presentation is available [here](https://www.first.org/conference/2020/recordings).\n\nThe app\u0027s \"Getting started\" page will give you the instructions for the import process.\n\nOnce indexed the dashboards provided by the app will help the DFIR analyst to spot some Acitve Directory persistence mechanisms, misconfigurations, security audit logging bypass, mail exfiltration, brute force attacks ...\n\nThe app is also packaged and available on [Splunkbase](https://splunkbase.splunk.com/app/4897/). It has no prerequisite and will work with a [free Splunk](https://docs.splunk.com/Documentation/Splunk/latest/Admin/MoreaboutSplunkFree) license.\n\n![Splunkapp](./SA-ADTimeline.png)\n\n## Sourcetypes: \u003ca name=\"sourcetype\"\u003e\u003c/a\u003e\n\nAfter processing the ADTimeline script you should have two or three files to import in Splunk (%DOMAINFQDN% is the Active Directory fully qualified domain name):\n\n- timeline_%DOMAINFQDN%.csv: The timeline generated with the AD replication metadata of objects retrieved. The corresponding source type is *adtimeline*.\n- ADobjects_%DOMAINFQDN%.xml: Objects of interest retrieved via LDAP. The corresponding sourcetype is *adobjects*.\n- gcADobjects_%DOMAINFQDN%.xml: If any, objects of interest retrieved via the Global Catalog. The corresponding source type is *gcobjects*.\n\n### The adtimeline sourcetype:\n\n The *adtimeline* sourcetype is the data from the timeline_%DOMAINFQDN%.csv file, which is the Active Directory timeline built with replication metadata for objects considered of interest.\n\nThe timestamp value is the ftimeLastOriginatingChange value of the replication metadata, which is the time the attribute was last changed, time is UTC.\n\nThe extracted fields are:\n\n- Name: LDAP object name.\n- pszAttributeName: The attribute name.\n- dwVersion: Counter incremented every time the attribute is changed.\n- DN: LDAP object DistinguishedName.\n- WhenCreated: LDAP object creation time.\n- ObjectClass and ObjectCategory: LDAP object type (user, computer, group...)\n- SamAccountName and SID: Account Name and security identifier, only applies to users, computers and groups.\n- usnOriginatingChange: USN on the originating server at which the last change to this attribute was made.\n- pszLastOriginatingDsaDN: DC on which the last change was made to this attribute.\n- uuidLastOriginatingDsaInvocationID: ID corresponding to the pszLastOriginatingDsaDN.\n- usnLocalChange: USN on the destination server (the server your LDAP bind is made) at which the last change to this attribute was applied.\n- Member: Only applies to the group ObjectClass and when the attribute name is member. Contains the value of the group member DistinguishedName.\n- ftimeCreated: Only applies to group ObjectClass and when the attribute name is member. Contains the time the member was added in the group.\n- ftimeDeleted: Only applies to group ObjectClass and when the attribute name is member. Contains the time the member was removed from the group.\n\n### The adobjects sourcetype:\n\nThe *adobjects* sourcetype is the data from the ADobjects_%DOMAINFQDN%.xml file, which is an export of the Active Directory objects considered of interested and retrieved via the LDAP protocol.\n\nThe timestamp value is the createTimeStamp attribute value, time zone is specified in the attribute value.\n\nThe extracted fields are:\n\n- Name: LDAP object name.\n- DN: LDAP object DistinguishedName.\n- DisplayName: LDAP object displayname.\n- WhenCreated: LDAP object creation time.\n- ObjectClass and ObjectCategory: LDAP object type (user, computer, group...)\n- SamAccountName and SID: Account Name and security identifier, only applies to users, computers and groups.\n- Members and MemberOf: Members of a group ObjectClass can be users, computers or groups and its linked attribute MemberOf which applies to groups, users and computers.\n- Owner, AccessToString and SDDL: Are values computed from the nTSecurityDescriptor attribute\n- adminCount: Privileged accounts protected by the SDProp process.\n- userAccountControl: Attribute which contains a range of flags which define some important basic properties of a computer or user object.\n- lastLogonTimestamp: This attribute is not updated with all logon types or at every logon but is replicated and gives you an idea of wether a user or computer account has recently logged on to the domain.\n- dNSHostName: DNS hostname attribute of a computer account.\n- SPNs: List of Service Principal Names of a computer or user account.\n\n### The gcobjects sourcetype:\n\nThe *gcobjects* sourcetype is the data from the gcADobjects_%DOMAINFQDN%.xml file, which is an export of the Active Directory objects within the forest but outside the current domain and considered of interested, those objects are retrieved via the Global Catalog protocol.\n\nThe timestamp value is the WhenCreated attribute value, time zone is UTC.\n\nThe extracted fields are:\n\n- Name: LDAP object name.\n- DN: LDAP object DistinguishedName.\n- DisplayName: LDAP object displayname.\n- WhenCreated: LDAP object creation time.\n- ObjectClass and ObjectCategory: LDAP object type (user, computer, group...)\n- SamAccountName and SID: Account Name and security identifier, only applies to users, computers and groups.\n- userAccountControl: Attribute which contains a range of flags which define some important basic properties of a computer or user object.\n- lastLogonTimestamp: This attribute is not updated with all logon types or at every logon but is replicated and gives you an idea if a user or computer account has recently logged onto the domain.\n- dNSHostName: DNS hostname attribute of a computer account.\n- SPNs: List of Service Principal Names of a computer or user account.\n\n## AD General information dashboards: \u003ca name=\"infradashboards\"\u003e\u003c/a\u003e\n\n### The Active Directory Infrastructure dashboard:\n\nThis dashboard analyses Adtimeline data in order to create some panels giving you information on the Windows domain infrastructure.\n\nThe different panels are:\n\n- General information: Information about the Schema version and functional levels. Depending on the result some AD security features may or may not be available. The Domain Controllers are also listed in this panel\n- Microsoft infrastructure products: Tells you if some important Microsoft Infrastructure components such as Exchange on premises, Active Directory Federation Services or Active Directory Certificate Services are installed. Please consider monitoring events related to those services (MSExchange CmdletLogs, ADFS auditing...)\n- Domain Trusts: List domain trusts by type and direction. Run ADTimeline on all your trusted domains, but most importantly make sure they are audited, monitored and secured as rigorously as the domain you are analyzing.\n- ADDS security features: Tells you if some security features are enabled or not. First feature is the AD Recycle bin which gives the administrator the ability to easily recover deleted objects, it will also change the time after an object is removed from the AD database after deletion. Second feature tells you if the schema extension for the Local Admin Password Solution was performed, if yes sysadmins can enable password randomization for local administrators accounts in order to mitigate lateral movement. Another feature is authentication silos which can help to restrict privileged user account logons in order to mitigate privilege escalation by implementing a tiered administrative model. The last feature is the Protected Users group, with a DFL 2012R2 or more the members of this group receive some additional hardening\n- Service Connection Points: Inventory of serviceConnectionPoint (SCP) object class. SCP make it easy for a service to publish service-specific data in the directory Clients of the service use the data in an SCP to locate an instance of the service. Infrastructure assets such as RDS Gateway, SCCM, VMWare Vcenter, some Backup solutions publish an SCP in the directory.\n- Active Directory infrastructure timeline: Displays a timeline of the infrastructure changes listed above. This timeline tells you the story of the evolution of your infrastructure.\n\n### The sensitive accounts dashboard:\n\nThis dashboard provides an inventory of the privileged accounts in the domain and accounts prone to common attack scenarios due to their configuration.\n\n The different panels are:\n\n- Admin Accounts: This panel lists the accounts where the Admincount attribute value equals 1. Those accounts have their ACL protected by the SDProp process and it means the account has or had at some point high privileges in Active Directory. The first table lists them and provides some information about the accounts, the second table displays a timeline of modifications for some attributes of these accounts.\n- Accounts sensitive to Kerberoast attacks: Kerberoasting is an attack method that allows an attacker to crack the passwords of service accounts in Active Directory offline. The chart is a ratio of accounts prone to this attack and whether or not they are privileged accounts. The table lists them and provides some information about the accounts. Use least privilege principle for those accounts and consider using Group Managed Service Accounts.\n- Accounts sensitive to AS-REP Roast attacks: AS-REP Roast is an attack method that allows an attacker to crack the passwords of accounts in Active Directory offline. The chart is a ratio of accounts prone to this attack and whether or not they are privileged accounts. The table lists them and provides some information about the accounts. Use least privilege principle for those accounts.\n- Sensitive default accounts: Some general information about the default administrator, guest and krbtgt accounts. Administrator can be disabled or renamed as a measure against account lockout. Guest account must be disabled and krbtgt password should be changed on a regular schedule.\n- Accounts trusted for delegation: Kerberos Delegation is a feature that allows an application to reuse the end-user credentials to access resources hosted on a different server. An account trusted for unconstrained delegation is allowed to impersonate almost any user to any service within the network, whereas an account trusted for constrained delegation is allowed to impersonate almost any user for a given service within the network. The chart is a ratio of accounts trusted for constrained/unconstrained delegation. The tables list those accounts, the service name is given for accounts trusted for constrained delegation. A table listing objects with resource based constrained delegation configured is also displayed\n\n## AD threat hunting dashboards: \u003ca name=\"threathuntdashboards\"\u003e\u003c/a\u003e\n\n### The investigate timeframe dashboard:\n\nUse this dashboard to investigate a particular timeframe.\n\n The different panels are:\n\n- AD Timeline: A table displaying the timeline for the given timeframe.\n- Global stats: Global statistics on modifications occurring during the given timeframe, including modifications by ObjectClass, by pszAttributeName, by Originating DC, by time (i.e. day of the week or hour of the day) and finally stats on deletions by ObjectClass.\n- Items created and deleted within timeframe: A table displaying the creations and deletions of the same object within the given timeframe. A first chart gives you stats about object lifetimes in hours and a second one figures by ObjectClass.\n- Objects added or removed from groups or ACL modifications within timeframe: This table focuses on the Member and nTSecurityDescriptor attributes, which can help detect an elevation of privilege for a specific account or a backdoor setup by the attacker, the DistinguishedName value of the member and the time the member was added or removed from the group is given in that table. Which makes it more detailed than the above AD Timeline panel. A chart displaying nTSecurityDescriptor modifications by ObjectClass and another displaying the number of times an object was added or removed from a group are given\n- GPOs modifications within timeframe: A GPO can used by an attacker in various ways, for example to inject malicious code in logon/startup scripts, deploy malware at scale with an immediate scheduled task, setup a backdoor by modifying the nTSecurityDescriptor... For each attribute modification this table gives you the current client side extensions of the GPO and where the object is linked (OU, site or domain root).\n\n### The track suspicious activity dashboard\n\nThis dashboard analyses the Active Directory timeline and highlights some modifications which can be a sign of a suspicious activity, the modifications spoted can also be legitimate and need a triage analysis.\n\n The different panels are:\n\n- ACL modifications: This panel does not replace a thorough analysis of the Active Directory permissions with tools such as AD Control Paths. The panel contains a graph displaying a weekly timeline of ACL modifications per ObjectClass which occured one year back, some tables are focusing on the domain root and AdminSDHolder objects where permissions can be used as backdoor by an attacker. Finally, some statistics by ObjectClass and by least frequent owners are displayed.\n- Accounts: This panel show account modifications which can a be sign of suspicious activity such as users added and removed from groups, some charts provide stats by number of times the account was added or removed, membership time in days, Organizational unit where accounts are located (an account from the \"non_privileged_users\" OU added and removed from a privileged group can be a sign of suspicious activity). There are some graphs, the first graph shows a timeline of accounts lockouts in order to highlight brute force attacks, the second graph shows SID history editions which can be suspicious outside a domain migration period, the next graph analyses all the different attributes modified on an account during a password change (supplementalCredentials, lmPwdHistory, unicodePwd...) and checks they are modified at the same time. A table displays resource based constrained delegation setup on privileged account or domain controller computer objects, which can be a backdoor setup by an attacker. Finally a chart displays domain controller computer objects password change frequency, an attacker could modify the DC registry to disable computer password change and use this password as a backdoor.\n- GPOs: A table of GPOs modifications having an audit client side extension is displayed, an attacker could change the audit settings on the domain to perform malicious actions with stealth. Finally modifications which could result in a GPO processing malfunctioning are displayed, this includes gPCFunctionalityVersion, gPCFileSysPath or versionNumber attribute modification.\n- DCshadow detection: The DCshadow is an attack which allows an attacker to push modifications in Active Directory and bypass traditional alerting by installing a fake DC. It was first presented by Vincent Le Toux and Benjamin Delpy at the BlueHat IL 2018 conference. The first graph will try to detect the installation of the fake DC by analyzing server and nTDSDSA ObjectClass. The two following tables will try to detect replication metadata tampering by analyzing usnOriginatingChange and usnLocalChange values which should increment through the time.\n- Schema and configuration partition suspicious modifications: The first graph displays Active Directory attribute modifications related to the configuration and schema partitions which can lower the security of the domain, used as backdoor by an attacker or hide information to the security team. The second graph is relevant if you have Exchange on premises and track modifications in the configuration partition which can be a sign of mail exfiltration.\n\n### The track suspicious Exchange activity dashboard\n\nThis dashboard analyses your Exchange onprem Active Directory objects and highlights some modifications which can be a sign of a suspicious activity, the modifications spotted can also be legitimate and need a triage analysis.\n\n The different panels are:\n\n- Microsoft Exchange infrastructure: Exchange version and servers.\n- Possible mail exfiltration: Mail forwarders setup on mailboxes, transport rules, remote domains, maibox export requests, content searches...\n- Persistence: RBAC roles and ACL modifications on Exchange objects.\n- MsExchangeSecurityGroups: Timeline and group membership of builtin Exchange security groups.\n- Phishing:Modifications on transport rules related to SCL and disclaimer.\n\n## Enhance your traditional event logs threat hunting with ADTimeline: \u003ca name=\"threathuntevtx\"\u003e\u003c/a\u003e\n\nThe *adobjects* sourcetype is a set of data which can be used to uncover suspicious activity by performing Active Directory educated queries on the Windows event logs. We assume the sourcetype used for event logs is called *winevent* and its *EventData* part has the correct field extraction applied, for example *EventID 4624* has among other fields *TargetUserName* and *TargetUserSid* extracted:\n\n![EVtx](./SA-ADTimeline/appserver/static/images/tuto10.png)\n\nYou can perfrom similar queries with the [Splunk App for Windows Infrastructure](https://docs.splunk.com/Documentation/MSApp/2.0.0/Reference/Aboutthismanual) and the [Splunk Supporting Add-on for Active Directory](https://docs.splunk.com/Documentation/SA-LdapSearch/3.0.0/User/AbouttheSplunkSupportingAdd-onforActiveDirectory). Here are some queries using ADTimeline data and Windows event logs which can help with your threat hunt.\n\n- Statistics on privileged accounts logons:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[ search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as TargetUserSid | fields TargetUserSid ] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullName , values(LogonType) as logonTypes, values(IpAddress) as IpAdresses, values(Computer) as Computers, dc(Computer) as CountComputers, dc(IpAddress) as CountIpAdresses by TargetUserSid\n```\n- Get processes running under a privileged account, [detailed process auditing](https://docs.microsoft.com/en-us/windows-server/identity/ad-ds/manage/component-updates/command-line-process-auditing) should be enabled:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4688\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SamAccountName as TargetUserName | fields TargetUserName] \nOR [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as SubjectUserSid | fields SubjectUserSid] \n|  stats values(CommandLine) by Computer, TargetUserName,SubjectUserName\n```\n- Get all privileged accounts PowerShell activity eventlogs:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"*PowerShell*\"  \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | dedup SamAccountName | return 1000 $SamAccountName]\n```\n- Detect Kerberoasting possible activity:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4769\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt) earliest = 1 latest = now() | rename SID as ServiceSid | fields ServiceSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName  \n|  eval time=strftime(_time,\"%Y-%m-%d %H:%M:%S\") \n|  stats list(ServiceName) as Services, dc(ServiceName) as nbServices, list(time) as time by IpAddress, TicketEncryptionType \n| sort -nbServices\n```\n\n- Detect abnormal processes running under Kerberoastable accounts, [detailed process auditing](https://docs.microsoft.com/en-us/windows-server/identity/ad-ds/manage/component-updates/command-line-process-auditing) should be enabled:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4688\" \n[search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SamAccountName as TargetUserName | fields TargetUserName ] \nOR  [search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SID as SubjectUserSid | fields SubjectUserSid ] \n|  stats values(CommandLine) as cmdlines, values(TargetUserName) as TargetSubjectNames, values(SubjectUserName) as SubjectUserNames by Computer\n```\n- Detect abnormal Kerberoastable user account logons:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects   (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now() | rename SID as TargetUserSid  | fields TargetUserSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n```\n\n- Detect abnormal AS-REP roastable user account logons:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") earliest = 1 latest = now() |  eval eval_asrep_bit = floor(userAccountControl / pow(2, 22)) %2 | search eval_asrep_bit = 1 |  rename SID as TargetUserSid  | fields TargetUserSid]  \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n```\n- Privileged accounts with flag \"cannot be delegated\" not set authenticating against computer configured for unconstrained delegation:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\" earliest = 1 latest = now() | eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0 |   rename dNSHostName as Computer | fields Computer] \n|  search *   [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | eval canbedelagated = round(((userAccountControl / pow(2, 20)) %2), 0) | search canbedelagated = 0 | rename SID as TargetUserSid  | fields TargetUserSid ] \n|  strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName \n| table  _time, Computer, TargetFullName, IpAddress, LogonType, LogonProcessName\n```\n\n- Detect possible [printer bug](https://posts.specterops.io/not-a-security-boundary-breaking-forest-trusts-cd125829518d) triggering:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" TargetUserName = \"*$\" NOT TargetUserSid=\"S-1-5-18\"\n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\"  earliest = 1 latest = now() |  eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0  |  rename dNSHostName as Computer | fields Computer]\n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(LogonType), values(IpAddress), values(LogonProcessName) count by Computer, TargetFullName\n```\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [

                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:21:02Z",
                         "PatternType":  "best_practice",
                         "Id":  "pattern_domain_management_4fccb4e0",
                         "Operation":  "read",
                         "Stars":  2114,
                         "UpdatedAt":  "2025-07-28T16:21:02Z",
                         "HasReadme":  true,
                         "BestPractices":  [
                                               "Repository contains documented best practices"
                                           ],
                         "Title":  "GitHub Repository: AutomatedLab/AutomatedLab",
                         "LastUpdated":  "2025-07-27T07:26:53Z",
                         "CodeTemplate":  "",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  380,
                         "PowerShellFiles":  null,
                         "Domain":  "domain_management",
                         "RepoSize":  39743,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "domain_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: AutomatedLab/AutomatedLab\n\n**Stars:** 2114 | **Language:** PowerShell | **Forks:** 380\n\n**Description:** AutomatedLab is a provisioning solution and framework that lets you deploy complex labs on HyperV and Azure with simple PowerShell scripts. It supports all Windows operating systems from 2008 R2 to 2022, some Linux distributions and various products like AD, Exchange, PKI, IIS, etc.\n\n**URL:** https://github.com/AutomatedLab/AutomatedLab\n\n**Clone URL:** https://github.com/Automat...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:21:02Z",
                                             "Url":  "https://github.com/AutomatedLab/AutomatedLab",
                                             "ScrapedAt":  "2025-07-28T16:21:02Z",
                                             "Id":  "899c493c-6ac4-4113-a08f-49e784095ae2",
                                             "Author":  "AutomatedLab",
                                             "Title":  "GitHub Repository: AutomatedLab/AutomatedLab"
                                         }
                                     ],
                         "Topics":  [
                                        "active",
                                        "active-directory",
                                        "automated-deployment",
                                        "azure",
                                        "deployment",
                                        "directory",
                                        "domain-controller",
                                        "exchange",
                                        "hyper",
                                        "hyperv",
                                        "lab-machine",
                                        "pki",
                                        "powershell",
                                        "scripted",
                                        "scripted-deployment"
                                    ],
                         "Content":  "# Repository: AutomatedLab/AutomatedLab\n\n**Stars:** 2114 | **Language:** PowerShell | **Forks:** 380\n\n**Description:** AutomatedLab is a provisioning solution and framework that lets you deploy complex labs on HyperV and Azure with simple PowerShell scripts. It supports all Windows operating systems from 2008 R2 to 2022, some Linux distributions and various products like AD, Exchange, PKI, IIS, etc.\n\n**URL:** https://github.com/AutomatedLab/AutomatedLab\n\n**Clone URL:** https://github.com/AutomatedLab/AutomatedLab.git\n\n**Topics:** active, active-directory, automated-deployment, azure, deployment, directory, domain-controller, exchange, hyper, hyperv, lab-machine, pki, powershell, scripted, scripted-deployment\n\n## README\n\n# AutomatedLab\n\nBuild | Status | Last Commit | Latest Release\n--- | --- | --- | ---\nDevelop | [![Build status dev](https://ci.appveyor.com/api/projects/status/9yynk81k3k05nasp/branch/develop?svg=true)](https://ci.appveyor.com/project/automatedlab/automatedlab) | [![GitHub last commit](https://img.shields.io/github/last-commit/AutomatedLab/AutomatedLab/develop.svg)](https://github.com/AutomatedLab/AutomatedLab/tree/develop/)\nMaster | [![Build status](https://ci.appveyor.com/api/projects/status/9yynk81k3k05nasp/branch/master?svg=true)](https://ci.appveyor.com/project/automatedlab/automatedlab) | [![GitHub last commit](https://img.shields.io/github/last-commit/AutomatedLab/AutomatedLab/master.svg)](https://github.com/AutomatedLab/AutomatedLab/tree/master/) | [![GitHub release](https://img.shields.io/github/release/AutomatedLab/AutomatedLab.svg)](https://github.com/AutomatedLab/AutomatedLab/releases)[![PowerShell Gallery](https://img.shields.io/powershellgallery/v/AutomatedLab.svg)](https://www.powershellgallery.com/packages/AutomatedLab/)\n\n[![GitHub issues](https://img.shields.io/github/issues/AutomatedLab/AutomatedLab.svg)](https://github.com/AutomatedLab/AutomatedLab/issues)\n[![Downloads](https://img.shields.io/github/downloads/AutomatedLab/AutomatedLab/total.svg?label=Downloads\u0026maxAge=999)](https://github.com/AutomatedLab/AutomatedLab/releases)\n[![PowerShell Gallery](https://img.shields.io/powershellgallery/dt/AutomatedLab.svg)](https://www.powershellgallery.com/packages/AutomatedLab/)\n\n## Project Summary\n\nAutomatedLab (AL) enables you to setup test and lab environments on Hyper-v or Azure with multiple products or just a single VM in a very short time. There are only two requirements you need to make sure: You need the DVD ISO images and a Hyper-V host or an Azure subscription.\n\n## Sponsors\n\nHuge thanks to our regular sponsor [@chocolatey](https://github.com/chocolatey)! In case you have lived under a rock (or worked mainly on non-Windows systems) for the past decade, Chocolatey\nis a brilliant package management system with a public binary package gallery as well as self-hosted options complete with enterprise-grade support. Give it a try with\na local lab environment - it could not be easier 😊 Start with a Domain Controller and a Certificate Authority to validate not only package distribution, but also digital signatures. Our samples\nsuch as the [NuGet server](https://automatedlab.org/en/latest/Wiki/SampleScripts/Scenarios/en-us/NuGetServer/) or [Azure DevOps](https://github.com/dsccommunity/DscWorkshop/tree/main/Lab) all include galleries that Chocolatey can use for publishing and downloading packages.\n\n### Requirements\n\nApart from the module itself your system needs to meet the following requirements:\n\n- Windows Management Framework 5+ (Windows)\n- .NET 4.7.1 (Windows PowerShell) or .NET Core 2.x (PowerShell 6+)\n- OS\n  - Windows Server 2012 R2+/Windows 8.1+ (Hyper-V, Azure)\n  - Linux (Azure)\n  - macOS (Azure, best-effort support since neither @raandree nor @nyanhp have Apple devices)\n- Recommended OS language is en-us\n- Admin privileges are required on Windows\n- ISO files for all operating systems (Hyper-V only) and roles to be deployed (Hyper-V, Azure)\n- Intel VT-x or AMD/V capable CPU\n- A decent amount of RAM\n- Low-Latency high-throughput storage (No spinning disks please, as there are issues related to them)\n\n#### Windows\n\n- Windows Management Framework 5+ or ideally PowerShell 7\n- Windows Server 2012 R2+/Windows 8.1+\n- Recommended OS language is en-us\n- Admin privileges are required\n\n#### Linux, macOS\n\n- Fedora, Ubuntu, Ubuntu WSL \u0026 Azure Cloud Shell supported\n- macOS supported on best effort due to lack of Apple devices - feel free to sponsor two though :D\n- Tested on Ubuntu and Fedora. Due to fragmented nature of Linux distributions, we cannot support much else.\n- PowerShell Core 6+\n- SSH or gss-ntlmssp to enable remoting (*mandatory - no remoting, no way for AutomatedLab to do its thing*)\n  - If in doubt, try to `Install-Module PSWSMAN; Install-WSMAN` - no success warranted\n- IP and route commands available\n- **Azure subscription**\n  - At the moment, AutomatedLab only works using Azure when using Linux.\n  - KVM planned for a later date by virtue of libvirt.\n\n### Download AutomatedLab\n\nThere are two options installing AutomatedLab:\n\n- You can use the [MSI installer](https://github.com/AutomatedLab/AutomatedLab/releases) published on GitHub.\n- Or you install from the [PowerShell Gallery](https://www.powershellgallery.com/packages/AutomatedLab/) using the cmdlet Install-Module. Please refer to the [wiki](https://automatedlab.org/en/latest) for some details.\n\n### [1. Installation](https://automatedlab.org/en/latest/Wiki/Basic/install/)\n\n### [2. Getting started](https://automatedlab.org/en/latest/Wiki/Basic/gettingstarted/)\n\n### [3. Contributing](/CONTRIBUTING.md)\n\n### [Version History](/CHANGELOG.md)\n\n### Supported products\n\nThis solution supports setting up virtual machines with the following products\n\n- Windows 7, 2008 R2, 8 / 8.1 and 2012 / 2012 R2, 10 / 2016, 2019, 2022\n- SQL Server 2008, 2008R2, 2012, 2014, 2016, 2017, 2019\n- Visual Studio 2012, 2013, 2015\n- Team Foundation Services 2018, Azure DevOps Server\n- Exchange 2013, 2016, 2019\n- System Center Orchestrator 2012\n- System Center Operations Manager 2019\n- System Center Service Manager 2019\n- Microsoft Endpoint Manager Configuration Manager 1902 (and newer)\n- MDT\n- ProGet (Private PowerShell Gallery)\n- Office 2013, 2016, 2019\n- DSC Pull Server (with SQL Reporting)\n- Dynamics 365\n- Remote Desktop Services including HTML5 web client\n\n### Feature List\n\n- AutomatedLab (AL) makes the setup of labs extremely easy. Setting up a lab with just a single machine is [only 3 lines](/LabSources/SampleScripts/Introduction/01%20Single%20Win10%20Client.ps1). And even [complex labs](/LabSources/SampleScripts/HyperV/BigLab%202012R2%20EX%20SQL%20ORCH%20VS%20OFF.ps1) can be defined with about 100 lines (see [sample scripts](https://github.com/AutomatedLab/AutomatedLab/tree/master/LabSources/SampleScripts)).\n- Labs on Azure can be connected to each other or connected to a Hyper-V lab [using a single command](https://github.com/AutomatedLab/AutomatedLab/wiki/Connect-on-premises-and-cloud-labs).\n- AL can be used to setup scenarios to demo a [PowerShell Gallery using Inedo ProGet](/LabSources/SampleScripts/Scenarios/ProGet%20Lab%20-%20HyperV.ps1), [PowerShell DSC Pull Server scenarios](/LabSources/SampleScripts/Scenarios/DSC%20Pull%20Scenario%201%20(Pull%20Configuration).ps1), ADFS or a lab with [3 Active Directory forests trusting each other](/LabSources/SampleScripts/Scenarios/Multi-AD%20Forest%20with%20Trusts.ps1).\n- Create, restore and remove snapshots of some or all lab machines with one cmdlet (Checkpoint-LabVM, Restore-LabVMSnapshot, Remove-LabVMSnapshot).\n- Install Windows Features on one, some or all lab machines with one line of code (Install-LabWindowsFeature).\n- Install software to a bunch of lab machines with just one cmdlet (Install-LabSoftwarePackages). You only need to know the argument to make the MSI or EXE go into silent installation mode. This can also work in parallel thanks to PowerShell workflows.\n- Run any custom activity (Script or ScriptBlock) on a number of lab machines (Invoke-LabCommand). You do not have to care about credentials or double-hop authentication issues as CredSsp is always enabled and can be used with the UseCredSsp switch.\n- Creating a [virtual environment that is connected to the internet](/LabSources/SampleScripts/Introduction/05%20Single%20domain-joined%20server%20(internet%20facing).ps1) was never easier. The only requirements are defining an external facing virtual switch and a machine with two network cards that acts as the router. AL takes care about all the configuration details like setting the gateway on all machines and also the DNS settings (see introduction script [05 Single domain-joined server (internet facing).ps1](/LabSources/SampleScripts/Introduction/05%20Single%20domain-joined%20server%20(internet%20facing).ps1)).\n- AL offers offline patching with a single command. As all machines a based on one disk per OS, it is much more efficient to patch the ISO files that are used to create the base images (Update-LabIsoImage). See script [11 ISO Offline Patching.ps1](/LabSources/SampleScripts/Introduction/11%20ISO%20Offline%20Patching.ps1) for more details.\n- If a lab is no longer required, one command is enough to remove everything to be ready to start from scratch (Remove-Lab)\n\n### Local build\n\nWhile we frequently release prereleases to the PowerShell Gallery, you might be interested\nto build the entire module locally yourself.\n\nWhile the steps remain the same, the prerequisites are slightly different on Windows and Linux\n\nWindows:\n  - WiX 3 targets installed properly (!)\n  - .NET SDKs 4.6.2 and 6.0\nLinux:\n  - .NET 6.0\n\nAfter the prerequisites are satisfied, you can:\n- `./.build/01-prerequisites.ps1`\n- `./.build/02-build.ps1`\n- `./.build/03-validate.ps1` - Optionally validate the built module\n\nThe fourth step, publishing, relies on AppVeyor. The built module will be stores in `./publish`,\nthe installer can be found in `./Install/bin\n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "﻿$ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n$PrimaryModule = Get-ChildItem -Path $PSScriptRoot -Filter \u0027*.psd1\u0027 -Recurse -ErrorAction SilentlyContinue -Depth 1\nif (-not $PrimaryModule) {\n    throw \"Path $PSScriptRoot doesn\u0027t contain PSD1 files. Failing tests.\"\n}\nif ($PrimaryModule.Count -ne 1) {\n    throw \u0027More than one PSD1 files detected. Failing tests.\u0027\n}\n$PSDInformation = Import-PowerShellDataFile -Path $PrimaryModule.FullName\n$RequiredModules = @(\n    \u0027Pester\u0027\n    \u0027PSWriteColor\u0027\n    \u0027PSParseHTML\u0027\n    if ($PSDInformation.RequiredModules) {\n        $PSDInformation.RequiredModules\n    }\n)\nforeach ($Module in $RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        $Exists = Get-Module -ListAvailable -Name $Module.ModuleName\n        if (-not $Exists) {\n            Write-Warning \"$ModuleName - Downloading $($Module.ModuleName) from PSGallery\"\n            Install-Module -Name $Module.ModuleName -Force -SkipPublisherCheck\n        }\n    } else {\n        $Exists = Get-Module -ListAvailable $Module -ErrorAction SilentlyContinue\n        if (-not $Exists) {\n            Install-Module -Name $Module -Force -SkipPublisherCheck\n        }\n    }\n}\n\nWrite-Color \u0027ModuleName: \u0027, $ModuleName, \u0027 Version: \u0027, $PSDInformation.ModuleVersion -Color Yellow, Green, Yellow, Green -LinesBefore 2\nWrite-Color \u0027PowerShell Version: \u0027, $PSVersionTable.PSVersion -Color Yellow, Green\nWrite-Color \u0027PowerShell Edition: \u0027, $PSVersionTable.PSEdition -Color Yellow, Green\nWrite-Color \u0027Required modules: \u0027 -Color Yellow\nforeach ($Module in $PSDInformation.RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        Write-Color \u0027   [\u003e] \u0027, $Module.ModuleName, \u0027 Version: \u0027, $Module.ModuleVersion -Color Yellow, Green, Yellow, Green\n    } else {\n        Write-Color \u0027   [\u003e] \u0027, $Module -Color Yellow, Green\n    }\n}\nWrite-Color\n\nImport-Module $PSScriptRoot\\*.psd1 -Force\nImport-Module Pester -Force\n$Configuration = [PesterConfiguration]::Default\n$Configuration.Run.Path = \"$PSScriptRoot\\Tests\"\n$Configuration.Run.Exit = $true\n$Configuration.Should.ErrorAction = \u0027Continue\u0027\n$Configuration.CodeCoverage.Enabled = $false\n$Configuration.Output.Verbosity = \u0027Detailed\u0027\n$Result = Invoke-Pester -Configuration $Configuration\n#$result = Invoke-Pester -Script $PSScriptRoot\\Tests -Verbose -Output Detailed #-EnableExit\n\nif ($Result.FailedCount -gt 0) {\n    throw \"$($Result.FailedCount) tests failed.\"\n}",
                                               "﻿@{\n    AliasesToExport      = @(\u0027Get-GPOZaurrSysvol\u0027, \u0027Get-GPOZaurrFilesPolicyDefinitions\u0027, \u0027Show-GPOZaurr\u0027, \u0027Show-GPO\u0027, \u0027Find-GPO\u0027, \u0027Remove-GPOZaurrOrphaned\u0027)\n    Author               = \u0027Przemyslaw Klys\u0027\n    CmdletsToExport      = @()\n    CompanyName          = \u0027Evotec\u0027\n    CompatiblePSEditions = @(\u0027Desktop\u0027)\n    Copyright            = \u0027(c) 2011 - 2024 Przemyslaw Klys @ Evotec. All rights reserved.\u0027\n    Description          = \u0027Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them.\u0027\n    FunctionsToExport    = @(\u0027Add-GPOPermission\u0027, \u0027Add-GPOZaurrPermission\u0027, \u0027Backup-GPOZaurr\u0027, \u0027Clear-GPOZaurrSysvolDFSR\u0027, \u0027ConvertFrom-CSExtension\u0027, \u0027Export-GPOZaurrContent\u0027, \u0027Find-CSExtension\u0027, \u0027Get-GPOZaurr\u0027, \u0027Get-GPOZaurrAD\u0027, \u0027Get-GPOZaurrBackupInformation\u0027, \u0027Get-GPOZaurrBroken\u0027, \u0027Get-GPOZaurrBrokenLink\u0027, \u0027Get-GPOZaurrDictionary\u0027, \u0027Get-GPOZaurrDuplicateObject\u0027, \u0027Get-GPOZaurrFiles\u0027, \u0027Get-GPOZaurrFilesPolicyDefinition\u0027, \u0027Get-GPOZaurrFolders\u0027, \u0027Get-GPOZaurrInheritance\u0027, \u0027Get-GPOZaurrLegacyFiles\u0027, \u0027Get-GPOZaurrLink\u0027, \u0027Get-GPOZaurrLinkSummary\u0027, \u0027Get-GPOZaurrMissingFiles\u0027, \u0027Get-GPOZaurrNetLogon\u0027, \u0027Get-GPOZaurrOrganizationalUnit\u0027, \u0027Get-GPOZaurrOwner\u0027, \u0027Get-GPOZaurrPassword\u0027, \u0027Get-GPOZaurrPermission\u0027, \u0027Get-GPOZaurrPermissionAnalysis\u0027, \u0027Get-GPOZaurrPermissionConsistency\u0027, \u0027Get-GPOZaurrPermissionIssue\u0027, \u0027Get-GPOZaurrPermissionRoot\u0027, \u0027Get-GPOZaurrPermissionSummary\u0027, \u0027Get-GPOZaurrRedirect\u0027, \u0027Get-GPOZaurrSysvolDFSR\u0027, \u0027Get-GPOZaurrUpdates\u0027, \u0027Get-GPOZaurrWMI\u0027, \u0027Invoke-GPOZaurr\u0027, \u0027Invoke-GPOZaurrContent\u0027, \u0027Invoke-GPOZaurrPermission\u0027, \u0027Invoke-GPOZaurrSupport\u0027, \u0027New-GPOZaurrWMI\u0027, \u0027Optimize-GPOZaurr\u0027, \u0027Remove-GPOPermission\u0027, \u0027Remove-GPOZaurr\u0027, \u0027Remove-GPOZaurrBroken\u0027, \u0027Remove-GPOZaurrDuplicateObject\u0027, \u0027Remove-GPOZaurrFolders\u0027, \u0027Remove-GPOZaurrLegacyFiles\u0027, \u0027Remove-GPOZaurrLinkEmptyOU\u0027, \u0027Remove-GPOZaurrPermission\u0027, \u0027Remove-GPOZaurrWMI\u0027, \u0027Repair-GPOZaurrBrokenLink\u0027, \u0027Repair-GPOZaurrNetLogonOwner\u0027, \u0027Repair-GPOZaurrPermission\u0027, \u0027Repair-GPOZaurrPermissionConsistency\u0027, \u0027Restore-GPOZaurr\u0027, \u0027Save-GPOZaurrFiles\u0027, \u0027Set-GPOOwner\u0027, \u0027Set-GPOZaurrOwner\u0027, \u0027Set-GPOZaurrStatus\u0027, \u0027Skip-GroupPolicy\u0027)\n    GUID                 = \u0027f7d4c9e4-0298-4f51-ad77-e8e3febebbde\u0027\n    ModuleVersion        = \u00271.1.9\u0027\n    PowerShellVersion    = \u00275.1\u0027\n    PrivateData          = @{\n        PSData = @{\n            ExternalModuleDependencies = @(\u0027CimCmdlets\u0027, \u0027Microsoft.PowerShell.Management\u0027, \u0027Microsoft.PowerShell.Utility\u0027, \u0027Microsoft.PowerShell.Security\u0027)\n            ProjectUri                 = \u0027https://github.com/EvotecIT/GPOZaurr\u0027\n            Tags                       = @(\u0027Windows\u0027, \u0027ActiveDirectory\u0027, \u0027GPO\u0027, \u0027GroupPolicy\u0027)\n        }\n    }\n    RequiredModules      = @(@{\n            Guid          = \u00270b0ba5c5-ec85-4c2b-a718-874e55a8bc3f\u0027\n            ModuleName    = \u0027PSWriteColor\u0027\n            ModuleVersion = \u00271.0.1\u0027\n        }, @{\n            Guid          = \u0027ee272aa8-baaa-4edf-9f45-b6d6f7d844fe\u0027\n            ModuleName    = \u0027PSSharedGoods\u0027\n            ModuleVersion = \u00270.0.301\u0027\n        }, @{\n            Guid          = \u00279fc9fd61-7f11-4f4b-a527-084086f1905f\u0027\n            ModuleName    = \u0027ADEssentials\u0027\n            ModuleVersion = \u00270.0.226\u0027\n        }, @{\n            Guid          = \u0027a7bdf640-f5cb-4acf-9de0-365b322d245c\u0027\n            ModuleName    = \u0027PSWriteHTML\u0027\n            ModuleVersion = \u00271.27.0\u0027\n        }, \u0027CimCmdlets\u0027, \u0027Microsoft.PowerShell.Management\u0027, \u0027Microsoft.PowerShell.Utility\u0027, \u0027Microsoft.PowerShell.Security\u0027)\n    RootModule           = \u0027GPOZaurr.psm1\u0027\n}",
                                               "﻿#Get public and private function definition files.\n$Public = @( Get-ChildItem -Path $PSScriptRoot\\Public\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Private = @( Get-ChildItem -Path $PSScriptRoot\\Private\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n\n$AssemblyFolders = Get-ChildItem -Path $PSScriptRoot\\Lib -Directory -ErrorAction SilentlyContinue\nif ($AssemblyFolders.BaseName -contains \u0027Standard\u0027) {\n    $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Standard\\*.dll -ErrorAction SilentlyContinue )\n} else {\n    if ($PSEdition -eq \u0027Core\u0027) {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Core\\*.dll -ErrorAction SilentlyContinue )\n    } else {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Default\\*.dll -ErrorAction SilentlyContinue )\n    }\n}\n$FoundErrors = @(\n    Foreach ($Import in @($Assembly)) {\n        try {\n            Add-Type -Path $Import.Fullname -ErrorAction Stop\n        } catch [System.Reflection.ReflectionTypeLoadException] {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        } catch {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        }\n    }\n    #Dot source the files\n    Foreach ($Import in @($Private + $Public)) {\n        Try {\n            . $Import.Fullname\n        } Catch {\n            Write-Error -Message \"Failed to import functions from $($import.Fullname): $_\"\n            $true\n        }\n    }\n)\n\nif ($FoundErrors.Count -gt 0) {\n    $ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n    Write-Warning \"Importing module $ModuleName failed. Fix errors before continuing.\"\n    break\n}\n\nExport-ModuleMember -Function \u0027*\u0027 -Alias \u0027*\u0027"
                                           ],
                         "RelevanceScore":  0.86219387204838949,
                         "CreatedAt":  "2025-07-28T16:21:07Z",
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_user_management_154a279f",
                         "Operation":  "read",
                         "Stars":  1048,
                         "UpdatedAt":  "2025-07-28T16:21:07Z",
                         "HasReadme":  true,
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub Repository: EvotecIT/GPOZaurr",
                         "LastUpdated":  "2025-07-17T07:58:09Z",
                         "CodeTemplate":  "﻿$ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n$PrimaryModule = Get-ChildItem -Path $PSScriptRoot -Filter \u0027*.psd1\u0027 -Recurse -ErrorAction SilentlyContinue -Depth 1\nif (-not $PrimaryModule) {\n    throw \"Path $PSScriptRoot doesn\u0027t contain PSD1 files. Failing tests.\"\n}\nif ($PrimaryModule.Count -ne 1) {\n    throw \u0027More than one PSD1 files detected. Failing tests.\u0027\n}\n$PSDInformation = Import-PowerShellDataFile -Path $PrimaryModule.FullName\n$RequiredModules = @(\n    \u0027Pester\u0027\n    \u0027PSWriteColor\u0027\n    \u0027PSParseHTML\u0027\n    if ($PSDInformation.RequiredModules) {\n        $PSDInformation.RequiredModules\n    }\n)\nforeach ($Module in $RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        $Exists = Get-Module -ListAvailable -Name $Module.ModuleName\n        if (-not $Exists) {\n            Write-Warning \"$ModuleName - Downloading $($Module.ModuleName) from PSGallery\"\n            Install-Module -Name $Module.ModuleName -Force -SkipPublisherCheck\n        }\n    } else {\n        $Exists = Get-Module -ListAvailable $Module -ErrorAction SilentlyContinue\n        if (-not $Exists) {\n            Install-Module -Name $Module -Force -SkipPublisherCheck\n        }\n    }\n}\n\nWrite-Color \u0027ModuleName: \u0027, $ModuleName, \u0027 Version: \u0027, $PSDInformation.ModuleVersion -Color Yellow, Green, Yellow, Green -LinesBefore 2\nWrite-Color \u0027PowerShell Version: \u0027, $PSVersionTable.PSVersion -Color Yellow, Green\nWrite-Color \u0027PowerShell Edition: \u0027, $PSVersionTable.PSEdition -Color Yellow, Green\nWrite-Color \u0027Required modules: \u0027 -Color Yellow\nforeach ($Module in $PSDInformation.RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        Write-Color \u0027   [\u003e] \u0027, $Module.ModuleName, \u0027 Version: \u0027, $Module.ModuleVersion -Color Yellow, Green, Yellow, Green\n    } else {\n        Write-Color \u0027   [\u003e] \u0027, $Module -Color Yellow, Green\n    }\n}\nWrite-Color\n\nImport-Module $PSScriptRoot\\*.psd1 -Force\nImport-Module Pester -Force\n$Configuration = [PesterConfiguration]::Default\n$Configuration.Run.Path = \"$PSScriptRoot\\Tests\"\n$Configuration.Run.Exit = $true\n$Configuration.Should.ErrorAction = \u0027Continue\u0027\n$Configuration.CodeCoverage.Enabled = $false\n$Configuration.Output.Verbosity = \u0027Detailed\u0027\n$Result = Invoke-Pester -Configuration $Configuration\n#$result = Invoke-Pester -Script $PSScriptRoot\\Tests -Verbose -Output Detailed #-EnableExit\n\nif ($Result.FailedCount -gt 0) {\n    throw \"$($Result.FailedCount) tests failed.\"\n}",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  107,
                         "PowerShellFiles":  3,
                         "Domain":  "user_management",
                         "RepoSize":  1753,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "user_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: EvotecIT/GPOZaurr\n\n**Stars:** 1048 | **Language:** PowerShell | **Forks:** 107\n\n**Description:** Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them. \n\n**URL:** https://github.com/EvotecIT/GPOZaurr\n\n**Clone URL:** https://github.com/EvotecIT/GPOZaurr.git\n\n**Topics:** activedirectory, gpo, group-policy, hacktoberfest, powershell\n\n## README\n\n﻿\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://www.po...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:21:07Z",
                                             "Url":  "https://github.com/EvotecIT/GPOZaurr",
                                             "ScrapedAt":  "2025-07-28T16:21:07Z",
                                             "Id":  "a0a4d178-4d85-4d09-8f2a-6bcfb25c3ff4",
                                             "Author":  "EvotecIT",
                                             "Title":  "GitHub Repository: EvotecIT/GPOZaurr"
                                         }
                                     ],
                         "Topics":  [
                                        "activedirectory",
                                        "gpo",
                                        "group-policy",
                                        "hacktoberfest",
                                        "powershell"
                                    ],
                         "Content":  "# Repository: EvotecIT/GPOZaurr\n\n**Stars:** 1048 | **Language:** PowerShell | **Forks:** 107\n\n**Description:** Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them. \n\n**URL:** https://github.com/EvotecIT/GPOZaurr\n\n**Clone URL:** https://github.com/EvotecIT/GPOZaurr.git\n\n**Topics:** activedirectory, gpo, group-policy, hacktoberfest, powershell\n\n## README\n\n﻿\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/v/GPOZaurr.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/vpre/GPOZaurr.svg?label=powershell%20gallery%20preview\u0026colorB=yellow\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/github/license/EvotecIT/GPOZaurr.svg\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/p/GPOZaurr.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/github/languages/top/evotecit/GPOZaurr.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/github/languages/code-size/evotecit/GPOZaurr.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/dt/GPOZaurr.svg\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://twitter.com/PrzemyslawKlys\"\u003e\u003cimg src=\"https://img.shields.io/twitter/follow/PrzemyslawKlys.svg?label=Twitter%20%40PrzemyslawKlys\u0026style=social\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://evotec.xyz/hub\"\u003e\u003cimg src=\"https://img.shields.io/badge/Blog-evotec.xyz-2A6496.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.linkedin.com/in/pklys\"\u003e\u003cimg src=\"https://img.shields.io/badge/LinkedIn-pklys-0077B5.svg?logo=LinkedIn\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n# GPOZaurr\n\nGroup Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them.\n**GPOZaurr** provides 360 degrees of information about Group Policies and their settings.\n\nJust a single command (`Invoke-GPOZaurr`) provides following reports:\n\n- GPOBroken\n- GPOBrokenLink\n- GPOOwners\n- GPOConsistency\n- GPODuplicates\n- GPOOrganizationalUnit\n- GPOList\n- GPOLinks\n- GPOPassword\n- GPOPermissions\n- GPOPermissionsAdministrative\n- GPOPermissionsRead\n- GPOPermissionsRoot\n- GPOPermissionsUnknown\n- GPOFiles\n- GPOBlockedInheritance\n- GPOAnalysis\n- GPOUpdates\n- NetLogonOwners\n- NetLogonPermissions\n- SysVolLegacyFiles\n\nBut that\u0027s not all.\nThere are over 50 other commands available that make it even more powerful helping with day to day tasks to manage Group Policies.\n\nTo understand the usage of `Invoke-GPOZaurr` I\u0027ve created blog post you may find useful\n\n- [The only command you will ever need to understand and fix your Group Policies (GPO)](https://evotec.xyz/the-only-command-you-will-ever-need-to-understand-and-fix-your-group-policies-gpo/)\n\n## Installing\n\nGPOZaurr requires `RSAT` installed to provide results. If you don\u0027t have them you can install them as below. Keep in mind it also installs GUI tools so it shouldn\u0027t be installed on user workstations.\n\n```powershell\n# Windows 10 Latest\nAdd-WindowsCapability -Online -Name \u0027Rsat.ActiveDirectory.DS-LDS.Tools~~~~*******\u0027\nAdd-WindowsCapability -Online -Name \u0027Rsat.GroupPolicy.Management.Tools~~~~*******\u0027\n```\n\nFinally just install module:\n\n```powershell\nInstall-Module -Name GPOZaurr -AllowClobber -Force\n```\n\nForce and AllowClobber aren\u0027t necessary, but they do skip errors in case some appear.\n\n## Updating\n\n```powershell\nUpdate-Module -Name GPOZaurr\n```\n\nThat\u0027s it. Whenever there\u0027s a new version, you run the command, and you can enjoy it. Remember that you may need to close, reopen PowerShell session if you have already used module before updating it.\n\n**The essential thing** is if something works for you on production, keep using it till you test the new version on a test computer. I do changes that may not be big, but big enough that auto-update may break your code. For example, small rename to a parameter and your code stops working! Be responsible!\n\n## PowerShell Files (3 files)\n\n### GPOZaurr.Tests.ps1\n\n**Path:** GPOZaurr.Tests.ps1\n\n``powershell\n﻿$ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n$PrimaryModule = Get-ChildItem -Path $PSScriptRoot -Filter \u0027*.psd1\u0027 -Recurse -ErrorAction SilentlyContinue -Depth 1\nif (-not $PrimaryModule) {\n    throw \"Path $PSScriptRoot doesn\u0027t contain PSD1 files. Failing tests.\"\n}\nif ($PrimaryModule.Count -ne 1) {\n    throw \u0027More than one PSD1 files detected. Failing tests.\u0027\n}\n$PSDInformation = Import-PowerShellDataFile -Path $PrimaryModule.FullName\n$RequiredModules = @(\n    \u0027Pester\u0027\n    \u0027PSWriteColor\u0027\n    \u0027PSParseHTML\u0027\n    if ($PSDInformation.RequiredModules) {\n        $PSDInformation.RequiredModules\n    }\n)\nforeach ($Module in $RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        $Exists = Get-Module -ListAvailable -Name $Module.ModuleName\n        if (-not $Exists) {\n            Write-Warning \"$ModuleName - Downloading $($Module.ModuleName) from PSGallery\"\n            Install-Module -Name $Module.ModuleName -Force -SkipPublisherCheck\n        }\n    } else {\n        $Exists = Get-Module -ListAvailable $Module -ErrorAction SilentlyContinue\n        if (-not $Exists) {\n            Install-Module -Name $Module -Force -SkipPublisherCheck\n        }\n    }\n}\n\nWrite-Color \u0027ModuleName: \u0027, $ModuleName, \u0027 Version: \u0027, $PSDInformation.ModuleVersion -Color Yellow, Green, Yellow, Green -LinesBefore 2\nWrite-Color \u0027PowerShell Version: \u0027, $PSVersionTable.PSVersion -Color Yellow, Green\nWrite-Color \u0027PowerShell Edition: \u0027, $PSVersionTable.PSEdition -Color Yellow, Green\nWrite-Color \u0027Required modules: \u0027 -Color Yellow\nforeach ($Module in $PSDInformation.RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        Write-Color \u0027   [\u003e] \u0027, $Module.ModuleName, \u0027 Version: \u0027, $Module.ModuleVersion -Color Yellow, Green, Yellow, Green\n    } else {\n        Write-Color \u0027   [\u003e] \u0027, $Module -Color Yellow, Green\n    }\n}\nWrite-Color\n\nImport-Module $PSScriptRoot\\*.psd1 -Force\nImport-Module Pester -Force\n$Configuration = [PesterConfiguration]::Default\n$Configuration.Run.Path = \"$PSScriptRoot\\Tests\"\n$Configuration.Run.Exit = $true\n$Configuration.Should.ErrorAction = \u0027Continue\u0027\n$Configuration.CodeCoverage.Enabled = $false\n$Configuration.Output.Verbosity = \u0027Detailed\u0027\n$Result = Invoke-Pester -Configuration $Configuration\n#$result = Invoke-Pester -Script $PSScriptRoot\\Tests -Verbose -Output Detailed #-EnableExit\n\nif ($Result.FailedCount -gt 0) {\n    throw \"$($Result.FailedCount) tests failed.\"\n}\n``n\n### GPOZaurr.psd1\n\n**Path:** GPOZaurr.psd1\n\n``powershell\n﻿@{\n    AliasesToExport      = @(\u0027Get-GPOZaurrSysvol\u0027, \u0027Get-GPOZaurrFilesPolicyDefinitions\u0027, \u0027Show-GPOZaurr\u0027, \u0027Show-GPO\u0027, \u0027Find-GPO\u0027, \u0027Remove-GPOZaurrOrphaned\u0027)\n    Author               = \u0027Przemyslaw Klys\u0027\n    CmdletsToExport      = @()\n    CompanyName          = \u0027Evotec\u0027\n    CompatiblePSEditions = @(\u0027Desktop\u0027)\n    Copyright            = \u0027(c) 2011 - 2024 Przemyslaw Klys @ Evotec. All rights reserved.\u0027\n    Description          = \u0027Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them.\u0027\n    FunctionsToExport    = @(\u0027Add-GPOPermission\u0027, \u0027Add-GPOZaurrPermission\u0027, \u0027Backup-GPOZaurr\u0027, \u0027Clear-GPOZaurrSysvolDFSR\u0027, \u0027ConvertFrom-CSExtension\u0027, \u0027Export-GPOZaurrContent\u0027, \u0027Find-CSExtension\u0027, \u0027Get-GPOZaurr\u0027, \u0027Get-GPOZaurrAD\u0027, \u0027Get-GPOZaurrBackupInformation\u0027, \u0027Get-GPOZaurrBroken\u0027, \u0027Get-GPOZaurrBrokenLink\u0027, \u0027Get-GPOZaurrDictionary\u0027, \u0027Get-GPOZaurrDuplicateObject\u0027, \u0027Get-GPOZaurrFiles\u0027, \u0027Get-GPOZaurrFilesPolicyDefinition\u0027, \u0027Get-GPOZaurrFolders\u0027, \u0027Get-GPOZaurrInheritance\u0027, \u0027Get-GPOZaurrLegacyFiles\u0027, \u0027Get-GPOZaurrLink\u0027, \u0027Get-GPOZaurrLinkSummary\u0027, \u0027Get-GPOZaurrMissingFiles\u0027, \u0027Get-GPOZaurrNetLogon\u0027, \u0027Get-GPOZaurrOrganizationalUnit\u0027, \u0027Get-GPOZaurrOwner\u0027, \u0027Get-GPOZaurrPassword\u0027, \u0027Get-GPOZaurrPermission\u0027, \u0027Get-GPOZaurrPermissionAnalysis\u0027, \u0027Get-GPOZaurrPermissionConsistency\u0027, \u0027Get-GPOZaurrPermissionIssue\u0027, \u0027Get-GPOZaurrPermissionRoot\u0027, \u0027Get-GPOZaurrPermissionSummary\u0027, \u0027Get-GPOZaurrRedirect\u0027, \u0027Get-GPOZaurrSysvolDFSR\u0027, \u0027Get-GPOZaurrUpdates\u0027, \u0027Get-GPOZaurrWMI\u0027, \u0027Invoke-GPOZaurr\u0027, \u0027Invoke-GPOZaurrContent\u0027, \u0027Invoke-GPOZaurrPermission\u0027, \u0027Invoke-GPOZaurrSupport\u0027, \u0027New-GPOZaurrWMI\u0027, \u0027Optimize-GPOZaurr\u0027, \u0027Remove-GPOPermission\u0027, \u0027Remove-GPOZaurr\u0027, \u0027Remove-GPOZaurrBroken\u0027, \u0027Remove-GPOZaurrDuplicateObject\u0027, \u0027Remove-GPOZaurrFolders\u0027, \u0027Remove-GPOZaurrLegacyFiles\u0027, \u0027Remove-GPOZaurrLinkEmptyOU\u0027, \u0027Remove-GPOZaurrPermission\u0027, \u0027Remove-GPOZaurrWMI\u0027, \u0027Repair-GPOZaurrBrokenLink\u0027, \u0027Repair-GPOZaurrNetLogonOwner\u0027, \u0027Repair-GPOZaurrPermission\u0027, \u0027Repair-GPOZaurrPermissionConsistency\u0027, \u0027Restore-GPOZaurr\u0027, \u0027Save-GPOZaurrFiles\u0027, \u0027Set-GPOOwner\u0027, \u0027Set-GPOZaurrOwner\u0027, \u0027Set-GPOZaurrStatus\u0027, \u0027Skip-GroupPolicy\u0027)\n    GUID                 = \u0027f7d4c9e4-0298-4f51-ad77-e8e3febebbde\u0027\n    ModuleVersion        = \u00271.1.9\u0027\n    PowerShellVersion    = \u00275.1\u0027\n    PrivateData          = @{\n        PSData = @{\n            ExternalModuleDependencies = @(\u0027CimCmdlets\u0027, \u0027Microsoft.PowerShell.Management\u0027, \u0027Microsoft.PowerShell.Utility\u0027, \u0027Microsoft.PowerShell.Security\u0027)\n            ProjectUri                 = \u0027https://github.com/EvotecIT/GPOZaurr\u0027\n            Tags                       = @(\u0027Windows\u0027, \u0027ActiveDirectory\u0027, \u0027GPO\u0027, \u0027GroupPolicy\u0027)\n        }\n    }\n    RequiredModules      = @(@{\n            Guid          = \u00270b0ba5c5-ec85-4c2b-a718-874e55a8bc3f\u0027\n            ModuleName    = \u0027PSWriteColor\u0027\n            ModuleVersion = \u00271.0.1\u0027\n        }, @{\n            Guid          = \u0027ee272aa8-baaa-4edf-9f45-b6d6f7d844fe\u0027\n            ModuleName    = \u0027PSSharedGoods\u0027\n            ModuleVersion = \u00270.0.301\u0027\n        }, @{\n            Guid          = \u00279fc9fd61-7f11-4f4b-a527-084086f1905f\u0027\n            ModuleName    = \u0027ADEssentials\u0027\n            ModuleVersion = \u00270.0.226\u0027\n        }, @{\n            Guid          = \u0027a7bdf640-f5cb-4acf-9de0-365b322d245c\u0027\n            ModuleName    = \u0027PSWriteHTML\u0027\n            ModuleVersion = \u00271.27.0\u0027\n        }, \u0027CimCmdlets\u0027, \u0027Microsoft.PowerShell.Management\u0027, \u0027Microsoft.PowerShell.Utility\u0027, \u0027Microsoft.PowerShell.Security\u0027)\n    RootModule           = \u0027GPOZaurr.psm1\u0027\n}\n``n\n### GPOZaurr.psm1\n\n**Path:** GPOZaurr.psm1\n\n``powershell\n﻿#Get public and private function definition files.\n$Public = @( Get-ChildItem -Path $PSScriptRoot\\Public\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Private = @( Get-ChildItem -Path $PSScriptRoot\\Private\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n\n$AssemblyFolders = Get-ChildItem -Path $PSScriptRoot\\Lib -Directory -ErrorAction SilentlyContinue\nif ($AssemblyFolders.BaseName -contains \u0027Standard\u0027) {\n    $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Standard\\*.dll -ErrorAction SilentlyContinue )\n} else {\n    if ($PSEdition -eq \u0027Core\u0027) {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Core\\*.dll -ErrorAction SilentlyContinue )\n    } else {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Default\\*.dll -ErrorAction SilentlyContinue )\n    }\n}\n$FoundErrors = @(\n    Foreach ($Import in @($Assembly)) {\n        try {\n            Add-Type -Path $Import.Fullname -ErrorAction Stop\n        } catch [System.Reflection.ReflectionTypeLoadException] {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        } catch {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        }\n    }\n    #Dot source the files\n    Foreach ($Import in @($Private + $Public)) {\n        Try {\n            . $Import.Fullname\n        } Catch {\n            Write-Error -Message \"Failed to import functions from $($import.Fullname): $_\"\n            $true\n        }\n    }\n)\n\nif ($FoundErrors.Count -gt 0) {\n    $ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n    Write-Warning \"Importing module $ModuleName failed. Fix errors before continuing.\"\n    break\n}\n\nExport-ModuleMember -Function \u0027*\u0027 -Alias \u0027*\u0027\n``n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "﻿@{\n    AliasesToExport   = @()\n    Author            = \u0027Przemyslaw Klys\u0027\n    CmdletsToExport   = @()\n    CompanyName       = \u0027Evotec\u0027\n    Copyright         = \u0027(c) 2011 - 2022 Przemyslaw Klys @ Evotec. All rights reserved.\u0027\n    Description       = \u0027PSWinReportingV2 is fast and efficient Event Viewing, Event Reporting and Event Collecting tool. It\u0027\u0027s version 2 of known PSWinReporting PowerShell module and can work next to it.\u0027\n    FunctionsToExport = @(\u0027Add-EventsDefinitions\u0027, \u0027Add-WinTaskScheduledForwarder\u0027, \u0027Find-Events\u0027, \u0027New-WinSubscriptionTemplates\u0027, \u0027Remove-WinTaskScheduledForwarder\u0027, \u0027Start-WinNotifications\u0027, \u0027Start-WinReporting\u0027, \u0027Start-WinSubscriptionService\u0027)\n    GUID              = \u0027ea2bd8d2-cca1-4dc3-9e1c-ff80b06e8fbe\u0027\n    ModuleVersion     = \u00272.0.23\u0027\n    PrivateData       = @{\n        PSData = @{\n            Tags       = @(\u0027PSWinReporting\u0027, \u0027ActiveDirectory\u0027, \u0027Events\u0027, \u0027Reporting\u0027, \u0027Windows\u0027, \u0027EventLog\u0027)\n            ProjectUri = \u0027https://github.com/EvotecIT/PSWinReporting\u0027\n            IconUri    = \u0027https://evotec.xyz/wp-content/uploads/2018/10/PSWinReporting.png\u0027\n        }\n    }\n    RequiredModules   = @(@{\n            ModuleVersion = \u00271.0.22\u0027\n            ModuleName    = \u0027PSEventViewer\u0027\n            Guid          = \u00275df72a79-cdf6-4add-b38d-bcacf26fb7bc\u0027\n        }, @{\n            ModuleVersion = \u00270.0.254\u0027\n            ModuleName    = \u0027PSSharedGoods\u0027\n            Guid          = \u0027ee272aa8-baaa-4edf-9f45-b6d6f7d844fe\u0027\n        }, @{\n            ModuleVersion = \u00270.1.15\u0027\n            ModuleName    = \u0027PSWriteExcel\u0027\n            Guid          = \u002782232c6a-27f1-435d-a496-929f7221334b\u0027\n        }, @{\n            ModuleVersion = \u00270.0.180\u0027\n            ModuleName    = \u0027PSWriteHTML\u0027\n            Guid          = \u0027a7bdf640-f5cb-4acf-9de0-365b322d245c\u0027\n        })\n    RootModule        = \u0027PSWinReportingV2.psm1\u0027\n}",
                                               "#Get public and private function definition files.\n$Public = @( Get-ChildItem -Path $PSScriptRoot\\Public\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Private = @( Get-ChildItem -Path $PSScriptRoot\\Private\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Classes = @( Get-ChildItem -Path $PSScriptRoot\\Classes\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Enums = @( Get-ChildItem -Path $PSScriptRoot\\Enums\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n\n$AssemblyFolders = Get-ChildItem -Path $PSScriptRoot\\Lib -Directory -ErrorAction SilentlyContinue\nif ($AssemblyFolders.BaseName -contains \u0027Standard\u0027) {\n    $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Standard\\*.dll -ErrorAction SilentlyContinue )\n} else {\n    if ($PSEdition -eq \u0027Core\u0027) {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Core\\*.dll -ErrorAction SilentlyContinue )\n    } else {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Default\\*.dll -ErrorAction SilentlyContinue )\n    }\n}\n$FoundErrors = @(\n    Foreach ($Import in @($Assembly)) {\n        try {\n            Add-Type -Path $Import.Fullname -ErrorAction Stop\n        } catch [System.Reflection.ReflectionTypeLoadException] {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        } catch {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        }\n    }\n    #Dot source the files\n    Foreach ($Import in @($Private + $Public + $Classes + $Enums)) {\n        Try {\n            . $Import.Fullname\n        } Catch {\n            Write-Error -Message \"Failed to import functions from $($import.Fullname): $_\"\n            $true\n        }\n    }\n)\n\nif ($FoundErrors.Count -gt 0) {\n    $ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n    Write-Warning \"Importing module $ModuleName failed. Fix errors before continuing.\"\n    break\n}\n\nExport-ModuleMember -Function \u0027*\u0027 -Alias \u0027*\u0027"
                                           ],
                         "RelevanceScore":  0.78618222259572057,
                         "CreatedAt":  "2025-07-28T16:21:10Z",
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_policy_management_1605ec04",
                         "Operation":  "read",
                         "Stars":  718,
                         "UpdatedAt":  "2025-07-28T16:21:10Z",
                         "HasReadme":  true,
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub Repository: EvotecIT/PSWinReporting",
                         "LastUpdated":  "2025-07-25T06:36:41Z",
                         "CodeTemplate":  "﻿@{\n    AliasesToExport   = @()\n    Author            = \u0027Przemyslaw Klys\u0027\n    CmdletsToExport   = @()\n    CompanyName       = \u0027Evotec\u0027\n    Copyright         = \u0027(c) 2011 - 2022 Przemyslaw Klys @ Evotec. All rights reserved.\u0027\n    Description       = \u0027PSWinReportingV2 is fast and efficient Event Viewing, Event Reporting and Event Collecting tool. It\u0027\u0027s version 2 of known PSWinReporting PowerShell module and can work next to it.\u0027\n    FunctionsToExport = @(\u0027Add-EventsDefinitions\u0027, \u0027Add-WinTaskScheduledForwarder\u0027, \u0027Find-Events\u0027, \u0027New-WinSubscriptionTemplates\u0027, \u0027Remove-WinTaskScheduledForwarder\u0027, \u0027Start-WinNotifications\u0027, \u0027Start-WinReporting\u0027, \u0027Start-WinSubscriptionService\u0027)\n    GUID              = \u0027ea2bd8d2-cca1-4dc3-9e1c-ff80b06e8fbe\u0027\n    ModuleVersion     = \u00272.0.23\u0027\n    PrivateData       = @{\n        PSData = @{\n            Tags       = @(\u0027PSWinReporting\u0027, \u0027ActiveDirectory\u0027, \u0027Events\u0027, \u0027Reporting\u0027, \u0027Windows\u0027, \u0027EventLog\u0027)\n            ProjectUri = \u0027https://github.com/EvotecIT/PSWinReporting\u0027\n            IconUri    = \u0027https://evotec.xyz/wp-content/uploads/2018/10/PSWinReporting.png\u0027\n        }\n    }\n    RequiredModules   = @(@{\n            ModuleVersion = \u00271.0.22\u0027\n            ModuleName    = \u0027PSEventViewer\u0027\n            Guid          = \u00275df72a79-cdf6-4add-b38d-bcacf26fb7bc\u0027\n        }, @{\n            ModuleVersion = \u00270.0.254\u0027\n            ModuleName    = \u0027PSSharedGoods\u0027\n            Guid          = \u0027ee272aa8-baaa-4edf-9f45-b6d6f7d844fe\u0027\n        }, @{\n            ModuleVersion = \u00270.1.15\u0027\n            ModuleName    = \u0027PSWriteExcel\u0027\n            Guid          = \u002782232c6a-27f1-435d-a496-929f7221334b\u0027\n        }, @{\n            ModuleVersion = \u00270.0.180\u0027\n            ModuleName    = \u0027PSWriteHTML\u0027\n            Guid          = \u0027a7bdf640-f5cb-4acf-9de0-365b322d245c\u0027\n        })\n    RootModule        = \u0027PSWinReportingV2.psm1\u0027\n}",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  72,
                         "PowerShellFiles":  2,
                         "Domain":  "policy_management",
                         "RepoSize":  9383,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "policy_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: EvotecIT/PSWinReporting\n\n**Stars:** 718 | **Language:** PowerShell | **Forks:** 72\n\n**Description:** This PowerShell Module has multiple functionalities, but one of the signature features of this module is the ability to parse Security logs on Domain Controllers providing easy to use access to AD Events.\n\n**URL:** https://github.com/EvotecIT/PSWinReporting\n\n**Clone URL:** https://github.com/EvotecIT/PSWinReporting.git\n\n**Topics:** activedirectory, hacktoberfest, powershell, powersh...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:21:10Z",
                                             "Url":  "https://github.com/EvotecIT/PSWinReporting",
                                             "ScrapedAt":  "2025-07-28T16:21:10Z",
                                             "Id":  "efa0c3a7-488e-437c-aa2f-294a4ca08696",
                                             "Author":  "EvotecIT",
                                             "Title":  "GitHub Repository: EvotecIT/PSWinReporting"
                                         }
                                     ],
                         "Topics":  [
                                        "activedirectory",
                                        "hacktoberfest",
                                        "powershell",
                                        "powershell-module",
                                        "reporting",
                                        "windows"
                                    ],
                         "Content":  "# Repository: EvotecIT/PSWinReporting\n\n**Stars:** 718 | **Language:** PowerShell | **Forks:** 72\n\n**Description:** This PowerShell Module has multiple functionalities, but one of the signature features of this module is the ability to parse Security logs on Domain Controllers providing easy to use access to AD Events.\n\n**URL:** https://github.com/EvotecIT/PSWinReporting\n\n**Clone URL:** https://github.com/EvotecIT/PSWinReporting.git\n\n**Topics:** activedirectory, hacktoberfest, powershell, powershell-module, reporting, windows\n\n## README\n\n﻿\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/PSWinReportingV2\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/v/PSWinReportingV2.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/PSWinReportingV2\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/vpre/PSWinReportingV2.svg?label=powershell%20gallery%20preview\u0026colorB=yellow\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/PSWinReporting\"\u003e\u003cimg src=\"https://img.shields.io/github/license/EvotecIT/PSWinReporting.svg\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/PSWinReportingV2\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/p/PSWinReportingV2.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/PSWinReporting\"\u003e\u003cimg src=\"https://img.shields.io/github/languages/top/evotecit/PSWinReporting.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/PSWinReporting\"\u003e\u003cimg src=\"https://img.shields.io/github/languages/code-size/evotecit/PSWinReporting.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/PSWinReporting\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/dt/PSWinReporting.svg?label=downloads%20PSWinReporting\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/PSWinReportingV2\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/dt/PSWinReportingv2?label=downloads%20PSWinReportingV2\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://twitter.com/PrzemyslawKlys\"\u003e\u003cimg src=\"https://img.shields.io/twitter/follow/PrzemyslawKlys.svg?label=Twitter%20%40PrzemyslawKlys\u0026style=social\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://evotec.xyz/hub\"\u003e\u003cimg src=\"https://img.shields.io/badge/Blog-evotec.xyz-2A6496.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.linkedin.com/in/pklys\"\u003e\u003cimg src=\"https://img.shields.io/badge/LinkedIn-pklys-0077B5.svg?logo=LinkedIn\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n# PSWinReporting\n\n**PSWinReporting** is a little PowerShell module that solves the problem of monitoring and reading **Windows Events**. It allows you to set up monitoring of **Domain Controllers** (and from **2.X** any other servers) for events that happen on them. By default, it comes with **built-in Active Directory** events supports, but since **2.0** you can configure it to monitor anything. You can set up reporting on any types of events and have emails delivered with a summary of hourly, daily, weekly, monthly, or quarterly changes. It also supports sending notifications to Microsoft Teams, Slack, and Discord. Make sure to go thru related articles as they have all the KNOW HOW which is quite useful if you want to get everything from this module.\n\nThe full project description is available on my website - [Full project description](https://evotec.xyz/hub/scripts/pswinreporting-powershell-module/).\n\nCurrently, there are 2 branches of PSWinReporting.\n\n- [x] Legacy branch - available in PS Gallery as [PSWinReporting](https://www.powershellgallery.com/packages/PSWinReporting/) - `Install-Module -Name \u0027PSWinReporting\u0027 -Force`\n- [x] Master branch - available in PS Gallery as [PSWinReportingV2](https://www.powershellgallery.com/packages/PSWinReportingV2/) - `Install-Module -Name \u0027PSWinReportingV2\u0027 -Force`\n\nI\u0027ve decided that both PowerShell modules can coexist together, especially for scenarios for people who want to switch, but don\u0027t want to do it right away. This way, you can keep using old version as is, and slowly fix your other stuff, or use new `Find-Events` command. I\u0027ve slightly renamed the commands for V2 release.\n\n## PSWinReportingV2 - Master Edition\n\nMaster edition is a complete rewrite and a new beginning. It provides the same functionality as **Legacy 1.X** version and then some more.\n\n- [x] Ability to translate report and have it suite your needs\n- [x] Ability to completely modify events monitoring\n- [x] Ability to monitor any servers, for any events using simple to use schema\n- [x] Ability to target multiple servers, computers or files at the same time\n\n\nAt this moment there is no documentation for PSWinReportingV2 except for those articles below. Feel free to explore Examples if you\u0027re eager to try the new version — otherwise fallback to PSWinReporting **Legacy Edition**.\n\n- [x] [Find-Events - The only PowerShell Command you will ever need to find out who did what in Active Directory](https://evotec.xyz/the-only-powershell-command-you-will-ever-need-to-find-out-who-did-what-in-active-directory/)\n\n### Built-in Active Directory Reports\n\nPSWinReporting comes with predefined, built-in reports. Those are for `Find-Events`. Those also come defined in example configuration script which you can use straight away after verifying everything is as per your requirement.\n\n- [x] ADComputerChangesDetailed\n- [x] ADComputerCreatedChanged\n- [x] ADComputerDeleted\n- [x] ADGroupChanges\n- [x] ADGroupChangesDetailed\n- [x] ADGroupCreateDelete\n- [x] ADGroupEnumeration\n- [x] ADGroupMembershipChanges\n- [x] ADGroupPolicyChanges\n- [x] ADLogsClearedOther\n- [x] ADLogsClearedSecurity\n- [x] ADUserChanges\n- [x] ADUserChangesDetailed\n- [x] ADUserLockouts\n- [x] ADUserLogon\n- [x] ADUserLogonKerberos\n- [x] ADUserStatus\n- [x] ADUserUnlocked\n- [X] ADOrganizationalUnitChangesDetailed (added in 2.0.10)\n- [x] OSStartupShutdownCrash (added in 2.0.12) - covers startup, shutdown and crashes - probably needs some work on the engine later on to allow field merging\n- [x] OSCrash (added in 2.0.12) - covers system crashes\n- [x] NetworkAccessAuthenticationPolicy (added in 2.0.12) - covers authorizations approved/denied for WIFI and ETHERNET\n\n### Built-in Reporting Times\n\nPSWinReporting comes with predefined report times. This means you can use **True/False** to enable/disable period. In case of `Find-Events`, you can use defined times (checked only) from **DatesRange** parameter.\n\n- [ ] CurrentDay\n- [ ] CurrentDayMinusDayX\n- [ ] CurrentDayMinuxDaysX\n- [x] CurrentHour\n- [x] CurrentMonth\n- [x] CurrentQuarter\n- [ ] CustomDate\n- [x] Everything\n- [x] Last14days\n- [x] Last3days\n- [x] Last7days\n- [ ] OnDay\n- [x] PastDay\n- [x] PastHour\n- [x] PastMonth\n- [x] PastQuarter\n\nOf course, you can also define **DateFrom**, **DateTo** parameters for custom use when using `Find-Events` command.\n\n## PSWinReporting - Legacy Edition\n\n***Legacy edition*** will continue it\u0027s life as ***1.X.X***. If you want to keep on using it, feel free, but it\u0027s highly encouraged to use ***2.x.x*** when it\u0027s fully functional with all features. Code is available as [Legacy Branch](https://github.com/EvotecIT/PSWinReporting/tree/Legacy). Following links can help in understanding how it works and how to set it up:\n\n- [Review of features coming in 2.0 along with some features description for the current version](https://evotec.xyz/pswinreporting-1-8-split-of-branches-legacy-vs-new-hope/) - Overview of configuration and features.\n- [Review of new features in PSWinReporting 1.7](https://evotec.xyz/pswinreporting-forwarders-microsoft-teams-slack-microsoft-sql-and-more/) - Lots of changes, review required. Microsoft Teams, Slack, SQL and forwarders support\n- [Review of new features in PSWinReporting 1.0](https://evotec.xyz/pswinreporting-1-0-is-out/) - Lots of changes, review required.\n- [Last version of Get-EventsLibrary.ps1](https://evotec.xyz/get-eventslibrary-ps1-monitoring-events-powershell/) - This is the actual code base for the old version. Just in caseâ€¦\n- [Blog post about version 0.8](https://evotec.xyz/whats-new-event-monitoring-0-8/) - Updates from feedback. Last version before the name change.\n- [Blog post about version 0.7](https://evotec.xyz/whats-new-event-monitoring-v0-7/) - Updates from feedback.\n- [Blog post about version 0.6](https://evotec.xyz/whats-new-event-monitoring-v0-6/) - Updates from feedback.\n- [Blog post about initial version and the differences in monitoring approach](https://evotec.xyz/monitoring-active-directory-changes-on-users-and-groups-with-powershell/)\n\nFollowing AD Events are supported:\n\n- [x] Group create, delete, modify (Who / When / What)\n- [x] Group membership changes (Who / When / What)\n- [x] User changes (Who / When / What)\n- [x] User created / deleted (Who / When)\n- [x] User password changes (Who / When)\n- [x] User lockouts (Who / When / Where)\n- [x] Computer Created / Modified (Who / When / Where)\n- [x] Computer Deleted (Who / When / Where)\n- [x] Event Log Backup (Who / When)\n- [x] Event Log Clear (Who / When)\n\nFeatures:\n\n- [x] Support for Event Forwarding - monitoring one event log instead of scanning all domain controllers\n- [x] Support for Microsoft Teams - Sending events as they happen to Microsoft Teams (only supported when forwarders are in use)\n- [x] Support for Slack - Sending events as they happen to Slack (only supported when forwarders are in use)\n- [x] Support for Microsoft SQL - Sending events directly to SQL (some people prefer it that way)\n- [x] Support for backing up old archived logs (moves logs from Domain Controllers into chosen place)\n- [x] Support for re-scanning logs from files - a way to recheck your logs for missing information\n\n### Example - Script running\n\n![image](https://evotec.xyz/wp-content/uploads/2018/06/2018-06-10_11-20-08.gif.pagespeed.ce.xrLSOGTIkk.gif)\n\n### Example - Email Report\n\n![image](https://evotec.xyz/wp-content/uploads/2018/06/PSWinReporting1.0-Example1.png)\n\n### Example - Microsoft Teams\n\n![image](https://evotec.xyz/wp-content/uploads/2018/09/img_5b9e830101081.png)\n\n### Example - Slack\n\n![image](https://evotec.xyz/wp-content/uploads/2018/09/img_5b9e7041638f5.png)\n\n\n## PowerShell Files (2 files)\n\n### PSWinReportingV2.psd1\n\n**Path:** PSWinReportingV2.psd1\n\n``powershell\n﻿@{\n    AliasesToExport   = @()\n    Author            = \u0027Przemyslaw Klys\u0027\n    CmdletsToExport   = @()\n    CompanyName       = \u0027Evotec\u0027\n    Copyright         = \u0027(c) 2011 - 2022 Przemyslaw Klys @ Evotec. All rights reserved.\u0027\n    Description       = \u0027PSWinReportingV2 is fast and efficient Event Viewing, Event Reporting and Event Collecting tool. It\u0027\u0027s version 2 of known PSWinReporting PowerShell module and can work next to it.\u0027\n    FunctionsToExport = @(\u0027Add-EventsDefinitions\u0027, \u0027Add-WinTaskScheduledForwarder\u0027, \u0027Find-Events\u0027, \u0027New-WinSubscriptionTemplates\u0027, \u0027Remove-WinTaskScheduledForwarder\u0027, \u0027Start-WinNotifications\u0027, \u0027Start-WinReporting\u0027, \u0027Start-WinSubscriptionService\u0027)\n    GUID              = \u0027ea2bd8d2-cca1-4dc3-9e1c-ff80b06e8fbe\u0027\n    ModuleVersion     = \u00272.0.23\u0027\n    PrivateData       = @{\n        PSData = @{\n            Tags       = @(\u0027PSWinReporting\u0027, \u0027ActiveDirectory\u0027, \u0027Events\u0027, \u0027Reporting\u0027, \u0027Windows\u0027, \u0027EventLog\u0027)\n            ProjectUri = \u0027https://github.com/EvotecIT/PSWinReporting\u0027\n            IconUri    = \u0027https://evotec.xyz/wp-content/uploads/2018/10/PSWinReporting.png\u0027\n        }\n    }\n    RequiredModules   = @(@{\n            ModuleVersion = \u00271.0.22\u0027\n            ModuleName    = \u0027PSEventViewer\u0027\n            Guid          = \u00275df72a79-cdf6-4add-b38d-bcacf26fb7bc\u0027\n        }, @{\n            ModuleVersion = \u00270.0.254\u0027\n            ModuleName    = \u0027PSSharedGoods\u0027\n            Guid          = \u0027ee272aa8-baaa-4edf-9f45-b6d6f7d844fe\u0027\n        }, @{\n            ModuleVersion = \u00270.1.15\u0027\n            ModuleName    = \u0027PSWriteExcel\u0027\n            Guid          = \u002782232c6a-27f1-435d-a496-929f7221334b\u0027\n        }, @{\n            ModuleVersion = \u00270.0.180\u0027\n            ModuleName    = \u0027PSWriteHTML\u0027\n            Guid          = \u0027a7bdf640-f5cb-4acf-9de0-365b322d245c\u0027\n        })\n    RootModule        = \u0027PSWinReportingV2.psm1\u0027\n}\n``n\n### PSWinReportingV2.psm1\n\n**Path:** PSWinReportingV2.psm1\n\n``powershell\n#Get public and private function definition files.\n$Public = @( Get-ChildItem -Path $PSScriptRoot\\Public\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Private = @( Get-ChildItem -Path $PSScriptRoot\\Private\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Classes = @( Get-ChildItem -Path $PSScriptRoot\\Classes\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Enums = @( Get-ChildItem -Path $PSScriptRoot\\Enums\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n\n$AssemblyFolders = Get-ChildItem -Path $PSScriptRoot\\Lib -Directory -ErrorAction SilentlyContinue\nif ($AssemblyFolders.BaseName -contains \u0027Standard\u0027) {\n    $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Standard\\*.dll -ErrorAction SilentlyContinue )\n} else {\n    if ($PSEdition -eq \u0027Core\u0027) {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Core\\*.dll -ErrorAction SilentlyContinue )\n    } else {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Default\\*.dll -ErrorAction SilentlyContinue )\n    }\n}\n$FoundErrors = @(\n    Foreach ($Import in @($Assembly)) {\n        try {\n            Add-Type -Path $Import.Fullname -ErrorAction Stop\n        } catch [System.Reflection.ReflectionTypeLoadException] {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        } catch {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        }\n    }\n    #Dot source the files\n    Foreach ($Import in @($Private + $Public + $Classes + $Enums)) {\n        Try {\n            . $Import.Fullname\n        } Catch {\n            Write-Error -Message \"Failed to import functions from $($import.Fullname): $_\"\n            $true\n        }\n    }\n)\n\nif ($FoundErrors.Count -gt 0) {\n    $ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n    Write-Warning \"Importing module $ModuleName failed. Fix errors before continuing.\"\n    break\n}\n\nExport-ModuleMember -Function \u0027*\u0027 -Alias \u0027*\u0027\n``n\n"
                     }
                 ],
    "SourceType":  "enhanced_comprehensive",
    "TotalPatterns":  11
}
