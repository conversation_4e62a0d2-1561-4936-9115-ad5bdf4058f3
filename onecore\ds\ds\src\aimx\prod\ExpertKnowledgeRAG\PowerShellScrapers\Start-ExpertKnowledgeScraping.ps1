# Expert Knowledge RAG - Master Scraping Orchestrator
# Coordinates all scraping activities and consolidates results

#Requires -Version 5.1

param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [string[]]$ScrapersToRun = @("MicrosoftDocs", "StackOverflow", "GitHub"),
    
    [Parameter(Mandatory = $false)]
    [string]$GitHubToken = "",
    
    [Parameter(Mandatory = $false)]
    [switch]$FullScrape = $false,
    
    [Parameter(Mandatory = $false)]
    [switch]$ConsolidateOnly = $false,
    
    [Parameter(Mandatory = $false)]
    [string]$OutputFormat = "JSON"  # JSON, CSV, XML
)

# Import the core framework
Import-Module -Name (Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1") -Force

function Start-ScrapingOrchestration {
    [CmdletBinding()]
    param()
    
    Write-Host "🚀 Expert Knowledge RAG - Scraping Orchestrator" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Cyan
    
    try {
        # Initialize framework
        Write-Host "📋 Initializing scraping framework..." -ForegroundColor Yellow
        if (-not (Initialize-ScrapingFramework -ConfigPath $ConfigPath)) {
            throw "Failed to initialize scraping framework"
        }
        
        Write-Host "✅ Framework initialized successfully" -ForegroundColor Green
        
        # Create execution plan
        $executionPlan = Create-ExecutionPlan -ScrapersToRun $ScrapersToRun
        Write-Host "📊 Execution plan created with $($executionPlan.Count) scrapers" -ForegroundColor Green
        
        $allResults = @()
        
        if (-not $ConsolidateOnly) {
            # Execute scrapers
            foreach ($scraper in $executionPlan) {
                Write-Host "`n🔄 Executing: $($scraper.Name)" -ForegroundColor Cyan
                Write-Host "   Priority: $($scraper.Priority) | Expected patterns: ~$($scraper.EstimatedPatterns)" -ForegroundColor Gray
                
                try {
                    $startTime = Get-Date
                    $results = & $scraper.ScriptPath
                    $endTime = Get-Date
                    $duration = ($endTime - $startTime).TotalMinutes
                    
                    if ($results -and $results.Count -gt 0) {
                        $allResults += $results
                        Write-Host "   ✅ Completed: $($results.Count) patterns in $([math]::Round($duration, 2)) minutes" -ForegroundColor Green
                    } else {
                        Write-Host "   ⚠️  No patterns extracted" -ForegroundColor Yellow
                    }
                }
                catch {
                    Write-Host "   ❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
                    Write-ScrapingLog -Message "Scraper $($scraper.Name) failed: $($_.Exception.Message)" -Level "Error" -Source "Orchestrator"
                }
            }
        }
        
        # Consolidate results
        Write-Host "`n📊 Consolidating results..." -ForegroundColor Cyan
        $consolidatedResults = Consolidate-ScrapingResults -AllResults $allResults
        
        # Generate final report
        Write-Host "`n📈 Generating final report..." -ForegroundColor Cyan
        $report = Generate-ScrapingReport -ConsolidatedResults $consolidatedResults
        
        # Save consolidated results
        $finalOutputPath = Save-ConsolidatedResults -Results $consolidatedResults -Format $OutputFormat
        
        # Display summary
        Display-ScrapingSummary -Report $report -OutputPath $finalOutputPath
        
        return $consolidatedResults
    }
    catch {
        Write-Host "❌ Orchestration failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-ScrapingLog -Message "Orchestration failed: $($_.Exception.Message)" -Level "Error" -Source "Orchestrator"
        throw
    }
}

function Create-ExecutionPlan {
    [CmdletBinding()]
    param([string[]]$ScrapersToRun)
    
    $availableScrapers = @{
        "MicrosoftDocs" = @{
            Name = "Microsoft Docs"
            ScriptPath = (Join-Path $PSScriptRoot "Scrapers/MicrosoftDocsScraper.ps1")
            Priority = 1
            EstimatedPatterns = 150
            EstimatedTimeMinutes = 30
            Parameters = @{}
        }
        "StackOverflow" = @{
            Name = "Stack Overflow"
            ScriptPath = (Join-Path $PSScriptRoot "Scrapers/StackOverflowScraper.ps1")
            Priority = 2
            EstimatedPatterns = 300
            EstimatedTimeMinutes = 45
            Parameters = @{}
        }
        "GitHub" = @{
            Name = "GitHub Repositories"
            ScriptPath = (Join-Path $PSScriptRoot "Scrapers/GitHubScraper.ps1")
            Priority = 2
            EstimatedPatterns = 200
            EstimatedTimeMinutes = 60
            Parameters = @{ GitHubToken = $GitHubToken }
        }
    }
    
    $executionPlan = @()
    
    foreach ($scraperName in $ScrapersToRun) {
        if ($availableScrapers.ContainsKey($scraperName)) {
            $scraper = $availableScrapers[$scraperName]
            
            # Verify scraper script exists
            if (Test-Path $scraper.ScriptPath) {
                $executionPlan += $scraper
            } else {
                Write-ScrapingLog -Message "Scraper script not found: $($scraper.ScriptPath)" -Level "Warning" -Source "Orchestrator"
            }
        } else {
            Write-ScrapingLog -Message "Unknown scraper: $scraperName" -Level "Warning" -Source "Orchestrator"
        }
    }
    
    # Sort by priority
    $executionPlan = $executionPlan | Sort-Object Priority
    
    return $executionPlan
}

function Consolidate-ScrapingResults {
    [CmdletBinding()]
    param([array]$AllResults)
    
    Write-ScrapingLog -Message "Consolidating $($AllResults.Count) total patterns" -Source "Orchestrator"
    
    # Load existing results if ConsolidateOnly mode
    if ($ConsolidateOnly) {
        $AllResults = Load-ExistingResults
    }
    
    # Remove duplicates based on URL and title
    $uniquePatterns = @()
    $seenUrls = @{}
    $seenTitles = @{}
    
    foreach ($pattern in $AllResults) {
        $urlKey = $pattern.Sources[0].Url
        $titleKey = $pattern.Title.ToLower().Trim()
        
        if (-not $seenUrls.ContainsKey($urlKey) -and -not $seenTitles.ContainsKey($titleKey)) {
            $uniquePatterns += $pattern
            $seenUrls[$urlKey] = $true
            $seenTitles[$titleKey] = $true
        } else {
            Write-ScrapingLog -Message "Duplicate pattern removed: $($pattern.Title)" -Level "Debug" -Source "Orchestrator"
        }
    }
    
    # Quality filtering
    $qualityThreshold = $Script:Config.QualityFilters.MinimumCredibilityScore
    $highQualityPatterns = $uniquePatterns | Where-Object { $_.CredibilityScore -ge $qualityThreshold }
    
    Write-ScrapingLog -Message "Quality filtering: $($uniquePatterns.Count) -> $($highQualityPatterns.Count) patterns" -Source "Orchestrator"
    
    # Categorize patterns
    $categorizedPatterns = @{
        UserManagement = $highQualityPatterns | Where-Object { $_.Domain -eq "user_management" }
        GroupManagement = $highQualityPatterns | Where-Object { $_.Domain -eq "group_management" }
        ComputerManagement = $highQualityPatterns | Where-Object { $_.Domain -eq "computer_management" }
        OUManagement = $highQualityPatterns | Where-Object { $_.Domain -eq "ou_management" }
        PasswordManagement = $highQualityPatterns | Where-Object { $_.Domain -eq "password_management" }
        SecurityManagement = $highQualityPatterns | Where-Object { $_.Domain -eq "security_management" }
        GeneralAD = $highQualityPatterns | Where-Object { $_.Domain -eq "general_ad" }
    }
    
    return @{
        AllPatterns = $highQualityPatterns
        CategorizedPatterns = $categorizedPatterns
        Statistics = @{
            TotalPatterns = $highQualityPatterns.Count
            OriginalCount = $AllResults.Count
            DuplicatesRemoved = $AllResults.Count - $uniquePatterns.Count
            QualityFiltered = $uniquePatterns.Count - $highQualityPatterns.Count
        }
    }
}

function Load-ExistingResults {
    [CmdletBinding()]
    param()
    
    $outputDir = $Script:Config.ScrapingConfiguration.OutputDirectory
    $allPatterns = @()
    
    # Load all JSON files from output directory
    $jsonFiles = Get-ChildItem -Path $outputDir -Filter "scraped_*.json"
    
    foreach ($file in $jsonFiles) {
        try {
            $data = Get-Content $file.FullName | ConvertFrom-Json
            if ($data.Patterns) {
                $allPatterns += $data.Patterns
            }
        }
        catch {
            Write-ScrapingLog -Message "Failed to load file $($file.Name): $($_.Exception.Message)" -Level "Warning" -Source "Orchestrator"
        }
    }
    
    Write-ScrapingLog -Message "Loaded $($allPatterns.Count) patterns from existing files" -Source "Orchestrator"
    return $allPatterns
}

function Generate-ScrapingReport {
    [CmdletBinding()]
    param([hashtable]$ConsolidatedResults)
    
    $patterns = $ConsolidatedResults.AllPatterns
    $stats = $ConsolidatedResults.Statistics
    $categories = $ConsolidatedResults.CategorizedPatterns
    
    # Source distribution
    $sourceDistribution = $patterns | Group-Object { $_.Sources[0].SourceType } | ForEach-Object {
        @{ Source = $_.Name; Count = $_.Count; Percentage = [math]::Round(($_.Count / $patterns.Count) * 100, 1) }
    }
    
    # Domain distribution
    $domainDistribution = $patterns | Group-Object Domain | ForEach-Object {
        @{ Domain = $_.Name; Count = $_.Count; Percentage = [math]::Round(($_.Count / $patterns.Count) * 100, 1) }
    }
    
    # Quality metrics
    $avgCredibility = ($patterns | Measure-Object -Property CredibilityScore -Average).Average
    $avgRelevance = ($patterns | Measure-Object -Property RelevanceScore -Average).Average
    
    # Top patterns by credibility
    $topPatterns = $patterns | Sort-Object CredibilityScore -Descending | Select-Object -First 10
    
    return @{
        Statistics = $stats
        SourceDistribution = $sourceDistribution
        DomainDistribution = $domainDistribution
        QualityMetrics = @{
            AverageCredibility = [math]::Round($avgCredibility, 3)
            AverageRelevance = [math]::Round($avgRelevance, 3)
            HighQualityCount = ($patterns | Where-Object { $_.CredibilityScore -gt 0.8 }).Count
        }
        TopPatterns = $topPatterns
        GeneratedAt = Get-Date
    }
}

function Save-ConsolidatedResults {
    [CmdletBinding()]
    param(
        [hashtable]$Results,
        [string]$Format = "JSON"
    )
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $outputDir = $Script:Config.ScrapingConfiguration.OutputDirectory
    
    switch ($Format.ToUpper()) {
        "JSON" {
            $fileName = "consolidated_expert_knowledge_$timestamp.json"
            $filePath = Join-Path $outputDir $fileName
            $Results | ConvertTo-Json -Depth 10 | Out-File -FilePath $filePath -Encoding UTF8
        }
        "CSV" {
            $fileName = "consolidated_expert_knowledge_$timestamp.csv"
            $filePath = Join-Path $outputDir $fileName
            $Results.AllPatterns | Export-Csv -Path $filePath -NoTypeInformation -Encoding UTF8
        }
        "XML" {
            $fileName = "consolidated_expert_knowledge_$timestamp.xml"
            $filePath = Join-Path $outputDir $fileName
            $Results.AllPatterns | Export-Clixml -Path $filePath
        }
    }
    
    Write-ScrapingLog -Message "Consolidated results saved to: $filePath" -Source "Orchestrator"
    return $filePath
}

function Display-ScrapingSummary {
    [CmdletBinding()]
    param(
        [hashtable]$Report,
        [string]$OutputPath
    )
    
    Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
    Write-Host "📊 EXPERT KNOWLEDGE SCRAPING SUMMARY" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Cyan
    
    # Statistics
    Write-Host "`n📈 STATISTICS:" -ForegroundColor Yellow
    Write-Host "   Total Patterns Extracted: $($Report.Statistics.TotalPatterns)" -ForegroundColor Green
    Write-Host "   Original Count: $($Report.Statistics.OriginalCount)" -ForegroundColor Gray
    Write-Host "   Duplicates Removed: $($Report.Statistics.DuplicatesRemoved)" -ForegroundColor Gray
    Write-Host "   Quality Filtered: $($Report.Statistics.QualityFiltered)" -ForegroundColor Gray
    
    # Source distribution
    Write-Host "`n🔍 SOURCE DISTRIBUTION:" -ForegroundColor Yellow
    foreach ($source in $Report.SourceDistribution) {
        Write-Host "   $($source.Source): $($source.Count) ($($source.Percentage)%)" -ForegroundColor Green
    }
    
    # Domain distribution
    Write-Host "`n🏷️  DOMAIN DISTRIBUTION:" -ForegroundColor Yellow
    foreach ($domain in $Report.DomainDistribution) {
        Write-Host "   $($domain.Domain): $($domain.Count) ($($domain.Percentage)%)" -ForegroundColor Green
    }
    
    # Quality metrics
    Write-Host "`n⭐ QUALITY METRICS:" -ForegroundColor Yellow
    Write-Host "   Average Credibility: $($Report.QualityMetrics.AverageCredibility)" -ForegroundColor Green
    Write-Host "   Average Relevance: $($Report.QualityMetrics.AverageRelevance)" -ForegroundColor Green
    Write-Host "   High Quality Patterns (>0.8): $($Report.QualityMetrics.HighQualityCount)" -ForegroundColor Green
    
    # Output location
    Write-Host "`n💾 OUTPUT:" -ForegroundColor Yellow
    Write-Host "   Consolidated Results: $OutputPath" -ForegroundColor Green
    
    Write-Host "`n✅ Scraping completed successfully!" -ForegroundColor Green
    Write-Host "Generated at: $($Report.GeneratedAt)" -ForegroundColor Gray
    Write-Host "=" * 60 -ForegroundColor Cyan
}

# Main execution
if ($MyInvocation.InvocationName -ne '.') {
    try {
        $results = Start-ScrapingOrchestration
        Write-Host "`n🎉 Expert Knowledge RAG scraping completed successfully!" -ForegroundColor Green
        Write-Host "Total patterns collected: $($results.AllPatterns.Count)" -ForegroundColor Cyan
    }
    catch {
        Write-Host "`n💥 Scraping failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}
