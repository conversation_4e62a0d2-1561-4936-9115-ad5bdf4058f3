using Microsoft.Extensions.AI;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text;

namespace NetRagService;

/// <summary>
/// Enhanced ingestion service for RAG-optimized PowerShell Active Directory commands
/// Supports the new advanced RAG format with rag_document, keywords, and enhanced metadata
/// </summary>
public class EnhancedPowerShellCommandIngestionService
{
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly VectorStoreService _vectorStoreService;
    private readonly ILogger<EnhancedPowerShellCommandIngestionService> _logger;

    public EnhancedPowerShellCommandIngestionService(
        IEmbeddingGenerator<string, Embedding<float>> embeddingService,
        VectorStoreService vectorStoreService,
        ILogger<EnhancedPowerShellCommandIngestionService> logger)
    {
        _embeddingService = embeddingService;
        _vectorStoreService = vectorStoreService;
        _logger = logger;
    }

    /// <summary>
    /// Ingest RAG-optimized PowerShell command data
    /// </summary>
    public async Task IngestRagOptimizedDataAsync(string jsonFilePath)
    {
        if (!File.Exists(jsonFilePath))
        {
            throw new FileNotFoundException($"RAG-optimized data file not found: {jsonFilePath}");
        }

        _logger.LogInformation("Starting ingestion of RAG-optimized PowerShell commands from: {FilePath}", jsonFilePath);

        var jsonContent = await File.ReadAllTextAsync(jsonFilePath);
        var data = JsonSerializer.Deserialize<RagOptimizedData>(jsonContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        if (data?.Commands == null)
        {
            throw new InvalidOperationException("Invalid RAG-optimized data format");
        }

        _logger.LogInformation("Found {CommandCount} RAG-optimized PowerShell commands to ingest", data.Commands.Count);

        int processedCount = 0;
        foreach (var command in data.Commands)
        {
            try
            {
                await IngestSingleRagCommandAsync(command, processedCount);
                processedCount++;

                if (processedCount % 10 == 0)
                {
                    _logger.LogInformation("Processed {ProcessedCount}/{TotalCount} commands", 
                        processedCount, data.Commands.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to ingest command: {CommandName}", command.CommandName ?? "Unknown");
                throw;
            }
        }

        _logger.LogInformation("Successfully ingested {ProcessedCount} RAG-optimized PowerShell commands", processedCount);
    }

    private async Task IngestSingleRagCommandAsync(RagOptimizedCommand command, int index)
    {
        // Use the rag_document field as the primary text for embedding
        // This field is specifically designed for vector search and semantic matching
        string embeddingText = command.RagDocument ?? "";
        
        // Fallback to building comprehensive text if rag_document is missing
        if (string.IsNullOrEmpty(embeddingText))
        {
            embeddingText = BuildComprehensiveCommandText(command);
        }
        
        // Generate embedding for the command using the optimized text
        var embeddingResult = await _embeddingService.GenerateAsync(embeddingText);
        var embedding = embeddingResult.Vector;

        // Create enhanced metadata with all RAG-optimized fields
        var metadata = new Dictionary<string, object>
        {
            // Core command information
            ["command_name"] = command.CommandName ?? "Unknown",
            ["verb"] = command.Verb ?? "",
            ["noun"] = command.Noun ?? "",
            ["module"] = command.Module ?? "ActiveDirectory",
            ["category"] = command.Category ?? "",
            ["primary_purpose"] = command.PrimaryPurpose ?? "",
            ["description"] = command.Description ?? "",
            
            // RAG-specific fields for enhanced search
            ["rag_document"] = command.RagDocument ?? "",
            ["keywords"] = JsonSerializer.Serialize(command.Keywords ?? new List<string>()),
            ["searchable_keywords"] = string.Join(" ", command.Keywords ?? new List<string>()),
            
            // Parameter information for filtering and context
            ["required_parameters"] = JsonSerializer.Serialize(command.RequiredParameters ?? new List<string>()),
            ["optional_parameters"] = JsonSerializer.Serialize(command.OptionalParameters ?? new List<string>()),
            ["parameter_count"] = command.Parameters?.Count ?? 0,
            ["parameter_names"] = string.Join(", ", command.Parameters?.Select(p => p.Name) ?? new List<string>()),
            
            // Example information
            ["example_count"] = command.Examples?.Count ?? 0,
            ["example_scenarios"] = JsonSerializer.Serialize(command.Examples?.Select(e => e.Scenario).Distinct() ?? new List<string>()),
            
            // Full structured data for LLM context (when command is retrieved)
            ["parameters_full"] = JsonSerializer.Serialize(command.Parameters ?? new List<ParameterData>()),
            ["examples_full"] = JsonSerializer.Serialize(command.Examples ?? new List<ExampleData>()),
            
            // Search optimization fields
            ["full_text"] = embeddingText,
            ["command_index"] = index,
            ["ingestion_timestamp"] = DateTime.UtcNow.ToString("O"),
            ["data_type"] = "rag_optimized_powershell_command",

            // Usage frequency for ranking boost
            ["usage_frequency"] = command.UsageFrequency,
            ["usage_category"] = command.UsageCategory ?? "moderate"
        };

        // Add category-specific metadata for better filtering
        AddCategorySpecificMetadata(metadata, command);

        // Store in vector database with enhanced ID format
        await _vectorStoreService.UpsertAsync(
            id: $"rag_ps_{index}_{command.CommandName?.Replace("-", "_").ToLowerInvariant()}",
            embedding: embedding,
            metadata: metadata
        );
    }

    private string BuildComprehensiveCommandText(RagOptimizedCommand command)
    {
        var sb = new StringBuilder();
        
        // Command identification
        if (!string.IsNullOrEmpty(command.CommandName))
        {
            sb.AppendLine($"Command: {command.CommandName}");
        }

        // Purpose and description
        if (!string.IsNullOrEmpty(command.PrimaryPurpose))
        {
            sb.AppendLine($"Purpose: {command.PrimaryPurpose}");
        }

        if (!string.IsNullOrEmpty(command.Description))
        {
            sb.AppendLine($"Description: {command.Description}");
        }

        // Category and keywords for semantic context
        if (!string.IsNullOrEmpty(command.Category))
        {
            sb.AppendLine($"Category: {command.Category}");
        }

        if (command.Keywords?.Any() == true)
        {
            sb.AppendLine($"Keywords: {string.Join(", ", command.Keywords)}");
        }

        // Key parameters (focus on required ones first)
        if (command.Parameters?.Any() == true)
        {
            sb.AppendLine("Key Parameters:");
            
            // Required parameters first
            var requiredParams = command.Parameters.Where(p => p.Mandatory).Take(3);
            foreach (var param in requiredParams)
            {
                sb.AppendLine($"- {param.Name} (Required): {param.Description}");
            }
            
            // Then important optional parameters
            var optionalParams = command.Parameters.Where(p => !p.Mandatory && 
                (p.Name.Contains("Filter") || p.Name.Contains("Properties") || p.Name.Contains("Identity"))).Take(2);
            foreach (var param in optionalParams)
            {
                sb.AppendLine($"- {param.Name}: {param.Description}");
            }
        }

        // Example scenarios for context
        if (command.Examples?.Any() == true)
        {
            sb.AppendLine("Common Usage:");
            foreach (var example in command.Examples.Take(2))
            {
                if (!string.IsNullOrEmpty(example.Description))
                {
                    sb.AppendLine($"- {example.Description}");
                }
            }
        }

        return sb.ToString();
    }

    private void AddCategorySpecificMetadata(Dictionary<string, object> metadata, RagOptimizedCommand command)
    {
        // Add category-specific tags for better filtering
        var category = command.Category?.ToLower() ?? "";
        
        if (category.Contains("user"))
        {
            metadata["object_type"] = "user";
            metadata["management_scope"] = "identity";
        }
        else if (category.Contains("group"))
        {
            metadata["object_type"] = "group";
            metadata["management_scope"] = "membership";
        }
        else if (category.Contains("computer"))
        {
            metadata["object_type"] = "computer";
            metadata["management_scope"] = "device";
        }
        else if (category.Contains("domain"))
        {
            metadata["object_type"] = "domain";
            metadata["management_scope"] = "infrastructure";
        }
        else if (category.Contains("policy"))
        {
            metadata["object_type"] = "policy";
            metadata["management_scope"] = "security";
        }

        // Add verb-specific intent
        var verb = command.Verb?.ToLower() ?? "";
        switch (verb)
        {
            case "get":
                metadata["intent"] = "query";
                metadata["operation_type"] = "read";
                break;
            case "new":
                metadata["intent"] = "create";
                metadata["operation_type"] = "write";
                break;
            case "set":
                metadata["intent"] = "modify";
                metadata["operation_type"] = "write";
                break;
            case "remove":
                metadata["intent"] = "delete";
                metadata["operation_type"] = "write";
                break;
            case "add":
                metadata["intent"] = "associate";
                metadata["operation_type"] = "write";
                break;
            case "enable":
            case "disable":
                metadata["intent"] = "configure";
                metadata["operation_type"] = "write";
                break;
        }

        // Add complexity indicators
        var paramCount = command.Parameters?.Count ?? 0;
        var requiredParamCount = command.Parameters?.Count(p => p.Mandatory) ?? 0;
        
        metadata["complexity"] = paramCount switch
        {
            <= 3 => "simple",
            <= 7 => "moderate",
            _ => "complex"
        };
        
        metadata["required_param_count"] = requiredParamCount;
    }
}

// Data structures are now in RagDataModels.cs to avoid duplication
