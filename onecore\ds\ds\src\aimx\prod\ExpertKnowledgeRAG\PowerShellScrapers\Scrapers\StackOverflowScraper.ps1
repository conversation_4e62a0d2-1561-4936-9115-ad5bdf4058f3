# Stack Overflow PowerShell AD Questions Scraper
# Scrapes high-quality Q&A content from Stack Overflow

#Requires -Version 5.1

param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "../ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxQuestions = 500,
    
    [Parameter(Mandatory = $false)]
    [int]$MinScore = 5,
    
    [Parameter(Mandatory = $false)]
    [string[]]$Tags = @("powershell", "active-directory")
)

Import-Module -Name (Join-Path $PSScriptRoot "../Core/ScrapingFramework.psm1") -Force

function Get-StackOverflowQuestions {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string[]]$Tags,
        
        [Parameter(Mandatory = $false)]
        [int]$PageSize = 100,
        
        [Parameter(Mandatory = $false)]
        [int]$MaxPages = 5,
        
        [Parameter(Mandatory = $false)]
        [int]$MinScore = 5
    )
    
    Write-ScrapingLog -Message "Fetching Stack Overflow questions for tags: $($Tags -join ', ')" -Source "StackOverflowScraper"
    
    $baseUrl = "https://api.stackexchange.com/2.3/questions"
    $allQuestions = @()
    
    for ($page = 1; $page -le $MaxPages; $page++) {
        try {
            $params = @{
                site = "stackoverflow"
                tagged = ($Tags -join ';')
                sort = "votes"
                order = "desc"
                pagesize = $PageSize
                page = $page
                filter = "!9_bDDxJY5"  # Includes question body, answers, and code
            }
            
            $queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
            $url = "$baseUrl?$queryString"
            
            Write-ScrapingLog -Message "Fetching page $page of Stack Overflow questions" -Source "StackOverflowScraper"
            
            $response = Invoke-WebRequestWithRetry -Uri $url
            $data = $response.Content | ConvertFrom-Json
            
            if ($data.items) {
                $filteredQuestions = $data.items | Where-Object { $_.score -ge $MinScore -and $_.answer_count -gt 0 }
                $allQuestions += $filteredQuestions
                
                Write-ScrapingLog -Message "Found $($filteredQuestions.Count) qualifying questions on page $page" -Source "StackOverflowScraper"
            }
            
            # Check if we have more pages
            if (-not $data.has_more) {
                Write-ScrapingLog -Message "No more pages available" -Source "StackOverflowScraper"
                break
            }
            
            # Rate limiting - Stack Overflow API allows 300 requests per day
            Start-Sleep -Seconds 2
        }
        catch {
            Write-ScrapingLog -Message "Failed to fetch page $page : $($_.Exception.Message)" -Level "Error" -Source "StackOverflowScraper"
            break
        }
    }
    
    Write-ScrapingLog -Message "Retrieved $($allQuestions.Count) total questions from Stack Overflow" -Source "StackOverflowScraper"
    return $allQuestions
}

function Get-QuestionDetails {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [int]$QuestionId
    )
    
    try {
        $url = "https://api.stackexchange.com/2.3/questions/$QuestionId"
        $params = @{
            site = "stackoverflow"
            filter = "!9_bDDxJY5"  # Includes body and answers
        }
        
        $queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
        $fullUrl = "$url?$queryString"
        
        $response = Invoke-WebRequestWithRetry -Uri $fullUrl
        $data = $response.Content | ConvertFrom-Json
        
        if ($data.items -and $data.items.Count -gt 0) {
            return $data.items[0]
        }
        
        return $null
    }
    catch {
        Write-ScrapingLog -Message "Failed to get details for question $QuestionId : $($_.Exception.Message)" -Level "Warning" -Source "StackOverflowScraper"
        return $null
    }
}

function Get-QuestionAnswers {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [int]$QuestionId
    )
    
    try {
        $url = "https://api.stackexchange.com/2.3/questions/$QuestionId/answers"
        $params = @{
            site = "stackoverflow"
            sort = "votes"
            order = "desc"
            filter = "!9_bDDxJY5"  # Includes answer body
        }
        
        $queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
        $fullUrl = "$url?$queryString"
        
        $response = Invoke-WebRequestWithRetry -Uri $fullUrl
        $data = $response.Content | ConvertFrom-Json
        
        if ($data.items) {
            # Filter for accepted answers or high-scored answers
            $goodAnswers = $data.items | Where-Object { $_.is_accepted -eq $true -or $_.score -ge 3 }
            return $goodAnswers
        }
        
        return @()
    }
    catch {
        Write-ScrapingLog -Message "Failed to get answers for question $QuestionId : $($_.Exception.Message)" -Level "Warning" -Source "StackOverflowScraper"
        return @()
    }
}

function Convert-HtmlToText {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$HtmlContent
    )
    
    # Basic HTML to text conversion
    $text = $HtmlContent
    
    # Convert common HTML entities
    $text = $text -replace '&lt;', '<'
    $text = $text -replace '&gt;', '>'
    $text = $text -replace '&amp;', '&'
    $text = $text -replace '&quot;', '"'
    $text = $text -replace '&#39;', "'"
    
    # Remove HTML tags but preserve code blocks
    $text = $text -replace '<code>(.*?)</code>', '`$1`'
    $text = $text -replace '<pre><code>(.*?)</code></pre>', "```powershell`n`$1`n```"
    $text = $text -replace '<[^>]+>', ''
    
    # Clean up whitespace
    $text = $text -replace '\s+', ' '
    $text = $text.Trim()
    
    return $text
}

function Process-StackOverflowQuestion {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Question
    )
    
    Write-ScrapingLog -Message "Processing Stack Overflow question: $($Question.title)" -Source "StackOverflowScraper"
    
    try {
        # Get question details if not already included
        $questionDetails = if ($Question.body) { $Question } else { Get-QuestionDetails -QuestionId $Question.question_id }
        
        if (-not $questionDetails) {
            Write-ScrapingLog -Message "Could not get details for question $($Question.question_id)" -Level "Warning" -Source "StackOverflowScraper"
            return $null
        }
        
        # Get answers
        $answers = Get-QuestionAnswers -QuestionId $Question.question_id
        
        # Convert HTML content to text
        $questionText = Convert-HtmlToText -HtmlContent $questionDetails.body
        $answerTexts = $answers | ForEach-Object { Convert-HtmlToText -HtmlContent $_.body }
        
        # Combine question and best answers
        $fullContent = @"
# Question: $($questionDetails.title)

**Asked by:** $($questionDetails.owner.display_name)
**Score:** $($questionDetails.score)
**Views:** $($questionDetails.view_count)
**Tags:** $($questionDetails.tags -join ', ')

## Problem Description
$questionText

## Solutions
$($answerTexts -join "`n`n---`n`n")
"@
        
        # Determine domain and operation from question content
        $domain = Get-DomainFromContent -Content $fullContent
        $operation = Get-OperationFromContent -Content $fullContent
        
        # Create knowledge pattern
        $pattern = New-KnowledgePattern -Title $questionDetails.title -Content $fullContent -SourceUrl $questionDetails.link -SourceType "stackoverflow" -Domain $domain -Operation $operation -Author $questionDetails.owner.display_name -CredibilityScore (Calculate-StackOverflowCredibility -Question $questionDetails -Answers $answers)
        
        # Add Stack Overflow specific metadata
        $pattern.Tags += @("stackoverflow", "community", "qa")
        $pattern.RelevanceScore = [Math]::Min(1.0, ($questionDetails.score / 50.0))
        
        return $pattern
    }
    catch {
        Write-ScrapingLog -Message "Failed to process question $($Question.question_id): $($_.Exception.Message)" -Level "Error" -Source "StackOverflowScraper"
        return $null
    }
}

function Get-DomainFromContent {
    [CmdletBinding()]
    param([string]$Content)
    
    switch -Regex ($Content) {
        "Get-ADUser|Set-ADUser|New-ADUser|Remove-ADUser" { return "user_management" }
        "Get-ADGroup|Set-ADGroup|New-ADGroup|Add-ADGroupMember|Remove-ADGroupMember" { return "group_management" }
        "Get-ADComputer|Set-ADComputer|New-ADComputer" { return "computer_management" }
        "Get-ADOrganizationalUnit|New-ADOrganizationalUnit" { return "ou_management" }
        "password|Set-ADAccountPassword|Reset" { return "password_management" }
        "permission|ACL|security" { return "security_management" }
        default { return "general_ad" }
    }
}

function Get-OperationFromContent {
    [CmdletBinding()]
    param([string]$Content)
    
    switch -Regex ($Content) {
        "Get-AD|Search-AD|Find" { return "read" }
        "Set-AD|Update|Modify|Change" { return "update" }
        "New-AD|Create|Add" { return "create" }
        "Remove-AD|Delete" { return "delete" }
        "Enable-AD" { return "enable" }
        "Disable-AD" { return "disable" }
        "Unlock-AD" { return "unlock" }
        "Reset" { return "reset" }
        default { return "other" }
    }
}

function Calculate-StackOverflowCredibility {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Question,
        
        [Parameter(Mandatory = $true)]
        [array]$Answers
    )
    
    $baseScore = 0.6  # Base credibility for Stack Overflow
    
    # Question score factor
    $questionFactor = [Math]::Min(0.2, ($Question.score / 100.0))
    
    # Answer quality factor
    $answerFactor = 0.0
    if ($Answers.Count -gt 0) {
        $hasAcceptedAnswer = $Answers | Where-Object { $_.is_accepted -eq $true }
        $maxAnswerScore = ($Answers | Measure-Object -Property score -Maximum).Maximum
        
        if ($hasAcceptedAnswer) {
            $answerFactor += 0.1
        }
        
        $answerFactor += [Math]::Min(0.1, ($maxAnswerScore / 50.0))
    }
    
    # View count factor (popularity)
    $viewFactor = [Math]::Min(0.1, ($Question.view_count / 10000.0))
    
    $totalScore = $baseScore + $questionFactor + $answerFactor + $viewFactor
    return [Math]::Min(1.0, $totalScore)
}

# Main execution
function Start-StackOverflowScraping {
    [CmdletBinding()]
    param()
    
    Write-ScrapingLog -Message "Starting Stack Overflow scraping process" -Source "StackOverflowScraper"
    
    try {
        # Initialize framework
        if (-not (Initialize-ScrapingFramework -ConfigPath $ConfigPath)) {
            throw "Failed to initialize scraping framework"
        }
        
        # Get questions
        $questions = Get-StackOverflowQuestions -Tags $Tags -MinScore $MinScore
        
        if ($questions.Count -eq 0) {
            Write-ScrapingLog -Message "No questions found matching criteria" -Level "Warning" -Source "StackOverflowScraper"
            return @()
        }
        
        # Limit to max questions
        if ($questions.Count -gt $MaxQuestions) {
            $questions = $questions | Select-Object -First $MaxQuestions
            Write-ScrapingLog -Message "Limited to first $MaxQuestions questions" -Source "StackOverflowScraper"
        }
        
        # Process each question
        $patterns = @()
        $totalQuestions = $questions.Count
        $currentIndex = 0
        
        foreach ($question in $questions) {
            $currentIndex++
            Write-Progress -Activity "Scraping Stack Overflow" -Status "Processing question $currentIndex of $totalQuestions" -PercentComplete (($currentIndex / $totalQuestions) * 100)
            
            $pattern = Process-StackOverflowQuestion -Question $question
            if ($pattern) {
                $patterns += $pattern
            }
            
            # Rate limiting
            Start-Sleep -Seconds 2
        }
        
        Write-Progress -Activity "Scraping Stack Overflow" -Completed
        
        # Save results
        if ($patterns.Count -gt 0) {
            $outputFile = Save-ScrapingResults -Patterns $patterns -SourceType "stackoverflow"
            Write-ScrapingLog -Message "Successfully scraped $($patterns.Count) patterns from Stack Overflow" -Source "StackOverflowScraper"
            Write-ScrapingLog -Message "Results saved to: $outputFile" -Source "StackOverflowScraper"
        } else {
            Write-ScrapingLog -Message "No patterns extracted from Stack Overflow" -Level "Warning" -Source "StackOverflowScraper"
        }
        
        return $patterns
    }
    catch {
        Write-ScrapingLog -Message "Stack Overflow scraping failed: $($_.Exception.Message)" -Level "Error" -Source "StackOverflowScraper"
        throw
    }
}

# Execute if run directly
if ($MyInvocation.InvocationName -ne '.') {
    Start-StackOverflowScraping
}
