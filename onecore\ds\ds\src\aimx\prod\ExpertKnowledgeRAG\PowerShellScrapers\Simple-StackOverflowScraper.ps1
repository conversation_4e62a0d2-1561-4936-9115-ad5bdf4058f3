# Simple StackOverflow Scraper - Working Version
# Gets complete Q&A content without complex URL construction
param(
    [Parameter(Mandatory = $false)]
    [int]$MaxQuestions = 10,
    
    [Parameter(Mandatory = $false)]
    [int]$MinScore = 50,
    
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "./ScrapedData/simple_stackoverflow_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
)

Write-Host "Simple StackOverflow Scraper - Working Version" -ForegroundColor Cyan
Write-Host "Getting complete Q&A content for RAG usage" -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Gray

# Helper function to clean HTML content
function Clean-HtmlContent {
    param([string]$HtmlContent)
    
    if (-not $HtmlContent) { return "" }
    
    # Remove HTML tags but preserve code blocks
    $cleaned = $HtmlContent -replace '<pre><code>', '```powershell' -replace '</code></pre>', '```'
    $cleaned = $cleaned -replace '<code>', '`' -replace '</code>', '`'
    $cleaned = $cleaned -replace '<[^>]+>', ''
    $cleaned = $cleaned -replace '&lt;', '<' -replace '&gt;', '>' -replace '&amp;', '&' -replace '&quot;', '"'
    $cleaned = $cleaned -replace '\s+', ' ' -replace '^\s+|\s+$', ''
    
    return $cleaned
}

# Helper function to extract PowerShell code blocks
function Extract-PowerShellCode {
    param([string]$Content)
    
    $codeBlocks = @()
    
    # Extract code from various patterns
    $patterns = @(
        '```powershell\s*(.*?)```',
        '```ps\s*(.*?)```',
        '```\s*((?:Get-|Set-|New-|Remove-|Add-)[^\s`]+.*?)```',
        '`([^`]*(?:Get-|Set-|New-|Remove-|Add-)[^`]*)`'
    )
    
    foreach ($pattern in $patterns) {
        $matches = [regex]::Matches($Content, $pattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        foreach ($match in $matches) {
            $code = $match.Groups[1].Value.Trim()
            if ($code.Length -gt 5 -and $code -match '(Get-|Set-|New-|Remove-|Add-)') {
                $codeBlocks += $code
            }
        }
    }
    
    return $codeBlocks | Select-Object -Unique
}

try {
    # Search for PowerShell questions using the working approach
    $baseUrl = "https://api.stackexchange.com/2.3/"
    $tags = "powershell"
    $searchUrl = "${baseUrl}questions?order=desc&sort=votes&tagged=${tags}&site=stackoverflow&pagesize=100&filter=withbody"
    
    Write-Host "Searching for PowerShell questions..." -ForegroundColor Yellow
    Write-Host "URL: $searchUrl" -ForegroundColor Gray
    
    $response = Invoke-RestMethod -Uri $searchUrl -Method Get -ErrorAction Stop
    $allQuestions = $response.items | Where-Object { $_.score -ge $MinScore }
    $questions = $allQuestions | Select-Object -First $MaxQuestions
    
    Write-Host "Found $($questions.Count) questions to process (from $($allQuestions.Count) total above score threshold)" -ForegroundColor Green
    
    $knowledgeBase = @{
        schema_version = "1.0"
        scraped_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        source = "stackoverflow_simple"
        total_entries = 0
        entries = @()
    }
    
    $processedCount = 0
    
    foreach ($question in $questions) {
        $processedCount++
        Write-Host "Processing question $processedCount/$($questions.Count): $($question.title)" -ForegroundColor White
        
        try {
            # Get answers for this question
            $answersUrl = "${baseUrl}questions/$($question.question_id)/answers?order=desc&sort=votes&site=stackoverflow&filter=withbody"
            Write-Host "  Getting answers from: $answersUrl" -ForegroundColor Gray
            
            $answersResponse = Invoke-RestMethod -Uri $answersUrl -Method Get -ErrorAction Stop
            
            # Build clean content
            $cleanTitle = Clean-HtmlContent $question.title
            $cleanQuestionBody = Clean-HtmlContent $question.body
            
            $content = "# Question: $cleanTitle`n`n"
            $content += "**Score:** $($question.score) | **Views:** $($question.view_count) | **Answers:** $($answersResponse.items.Count)`n`n"
            $content += "## Problem Description`n`n$cleanQuestionBody`n`n"
            
            # Add all answers
            $allCodeExamples = @()
            $allCmdlets = @()
            
            if ($answersResponse.items.Count -gt 0) {
                $content += "## Solutions`n`n"
                
                $answerIndex = 1
                foreach ($answer in $answersResponse.items) {
                    $cleanAnswerBody = Clean-HtmlContent $answer.body
                    
                    $content += "### Solution $answerIndex"
                    if ($answer.is_accepted) {
                        $content += " ✅ (Accepted Answer)"
                    }
                    $content += " - Score: $($answer.score)`n`n"
                    $content += "$cleanAnswerBody`n`n"
                    
                    # Extract code examples from this answer
                    $answerCode = Extract-PowerShellCode $answer.body
                    foreach ($code in $answerCode) {
                        $allCodeExamples += @{
                            description = "Solution $answerIndex code example"
                            code = $code
                            language = "powershell"
                        }
                    }
                    
                    $answerIndex++
                }
            }
            
            # Extract code from question too
            $questionCode = Extract-PowerShellCode $question.body
            foreach ($code in $questionCode) {
                $allCodeExamples += @{
                    description = "Question code example"
                    code = $code
                    language = "powershell"
                }
            }
            
            # Extract cmdlets from all content
            $cmdletPattern = '(Get-|Set-|New-|Remove-|Add-|Enable-|Disable-|Test-|Start-|Stop-|Restart-)[A-Za-z][A-Za-z0-9]*'
            $cmdletMatches = [regex]::Matches($content, $cmdletPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            $allCmdlets = $cmdletMatches | ForEach-Object { $_.Value } | Select-Object -Unique
            
            # Create clean knowledge entry
            $entry = @{
                id = "stackoverflow_$($question.question_id)"
                title = $cleanTitle
                content = $content
                code_examples = $allCodeExamples | Select-Object -Unique
                source = @{
                    type = "stackoverflow"
                    url = $question.link
                    credibility = [Math]::Min(0.95, 0.5 + ($question.score / 1000))
                }
                tags = @($question.tags) + @("powershell", "stackoverflow")
                cmdlets = $allCmdlets
                last_updated = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
                metadata = @{
                    score = $question.score
                    view_count = $question.view_count
                    answer_count = $answersResponse.items.Count
                    has_accepted_answer = ($answersResponse.items | Where-Object { $_.is_accepted }).Count -gt 0
                }
            }
            
            $knowledgeBase.entries += $entry
            Write-Host "  ✅ Successfully processed" -ForegroundColor Green
            
            # Rate limiting
            Start-Sleep -Milliseconds 200
        }
        catch {
            Write-Host "  ❌ Failed to process question $($question.question_id): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    $knowledgeBase.total_entries = $knowledgeBase.entries.Count
    
    # Save results
    $knowledgeBase | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "`nScraping completed successfully!" -ForegroundColor Green
    Write-Host "Total entries: $($knowledgeBase.total_entries)" -ForegroundColor Green
    Write-Host "Output saved to: $OutputPath" -ForegroundColor Green
    
    # Show sample of what we got
    if ($knowledgeBase.entries.Count -gt 0) {
        $sample = $knowledgeBase.entries[0]
        Write-Host "`nSample entry:" -ForegroundColor Yellow
        Write-Host "Title: $($sample.title)" -ForegroundColor White
        Write-Host "Content length: $($sample.content.Length) characters" -ForegroundColor White
        Write-Host "Code examples: $($sample.code_examples.Count)" -ForegroundColor White
        Write-Host "Cmdlets found: $($sample.cmdlets.Count)" -ForegroundColor White
        if ($sample.cmdlets.Count -gt 0) {
            Write-Host "Cmdlets: $($sample.cmdlets -join ', ')" -ForegroundColor Gray
        }
    }
}
catch {
    Write-Host "Scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Full error: $($_.Exception)" -ForegroundColor Red
    exit 1
}
