# Debug the code extraction function
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Debug Code Extraction" -ForegroundColor Cyan

# Import core module
$coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
Import-Module $coreModulePath -Force

# Initialize framework
$result = Initialize-ScrapingFramework -ConfigPath $ConfigPath

# Load config to check patterns
$config = Get-Content $ConfigPath | ConvertFrom-Json

Write-Host "`nConfig patterns:" -ForegroundColor Yellow
foreach ($pattern in $config.PatternExtraction.CodeBlockPatterns) {
    Write-Host "  $pattern" -ForegroundColor Gray
}

Write-Host "`nMinimum code length: $($config.QualityFilters.MinimumCodeLength)" -ForegroundColor Yellow

# Test content
$testContent = @'
Here is some PowerShell code:
```powershell
Get-ADUser -Filter "Name -eq '<PERSON>'"
Set-ADUser -Identity jsmith -Title "Manager"
```
And some more text with Get-ADGroup and New-ADUser commands.

Another code block:
<code>
New-ADGroup -Name "TestGroup" -GroupScope Global
</code>
'@

Write-Host "`nTest content:" -ForegroundColor Yellow
Write-Host $testContent -ForegroundColor Gray

# Debug each pattern manually
Write-Host "`nTesting each pattern manually:" -ForegroundColor Yellow
foreach ($pattern in $config.PatternExtraction.CodeBlockPatterns) {
    Write-Host "`nPattern: $pattern" -ForegroundColor Cyan
    try {
        $matches = [regex]::Matches($testContent, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Multiline)
        Write-Host "  Found $($matches.Count) matches" -ForegroundColor Green
        
        foreach ($match in $matches) {
            Write-Host "  Match groups: $($match.Groups.Count)" -ForegroundColor Gray
            for ($i = 0; $i -lt $match.Groups.Count; $i++) {
                $group = $match.Groups[$i]
                Write-Host "    Group $i : '$($group.Value.Substring(0, [Math]::Min(50, $group.Value.Length)))...'" -ForegroundColor Gray
            }
            
            if ($match.Groups.Count -gt 1) {
                $code = $match.Groups[1].Value.Trim()
                Write-Host "  Code length: $($code.Length)" -ForegroundColor Green
                Write-Host "  Passes minimum length: $($code.Length -gt $config.QualityFilters.MinimumCodeLength)" -ForegroundColor Green
                if ($code.Length -gt $config.QualityFilters.MinimumCodeLength) {
                    Write-Host "  Code content: $($code.Substring(0, [Math]::Min(100, $code.Length)))..." -ForegroundColor Cyan
                }
            }
        }
    }
    catch {
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test the actual function
Write-Host "`nTesting Extract-CodeBlocks function:" -ForegroundColor Yellow
try {
    $codeBlocks = Extract-CodeBlocks -Content $testContent
    Write-Host "  Function returned $($codeBlocks.Count) code blocks" -ForegroundColor Green
    foreach ($block in $codeBlocks) {
        Write-Host "  Block: $($block.Substring(0, [Math]::Min(100, $block.Length)))..." -ForegroundColor Cyan
    }
}
catch {
    Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nDebug extraction completed!" -ForegroundColor Cyan
