# Test config loading and framework initialization
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing Config Loading and Framework Initialization" -ForegroundColor Cyan

# Test 1: Check config file
Write-Host "`nTest 1: Config file validation" -ForegroundColor Yellow
if (Test-Path $ConfigPath) {
    Write-Host "✅ Config file exists: $ConfigPath" -ForegroundColor Green
    
    try {
        $config = Get-Content $ConfigPath | ConvertFrom-Json
        Write-Host "✅ Config file is valid JSON" -ForegroundColor Green
        
        # Check key properties
        if ($config.ScrapingConfiguration) {
            Write-Host "✅ ScrapingConfiguration section found" -ForegroundColor Green
            
            if ($config.ScrapingConfiguration.UserAgent) {
                Write-Host "✅ UserAgent found: $($config.ScrapingConfiguration.UserAgent)" -ForegroundColor Green
            } else {
                Write-Host "❌ UserAgent not found in config" -ForegroundColor Red
            }
            
            if ($config.ScrapingConfiguration.OutputDirectory) {
                Write-Host "✅ OutputDirectory found: $($config.ScrapingConfiguration.OutputDirectory)" -ForegroundColor Green
            } else {
                Write-Host "❌ OutputDirectory not found in config" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ ScrapingConfiguration section not found" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Config file is invalid JSON: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Config file not found: $ConfigPath" -ForegroundColor Red
}

# Test 2: Import core module
Write-Host "`nTest 2: Core module import" -ForegroundColor Yellow
try {
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    Write-Host "✅ Core module imported successfully" -ForegroundColor Green
}
catch {
    Write-Host "❌ Core module import failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 3: Framework initialization
Write-Host "`nTest 3: Framework initialization" -ForegroundColor Yellow
try {
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    if ($result) {
        Write-Host "✅ Framework initialization successful" -ForegroundColor Green
    } else {
        Write-Host "❌ Framework initialization returned false" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Framework initialization failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
}

# Test 4: Check script variables
Write-Host "`nTest 4: Script variables check" -ForegroundColor Yellow
try {
    # Access the script-scoped variables
    $moduleInfo = Get-Module ScrapingFramework
    if ($moduleInfo) {
        Write-Host "✅ Module is loaded: $($moduleInfo.Name)" -ForegroundColor Green
        Write-Host "  Version: $($moduleInfo.Version)" -ForegroundColor Gray
        Write-Host "  Exported functions: $($moduleInfo.ExportedFunctions.Count)" -ForegroundColor Gray
    }
    
    # Try to check if the config is accessible
    # Note: Script-scoped variables are not directly accessible from outside the module
    Write-Host "INFO: Script variables are internal to the module" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ Error checking script variables: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Simple web request without framework
Write-Host "`nTest 5: Direct web request (bypass framework)" -ForegroundColor Yellow
try {
    $testUrl = "https://httpbin.org/get"
    $headers = @{
        'User-Agent' = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    $response = Invoke-WebRequest -Uri $testUrl -Headers $headers -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ Direct web request successful" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
}
catch {
    Write-Host "❌ Direct web request failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Framework web request with explicit parameters
Write-Host "`nTest 6: Framework web request with explicit parameters" -ForegroundColor Yellow
try {
    $testUrl = "https://httpbin.org/get"
    $customHeaders = @{
        'User-Agent' = 'Test-Agent'
    }
    $response = Invoke-WebRequestWithRetry -Uri $testUrl -Headers $customHeaders -MaxRetries 1 -DelaySeconds 1 -TimeoutSec 10
    Write-Host "✅ Framework web request with explicit headers successful" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
}
catch {
    Write-Host "❌ Framework web request with explicit headers failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
}

Write-Host "`nConfig loading test completed!" -ForegroundColor Cyan
