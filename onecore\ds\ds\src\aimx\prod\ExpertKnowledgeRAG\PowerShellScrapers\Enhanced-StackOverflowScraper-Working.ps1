# Enhanced Stack Overflow Scraper - WORKING VERSION with Full Offline Content
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxQuestions = 10,
    
    [Parameter(Mandatory = $false)]
    [int]$MinScore = 5
)

Write-Host "Enhanced Stack Overflow Scraper - Full Offline Q&A Content" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

try {
    # Import core module for pattern creation and saving
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework for pattern creation
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    if (-not $result) {
        throw "Failed to initialize scraping framework"
    }
    
    Write-Host "Framework initialized successfully" -ForegroundColor Green
    
    # Get Stack Overflow questions using working API approach
    Write-Host "`nFetching PowerShell AD questions from Stack Overflow..." -ForegroundColor Yellow
    
    $baseUrl = "https://api.stackexchange.com/2.3/"
    $questionsUrl = "${baseUrl}questions?order=desc&sort=votes&tagged=powershell&site=stackoverflow&pagesize=${MaxQuestions}&filter=withbody"
    
    Write-Host "API URL: $questionsUrl" -ForegroundColor Gray
    
    $questionsData = Invoke-RestMethod -Uri $questionsUrl -Method Get
    
    Write-Host "Found $($questionsData.items.Count) questions, quota remaining: $($questionsData.quota_remaining)" -ForegroundColor Green
    
    # Filter by minimum score and AD relevance
    $filteredQuestions = $questionsData.items | Where-Object { 
        $_.score -ge $MinScore -and 
        ($_.title -like "*active*directory*" -or 
         $_.title -like "*AD*" -or 
         $_.body -like "*active*directory*" -or 
         $_.body -like "*Get-ADUser*" -or 
         $_.body -like "*Set-ADUser*" -or 
         $_.body -like "*Get-ADGroup*" -or
         $_.tags -contains "active-directory")
    }
    
    Write-Host "Filtered to $($filteredQuestions.Count) AD-related questions with score >= $MinScore" -ForegroundColor Gray
    
    # If no AD-specific questions, take top PowerShell questions
    if ($filteredQuestions.Count -eq 0) {
        $filteredQuestions = $questionsData.items | Where-Object { $_.score -ge $MinScore } | Select-Object -First 5
        Write-Host "Using top $($filteredQuestions.Count) PowerShell questions instead" -ForegroundColor Yellow
    }
    
    # Process each question with FULL content extraction
    $allPatterns = @()
    $count = 0
    
    foreach ($question in $filteredQuestions) {
        $count++
        Write-Progress -Activity "Enhanced Stack Overflow Scraping" -Status "Processing question $count/$($filteredQuestions.Count)" -PercentComplete (($count / $filteredQuestions.Count) * 100)
        Write-Host "`nProcessing: $($question.title)" -ForegroundColor White
        Write-Host "  Score: $($question.score), Views: $($question.view_count)" -ForegroundColor Gray
        
        try {
            # Get all answers for this question
            $answersUrl = "${baseUrl}questions/$($question.question_id)/answers?order=desc&sort=votes&site=stackoverflow&filter=withbody"
            $answersData = Invoke-RestMethod -Uri $answersUrl -Method Get
            
            Write-Host "  Found $($answersData.items.Count) answers" -ForegroundColor Gray
            
            # Build comprehensive Q&A content
            $fullContent = "# Question: $($question.title)`n`n"
            $fullContent += "**Score:** $($question.score) | **Views:** $($question.view_count) | **Tags:** $($question.tags -join ', ')`n`n"
            $fullContent += "**URL:** $($question.link)`n`n"
            $fullContent += "## Question Body`n`n"
            $fullContent += $question.body + "`n`n"
            
            # Extract code from question
            $allCodeBlocks = @()
            $questionCode = Extract-CodeBlocks -Content $question.body
            $allCodeBlocks += $questionCode
            
            # Add all answers with full content
            $totalScore = $question.score
            $bestAnswer = $null
            
            if ($answersData.items.Count -gt 0) {
                $fullContent += "## Answers ($($answersData.items.Count) total)`n`n"
                
                $answerIndex = 1
                foreach ($answer in $answersData.items) {
                    $fullContent += "### Answer $answerIndex (Score: $($answer.score))"
                    if ($answer.is_accepted) {
                        $fullContent += " ✅ ACCEPTED ANSWER"
                        $bestAnswer = $answer
                    }
                    $fullContent += "`n`n"
                    $fullContent += $answer.body + "`n`n"
                    
                    # Extract code from answer
                    $answerCode = Extract-CodeBlocks -Content $answer.body
                    $allCodeBlocks += $answerCode
                    
                    $totalScore += $answer.score
                    $answerIndex++
                }
                
                # If no accepted answer, use highest scored answer
                if (-not $bestAnswer -and $answersData.items.Count -gt 0) {
                    $bestAnswer = $answersData.items[0]
                }
            }
            
            # Create comprehensive knowledge pattern
            $pattern = New-KnowledgePattern -Title "Stack Overflow Q&A: $($question.title)" -Content $fullContent -SourceUrl $question.link -SourceType "stackoverflow_qa" -Domain "general_ad" -Operation "read" -Author "Stack Overflow Community" -CredibilityScore 0.85
            
            if ($pattern) {
                # Add comprehensive Q&A metadata
                $pattern.QuestionScore = $question.score
                $pattern.ViewCount = $question.view_count
                $pattern.AnswerCount = $answersData.items.Count
                $pattern.TotalScore = $totalScore
                $pattern.HasAcceptedAnswer = ($bestAnswer -and $bestAnswer.is_accepted)
                $pattern.AllCodeBlocks = $allCodeBlocks
                $pattern.Tags = $question.tags
                
                # Set primary code template from best answer or question
                if ($allCodeBlocks.Count -gt 0) {
                    if ($bestAnswer) {
                        $bestAnswerCode = Extract-CodeBlocks -Content $bestAnswer.body
                        if ($bestAnswerCode.Count -gt 0) {
                            $pattern.CodeTemplate = $bestAnswerCode[0]
                        } else {
                            $pattern.CodeTemplate = $allCodeBlocks[0]
                        }
                    } else {
                        $pattern.CodeTemplate = $allCodeBlocks[0]
                    }
                }
                
                # Calculate relevance based on total engagement
                $pattern.RelevanceScore = [Math]::Min(1.0, ($totalScore / 500.0) + ($question.view_count / 2000000.0))
                
                # Extract best practices and common mistakes from content
                $allText = $fullContent.ToLower()
                if ($allText -like "*best practice*" -or $allText -like "*recommended*" -or $allText -like "*should use*") {
                    $pattern.BestPractices += "Contains recommended practices and approaches"
                }
                if ($allText -like "*don't*" -or $allText -like "*avoid*" -or $allText -like "*mistake*" -or $allText -like "*wrong*" -or $allText -like "*error*") {
                    $pattern.CommonMistakes += "Contains warnings about common mistakes and errors"
                }
                
                $allPatterns += $pattern
                Write-Host "  ✅ Created comprehensive Q&A pattern: $($pattern.Id)" -ForegroundColor Green
                Write-Host "    Total score: $totalScore, Code blocks: $($allCodeBlocks.Count)" -ForegroundColor Gray
                Write-Host "    Full content: $($fullContent.Length) chars, Has accepted answer: $($pattern.HasAcceptedAnswer)" -ForegroundColor Gray
            }
        }
        catch {
            Write-Host "  ❌ Failed to process question: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Rate limiting
        Start-Sleep -Seconds 1
    }
    
    Write-Progress -Activity "Enhanced Stack Overflow Scraping" -Completed
    
    # Save comprehensive results
    if ($allPatterns.Count -gt 0) {
        $outputFile = Save-ScrapingResults -Patterns $allPatterns -SourceType "enhanced_stackoverflow"
        Write-Host "`nSUCCESS: Enhanced Stack Overflow scraping completed!" -ForegroundColor Green
        Write-Host "Patterns created: $($allPatterns.Count)" -ForegroundColor Cyan
        Write-Host "Output file: $outputFile" -ForegroundColor Cyan
        
        # Calculate comprehensive statistics
        $totalContentSize = ($allPatterns | ForEach-Object { $_.Content.Length }) | Measure-Object -Sum
        $totalCodeBlocks = ($allPatterns | ForEach-Object { $_.AllCodeBlocks.Count }) | Measure-Object -Sum
        $totalAnswers = ($allPatterns | ForEach-Object { $_.AnswerCount }) | Measure-Object -Sum
        $totalViews = ($allPatterns | ForEach-Object { $_.ViewCount }) | Measure-Object -Sum
        $avgScore = ($allPatterns | ForEach-Object { $_.QuestionScore }) | Measure-Object -Average
        
        Write-Host "`nComprehensive Statistics:" -ForegroundColor Yellow
        Write-Host "  Total offline content: $([Math]::Round($totalContentSize.Sum / 1024, 2)) KB" -ForegroundColor Cyan
        Write-Host "  Total code blocks: $($totalCodeBlocks.Sum)" -ForegroundColor Cyan
        Write-Host "  Total answers: $($totalAnswers.Sum)" -ForegroundColor Cyan
        Write-Host "  Total views: $($totalViews.Sum.ToString('N0'))" -ForegroundColor Cyan
        Write-Host "  Average question score: $([Math]::Round($avgScore.Average, 1))" -ForegroundColor Cyan
        
        # Show sample pattern
        if ($allPatterns.Count -gt 0) {
            $sample = $allPatterns[0]
            Write-Host "`nSample Q&A pattern:" -ForegroundColor Yellow
            Write-Host "  Title: $($sample.Title)" -ForegroundColor Gray
            Write-Host "  Question score: $($sample.QuestionScore), Views: $($sample.ViewCount.ToString('N0'))" -ForegroundColor Gray
            Write-Host "  Answers: $($sample.AnswerCount), Code blocks: $($sample.AllCodeBlocks.Count)" -ForegroundColor Gray
            Write-Host "  Has accepted answer: $($sample.HasAcceptedAnswer)" -ForegroundColor Gray
            Write-Host "  Content length: $($sample.Content.Length.ToString('N0')) characters" -ForegroundColor Gray
            Write-Host "  Relevance score: $($sample.RelevanceScore.ToString('F2'))" -ForegroundColor Gray
        }
    } else {
        Write-Host "`n❌ No patterns created" -ForegroundColor Red
    }
}
catch {
    Write-Host "`n❌ Enhanced Stack Overflow scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    exit 1
}
