using System.Text.Json.Serialization;

namespace IntentPlanningService.Models;

/// <summary>
/// Request for tool discovery from Universal Tool Manager
/// </summary>
public class ToolDiscoveryRequest
{
    [JsonPropertyName("category")]
    public string Category { get; set; } = string.Empty;

    [JsonPropertyName("requiredCapabilities")]
    public List<string> RequiredCapabilities { get; set; } = new();

    [JsonPropertyName("environment")]
    public string Environment { get; set; } = string.Empty;

    [JsonPropertyName("includeHealthStatus")]
    public bool IncludeHealthStatus { get; set; } = true;

    [JsonPropertyName("maxResults")]
    public int MaxResults { get; set; } = 50;
}

/// <summary>
/// Response from tool discovery service
/// </summary>
public class ToolDiscoveryResponse
{
    [JsonPropertyName("tools")]
    public List<AvailableTool> Tools { get; set; } = new();

    [JsonPropertyName("totalCount")]
    public int TotalCount { get; set; }

    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Represents a user's input request for IT operations
/// </summary>
public class UserRequest
{
    [JsonPropertyName("requestId")]
    public string RequestId { get; set; } = Guid.NewGuid().ToString();

    [JsonPropertyName("userId")]
    public string UserId { get; set; } = string.Empty;

    [JsonPropertyName("userInput")]
    public string UserInput { get; set; } = string.Empty;

    [JsonPropertyName("context")]
    public Dictionary<string, object> Context { get; set; } = new();

    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    [JsonPropertyName("priority")]
    public string Priority { get; set; } = "normal"; // low, normal, high, critical

    [JsonPropertyName("environment")]
    public string Environment { get; set; } = "production"; // production, staging, test, development
}

/// <summary>
/// Represents the extracted user goal from the input
/// </summary>
public class UserGoal
{
    [JsonPropertyName("goalId")]
    public string GoalId { get; set; } = Guid.NewGuid().ToString();

    [JsonPropertyName("primaryObjective")]
    public string PrimaryObjective { get; set; } = string.Empty;

    [JsonPropertyName("successCriteria")]
    public string SuccessCriteria { get; set; } = string.Empty;

    [JsonPropertyName("constraints")]
    public List<string> Constraints { get; set; } = new();

    [JsonPropertyName("urgencyLevel")]
    public string UrgencyLevel { get; set; } = "normal"; // low, normal, high, critical

    [JsonPropertyName("context")]
    public Dictionary<string, string> Context { get; set; } = new();

    [JsonPropertyName("extractionConfidence")]
    public double ExtractionConfidence { get; set; } = 0.0; // 0.0 - 1.0

    [JsonPropertyName("extractionMethod")]
    public string ExtractionMethod { get; set; } = "slm"; // slm, rule_based, hybrid
}

/// <summary>
/// Represents a single step in a workflow
/// </summary>
public class WorkflowStep
{
    [JsonPropertyName("stepId")]
    public string StepId { get; set; } = Guid.NewGuid().ToString();

    [JsonPropertyName("stepName")]
    public string StepName { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("toolId")]
    public string ToolId { get; set; } = string.Empty;

    [JsonPropertyName("operation")]
    public string Operation { get; set; } = string.Empty;

    [JsonPropertyName("parameters")]
    public Dictionary<string, object> Parameters { get; set; } = new();

    [JsonPropertyName("dependencies")]
    public List<string> Dependencies { get; set; } = new(); // stepIds this step depends on

    [JsonPropertyName("timeout")]
    public int TimeoutSeconds { get; set; } = 300; // 5 minutes default

    [JsonPropertyName("retryPolicy")]
    public RetryPolicy RetryPolicy { get; set; } = new();

    [JsonPropertyName("rollbackOperation")]
    public string? RollbackOperation { get; set; }

    [JsonPropertyName("rollbackParameters")]
    public Dictionary<string, object>? RollbackParameters { get; set; }

    [JsonPropertyName("isOptional")]
    public bool IsOptional { get; set; } = false;

    [JsonPropertyName("executionOrder")]
    public int ExecutionOrder { get; set; } = 0;
}

/// <summary>
/// Represents retry policy for workflow steps
/// </summary>
public class RetryPolicy
{
    [JsonPropertyName("maxRetries")]
    public int MaxRetries { get; set; } = 3;

    [JsonPropertyName("retryDelaySeconds")]
    public int RetryDelaySeconds { get; set; } = 5;

    [JsonPropertyName("exponentialBackoff")]
    public bool ExponentialBackoff { get; set; } = true;

    [JsonPropertyName("retryableErrors")]
    public List<string> RetryableErrors { get; set; } = new() { "timeout", "network_error", "temporary_failure" };
}

/// <summary>
/// Represents an executable workflow generated from user goals
/// </summary>
public class ExecutableWorkflow
{
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = Guid.NewGuid().ToString();

    [JsonPropertyName("workflowName")]
    public string WorkflowName { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("steps")]
    public List<WorkflowStep> Steps { get; set; } = new();

    [JsonPropertyName("errorHandling")]
    public Dictionary<string, string> ErrorHandling { get; set; } = new();

    [JsonPropertyName("requiredPermissions")]
    public List<string> RequiredPermissions { get; set; } = new();

    [JsonPropertyName("riskLevel")]
    public string RiskLevel { get; set; } = "medium"; // low, medium, high, critical

    [JsonPropertyName("estimatedTotalTimeSeconds")]
    public int EstimatedTotalTimeSeconds { get; set; } = 0;

    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    [JsonPropertyName("approvalRequired")]
    public bool ApprovalRequired { get; set; } = false;

    [JsonPropertyName("approvalReason")]
    public string? ApprovalReason { get; set; }

    [JsonPropertyName("planningConfidence")]
    public double PlanningConfidence { get; set; } = 0.0; // 0.0 - 1.0

    [JsonPropertyName("planningMethod")]
    public string PlanningMethod { get; set; } = "slm"; // slm, rule_based, hybrid
}

/// <summary>
/// Represents an available IT operation tool
/// </summary>
public class AvailableTool
{
    [JsonPropertyName("toolId")]
    public string ToolId { get; set; } = string.Empty;

    [JsonPropertyName("toolName")]
    public string ToolName { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("category")]
    public string Category { get; set; } = string.Empty; // ad, exchange, azure, powershell, etc.

    [JsonPropertyName("operations")]
    public List<ToolOperation> Operations { get; set; } = new();

    [JsonPropertyName("capabilities")]
    public ToolCapabilities Capabilities { get; set; } = new();

    [JsonPropertyName("isAvailable")]
    public bool IsAvailable { get; set; } = true;

    [JsonPropertyName("healthStatus")]
    public string HealthStatus { get; set; } = "healthy"; // healthy, degraded, unhealthy

    [JsonPropertyName("lastHealthCheck")]
    public DateTime LastHealthCheck { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Represents an operation that a tool can perform
/// </summary>
public class ToolOperation
{
    [JsonPropertyName("operationId")]
    public string OperationId { get; set; } = string.Empty;

    [JsonPropertyName("operationName")]
    public string OperationName { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("parameters")]
    public List<OperationParameter> Parameters { get; set; } = new();

    [JsonPropertyName("returnType")]
    public string ReturnType { get; set; } = string.Empty;

    [JsonPropertyName("estimatedDurationSeconds")]
    public int EstimatedDurationSeconds { get; set; } = 30;

    [JsonPropertyName("riskLevel")]
    public string RiskLevel { get; set; } = "medium"; // low, medium, high, critical

    [JsonPropertyName("requiredPermissions")]
    public List<string> RequiredPermissions { get; set; } = new();

    [JsonPropertyName("supportsRollback")]
    public bool SupportsRollback { get; set; } = false;

    [JsonPropertyName("rollbackOperation")]
    public string? RollbackOperation { get; set; }
}

/// <summary>
/// Represents a parameter for a tool operation
/// </summary>
public class OperationParameter
{
    [JsonPropertyName("parameterName")]
    public string ParameterName { get; set; } = string.Empty;

    [JsonPropertyName("parameterType")]
    public string ParameterType { get; set; } = string.Empty; // string, int, bool, array, object

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("isRequired")]
    public bool IsRequired { get; set; } = true;

    [JsonPropertyName("defaultValue")]
    public object? DefaultValue { get; set; }

    [JsonPropertyName("validationRules")]
    public List<string> ValidationRules { get; set; } = new();

    [JsonPropertyName("examples")]
    public List<object> Examples { get; set; } = new();
}

/// <summary>
/// Represents the capabilities of a tool
/// </summary>
public class ToolCapabilities
{
    [JsonPropertyName("supportsParallelExecution")]
    public bool SupportsParallelExecution { get; set; } = false;

    [JsonPropertyName("supportsBatching")]
    public bool SupportsBatching { get; set; } = false;

    [JsonPropertyName("supportsStreaming")]
    public bool SupportsStreaming { get; set; } = false;

    [JsonPropertyName("supportsRollback")]
    public bool SupportsRollback { get; set; } = false;

    [JsonPropertyName("maxConcurrentOperations")]
    public int MaxConcurrentOperations { get; set; } = 1;

    [JsonPropertyName("maxBatchSize")]
    public int MaxBatchSize { get; set; } = 1;

    [JsonPropertyName("supportedEnvironments")]
    public List<string> SupportedEnvironments { get; set; } = new() { "production", "staging", "test", "development" };

    [JsonPropertyName("resourceRequirements")]
    public Dictionary<string, object> ResourceRequirements { get; set; } = new();
}

/// <summary>
/// Represents the result of intent analysis
/// </summary>
public class IntentAnalysisResult
{
    [JsonPropertyName("requestId")]
    public string RequestId { get; set; } = string.Empty;

    [JsonPropertyName("userGoal")]
    public UserGoal UserGoal { get; set; } = new();

    [JsonPropertyName("workflow")]
    public ExecutableWorkflow Workflow { get; set; } = new();

    [JsonPropertyName("analysisConfidence")]
    public double AnalysisConfidence { get; set; } = 0.0; // 0.0 - 1.0

    [JsonPropertyName("analysisMethod")]
    public string AnalysisMethod { get; set; } = "slm"; // slm, rule_based, hybrid

    [JsonPropertyName("processingTimeMs")]
    public long ProcessingTimeMs { get; set; } = 0;

    [JsonPropertyName("warnings")]
    public List<string> Warnings { get; set; } = new();

    [JsonPropertyName("errors")]
    public List<string> Errors { get; set; } = new();

    [JsonPropertyName("requiresApproval")]
    public bool RequiresApproval { get; set; } = false;

    [JsonPropertyName("approvalReason")]
    public string? ApprovalReason { get; set; }

    [JsonPropertyName("alternativeWorkflows")]
    public List<ExecutableWorkflow> AlternativeWorkflows { get; set; } = new();

    [JsonPropertyName("success")]
    public bool Success { get; set; } = true;

    [JsonPropertyName("errorMessage")]
    public string? ErrorMessage { get; set; }

    [JsonPropertyName("primaryWorkflow")]
    public ExecutableWorkflow? PrimaryWorkflow { get; set; }

    [JsonPropertyName("availableTools")]
    public List<AvailableTool> AvailableTools { get; set; } = new();
}
