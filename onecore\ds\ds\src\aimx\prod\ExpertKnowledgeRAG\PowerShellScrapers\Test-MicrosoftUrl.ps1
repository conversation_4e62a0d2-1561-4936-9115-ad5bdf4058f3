# Test Microsoft URL specifically
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing Microsoft URL Specifically" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    
    $testUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/"
    
    # Test 1: Direct PowerShell request
    Write-Host "`nTest 1: Direct PowerShell request to Microsoft" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 30
        Write-Host "✅ Direct request successful" -ForegroundColor Green
        Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
        Write-Host "  Content length: $($response.Content.Length)" -ForegroundColor Gray
        Write-Host "  Content type: $($response.Headers['Content-Type'])" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ Direct request failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Test 2: Framework request with no custom headers
    Write-Host "`nTest 2: Framework request with no custom headers" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequestWithRetry -Uri $testUrl -MaxRetries 1
        Write-Host "✅ Framework request (no custom headers) successful" -ForegroundColor Green
        Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
        Write-Host "  Content length: $($response.Content.Length)" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ Framework request (no custom headers) failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "  Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
        Write-Host "  Stack trace:" -ForegroundColor Red
        Write-Host $_.ScriptStackTrace -ForegroundColor Red
    }
    
    # Test 3: Framework request with empty headers
    Write-Host "`nTest 3: Framework request with empty headers" -ForegroundColor Yellow
    try {
        $emptyHeaders = @{}
        $response = Invoke-WebRequestWithRetry -Uri $testUrl -Headers $emptyHeaders -MaxRetries 1
        Write-Host "✅ Framework request (empty headers) successful" -ForegroundColor Green
        Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
        Write-Host "  Content length: $($response.Content.Length)" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ Framework request (empty headers) failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "  Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    }
    
    # Test 4: Framework request with custom User-Agent
    Write-Host "`nTest 4: Framework request with custom User-Agent" -ForegroundColor Yellow
    try {
        $customHeaders = @{
            'User-Agent' = 'PowerShell-Scraper/1.0'
        }
        $response = Invoke-WebRequestWithRetry -Uri $testUrl -Headers $customHeaders -MaxRetries 1
        Write-Host "✅ Framework request (custom User-Agent) successful" -ForegroundColor Green
        Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
        Write-Host "  Content length: $($response.Content.Length)" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ Framework request (custom User-Agent) failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "  Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    }
    
    # Test 5: Check what headers are being sent
    Write-Host "`nTest 5: Debug headers being sent" -ForegroundColor Yellow
    try {
        # Manually construct headers like the framework does
        $config = Get-Content $ConfigPath | ConvertFrom-Json
        $defaultHeaders = @{
            'User-Agent' = $config.ScrapingConfiguration.UserAgent
        }
        
        Write-Host "  Default User-Agent: $($defaultHeaders['User-Agent'])" -ForegroundColor Gray
        
        $customHeaders = @{
            'User-Agent' = 'Test-Agent'
        }
        
        # Merge like the framework does
        $allHeaders = $defaultHeaders.Clone()
        foreach ($key in $customHeaders.Keys) {
            $allHeaders[$key] = $customHeaders[$key]
        }
        
        Write-Host "  Final User-Agent: $($allHeaders['User-Agent'])" -ForegroundColor Gray
        Write-Host "  All headers:" -ForegroundColor Gray
        foreach ($key in $allHeaders.Keys) {
            Write-Host "    $key : $($allHeaders[$key])" -ForegroundColor DarkGray
        }
        
        # Test with manually merged headers
        $response = Invoke-WebRequest -Uri $testUrl -Headers $allHeaders -UseBasicParsing -TimeoutSec 30
        Write-Host "✅ Manual header merge successful" -ForegroundColor Green
        Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ Manual header merge failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`n✅ Microsoft URL test completed!" -ForegroundColor Green
}
catch {
    Write-Host "`n❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
}
