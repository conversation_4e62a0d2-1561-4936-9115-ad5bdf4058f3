# Enhanced GitHub Scraper - Captures FULL offline repository content
# Scrapes actual PowerShell scripts, README files, and code content
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxRepos = 10,
    
    [Parameter(Mandatory = $false)]
    [int]$MinStars = 10,
    
    [Parameter(Mandatory = $false)]
    [int]$MaxFilesPerRepo = 20
)

# Helper function to get file content from GitHub
function Get-GitHubFileContent {
    param(
        [string]$RepoFullName,
        [string]$FilePath,
        [hashtable]$Headers
    )

    try {
        $fileUrl = "https://api.github.com/repos/$RepoFullName/contents/$FilePath"
        $fileData = Invoke-RestMethod -Uri $fileUrl -Headers $Headers -Method Get

        if ($fileData.content) {
            # Decode base64 content
            $decodedBytes = [System.Convert]::FromBase64String($fileData.content)
            $content = [System.Text.Encoding]::UTF8.GetString($decodedBytes)
            return $content
        }

        return $null
    }
    catch {
        return $null
    }
}

Write-Host "Enhanced GitHub Scraper - Full Offline Repository Content" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

try {
    # Import core module for pattern creation and saving
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework for pattern creation
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    if (-not $result) {
        throw "Failed to initialize scraping framework"
    }
    
    Write-Host "Framework initialized successfully" -ForegroundColor Green
    
    # Search for PowerShell AD repositories
    Write-Host "`nSearching for PowerShell Active Directory repositories..." -ForegroundColor Yellow
    
    $searchQueries = @(
        "PowerShell ActiveDirectory language:PowerShell",
        "Get-ADUser language:PowerShell",
        "Active Directory PowerShell language:PowerShell"
    )
    
    $allRepos = @()
    
    foreach ($query in $searchQueries) {
        Write-Host "Searching: $query" -ForegroundColor Gray
        
        $searchUrl = "https://api.github.com/search/repositories?q=$([System.Uri]::EscapeDataString($query))&sort=stars&order=desc&per_page=10"
        
        $headers = @{
            'User-Agent' = 'PowerShell-Scraper/1.0'
            'Accept' = 'application/vnd.github.v3+json'
        }
        
        try {
            $searchData = Invoke-RestMethod -Uri $searchUrl -Headers $headers -Method Get
            Write-Host "  Found $($searchData.items.Count) repositories" -ForegroundColor Gray
            
            # Filter by minimum stars and add to collection
            $filteredRepos = $searchData.items | Where-Object { $_.stargazers_count -ge $MinStars }
            $allRepos += $filteredRepos
            
            Start-Sleep -Seconds 2  # Rate limiting
        }
        catch {
            Write-Host "  Failed to search: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Remove duplicates and limit
    $uniqueRepos = $allRepos | Sort-Object stargazers_count -Descending | Sort-Object full_name -Unique | Select-Object -First $MaxRepos
    
    Write-Host "Found $($uniqueRepos.Count) unique repositories to process" -ForegroundColor Green
    
    # Process each repository with FULL content extraction
    $allPatterns = @()
    $count = 0
    
    foreach ($repo in $uniqueRepos) {
        $count++
        Write-Progress -Activity "Enhanced GitHub Scraping" -Status "Processing $($repo.full_name) ($count/$($uniqueRepos.Count))" -PercentComplete (($count / $uniqueRepos.Count) * 100)
        Write-Host "`nProcessing: $($repo.full_name)" -ForegroundColor White
        Write-Host "  Stars: $($repo.stargazers_count), Language: $($repo.language)" -ForegroundColor Gray
        Write-Host "  Description: $($repo.description)" -ForegroundColor Gray
        
        try {
            # Get repository contents
            $contentsUrl = "https://api.github.com/repos/$($repo.full_name)/contents"
            $contentsData = Invoke-RestMethod -Uri $contentsUrl -Headers $headers -Method Get
            
            # Build comprehensive repository content
            $fullContent = "# Repository: $($repo.full_name)`n`n"
            $fullContent += "**Stars:** $($repo.stargazers_count) | **Language:** $($repo.language) | **Forks:** $($repo.forks_count)`n`n"
            $fullContent += "**Description:** $($repo.description)`n`n"
            $fullContent += "**URL:** $($repo.html_url)`n`n"
            $fullContent += "**Clone URL:** $($repo.clone_url)`n`n"
            
            if ($repo.topics) {
                $fullContent += "**Topics:** $($repo.topics -join ', ')`n`n"
            }
            
            # Get README content
            $readmeContent = Get-GitHubFileContent -RepoFullName $repo.full_name -FilePath "README.md" -Headers $headers
            if (-not $readmeContent) {
                $readmeContent = Get-GitHubFileContent -RepoFullName $repo.full_name -FilePath "readme.md" -Headers $headers
            }
            if (-not $readmeContent) {
                $readmeContent = Get-GitHubFileContent -RepoFullName $repo.full_name -FilePath "README.txt" -Headers $headers
            }
            
            if ($readmeContent) {
                $fullContent += "## README`n`n"
                $fullContent += $readmeContent + "`n`n"
            }
            
            # Get PowerShell files
            $psFiles = $contentsData | Where-Object { $_.name -like "*.ps1" -or $_.name -like "*.psm1" -or $_.name -like "*.psd1" } | Select-Object -First $MaxFilesPerRepo
            
            $allCodeBlocks = @()
            $fileCount = 0
            
            if ($psFiles.Count -gt 0) {
                $fullContent += "## PowerShell Files ($($psFiles.Count) files)`n`n"
                
                foreach ($file in $psFiles) {
                    $fileCount++
                    Write-Host "    Processing file: $($file.name)" -ForegroundColor Gray
                    
                    $fileContent = Get-GitHubFileContent -RepoFullName $repo.full_name -FilePath $file.path -Headers $headers
                    
                    if ($fileContent) {
                        $fullContent += "### $($file.name)`n`n"
                        $fullContent += "**Path:** $($file.path)`n`n"
                        $fullContent += "````powershell`n"
                        $fullContent += $fileContent + "`n"
                        $fullContent += "````n`n"
                        
                        # Add to code blocks
                        $allCodeBlocks += $fileContent
                    }
                    
                    # Limit files to avoid hitting rate limits
                    if ($fileCount -ge $MaxFilesPerRepo) {
                        break
                    }
                    
                    Start-Sleep -Milliseconds 500  # Rate limiting
                }
            }
            
            # Determine domain based on repository content
            $domain = "general_ad"
            $repoText = ($repo.full_name + " " + $repo.description + " " + $fullContent).ToLower()
            if ($repoText -like "*user*" -and $repoText -like "*management*") { $domain = "user_management" }
            elseif ($repoText -like "*group*" -and $repoText -like "*management*") { $domain = "group_management" }
            elseif ($repoText -like "*computer*" -and $repoText -like "*management*") { $domain = "computer_management" }
            elseif ($repoText -like "*policy*" -or $repoText -like "*gpo*") { $domain = "policy_management" }
            elseif ($repoText -like "*domain*" -and $repoText -like "*management*") { $domain = "domain_management" }
            
            # Create comprehensive knowledge pattern
            $pattern = New-KnowledgePattern -Title "GitHub Repository: $($repo.full_name)" -Content $fullContent -SourceUrl $repo.html_url -SourceType "github_repository" -Domain $domain -Operation "read" -Author $repo.owner.login -CredibilityScore 0.80
            
            if ($pattern) {
                # Add comprehensive repository metadata
                $pattern.Stars = $repo.stargazers_count
                $pattern.Forks = $repo.forks_count
                $pattern.Language = $repo.language
                $pattern.Topics = $repo.topics
                $pattern.PowerShellFiles = $psFiles.Count
                $pattern.AllCodeBlocks = $allCodeBlocks
                $pattern.HasReadme = ($readmeContent -ne $null)
                $pattern.RepoSize = $repo.size
                $pattern.LastUpdated = $repo.updated_at
                
                # Set primary code template from first PowerShell file
                if ($allCodeBlocks.Count -gt 0) {
                    $pattern.CodeTemplate = $allCodeBlocks[0]
                }
                
                # Calculate relevance based on stars and activity
                $pattern.RelevanceScore = [Math]::Min(1.0, [Math]::Log10($repo.stargazers_count + 1) / 4.0 + ($repo.forks_count / 1000.0))
                
                # Extract best practices from README and code
                if ($readmeContent -and ($readmeContent.ToLower() -like "*best practice*" -or $readmeContent.ToLower() -like "*recommended*")) {
                    $pattern.BestPractices += "Repository contains documented best practices"
                }
                
                $allPatterns += $pattern
                Write-Host "  ✅ Created comprehensive repository pattern: $($pattern.Id)" -ForegroundColor Green
                Write-Host "    PowerShell files: $($psFiles.Count), Code blocks: $($allCodeBlocks.Count)" -ForegroundColor Gray
                Write-Host "    Full content: $($fullContent.Length) chars, Has README: $($pattern.HasReadme)" -ForegroundColor Gray
            }
        }
        catch {
            Write-Host "  ❌ Failed to process repository: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Rate limiting
        Start-Sleep -Seconds 2
    }
    
    Write-Progress -Activity "Enhanced GitHub Scraping" -Completed
    
    # Save comprehensive results
    if ($allPatterns.Count -gt 0) {
        $outputFile = Save-ScrapingResults -Patterns $allPatterns -SourceType "enhanced_github"
        Write-Host "`nSUCCESS: Enhanced GitHub scraping completed!" -ForegroundColor Green
        Write-Host "Patterns created: $($allPatterns.Count)" -ForegroundColor Cyan
        Write-Host "Output file: $outputFile" -ForegroundColor Cyan
        
        # Calculate comprehensive statistics
        $totalContentSize = ($allPatterns | ForEach-Object { $_.Content.Length }) | Measure-Object -Sum
        $totalCodeBlocks = ($allPatterns | ForEach-Object { $_.AllCodeBlocks.Count }) | Measure-Object -Sum
        $totalStars = ($allPatterns | ForEach-Object { $_.Stars }) | Measure-Object -Sum
        $totalFiles = ($allPatterns | ForEach-Object { $_.PowerShellFiles }) | Measure-Object -Sum
        $avgStars = ($allPatterns | ForEach-Object { $_.Stars }) | Measure-Object -Average
        
        Write-Host "`nComprehensive Statistics:" -ForegroundColor Yellow
        Write-Host "  Total offline content: $([Math]::Round($totalContentSize.Sum / 1024, 2)) KB" -ForegroundColor Cyan
        Write-Host "  Total PowerShell files: $($totalFiles.Sum)" -ForegroundColor Cyan
        Write-Host "  Total code blocks: $($totalCodeBlocks.Sum)" -ForegroundColor Cyan
        Write-Host "  Total stars: $($totalStars.Sum.ToString('N0'))" -ForegroundColor Cyan
        Write-Host "  Average stars per repo: $([Math]::Round($avgStars.Average, 1))" -ForegroundColor Cyan
        
        # Show sample pattern
        if ($allPatterns.Count -gt 0) {
            $sample = $allPatterns[0]
            Write-Host "`nSample repository pattern:" -ForegroundColor Yellow
            Write-Host "  Title: $($sample.Title)" -ForegroundColor Gray
            Write-Host "  Stars: $($sample.Stars), Forks: $($sample.Forks)" -ForegroundColor Gray
            Write-Host "  PowerShell files: $($sample.PowerShellFiles), Code blocks: $($sample.AllCodeBlocks.Count)" -ForegroundColor Gray
            Write-Host "  Has README: $($sample.HasReadme)" -ForegroundColor Gray
            Write-Host "  Content length: $($sample.Content.Length.ToString('N0')) characters" -ForegroundColor Gray
            Write-Host "  Relevance score: $($sample.RelevanceScore.ToString('F2'))" -ForegroundColor Gray
        }
    } else {
        Write-Host "`n❌ No patterns created" -ForegroundColor Red
    }
}
catch {
    Write-Host "`n❌ Enhanced GitHub scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    exit 1
}


