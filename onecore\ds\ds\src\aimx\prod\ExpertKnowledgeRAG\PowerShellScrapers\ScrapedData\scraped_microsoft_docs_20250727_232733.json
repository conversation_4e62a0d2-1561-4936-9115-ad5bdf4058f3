﻿{
    "ScrapedAt":  "2025-07-27T23:27:33Z",
    "BatchId":  "20250727_232733",
    "Patterns":  [
                     {
                         "RelevanceScore":  0,
                         "CreatedAt":  "2025-07-27T23:27:30Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_group_management_6554d710",
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-27T23:27:30Z",
                         "BestPractices":  null,
                         "Title":  "Microsoft Docs: Get-ADGroup",
                         "CodeTemplate":  "PS C:\\\u0026gt; Get-ADGroup -Identity Administrators\nDistinguishedName : CN=Administrators,CN=Builtin,DC=Fabrikam,DC=com\nGroupCategory     : Security\nGroupScope        : DomainLocal\nName              : Administrators\nObjectClass       : group\nObjectGUID        : 02ce3874-dd86-41ba-bddc-013f34019978\nSamAccountName    : Administrators\nSID               : S-1-5-32-544\n\nPS C:\\\u0026gt; Get-ADGroup -Identity S-1-5-32-544 -Properties member\nDistinguishedName : CN=Administrators,CN=Builtin,DC=Fabrikam,DC=com\nGroupCategory     : Security\nGroupScope        : DomainLocal\nmember            : {CN=Domain Admins,CN=Users,DC=Fabrikam,DC=com, CN=Enterprise Admins,CN=Users,DC=Fabrikam,DC=com, CN=LabAdmin,CN=Users,DC=Fabrikam,DC=com, C\nN=Administrator,CN=Users,DC=Fabrikam,DC=com}\nName              : Administrators\nObjectClass       : group\nObjectGUID        : 02ce3874-dd86-41ba-bddc-013f34019978\nSamAccountName    : Administrators\nSID               : S-1-5-32-544\n\nPS C:\\\u0026gt; Get-ADGroup -Filter \u0027GroupCategory -eq \"Security\" -and GroupScope -ne \"DomainLocal\"\u0027\n\nPS C:\\\u0026gt; Get-ADGroup -Server localhost:60000 -Filter \"GroupScope -eq \u0027DomainLocal\u0027\" -SearchBase \"DC=AppNC\"\n\n\nDistinguishedName : CN=AlphaGroup,OU=AccountDeptOU,DC=AppNC\nGroupCategory     : Security\nGroupScope        : DomainLocal\nName              : AlphaGroup\nObjectClass       : group\nObjectGUID        : 6498c9fb-7c62-48fe-9972-1461f7f3dec2\nSID               : S-1-*********-*********-**********-**********-**********-*********\n\nDistinguishedName : CN=BranchOffice1,OU=AccountDeptOU,DC=AppNC\nGroupCategory     : Security\nGroupScope        : DomainLocal\nName              : BranchOffice1\nObjectClass       : group\nObjectGUID        : 0b7504c5-482b-4a73-88f5-8a76960e4568\nSID               : S-1-*********-*********-**********-**********-**********-**********\n\nDistinguishedName : CN=AccountLeads,OU=AccountDeptOU,DC=AppNC\nGroupCategory     : Distribution\nGroupScope        : DomainLocal\nName              : AccountLeads\nObjectClass       : group\nObjectGUID        : b20c032b-2de9-401a-b48c-341854a37254\nSID               : S-1-*********-*********-**********-**********-**********-*********",
                         "CommonMistakes":  [

                                            ],
                         "Domain":  "group_management",
                         "RequiredParameters":  null,
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  0.95,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "group_management",
                                      "read"
                                  ],
                         "Abstract":  "",
                         "Sources":  [
                                         {
                                             "SourceType":  "microsoft_docs",
                                             "CredibilityScore":  0.95,
                                             "PublishedAt":  "2025-07-27T23:27:30Z",
                                             "Url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroup",
                                             "ScrapedAt":  "2025-07-27T23:27:30Z",
                                             "Id":  "5486be26-f533-4c49-87af-b871647b8d81",
                                             "Author":  "Microsoft",
                                             "Title":  "Microsoft Docs: Get-ADGroup"
                                         }
                                     ],
                         "Content":  "# Get-ADGroup\n\n## Synopsis\n\n\n## Description\n\n\n## Parameters\n\n\n## Examples\nPS C:\\\u0026gt; Get-ADGroup -Identity Administrators\nDistinguishedName : CN=Administrators,CN=Builtin,DC=Fabrikam,DC=com\nGroupCategory     : Security\nGroupScope        : DomainLocal\nName              : Administrators\nObjectClass       : group\nObjectGUID        : 02ce3874-dd86-41ba-bddc-013f34019978\nSamAccountName    : Administrators\nSID               : S-1-5-32-544\n\nPS C:\\\u0026gt; Get-ADGroup -Identity S-1-5-32-544 -Properties member\nDistinguishedName : CN=Administrators,CN=Builtin,DC=Fabrikam,DC=com\nGroupCategory     : Security\nGroupScope        : DomainLocal\nmember            : {CN=Domain Admins,CN=Users,DC=Fabrikam,DC=com, CN=Enterprise Admins,CN=Users,DC=Fabrikam,DC=com, CN=LabAdmin,CN=Users,DC=Fabrikam,DC=com, C\nN=Administrator,CN=Users,DC=Fabrikam,DC=com}\nName              : Administrators\nObjectClass       : group\nObjectGUID        : 02ce3874-dd86-41ba-bddc-013f34019978\nSamAccountName    : Administrators\nSID               : S-1-5-32-544\n\nPS C:\\\u0026gt; Get-ADGroup -Filter \u0027GroupCategory -eq \"Security\" -and GroupScope -ne \"DomainLocal\"\u0027\n\nPS C:\\\u0026gt; Get-ADGroup -Server localhost:60000 -Filter \"GroupScope -eq \u0027DomainLocal\u0027\" -SearchBase \"DC=AppNC\"\n\n\nDistinguishedName : CN=AlphaGroup,OU=AccountDeptOU,DC=AppNC\nGroupCategory     : Security\nGroupScope        : DomainLocal\nName              : AlphaGroup\nObjectClass       : group\nObjectGUID        : 6498c9fb-7c62-48fe-9972-1461f7f3dec2\nSID               : S-1-*********-*********-**********-**********-**********-*********\n\nDistinguishedName : CN=BranchOffice1,OU=AccountDeptOU,DC=AppNC\nGroupCategory     : Security\nGroupScope        : DomainLocal\nName              : BranchOffice1\nObjectClass       : group\nObjectGUID        : 0b7504c5-482b-4a73-88f5-8a76960e4568\nSID               : S-1-*********-*********-**********-**********-**********-**********\n\nDistinguishedName : CN=AccountLeads,OU=AccountDeptOU,DC=AppNC\nGroupCategory     : Distribution\nGroupScope        : DomainLocal\nName              : AccountLeads\nObjectClass       : group\nObjectGUID        : b20c032b-2de9-401a-b48c-341854a37254\nSID               : S-1-*********-*********-**********-**********-**********-*********\n\n## Notes\n"
                     },
                     {
                         "RelevanceScore":  0,
                         "CreatedAt":  "2025-07-27T23:27:32Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_general_ad_bc2e465c",
                         "Operation":  "read",
                         "UpdatedAt":  "2025-07-27T23:27:32Z",
                         "BestPractices":  null,
                         "Title":  "Microsoft Docs: Get-ADObject",
                         "CodeTemplate":  "PS C:\\\u0026gt; Get-ADObject -LDAPFilter \"(objectClass=site)\" -SearchBase \u0027CN=Configuration,DC=Fabrikam,DC=Com\u0027 -Properties CanonicalName | FT Name,CanonicalName -A\nName CanonicalName\n---- -------------\nHQ   FABRIKAM.COM/Configuration/Sites/HQ\nBO1  FABRIKAM.COM/Configuration/Sites/BO1\nBO2  FABRIKAM.COM/Configuration/Sites/BO2\nBO3  FABRIKAM.COM/Configuration/Sites/BO3\n\nPS C:\\\u0026gt; Get-ADObject -Filter \u0027ObjectClass -eq \"site\"\u0027 -SearchBase \u0027CN=Configuration,DC=Fabrikam,DC=Com\u0027 -Properties siteObjectBL | foreach {$_.siteObjectBL}\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.162.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.161.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.160.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.159.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.158.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.157.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\n\nPS C:\\\u0026gt; $ChangeDate = New-Object DateTime(2008, 11, 18, 1, 40, 02)\nPS C:\\\u0026gt; Get-ADObject -Filter \u0027whenChanged -gt $ChangeDate\u0027 -IncludeDeletedObjects\n\nPS C:\\\u0026gt; $ChangeDate = New-Object DateTime(2008, 11, 18, 1, 40, 02)\nPS C:\\\u0026gt; Get-ADObject -Filter \u0027whenChanged -gt $ChangeDate -and isDeleted -eq $True -and -not (isRecycled -eq $True) -and name -ne \"Deleted Objects\"\u0027 -IncludeDeletedObjects\n\n\nObjectGUID        : ********-91c7-437d-8ada-ba0b66db823b\nDeleted           : True\nDistinguishedName : CN=Andrew Ma\\0ADEL:********-91c7-437d-8ada-ba0b66db823b,CN=Deleted Objects,DC=FABRIKAM,DC=COM\nName              : Andrew Ma\nDEL:********-91c7-437d-8ada-ba0b66db823b\nObjectClass       : user\n\nname -ne \"Deleted Objects\"\n\nPS C:\\\u0026gt; $ChangeDate = New-Object DateTime(2008, 11, 18, 1, 40, 02)\nPS C:\\\u0026gt; Get-ADObject -Filter \u0027whenChanged -gt $ChangeDate -and isDeleted -eq $True -and -not (isRecycled -eq $True) -and lastKnownParent -eq \"OU=Accounting,DC=Fabrikam,DC=com\"\u0027 -IncludeDeletedObjects\n\n\nObjectGUID        : 12d53e7f-aaf7-4790-b41a-da19044504db\nDeleted           : True\nDistinguishedName : CN=Craig Dewar\\0ADEL:12d53e7f-aaf7-4790-b41a-da19044504db,CN=Deleted Objects,DC=Fabrikam,DC=com\nName              : Craig Dewar\nDEL:12d53e7f-aaf7-4790-b41a-da19044504db\nObjectClass       : user\n\nPS C:\\\u0026gt; Get-ADObject -Identity \"DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\"\nObjectGUID                    DistinguishedName             Name                          ObjectClass\n----------                    -----------------             ----                          -----------\n62b2e185-9322-4980-9c93-cf... DC=AppNC                      AppNC                         domainDNS",
                         "CommonMistakes":  [

                                            ],
                         "Domain":  "general_ad",
                         "RequiredParameters":  null,
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  0.95,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "general_ad",
                                      "read"
                                  ],
                         "Abstract":  "",
                         "Sources":  [
                                         {
                                             "SourceType":  "microsoft_docs",
                                             "CredibilityScore":  0.95,
                                             "PublishedAt":  "2025-07-27T23:27:32Z",
                                             "Url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adobject",
                                             "ScrapedAt":  "2025-07-27T23:27:32Z",
                                             "Id":  "75be9fcd-6c40-4907-804b-e794ca78fbae",
                                             "Author":  "Microsoft",
                                             "Title":  "Microsoft Docs: Get-ADObject"
                                         }
                                     ],
                         "Content":  "# Get-ADObject\n\n## Synopsis\n\n\n## Description\n\n\n## Parameters\n\n\n## Examples\nPS C:\\\u0026gt; Get-ADObject -LDAPFilter \"(objectClass=site)\" -SearchBase \u0027CN=Configuration,DC=Fabrikam,DC=Com\u0027 -Properties CanonicalName | FT Name,CanonicalName -A\nName CanonicalName\n---- -------------\nHQ   FABRIKAM.COM/Configuration/Sites/HQ\nBO1  FABRIKAM.COM/Configuration/Sites/BO1\nBO2  FABRIKAM.COM/Configuration/Sites/BO2\nBO3  FABRIKAM.COM/Configuration/Sites/BO3\n\nPS C:\\\u0026gt; Get-ADObject -Filter \u0027ObjectClass -eq \"site\"\u0027 -SearchBase \u0027CN=Configuration,DC=Fabrikam,DC=Com\u0027 -Properties siteObjectBL | foreach {$_.siteObjectBL}\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=***********/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.162.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.161.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.160.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.159.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.158.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\nCN=192.157.1.0/26,CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\n\nPS C:\\\u0026gt; $ChangeDate = New-Object DateTime(2008, 11, 18, 1, 40, 02)\nPS C:\\\u0026gt; Get-ADObject -Filter \u0027whenChanged -gt $ChangeDate\u0027 -IncludeDeletedObjects\n\nPS C:\\\u0026gt; $ChangeDate = New-Object DateTime(2008, 11, 18, 1, 40, 02)\nPS C:\\\u0026gt; Get-ADObject -Filter \u0027whenChanged -gt $ChangeDate -and isDeleted -eq $True -and -not (isRecycled -eq $True) -and name -ne \"Deleted Objects\"\u0027 -IncludeDeletedObjects\n\n\nObjectGUID        : ********-91c7-437d-8ada-ba0b66db823b\nDeleted           : True\nDistinguishedName : CN=Andrew Ma\\0ADEL:********-91c7-437d-8ada-ba0b66db823b,CN=Deleted Objects,DC=FABRIKAM,DC=COM\nName              : Andrew Ma\nDEL:********-91c7-437d-8ada-ba0b66db823b\nObjectClass       : user\n\nname -ne \"Deleted Objects\"\n\nPS C:\\\u0026gt; $ChangeDate = New-Object DateTime(2008, 11, 18, 1, 40, 02)\nPS C:\\\u0026gt; Get-ADObject -Filter \u0027whenChanged -gt $ChangeDate -and isDeleted -eq $True -and -not (isRecycled -eq $True) -and lastKnownParent -eq \"OU=Accounting,DC=Fabrikam,DC=com\"\u0027 -IncludeDeletedObjects\n\n\nObjectGUID        : 12d53e7f-aaf7-4790-b41a-da19044504db\nDeleted           : True\nDistinguishedName : CN=Craig Dewar\\0ADEL:12d53e7f-aaf7-4790-b41a-da19044504db,CN=Deleted Objects,DC=Fabrikam,DC=com\nName              : Craig Dewar\nDEL:12d53e7f-aaf7-4790-b41a-da19044504db\nObjectClass       : user\n\nPS C:\\\u0026gt; Get-ADObject -Identity \"DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\"\nObjectGUID                    DistinguishedName             Name                          ObjectClass\n----------                    -----------------             ----                          -----------\n62b2e185-9322-4980-9c93-cf... DC=AppNC                      AppNC                         domainDNS\n\n## Notes\n"
                     }
                 ],
    "SourceType":  "microsoft_docs",
    "TotalPatterns":  2
}
