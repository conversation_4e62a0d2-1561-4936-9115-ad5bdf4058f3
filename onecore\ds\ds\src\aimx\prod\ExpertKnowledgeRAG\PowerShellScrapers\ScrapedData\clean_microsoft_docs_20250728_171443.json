﻿{
    "source":  "microsoft_docs_clean",
    "schema_version":  "1.0",
    "total_entries":  20,
    "entries":  [
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Get-ADUser"
                                    ],
                        "id":  "microsoft_docs_get-aduser",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "Filter",
                                                            "AuthType",
                                                            "Credential",
                                                            "Properties",
                                                            "ResultPageSize",
                                                            "ResultSetSize",
                                                            "SearchBase",
                                                            "SearchScope",
                                                            "Server",
                                                            "Partition",
                                                            "LDAPFilter"
                                                        ],
                                         "cmdlet_name":  "Get-ADUser"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Get-ADUser (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Get-ADUser Module: ActiveDirectory Module ## Gets one or more Active Directory users. ## Syntax ## Filter (Default) ```powershellGet-ADUser -Filter \u003cString\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Properties \u003cString[]\u003e] [-ResultPageSize \u003cInt32\u003e] [-ResultSetSize \u003cInt32\u003e] [-SearchBase \u003cString\u003e] [-SearchScope \u003cADSearchScope\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Identity ```powershellGet-ADUser [-Identity] \u003cADUser\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Partition \u003cString\u003e] [-Properties \u003cString[]\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## LdapFilter ```powershellGet-ADUser -LDAPFilter \u003cString\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Properties \u003cString[]\u003e] [-ResultPageSize \u003cInt32\u003e] [-ResultSetSize \u003cInt32\u003e] [-SearchBase \u003cString\u003e] [-SearchScope \u003cADSearchScope\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The Get-ADUser cmdlet gets a specified user object or performs a search to get multiple user objects. The Identity parameter specifies the Active Directory user to get. You can identify a user by its distinguished name (DN), GUID, security identifier (SID), or Security Account Manager (SAM) account name. You can also set the parameter to a user object variable such as `$\u003clocalUserObject\u003e` or pass a user object through the pipeline to the Identity parameter. To search for and retrieve more than one user, use the Filter or LDAPFilter parameters. The Filter parameter uses the PowerShell Expression Language to write query strings for Active Directory. PowerShell Expression Language syntax provides rich type-conversion support for value types received by the Filter parameter. For more information about the Filter parameter syntax, type `Get-Help about_ActiveDirectory_Filter`. If you have existing Lightweight Directory Access Protocol (LDAP) query strings, you can use the LDAPFilter parameter. This cmdlet retrieves a default set of user object properties. To retrieve additional properties use the Properties parameter. For more information about how to determine the properties for user objects, see the Properties parameter description. ## Examples ## Example 1: Get all of the users in a container ```powershellPS C:\\\u003e Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\" ``` This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM. ## Example 2: Get a filtered list of users ```powershellPS C:\\\u003e Get-ADUser -Filter \u0027Name -like \"*SvcAccount\"\u0027 | Format-Table Name,SamAccountName -A ``` ```powershellName SamAccountName ---- -------------- SQL01 SvcAccount SQL01 SQL02 SvcAccount SQL02 IIS01 SvcAccount IIS01 ``` This command gets all users that have a name that ends with SvcAccount. ## Example 3: Get all of the properties for a specified user ```powershellPS C:\\\u003e Get-ADUser -Identity ChewDavid -Properties * ``` ```powershellSurname : David Name : Chew David UserPrincipalName : GivenName : David Enabled : False SamAccountName : ChewDavid ObjectClass : user SID : S-1-5-21-**********-**********-**********-3544 ObjectGUID : e1418d64-096c-4cb0-b903-ebb66562d99d DistinguishedName : CN=Chew David,OU=NorthAmerica,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM ``` This command gets all of the properties of the user with the SAM account name ChewDavid. ## Example 4: Get a specified user ```powershellPS C:\\\u003e Get-ADUser -Filter \"Name -eq \u0027ChewDavid\u0027\" -SearchBase \"DC=AppNC\" -Properties \"mail\" -Server lds.Fabrikam.com:50000 ``` This command gets the user with the name ChewDavid in the Active Directory Lightweight Directory Services (AD LDS) instance. ## Example 5: Get all enabled user accounts ```powershellC:\\PS\u003e Get-ADUser -LDAPFilter \u0027(!userAccountControl:1.2.840.113556.1.4.803:=2)\u0027 ``` This command gets all enabled user accounts in Active Directory using an LDAP filter. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Filter Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Expression Language syntax. The PowerShell Expression Language syntax provides rich type-conversion support for value types received by the Filter parameter. The syntax uses an in-order representation, which means that the operator is placed between the operand and the value. For more information about the Filter parameter, type `Get-Help about_ActiveDirectory_Filter`. Syntax: The following syntax uses Backus-Naur form to show how to use the PowerShell Expression Language for this parameter. \u003cfilter\u003e ::= \"{\" \u003cFilterComponentList\u003e \"}\" \u003cFilterComponentList\u003e ::= \u003cFilterComponent\u003e | \u003cFilterComponent\u003e \u003cJoinOperator\u003e \u003cFilterComponent\u003e | \u003cNotOperator\u003e \u003cFilterComponent\u003e \u003cFilterComponent\u003e ::= \u003cattr\u003e \u003cFilterOperator\u003e \u003cvalue\u003e | \"(\" \u003cFilterComponent\u003e \")\" \u003cFilterOperator\u003e ::= \"-eq\" | \"-le\" | \"-ge\" | \"-ne\" | \"-lt\" | \"-gt\"| \"-approx\" | \"-bor\" | \"-band\" | \"-recursivematch\" | \"-like\" | \"-notlike\" \u003cJoinOperator\u003e ::= \"-and\" | \"-or\" \u003cNotOperator\u003e ::= \"-not\" \u003cattr\u003e ::= \u003cPropertyName\u003e | \u003cLDAPDisplayName of the attribute\u003e \u003cvalue\u003e::= \u003ccompare this value with an \u003cattr\u003e by using the specified \u003cFilterOperator\u003e\u003e For a list of supported types for \u003cvalue\u003e, type `Get-Help about_ActiveDirectory_ObjectModel`. Note: For String parameter type, PowerShell will cast the filter query to a string while processing the command. When using a string variable as a value in the filter component, make sure that it complies with the PowerShell Quoting Rules. For example, if the filter expression is double-quoted, the variable should be enclosed using single quotation marks: Get-ADUser -Filter \"Name -like \u0027$UserName\u0027\". On the contrary, if curly braces are used to enclose the filter, the variable should not be quoted at all: Get-ADUser -Filter {Name -like $UserName}. Note: PowerShell wildcards other than *, such as ?, are not supported by the Filter syntax. Note: To query using LDAP query strings, use the LDAPFilter parameter. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory user object by providing one of the following property values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A SAM account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -LDAPFilter Specifies an LDAP query string that is used to filter Active Directory objects. You can use this parameter to run your existing LDAP queries. The Filter parameter syntax supports the same functionality as the LDAP syntax. For more information, see the Filter parameter description or type `Get-Help about_ActiveDirectory_Filter`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets LdapFilter Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first, and when a default value can be determined, no further rules are evaluated. In AD DS environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In AD LDS environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter does not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Properties Specifies the properties of the output object to retrieve from the server. Use this parameter to retrieve properties that are not included in the default set. Specify properties for this parameter as a comma-separated list of names. To display all of the attributes that are set on the object, specify * (asterisk). To specify an individual extended property, use the name of the property. For properties that are not default or extended properties, you must specify the LDAP display name of the attribute. To retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the Get-Member cmdlet. ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False Aliases:Property ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ResultPageSize Specifies the number of objects to include in one page for an Active Directory Domain Services query. The default is 256 objects per page. ## Parameter properties Type:Int32 Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ResultSetSize Specifies the maximum number of objects to return for an Active Directory Domain Services query. If you want to receive all of the objects, set this parameter to $Null (null value). You can use Ctrl+C to stop the query and return of objects. The default is $Null. ## Parameter properties Type:Int32 Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SearchBase Specifies an Active Directory path to search under. When you run a cmdlet from an Active Directory provider drive, the default value of this parameter is the current path of the drive. When you run a cmdlet outside of an Active Directory provider drive against an AD DS target, the default value of this parameter is the default naming context of the target domain. When you run a cmdlet outside of an Active Directory provider drive against an AD LDS target, the default value is the default naming context of the target LDS instance if one has been specified by setting the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. If no default naming context has been specified for the target AD LDS instance, then this parameter has no default value. When the value of the SearchBase parameter is set to an empty string and you are connected to a GC port, all partitions are searched. If the value of the SearchBase parameter is set to an empty string and you are not connected to a GC port, an error is thrown. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SearchScope Specifies the scope of an Active Directory search. The acceptable values for this parameter are: - Base or 0 - OneLevel or 1 - Subtree or 2 A SearchScope with a Base value searches only for the given user. If an OU is specified in the SearchBase parameter, no user will be returned by, for example, a specified Filter statement. A OneLevel query searches the immediate children of that path or object. This option only works when an OU is given as the SearchBase. If a user is given, no results are returned. A Subtree query searches the current path or object and all children of that path or object. ## Parameter properties Type:ADSearchScope Default value:None Accepted values:Base, OneLevel, Subtree Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance. Domain name values: - Fully qualified domain name (FQDN) - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for the Server parameter is determined by one of the following methods in the order that they are listed: - By using Server value from objects passed through the pipeline. - By using the server information associated with the Active Directory PowerShell provider drive, when running under that drive. - By using the domain of the computer running PowerShell. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADUser A user object is received by the Identity parameter. ## Outputs ## Microsoft.ActiveDirectory.Management.ADUser Returns one or more user objects. This cmdlet returns a default set of ADUser property values. To retrieve additional ADUser properties, use the Properties parameter. To get a list of the default set of properties of an ADUser object, use the following command: `Get-ADUser`\u003cuser\u003e`| Get-Member` To get a list of the most commonly used properties of an ADUser object, use the following command: `Get-ADUser`\u003cuser\u003e`-Properties Extended | Get-Member` To get a list of all the properties of an ADUser object, use the following command: `Get-ADUser`\u003cuser\u003e`-Properties * | Get-Member` ## Notes - This cmdlet does not work with an Active Directory snapshot. ## Related Links - New-ADUser - Remove-ADUser - Set-ADUser ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "get-aduser"
                                 ],
                        "last_updated":  "2025-07-28T17:14:43Z",
                        "title":  "Get-ADUser (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/set-aduser",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Set-ADUser"
                                    ],
                        "id":  "microsoft_docs_set-aduser",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AccountExpirationDate",
                                                            "AccountNotDelegated",
                                                            "Add",
                                                            "AllowReversiblePasswordEncryption",
                                                            "AuthenticationPolicy",
                                                            "AuthenticationPolicySilo",
                                                            "AuthType",
                                                            "CannotChangePassword",
                                                            "Certificates",
                                                            "ChangePasswordAtLogon",
                                                            "City",
                                                            "Clear",
                                                            "Company",
                                                            "CompoundIdentitySupported",
                                                            "Country",
                                                            "Credential",
                                                            "Department",
                                                            "Description",
                                                            "DisplayName",
                                                            "Division",
                                                            "EmailAddress",
                                                            "EmployeeID",
                                                            "EmployeeNumber",
                                                            "Enabled",
                                                            "Fax",
                                                            "GivenName",
                                                            "HomeDirectory",
                                                            "HomeDrive",
                                                            "HomePage",
                                                            "HomePhone",
                                                            "Initials",
                                                            "KerberosEncryptionType",
                                                            "LogonWorkstations",
                                                            "Manager",
                                                            "MobilePhone",
                                                            "Office",
                                                            "OfficePhone",
                                                            "Organization",
                                                            "OtherName",
                                                            "Partition",
                                                            "PasswordNeverExpires",
                                                            "PasswordNotRequired",
                                                            "POBox",
                                                            "PostalCode",
                                                            "PrincipalsAllowedToDelegateToAccount",
                                                            "ProfilePath",
                                                            "Remove",
                                                            "Replace",
                                                            "SamAccountName",
                                                            "ScriptPath",
                                                            "Server",
                                                            "ServicePrincipalNames",
                                                            "SmartcardLogonRequired",
                                                            "State",
                                                            "StreetAddress",
                                                            "Surname",
                                                            "Title",
                                                            "TrustedForDelegation",
                                                            "UserPrincipalName",
                                                            "Instance"
                                                        ],
                                         "cmdlet_name":  "Set-ADUser"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Set-ADUser (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Set-ADUser Module: ActiveDirectory Module ## Modifies an Active Directory user. ## Syntax ## Identity ```powershellSet-ADUser [-Identity] \u003cADUser\u003e [-WhatIf] [-Confirm] [-AccountExpirationDate \u003cDateTime\u003e] [-AccountNotDelegated \u003cBoolean\u003e] [-Add \u003cHashtable\u003e] [-AllowReversiblePasswordEncryption \u003cBoolean\u003e] [-AuthenticationPolicy \u003cADAuthenticationPolicy\u003e] [-AuthenticationPolicySilo \u003cADAuthenticationPolicySilo\u003e] [-AuthType \u003cADAuthType\u003e] [-CannotChangePassword \u003cBoolean\u003e] [-Certificates \u003cHashtable\u003e] [-ChangePasswordAtLogon \u003cBoolean\u003e] [-City \u003cString\u003e] [-Clear \u003cString[]\u003e] [-Company \u003cString\u003e] [-CompoundIdentitySupported \u003cBoolean\u003e] [-Country \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Department \u003cString\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-Division \u003cString\u003e] [-EmailAddress \u003cString\u003e] [-EmployeeID \u003cString\u003e] [-EmployeeNumber \u003cString\u003e] [-Enabled \u003cBoolean\u003e] [-Fax \u003cString\u003e] [-GivenName \u003cString\u003e] [-HomeDirectory \u003cString\u003e] [-HomeDrive \u003cString\u003e] [-HomePage \u003cString\u003e] [-HomePhone \u003cString\u003e] [-Initials \u003cString\u003e] [-KerberosEncryptionType \u003cADKerberosEncryptionType\u003e] [-LogonWorkstations \u003cString\u003e] [-Manager \u003cADUser\u003e] [-MobilePhone \u003cString\u003e] [-Office \u003cString\u003e] [-OfficePhone \u003cString\u003e] [-Organization \u003cString\u003e] [-OtherName \u003cString\u003e] [-Partition \u003cString\u003e] [-PassThru] [-PasswordNeverExpires \u003cBoolean\u003e] [-PasswordNotRequired \u003cBoolean\u003e] [-POBox \u003cString\u003e] [-PostalCode \u003cString\u003e] [-PrincipalsAllowedToDelegateToAccount \u003cADPrincipal[]\u003e] [-ProfilePath \u003cString\u003e] [-Remove \u003cHashtable\u003e] [-Replace \u003cHashtable\u003e] [-SamAccountName \u003cString\u003e] [-ScriptPath \u003cString\u003e] [-Server \u003cString\u003e] [-ServicePrincipalNames \u003cHashtable\u003e] [-SmartcardLogonRequired \u003cBoolean\u003e] [-State \u003cString\u003e] [-StreetAddress \u003cString\u003e] [-Surname \u003cString\u003e] [-Title \u003cString\u003e] [-TrustedForDelegation \u003cBoolean\u003e] [-UserPrincipalName \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Instance ```powershellSet-ADUser -Instance \u003cADUser\u003e [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-PassThru] [-SamAccountName \u003cString\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The `Set-ADUser` cmdlet modifies the properties of an Active Directory user. You can modify commonly used property values by using the cmdlet parameters. You can set property values that are not associated with cmdlet parameters by using the Add, Remove, Replace, and Clear parameters. The Identity parameter specifies the Active Directory user to modify. You can identify a user by its distinguished name, GUID, security identifier (SID), or Security Account Manager (SAM) account name. You can also set the Identity parameter to an object variable such as `$\u003clocalUserObject\u003e`, or you can pass an object through the pipeline to the Identity parameter. For example, you can use the Get-ADUser cmdlet to retrieve a user object and then pass the object through the pipeline to the Set-ADUser cmdlet. The Instance parameter provides a way to update a user object by applying the changes made to a copy of the object. When you set the Instance parameter to a copy of an Active Directory user object that has been modified, the Set-ADUser cmdlet makes the same changes to the original user object. To get a copy of the object to modify, use the Get-ADUser object. The Identity parameter is not allowed when you use the Instance parameter. For more information about the Instance parameter, see the Instance parameter description. Accounts created with the New-ADUser cmdlet are disabled if no password is provided. For AD LDS environments, the Partition parameter must be specified except in the following two conditions: - The cmdlet is run from an Active Directory provider drive. - A default naming context or partition is defined for the AD LDS environment. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. ## Examples ## Example 1: Set properties for a user ```powershell$params = @{ Identity = \u0027ChewDavid\u0027 HomePage = \u0027http://fabrikam.com/employees/ChewDavid\u0027 LogonWorkstations = \u0027ChewDavid-DSKTOP,ChewDavid-LPTOP\u0027 } Set-ADUser @params ``` This command sets the specified user\u0027s homepage property to http://fabrikam.com/employees/ChewDavid and the LogonWorkstations property to ChewDavid-DSKTOP,ChewDavid-LPTOP. ## Example 2: Set properties for multiple users ```powershellPS C:\\\u003e Get-ADUser -Filter \u0027Name -like \"*\"\u0027 -SearchBase \u0027OU=HumanResources,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 -Properties DisplayName | % {Set-ADUser $_ -DisplayName ($_.Surname + \u0027 \u0027 + $_.GivenName)} ``` This command gets all the users in the directory that are located in the OU=HumanResources,OU=UserAccounts,DC=FABRIKAM,DC=COM organizational unit. The command sets the DisplayName property on these user objects to the concatenation of the Surname property and the GivenName property. ## Example 3: Set properties ```powershellPS C:\\\u003e Set-ADUser -Identity GlenJohn -Replace @{title=\"director\";mail=\"<EMAIL>\"} ``` This command sets the specified user\u0027s title property to director and the mail <NAME_EMAIL>. ## Example 4: Modify a user otherMailbox property ```powershellPS C:\\\u003e Set-ADUser -Identity GlenJohn -Remove @{otherMailbox=\"glen.john\"} -Add @{url=\"fabrikam.com\"} -Replace @{title=\"manager\"} -Clear description ``` This command modifies the user with the SAM account name GlenJohn\u0027s object by removing glen.john from the otherMailbox property, adding fabrikam.com to the url property, replacing the title property with manager, and clearing the description property. ## Example 5: Set user properties to a local instance ```powershellPS C:\\\u003e $User = Get-ADUser -Identity GlenJohn -Properties mail,department PS C:\\\u003e $User.mail = \"<EMAIL>\" PS C:\\\u003e $User.department = \"Accounting\" PS C:\\\u003e Set-ADUser -Instance $User ``` This example sets the mail and department properties on the user object with the SAM account name GlenJohn by using the Instance parameter. ## Example 6: Set attributes for a user ```powershellPS C:\\\u003e $Hours = New-Object byte[] 21 PS C:\\\u003e $Hours[5] = 255; $Hours[8] = 255; $Hours[11] = 255; $Hours[14] = 255; $Hours[17] = 255; PS C:\\\u003e $Hours[6] = 1; $Hours[9] = 1; $Hours[12] = 1; $Hours[15] = 1; $Hours[18] = 1; PS C:\\\u003e $ReplaceHashTable = New-Object HashTable PS C:\\\u003e $ReplaceHashTable.Add(\"logonHours\", $Hours) PS C:\\\u003e $ReplaceHashTable.Add(\"description\", \"Sarah Davis can only logon from Monday through Friday from 8:00 AM to 5:00 PM\") PS C:\\\u003e Set-ADUser -Identity \"SarahDavis\" -Replace $ReplaceHashTable ``` This example sets the user logon hours to Monday through Friday from 8:00 AM to 5:00 PM and adds a description. It updates the logonHours attribute with the specified byte array and the description attribute with the specified string. ## Example 7: Set a property for a user ```powershellPS C:\\\u003e $Manager = Get-ADUser -Identity GlenJohn -Server Corp-DC01 PS C:\\\u003e Set-ADUser -Identity ChewDavid -Manager $Manager -Server Branch-DC02 ``` This example sets the Manager property for the user with the SAM account name of ChewDavid where the manager, GlenJohn, is a user in another domain. ## Example 8: Get a user and set a property ```powershellPS C:\\\u003e Get-ADUser -Identity \"DavidChew\" | Set-ADUser -Manager \"ElisaDaugherty\" ``` This command modifies the Manager property for the DavidChew user. The command uses the Get-ADUser cmdlet to get the user DavidChew, and then passes the object to the current cmdlet by using the pipeline operator. ## Parameters ## -AccountExpirationDate Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires. Use the DateTime syntax when you specify this parameter. Time is assumed to be local time unless otherwise specified. When a time value is not specified, the time is assumed to 12:00:00 AM local time. When a date is not specified, the date is assumed to be the current date. ## Parameter properties Type:DateTime Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AccountNotDelegated Indicates whether the security context of the user is delegated to a service. When this parameter is set to $True, the security context of the account is not delegated to a service even when the service account is set as trusted for Kerberos delegation. This parameter sets the AccountNotDelegated property for an Active Directory account. This parameter also sets the ADS_UF_NOT_DELEGATED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Add Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values, and more than one property by separating them using a semicolon. If any of the properties have a null or empty value the cmdlet will return an error. The format for this parameter is: `-Add @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Remove, Replace, and Clear parameters together, the operations will be performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AllowReversiblePasswordEncryption Indicates whether reversible password encryption is allowed for the account. This parameter sets the AllowReversiblePasswordEncryption property of the account. This parameter also sets the ADS_UF_ENCRYPTED_TEXT_PASSWORD_ALLOWED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthenticationPolicy Specifies an Active Directory Domain Services authentication policy object. Specify the authentication policy object in one of the following formats: - Distinguished name - GUID - Name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicy Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthenticationPolicySilo Specifies an Active Directory Domain Services authentication policy silo object. Specify the authentication policy silo object in one of the following formats: - Distinguished name - GUID - Name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicySilo Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -CannotChangePassword Indicates whether the account password can be changed. This parameter sets the CannotChangePassword property of an account. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Certificates Specifies an array of certificates. The cmdlet modifies the DER-encoded X.509v3 certificates of the account. These certificates include the public key certificates issued to this account by the Microsoft Certificate Service. This parameter sets the Certificates property of the account object. The Lightweight Directory Access Protocol (LDAP) display name (ldapDisplayName) for this property is userCertificate. To add values: `-Certificates @{Add=value1,value2,...}` To remove values: `-Certificates @{Remove=value3,value4,...}` To replace values: `-Certificates @{Replace=value1,value2,...}` To clear all values: `-Certificates $Null` You can specify more than one operation by using a list separated by semicolons. For example, use the following syntax to add and remove Certificates values: `-Certificates @{Add=value1;Remove=value3}` The operators are applied in the following sequence: - Remove - Add - Replace ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ChangePasswordAtLogon Indicates whether a password must be changed during the next logon attempt. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -City Specifies the user\u0027s town or city. This parameter sets the City property of a user object. The LDAP display name (ldapDisplayName) of this property is l. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Clear Specifies an array of object properties that are cleared in the directory. Use this parameter to clear one or more values of a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can modify more than one property by specifying a comma-separated list. The format for this parameter is: `-Clear Attribute1LDAPDisplayName, Attribute2LDAPDisplayName` When you use the Add, Remove, Replace, and Clear parameters together, the operations are performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Company Specifies the user\u0027s company. This parameter sets the Company property of a user object. The LDAP display name (ldapDisplayName) of this property is company. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -CompoundIdentitySupported Indicates whether an account supports Kerberos service tickets which includes the authorization data for the user\u0027s device. This value sets the compound identity supported flag of the Active Directory msDS-SupportedEncryptionTypes attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 Warning Domain-joined Windows systems and services such as clustering manage their own msDS-SupportedEncryptionTypes attribute. Therefore any changes to the flag on the msDS-SupportedEncryptionTypes attribute are overwritten by the service or system that manages the setting. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Country Specifies the country or region code for the user\u0027s language of choice. This parameter sets the Country property of a user object. The LDAP display name (ldapDisplayName) of this property is c. This value is not used by Windows 2000. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as `User1` or `Domain01\\User01` or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the `Get-Credential` cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Department Specifies the user\u0027s department. This parameter sets the Department property of a user object. The LDAP display name (ldapDisplayName) of this property is department. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Description Specifies a description of the object. This parameter sets the value of the Description property for the user object. The LDAP display name (ldapDisplayName) for this property is description. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -DisplayName Specifies the display name of the object. This parameter sets the DisplayName property of the user object. The LDAP display name (ldapDisplayName) for this property is displayName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Division Specifies the user\u0027s division. This parameter sets the Division property of a user object. The LDAP display name (ldapDisplayName) of this property is division. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -EmailAddress Specifies the user\u0027s e-mail address. This parameter sets the EmailAddress property of a user object. The LDAP display name (ldapDisplayName) of this property is mail. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -EmployeeID Specifies the user\u0027s employee ID. This parameter sets the EmployeeID property of a user object. The LDAP display name (ldapDisplayName) of this property is employeeID. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -EmployeeNumber Specifies the user\u0027s employee number. This parameter sets the EmployeeNumber property of a user object. The LDAP display name (ldapDisplayName) of this property is employeeNumber. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Enabled Indicates whether an account is enabled. An enabled account requires a password. This parameter sets the Enabled property for an account object. This parameter also sets the ADS_UF_ACCOUNTDISABLE flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Fax Specifies the user\u0027s fax phone number. This parameter sets the Fax property of a user object. The LDAP display name (ldapDisplayName) of this property is facsimileTelephoneNumber. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -GivenName Specifies the user\u0027s given name. This parameter sets the GivenName property of a user object. The LDAP display name (ldapDisplayName) of this property is givenName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -HomeDirectory Specifies a user\u0027s home directory. This parameter sets the HomeDirectory property of a user object. The LDAP display name (ldapDisplayName) for this property is homeDirectory. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -HomeDrive Specifies a drive that is associated with the UNC path defined by the HomeDirectory property. The drive letter is specified as `\u003cDriveLetter\u003e`: where `\u003cDriveLetter\u003e` indicates the letter of the drive to associate. The `\u003cDriveLetter\u003e` must be a single, uppercase letter and the colon is required. This parameter sets the HomeDrive property of the user object. The LDAP display name (ldapDisplayName) for this property is homeDrive. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -HomePage Specifies the URL of the home page of the object. This parameter sets the homePage property of an Active Directory object. The LDAP display name (ldapDisplayName) for this property is wWWHomePage. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -HomePhone Specifies the user\u0027s home telephone number. This parameter sets the HomePhone property of a user. The LDAP display name (ldapDisplayName) of this property is homePhone. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory user object by providing one of the following property values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A SAM account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Initials Specifies the initials that represent part of a user\u0027s name. You can use this value for the user\u0027s middle initial. This parameter sets the Initials property of a user. The LDAP display name (ldapDisplayName) of this property is initials. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Instance Specifies an ADUser object that identifies the Active Directory user object that should be modified and the set of changes that should be made to that object. When this parameter is specified, any modifications made to the ADUser object are also made to the corresponding Active Directory object. The cmdlet only updates the object properties that have changed. The ADUser object specified as the value of the Instance parameter must have been retrieved by using the `Get-ADUser` cmdlet. When you specify the Instance parameter, you cannot specify other parameters that set individual properties on the object. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets Instance Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -KerberosEncryptionType Specifies whether an account supports Kerberos encryption types which are used during creation of service tickets. This value sets the encryption types supported flags of the Active Directory msDS-SupportedEncryptionTypes attribute. The acceptable values for this parameter are: - `None` - `DES` - `RC4` - `AES128` - `AES256` `None` removes all encryption types from the account, resulting in the KDC being unable to issue service tickets for services using the account. DES is a weak encryption type that is not supported by default since Windows 7 and Windows Server 2008 R2. Warning Domain-joined Windows systems and services such as clustering manage their own msDS-SupportedEncryptionTypes attribute. Therefore any changes to the flag on the msDS-SupportedEncryptionTypes attribute are overwritten by the service or system that manages the setting. ## Parameter properties Type:ADKerberosEncryptionType Default value:None Accepted values:None, DES, RC4, AES128, AES256 Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -LogonWorkstations Specifies the computers that the user can access. To specify more than one computer, create a single comma-separated list. You can identify a computer by using the Security Account Manager (SAM) account name (sAMAccountName) or the DNS host name of the computer. The SAM account name is the same as the NetBIOS name of the computer. The LDAP display name (ldapDisplayName) for this property is userWorkStations. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Manager Specifies the user\u0027s manager. This parameter sets the Manager property of a user object. This parameter is set by providing one of the following property values. Note: The identifier in parentheses is the LDAP display name for the property. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A SAM account name (sAMAccountName) The LDAP display name (ldapDisplayName) of this property is manager. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -MobilePhone Specifies the user\u0027s mobile phone number. This parameter sets the MobilePhone property of a user object. The LDAP display name (ldapDisplayName) of this property is mobile. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Office Specifies the location of the user\u0027s office or place of business. This parameter sets the Office property of a user object. The LDAP display name (ldapDisplayName) of this property is physicalDeliveryOfficeName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -OfficePhone Specifies the user\u0027s office telephone number. This parameter sets the OfficePhone property of a user object. The LDAP display name (ldapDisplayName) of this property is telephoneNumber. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Organization Specifies the user\u0027s organization. This parameter sets the Organization property of a user object. The LDAP display name (ldapDisplayName) of this property is o. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -OtherName Specifies a name in addition to a user\u0027s given name and surname, such as the user\u0027s middle name. This parameter sets the OtherName property of a user object. The LDAP display name (ldapDisplayName) of this property is middleName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and when a default value can be determined, no further rules are evaluated. In AD DS environments, a default value for Partition are set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In AD LDS environments, a default value for Partition will be set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter does not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PasswordNeverExpires Specifies whether the password of an account can expire. This parameter sets the PasswordNeverExpires property of an account object. This parameter also sets the ADS_UF_DONT_EXPIRE_PASSWD flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$False` or `0` - `$True` or `1` Note This parameter cannot be set to `$True` or `1` for an account that also has the ChangePasswordAtLogon property set to `$True`. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PasswordNotRequired Specifies whether the account requires a password. This parameter sets the PasswordNotRequired property of an account, such as a user or computer account. This parameter also sets the ADS_UF_PASSWD_NOTREQD flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$False` or `0` - `$True` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -POBox Specifies the user\u0027s post office box number. This parameter sets the POBox property of a user object. The LDAP display name (ldapDisplayName) of this property is postOfficeBox. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PostalCode Specifies the postal code or zip code. This parameter sets the PostalCode property of a user object. The LDAP display name (ldapDisplayName) of this property is `postalCode`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PrincipalsAllowedToDelegateToAccount Specifies an array of principal objects. This parameter sets the msDS-AllowedToActOnBehalfOfOtherIdentity attribute of a computer account object. ## Parameter properties Type: ADPrincipal[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ProfilePath Specifies a path to the user\u0027s profile. This value can be a local absolute path or a Universal Naming Convention (UNC) path. This parameter sets the ProfilePath property of the user object. The LDAP display name (ldapDisplayName) for this property is profilePath. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Remove Specifies that the cmdlet remove values of an object property. Use this parameter to remove one or more values of a property that cannot be modified using a cmdlet parameter. To remove an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values, and more than one property by separating them using a semicolon. If any of the properties have a null or empty value the cmdlet will return an error. The format for this parameter is: `-Remove @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Remove, Replace, and Clear parameters together, the parameters are applied in the following sequence: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Replace Specifies values for an object property that will replace the current values. Use this parameter to replace one or more values of a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values, and more than one property by separating them using a semicolon. If any of the properties have a null or empty value the cmdlet will return an error. The format for this parameter is: `-Replace @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Remove, Replace, and Clear parameters together, the operations will be performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SamAccountName Specifies the Security Account Manager (SAM) account name of the user, group, computer, or service account. The maximum length of the description is 256 characters. To be compatible with older operating systems, create a SAM account name that is 20 characters or less. This parameter sets the SAMAccountName for an account object. The LDAP display name (ldapDisplayName) for this property is `sAMAccountName`. Note If the string value provided is not terminated with a `$` character, the system adds one if needed. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ScriptPath Specifies a path to the user\u0027s log on script. This value can be a local absolute path or a Universal Naming Convention (UNC) path. This parameter sets the ScriptPath property of the user. The LDAP display name (ldapDisplayName) for this property is scriptPath. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ServicePrincipalNames Specifies the service principal names for the account. This parameter sets the ServicePrincipalNames property of the account. The LDAP display name (ldapDisplayName) for this property is `servicePrincipalName`. This parameter uses the following syntax to add, remove, replace or clear service principal name values. Syntax: To add values: `-ServicePrincipalNames @{Add=value1,value2,...}` To remove values: `-ServicePrincipalNames @{Remove=value3,value4,...}` To replace values: `-ServicePrincipalNames @{Replace=value1,value2,...}` To clear all values: `-ServicePrincipalNames $null` You can specify more than one change by using a list separated by semicolons. For example, use the following syntax to add and remove service principal names. `@{Add=value1,value2,...};@{Remove=value3,value4,...}` The operators will be applied in the following sequence: - Remove - Add - Replace The following example shows how to add and remove service principal names. `-ServicePrincipalNames-@{Add=\"SQLservice\\accounting.corp.contoso.com:1456\"};{Remove=\"SQLservice\\finance.corp.contoso.com:1456\"}` ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SmartcardLogonRequired Indicates whether a smart card is required to logon. This parameter sets the SmartCardLoginRequired property for a user. This parameter also sets the ADS_UF_SMARTCARD_REQUIRED flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -State Specifies the user\u0027s state or province. This parameter sets the State property of a user object. The LDAP display name (ldapDisplayName) of this property is st. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -StreetAddress Specifies the user\u0027s street address. This parameter sets the StreetAddress property of a user object. The LDAP display name (ldapDisplayName) of this property is streetAddress. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Surname Specifies the user\u0027s last name or surname. This parameter sets the Surname property of a user object. The LDAP display name (ldapDisplayName) of this property is sn. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Title Specifies the user\u0027s title. This parameter sets the Title property of a user object. The LDAP display name (ldapDisplayName) of this property is title. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -TrustedForDelegation Specifies whether an account is trusted for Kerberos delegation. A service that runs under an account that is trusted for Kerberos delegation can assume the identity of a client requesting the service. This parameter sets the TrustedForDelegation property of an account object. This value also sets the ADS_UF_TRUSTED_FOR_DELEGATION flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$False` or `0` - `$True` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -UserPrincipalName Specifies a user principal name (UPN) in the format `\u003cuser\u003e@\u003cDNS-domain-name\u003e`. A UPN is a friendly name assigned by an administrator that is shorter than the LDAP distinguished name used by the system and easier to remember. The UPN is independent of the user object\u0027s distinguished name, so a user object can be moved or renamed without affecting the user logon name. When logging on using a UPN, users don\u0027t have to choose a domain from a list on the logon dialog box. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADUser A user object is received by the Identity parameter. A user object that was retrieved by using the `Get-ADUser` cmdlet and then modified is received by the Instance parameter. ## Outputs ## None or Microsoft.ActiveDirectory.Management.ADUser Returns the modified user object when the PassThru parameter is specified. By default, this cmdlet does not generate any output. ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. ## Related Links - Get-ADUser - New-ADUser - Remove-ADUser - Set-ADAccountControl ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "set-aduser"
                                 ],
                        "last_updated":  "2025-07-28T17:14:44Z",
                        "title":  "Set-ADUser (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-aduser",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "New-ADUser"
                                    ],
                        "id":  "microsoft_docs_new-aduser",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AccountExpirationDate",
                                                            "AccountNotDelegated",
                                                            "AccountPassword",
                                                            "AllowReversiblePasswordEncryption",
                                                            "AuthenticationPolicy",
                                                            "AuthenticationPolicySilo",
                                                            "AuthType",
                                                            "CannotChangePassword",
                                                            "Certificates",
                                                            "ChangePasswordAtLogon",
                                                            "City",
                                                            "Company",
                                                            "CompoundIdentitySupported",
                                                            "Country",
                                                            "Credential",
                                                            "Department",
                                                            "Description",
                                                            "DisplayName",
                                                            "Division",
                                                            "EmailAddress",
                                                            "EmployeeID",
                                                            "EmployeeNumber",
                                                            "Enabled",
                                                            "Fax",
                                                            "GivenName",
                                                            "HomeDirectory",
                                                            "HomeDrive",
                                                            "HomePage",
                                                            "HomePhone",
                                                            "Initials",
                                                            "Instance",
                                                            "KerberosEncryptionType",
                                                            "LogonWorkstations",
                                                            "Manager",
                                                            "MobilePhone",
                                                            "Office",
                                                            "OfficePhone",
                                                            "Organization",
                                                            "OtherAttributes",
                                                            "OtherName",
                                                            "PasswordNeverExpires",
                                                            "PasswordNotRequired",
                                                            "Path",
                                                            "POBox",
                                                            "PostalCode",
                                                            "PrincipalsAllowedToDelegateToAccount",
                                                            "ProfilePath",
                                                            "SamAccountName",
                                                            "ScriptPath",
                                                            "Server",
                                                            "ServicePrincipalNames",
                                                            "SmartcardLogonRequired",
                                                            "State",
                                                            "StreetAddress",
                                                            "Surname",
                                                            "Title",
                                                            "TrustedForDelegation",
                                                            "Type",
                                                            "UserPrincipalName"
                                                        ],
                                         "cmdlet_name":  "New-ADUser"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# New-ADUser (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## New-ADUser Module: ActiveDirectory Module ## Creates an Active Directory user. ## Syntax ## Default (Default) ```powershellNew-ADUser [-AccountExpirationDate \u003cDateTime\u003e] [-AccountNotDelegated \u003cBoolean\u003e] [-AccountPassword \u003cSecureString\u003e] [-AllowReversiblePasswordEncryption \u003cBoolean\u003e] [-AuthenticationPolicy \u003cADAuthenticationPolicy\u003e] [-AuthenticationPolicySilo \u003cADAuthenticationPolicySilo\u003e] [-AuthType \u003cADAuthType\u003e] [-CannotChangePassword \u003cBoolean\u003e] [-Certificates \u003cX509Certificate[]\u003e] [-ChangePasswordAtLogon \u003cBoolean\u003e] [-City \u003cString\u003e] [-Company \u003cString\u003e] [-CompoundIdentitySupported \u003cBoolean\u003e] [-Country \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Department \u003cString\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-Division \u003cString\u003e] [-EmailAddress \u003cString\u003e] [-EmployeeID \u003cString\u003e] [-EmployeeNumber \u003cString\u003e] [-Enabled \u003cBoolean\u003e] [-Fax \u003cString\u003e] [-GivenName \u003cString\u003e] [-HomeDirectory \u003cString\u003e] [-HomeDrive \u003cString\u003e] [-HomePage \u003cString\u003e] [-HomePhone \u003cString\u003e] [-Initials \u003cString\u003e] [-Instance \u003cADUser\u003e] [-KerberosEncryptionType \u003cADKerberosEncryptionType\u003e] [-LogonWorkstations \u003cString\u003e] [-Manager \u003cADUser\u003e] [-MobilePhone \u003cString\u003e] [-Name] \u003cString\u003e [-Office \u003cString\u003e] [-OfficePhone \u003cString\u003e] [-Organization \u003cString\u003e] [-OtherAttributes \u003cHashtable\u003e] [-OtherName \u003cString\u003e] [-PassThru] [-PasswordNeverExpires \u003cBoolean\u003e] [-PasswordNotRequired \u003cBoolean\u003e] [-Path \u003cString\u003e] [-POBox \u003cString\u003e] [-PostalCode \u003cString\u003e] [-PrincipalsAllowedToDelegateToAccount \u003cADPrincipal[]\u003e] [-ProfilePath \u003cString\u003e] [-SamAccountName \u003cString\u003e] [-ScriptPath \u003cString\u003e] [-Server \u003cString\u003e] [-ServicePrincipalNames \u003cString[]\u003e] [-SmartcardLogonRequired \u003cBoolean\u003e] [-State \u003cString\u003e] [-StreetAddress \u003cString\u003e] [-Surname \u003cString\u003e] [-Title \u003cString\u003e] [-TrustedForDelegation \u003cBoolean\u003e] [-Type \u003cString\u003e] [-UserPrincipalName \u003cString\u003e] [-WhatIf] [-Confirm] [\u003cCommonParameters\u003e] ``` ## Description The `New-ADUser` cmdlet creates an Active Directory user. You can set commonly used user property values by using the cmdlet parameters. You can set property values that are not associated with cmdlet parameters by using the OtherAttributes parameter. When using this parameter, be sure to place single quotes around the attribute name. You must specify the SamAccountName parameter to create a user. You can use the `New-ADUser` cmdlet to create different types of user accounts such as iNetOrgPerson accounts. To do this in Active Directory Domain Services (AD DS), set the Type parameter to the Lightweight Directory Access Protocol (LDAP) display name for the type of account you want to create. This type can be any class in the Active Directory schema that is a subclass of user and that has an object category of person. The Path parameter specifies the container or organizational unit (OU) for the new user. When you do not specify the Path parameter, the cmdlet creates a user object in the default container for user objects in the domain. The following methods explain different ways to create an object by using this cmdlet. - Method 1: Use the `New-ADUser` cmdlet, specify the required parameters, and set any additional property values by using the cmdlet parameters. - Method 2: Use a template to create the new object. To do this, create a new user object or retrieve a copy of an existing user object and set the Instance parameter to this object. The object provided to the Instance parameter is used as a template for the new object. You can override property values from the template by setting cmdlet parameters. For examples and more information, see the Instance parameter description for this cmdlet. - Method 3: Use the `Import-Csv` cmdlet with the `New-ADUser` cmdlet to create multiple Active Directory user objects. To do this, use the `Import-Csv` cmdlet to create the custom objects from a comma-separated value (CSV) file that contains a list of object properties. Then pass these objects through the pipeline to the `New-ADUser` cmdlet to create the user objects. ## Examples ## Example 1: Create a user with an imported certificate ```powershell$splat = @{ Name = \u0027ChewDavid\u0027 Certificate = (New-Object System.Security.Cryptography.X509Certificates.X509Certificate -ArgumentList \u0027Export.cer\u0027) } New-ADUser @splat ``` This command creates a user named `ChewDavid` with a certificate imported from the file `Export.cer`. ## Example 2: Create a user and set properties ```powershellNew-ADUser -Name \u0027ChewDavid\u0027 -OtherAttributes @{ \u0027title\u0027=\u0027director\u0027 \u0027mail\u0027=\<EMAIL>\u0027 } ``` This command creates a new user named ChewDavid and sets the title and mail properties on the new object. ## Example 3: Create an inetOrgPerson user ```powershellNew-ADUser -Name \u0027ChewDavid\u0027 -Type iNetOrgPerson -Path \u0027DC=AppNC\u0027 -Server lds.Fabrikam.com:50000 ``` This command creates an inetOrgPerson-class user named ChewDavid on an AD LDS instance. ## Example 4: Create a user and set password ```powershell$splat = @{ Name = \u0027ChewDavid\u0027 AccountPassword = (Read-Host -AsSecureString \u0027AccountPassword\u0027) Enabled = $true } New-ADUser @splat ``` This command creates a new user named ChewDavid and sets the account password. ## Parameters ## -AccountExpirationDate Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is `accountExpires`. Use the DateTime syntax when you specify this parameter. Time is assumed to be local time unless otherwise specified. When a time value is not specified, the time is assumed to 12:00:00 AM local time. When a date is not specified, the date is assumed to be the current date. ## Parameter properties Type:DateTime Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AccountNotDelegated Indicates whether the security context of the user is delegated to a service. When this parameter is set to `$true`, the security context of the account is not delegated to a service even when the service account is set as trusted for Kerberos delegation. This parameter sets the AccountNotDelegated property for an Active Directory account. This parameter also sets the ADS_UF_NOT_DELEGATED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - `$false` or 0 - `$true` or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AccountPassword Specifies a new password value for an account. This value is stored as an encrypted string. The following conditions apply based on the manner in which the password parameter is used: - $Null password is specified: No password is set and the account is disabled unless it is requested to be enabled. - No password is specified: No password is set and the account is disabled unless it is requested to be enabled. - User password is specified: Password is set and the account is disabled unless it is requested to be enabled. User accounts, by default, are created without a password. If you provide a password, an attempt will be made to set that password however, this can fail due to password policy restrictions. The user account will still be created and you may use `Set-ADAccountPassword` to set the password on that account. In order to ensure that accounts remain secure, user accounts will never be enabled unless a valid password is set or PasswordNotRequired is set to `$true`. The account is created if the password fails for any reason. ## Parameter properties Type:SecureString Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AllowReversiblePasswordEncryption Indicates whether reversible password encryption is allowed for the account. This parameter sets the AllowReversiblePasswordEncryption property of the account. This parameter also sets the ADS_UF_ENCRYPTED_TEXT_PASSWORD_ALLOWED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AuthenticationPolicy Specifies an Active Directory Domain Services authentication policy object. Specify the authentication policy object in one of the following formats: - Distinguished name - GUID - Name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicy Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AuthenticationPolicySilo Specifies an Active Directory Domain Services authentication policy silo object. Specify the authentication policy silo object in one of the following formats: - Distinguished name - GUID - Name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicySilo Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - `Negotiate` or `0` - `Basic` or `1` The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -CannotChangePassword Indicates whether the account password can be changed. This parameter sets the CannotChangePassword property of an account. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Certificates Specifies the DER-encoded X.509v3 certificates of the account. These certificates include the public key certificates issued to this account by the Microsoft Certificate Service. This parameter sets the Certificates property of the account object. The LDAP display name (ldapDisplayName) for this property is `userCertificate`. ## Parameter properties Type: X509Certificate[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -ChangePasswordAtLogon Indicates whether a password must be changed during the next logon attempt. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` This parameter cannot be set to `$true` or 1 for an account that also has the PasswordNeverExpires property set to `$true`. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -City Specifies the user\u0027s town or city. This parameter sets the City property of a user object. The LDAP display name (ldapDisplayName) of this property is `l`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Company Specifies the user\u0027s company. This parameter sets the Company property of a user object. The LDAP display name (ldapDisplayName) of this property is `company`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -CompoundIdentitySupported Specifies whether an account supports Kerberos service tickets which includes the authorization data for the user\u0027s device. This value sets the compound identity supported flag of the Active Directory `msDS-SupportedEncryptionTypes` attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` Warning Domain-joined Windows systems and services such as clustering manage their own `msDS-SupportedEncryptionTypes` attribute. Therefore any changes to the flag on the `msDS-SupportedEncryptionTypes` attribute are overwritten by the service or system that manages the setting. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Country Specifies the country or region code for the user\u0027s language of choice. This parameter sets the Country property of a user object. The LDAP display name (ldapDisplayName) of this property is `c`. This value is not used by Windows 2000. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the `Get-Credential` cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Department Specifies the user\u0027s department. This parameter sets the Department property of a user object. The LDAP display name (ldapDisplayName) of this property is `department`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Description Specifies a description of the object. This parameter sets the value of the Description property for the user object. The LDAP display name (ldapDisplayName) for this property is `description`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -DisplayName Specifies the display name of the object. This parameter sets the DisplayName property of the user object. The LDAP display name (ldapDisplayName) for this property is `displayName`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Division Specifies the user\u0027s division. This parameter sets the Division property of a user object. The LDAP display name (ldapDisplayName) of this property is `division`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -EmailAddress Specifies the user\u0027s e-mail address. This parameter sets the EmailAddress property of a user object. The LDAP display name (ldapDisplayName) of this property is `mail`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -EmployeeID Specifies the user\u0027s employee ID. This parameter sets the EmployeeID property of a user object. The LDAP display name (ldapDisplayName) of this property is `employeeID`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -EmployeeNumber Specifies the user\u0027s employee number. This parameter sets the EmployeeNumber property of a user object. The LDAP display name (ldapDisplayName) of this property is `employeeNumber`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Enabled Specifies if an account is enabled. An enabled account requires a password. This parameter sets the Enabled property for an account object. This parameter also sets the ADS_UF_ACCOUNTDISABLE flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Fax Specifies the user\u0027s fax phone number. This parameter sets the Fax property of a user object. The LDAP display name (ldapDisplayName) of this property is `facsimileTelephoneNumber`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -GivenName Specifies the user\u0027s given name. This parameter sets the GivenName property of a user object. The LDAP display name (ldapDisplayName) of this property is `givenName`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -HomeDirectory Specifies a user\u0027s home directory. This parameter sets the HomeDirectory property of a user object. The LDAP display name (ldapDisplayName) for this property is `homeDirectory`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -HomeDrive Specifies a drive that is associated with the UNC path defined by the HomeDirectory property. The drive letter is specified as `\u003cDriveLetter\u003e:` where `\u003cDriveLetter\u003e` indicates the letter of the drive to associate. The `\u003cDriveLetter\u003e` must be a single, uppercase letter and the colon is required. This parameter sets the HomeDrive property of the user object. The LDAP display name (ldapDisplayName) for this property is `homeDrive`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -HomePage Specifies the URL of the home page of the object. This parameter sets the homePage property of a user object. The LDAP display name (ldapDisplayName) for this property is `wWWHomePage`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -HomePhone Specifies the user\u0027s home telephone number. This parameter sets the HomePhone property of a user object. The LDAP display name (ldapDisplayName) of this property is `homePhone`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Initials Specifies the initials that represent part of a user\u0027s name. You can use this value for the user\u0027s middle initial. This parameter sets the Initials property of a user object. The LDAP display name (ldapDisplayName) of this property is `initials`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Instance Specifies an instance of a user object to use as a template for a new user object. You can use an instance of an existing user object as a template or you can construct a new user object for template use. You can construct a new user object using the Windows PowerShell command line or by using a script. - Method 1: Use an existing user object as a template for a new object. To retrieve an instance of an existing user object, use a cmdlet such as `Get-ADUser`. Then provide this object to the Instance parameter of the `New-ADUser` cmdlet to create a new user object. You can override property values of the new object by setting the appropriate parameters. - Method 2: Create a new ADUser object and set the property values by using the Windows PowerShell command line interface. Then pass this object to the Instance parameter of the `New-ADUser` cmdlet to create the new Active Directory user object. Note Specified attributes are not validated, so attempting to set attributes that do not exist or cannot be set raises an error. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -KerberosEncryptionType Specifies whether an account supports Kerberos encryption types which are used during creation of service tickets. This value sets the encryption types supported flags of the Active Directory `msDS-SupportedEncryptionTypes` attribute. Possible values for this parameter are: - `None` - `DES` - `RC4` - `AES128` - `AES256` `None` removes all encryption types from the account, resulting in the KDC being unable to issue service tickets for services using the account. `DES` is a weak encryption type that is not supported by default since Windows 7 and Windows Server 2008 R2. Warning Domain-joined Windows systems and services such as clustering manage their own `msDS-SupportedEncryptionTypes` attribute. Therefore any changes to the flag on the `msDS-SupportedEncryptionTypes` attribute are overwritten by the service or system that manages the setting. ## Parameter properties Type:ADKerberosEncryptionType Default value:None Accepted values:None, DES, RC4, AES128, AES256 Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -LogonWorkstations Specifies the computers that the user can access. To specify more than one computer, create a single comma-separated list. You can identify a computer by using the Security Account Manager (SAM) account name (sAMAccountName) or the DNS host name of the computer. The SAM account name is the same as the NetBIOS name of the computer. The LDAP display name (ldapDisplayName) for this property is `userWorkStations`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Manager Specifies the user\u0027s manager. This parameter sets the Manager property of a user object. This parameter is set by providing one of the following property values. The identifier in parentheses is the LDAP display name for the property. The acceptable values for this parameter are: - A distinguished name - A GUID (`objectGUID`) - A security identifier (`objectSid`) - A SAM account name (`sAMAccountName`) ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -MobilePhone Specifies the user\u0027s mobile phone number. This parameter sets the MobilePhone property of a user object. The LDAP display name (ldapDisplayName) of this property is `mobile`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Name Specifies the name of the object. This parameter sets the Name property of a user object. The LDAP display name (ldapDisplayName) of this property is `name`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:1 Mandatory:True Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Office Specifies the location of the user\u0027s office or place of business. This parameter sets the Office property of a user object. The LDAP display name (ldapDisplayName) of this property is `physicalDeliveryOfficeName`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OfficePhone Specifies the user\u0027s office telephone number. This parameter sets the OfficePhone property of a user object. The LDAP display name (ldapDisplayName) of this property is `telephoneNumber`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Organization Specifies the user\u0027s organization. This parameter sets the Organization property of a user object. The LDAP display name (ldapDisplayName) of this property is `o`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OtherAttributes Specifies object attribute values for attributes that are not represented by cmdlet parameters. You can set one or more parameters at the same time with this parameter. If an attribute takes more than one value, you can assign multiple values. To identify an attribute, specify the LDAP display name (ldapDisplayName) defined for it in the Active Directory schema. To specify a single value for an attribute: `-OtherAttributes @{\u0027AttributeLDAPDisplayName\u0027=value}` To specify multiple values for an attribute: `-OtherAttributes @{\u0027AttributeLDAPDisplayName\u0027=value1,value2,...}` To specify values for multiple attributes: `-OtherAttributes @{\u0027Attribute1LDAPDisplayName\u0027=value; \u0027Attribute2LDAPDisplayName\u0027=value1,value2;...}` ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -OtherName Specifies a name in addition to a user\u0027s given name and surname, such as the user\u0027s middle name. This parameter sets the OtherName property of a user object. The LDAP display name (ldapDisplayName) of this property is `middleName`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PasswordNeverExpires Specifies whether the password of an account can expire. This parameter sets the PasswordNeverExpires property of an account object. This parameter also sets the ADS_UF_DONT_EXPIRE_PASSWD flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` This parameter cannot be set to `$true` or `1` for an account that also has the ChangePasswordAtLogon property set to `$true`. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PasswordNotRequired Specifies whether the account requires a password. A password is not required for a new account. This parameter sets the PasswordNotRequired property of an account object. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Path Specifies the X.500 path of the OU or container where the new object is created. In many cases, a default value is used for the Path parameter if no value is specified. The rules for determining the default value are given below. The rules listed first are evaluated first and when a default value can be determined, no further rules are evaluated. In Active Directory Domain Services (AD DS) environments, a default value for Path is set in the following cases: - If the cmdlet is run from an Active Directory PowerShell provider drive, the parameter is set to the current path of the provider drive. - If the cmdlet has a default path, this is used. For example: in New-ADUser, the Path parameter defaults to the Users container. - If none of the previous cases apply, the default value of Path is set to the default partition or naming context of the target domain. In AD LDS environments, a default value for Path is set in the following cases: - If the cmdlet is run from an Active Directory module for PowerShell provider drive, the parameter is set to the current path of the provider drive. - If the cmdlet has a default path, this is used. For example: in `New-ADUser`, the Path parameter defaults to the Users container. - If the target AD LDS instance has a default naming context, the default value of Path is set to the default naming context. To specify a default naming context for an AD LDS environment, set the `msDS-defaultNamingContext` property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Path parameter does not take any default value. Note The Active Directory Provider cmdlets, such as `New-Item`, `Remove-Item`, `Remove-ItemProperty`, `Rename-Item`, and `Set-ItemProperty` also contain a Path property. However, for the Active Directory Provider cmdlets, the Path parameter identifies the path of the actual object rather than the container. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -POBox Specifies the user\u0027s post office box number. This parameter sets the POBox property of a user object. The LDAP display name (ldapDisplayName) of this property is `postOfficeBox`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PostalCode Specifies the user\u0027s postal code or zip code. This parameter sets the PostalCode property of a user object. The LDAP display name (ldapDisplayName) of this property is `postalCode`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PrincipalsAllowedToDelegateToAccount Specifies an array of principal objects. This parameter sets the `msDS-AllowedToActOnBehalfOfOtherIdentity` attribute of a computer account object. ## Parameter properties Type: ADPrincipal[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -ProfilePath Specifies a path to the user\u0027s profile. This value can be a local absolute path or a Universal Naming Convention (UNC) path. This parameter sets the ProfilePath property of the user object. The LDAP display name (ldapDisplayName) for this property is `profilePath`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -SamAccountName Specifies the Security Account Manager (SAM) account name of the user, group, computer, or service account. The maximum length of the description is 256 characters. To be compatible with older operating systems, create a SAM account name that is 20 characters or less. This parameter sets the SAMAccountName for an account object. The LDAP display name (ldapDisplayName) for this property is `sAMAccountName`. Note Information the user should notice even if skimmingIf the string value provided is not terminated with a `$` character, the system adds one if needed. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -ScriptPath Specifies a path to the user\u0027s log on script. This value can be a local absolute path or a Universal Naming Convention (UNC) path. This parameter sets the ScriptPath property of the user object. The LDAP display name (ldapDisplayName) for this property is `scriptPath`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Server Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ServicePrincipalNames Specifies the service principal names for the account. This parameter sets the ServicePrincipalNames property of the account. The LDAP display name (ldapDisplayName) for this property is servicePrincipalName. To enter multiple values, use the following syntax: `\u003cvalue1\u003e,\u003cvalue2\u003e,...\u003cvalueX\u003e`. If the values contain spaces or otherwise require quotation marks, use the following syntax: `\u0027\u003cvalue1\u003e\u0027,\u0027\u003cvalue2\u003e\u0027,...\u0027\u003cvalueX\u003e\u0027`. ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -SmartcardLogonRequired Specifies whether a smart card is required to logon. This parameter sets the SmartCardLoginRequired property for a user object. This parameter also sets the ADS_UF_SMARTCARD_REQUIRED flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -State Specifies the user\u0027s or Organizational Unit\u0027s state or province. This parameter sets the State property of a user object. The LDAP display name (ldapDisplayName) of this property is `st`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -StreetAddress Specifies the user\u0027s street address. This parameter sets the StreetAddress property of a user object. The LDAP display name (ldapDisplayName) of this property is streetAddress. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Surname Specifies the user\u0027s last name or surname. This parameter sets the Surname property of a user object. The LDAP display name (ldapDisplayName) of this property is `sn`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Title Specifies the user\u0027s title. This parameter sets the Title property of a user object. The LDAP display name (ldapDisplayName) of this property is `title`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -TrustedForDelegation Indicates whether an account is trusted for Kerberos delegation. A service that runs under an account that is trusted for Kerberos delegation can assume the identity of a client requesting the service. This parameter sets the TrustedForDelegation property of an account object. This value also sets the ADS_UF_TRUSTED_FOR_DELEGATION flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Type Specifies the type of object to create. Set the Type parameter to the LDAP display name of the Active Directory schema class that represents the type of object that you want to create. The selected type must be a subclass of the User schema class. If this parameter is not specified it defaults to `User`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -UserPrincipalName Specifies a user principal name (UPN) in the format `\u003cuser\u003e@\u003cDNS-domain-name\u003e`. A UPN is a friendly name assigned by an administrator that is shorter than the LDAP distinguished name used by the system and easier to remember. The UPN is independent of the user object\u0027s distinguished name, so a user object can be moved or renamed without affecting the user logon name. When signing on using a UPN, users no longer have to choose a domain from a list on the logon dialog box. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADUser A user object that is a template for the new user object is received by the Instance parameter. ## Outputs ## None By default, this cmdlet does not generate any output. ## Microsoft.ActiveDirectory.Management.ADUser Returns the new user object when the PassThru parameter is specified. ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. ## Related Links - Get-ADUser - Remove-ADUser - Set-ADUser - Set-ADAccountPassword ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "new-aduser"
                                 ],
                        "last_updated":  "2025-07-28T17:14:44Z",
                        "title":  "New-ADUser (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-aduser",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Remove-ADUser"
                                    ],
                        "id":  "microsoft_docs_remove-aduser",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AuthType",
                                                            "Credential",
                                                            "Partition",
                                                            "Server"
                                                        ],
                                         "cmdlet_name":  "Remove-ADUser"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Remove-ADUser (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Remove-ADUser Module: ActiveDirectory Module ## Removes an Active Directory user. ## Syntax ## Default (Default) ```powershellRemove-ADUser [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Identity] \u003cADUser\u003e [-Partition \u003cString\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The Remove-ADUser cmdlet removes an Active Directory user. The Identity parameter specifies the Active Directory user to remove. You can identify a user by its distinguished name (DN), GUID, security identifier (SID), or Security Account Manager (SAM) account name. You can also set the Identity parameter to a user object variable, such as `$\u003clocalUserObject\u003e`, or you can pass a user object through the pipeline to the Identity parameter. For example, you can use the Get-ADUser cmdlet to retrieve a user object and then pass the object through the pipeline to the Remove-ADUser cmdlet. If the ADUser is being identified by its DN, the Partition parameter will be automatically determined. For AD LDS environments, the Partition parameter must be specified except in the following two conditions: - The cmdlet is run from an Active Directory provider drive. - A default naming context or partition is defined for the AD LDS environment. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. ## Examples ## Example 1: Remove a specified user ```powershellPS C:\\\u003e Remove-ADUser -Identity GlenJohn ``` This command removes the user with SAM account name GlenJohn. ## Example 2: Remove a filtered list of users ```powershellPS C:\\\u003e Search-ADAccount -AccountDisabled | where {$_.ObjectClass -eq \u0027user\u0027} | Remove-ADUser ``` This command searches for any users that have disabled accounts and removes them. ## Example 3: Remove a user by distinguished name ```powershellPS C:\\\u003e Remove-ADUser -Identity \"CN=Glen John,OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\" ``` This command removes the user with the distinguished name CN=Glen John,OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM. ## Example 4: Get a user by distinguished name and remove it ```powershellPS C:\\\u003e Get-ADUser -Identity \"cn=glenjohn,dc=appnc\" -Server Lds.Fabrikam.com:50000 | Remove-ADUser ``` This command gets the user with the distinguished name cn=glenjohn,dc=appnc from the AD LDS instance and removes it. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory user object by providing one of the following property values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A Distinguished name - A GUID (objectGUID) - A Security Identifier (objectSid) - A SAM account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value will be used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules will be evaluated. In AD DS environments, a default value for Partition will be set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition will be set to the default partition or naming context of the target domain. In AD LDS environments, a default value for Partition will be set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition will be set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter will not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADUser A user object is received by the Identity parameter. ## Outputs ## None ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. - By default, this cmdlet prompts for confirmation as it is defined with High impact and the default value of the $ConfirmPreference variable is High. To bypass prompting for confirmation before removal, you can specify `-Confirm:$False` when using this cmdlet. ## Related Links - Get-ADUser - New-ADUser - Set-ADUser ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "remove-aduser"
                                 ],
                        "last_updated":  "2025-07-28T17:14:45Z",
                        "title":  "Remove-ADUser (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/enable-adaccount",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Enable-ADAccount"
                                    ],
                        "id":  "microsoft_docs_enable-adaccount",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AuthType",
                                                            "Credential",
                                                            "Partition",
                                                            "Server"
                                                        ],
                                         "cmdlet_name":  "Enable-ADAccount"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Enable-ADAccount (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Enable-ADAccount Module: ActiveDirectory Module ## Enables an Active Directory account. ## Syntax ## Default (Default) ```powershellEnable-ADAccount [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Identity] \u003cADAccount\u003e [-Partition \u003cString\u003e] [-PassThru] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The `Enable-ADAccount` cmdlet enables an Active Directory user, computer, or service account. The Identity parameter specifies the Active Directory user, computer, or service account that you want to enable. You can identify an account by its distinguished name, GUID, security identifier (SID) or Security Accounts Manager (SAM) account name. You can also set the Identity parameter to an object variable such as `$\u003clocalADAccountObject\u003e`, or you can pass an account object through the pipeline to the Identity parameter. For example, you can use the `Get-ADUser` cmdlet to retrieve an account object and then pass the object through the pipeline to the `Enable-ADAccount` cmdlet. Similarly, you can use `Get-ADComputer` and `Search-ADAccount` to retrieve account objects. ## Examples ## Example 1: Enable an account by identity ```powershellEnable-ADAccount -Identity \u0027PattiFul\u0027 ``` This command enables the account with identity SamAccountName `PattiFul`. ## Example 2: Enable an account by Distinguished Name ```powershellEnable-ADAccount -Identity \u0027CN=Patti Fuller,OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 ``` This command enables the account with DistinguishedName `CN=Patti Fuller,OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM`. ## Example 3: Enable all accounts in an organizational unit using a filter ```powershell$params = @{ Filter = \u0027Name -like \"*\"\u0027 SearchBase = \u0027OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 } Get-ADUser @params | Enable-ADAccount ``` This command enables all accounts in the organizational unit: `OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM`. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - `Negotiate` or `0` - `Basic` or `1` The default authentication method is `Negotiate`. A Secure Sockets Layer (SSL) connection is required for the `Basic` authentication method. ## Parameter properties Type:Microsoft.ActiveDirectory.Management.ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as `User1` or `Domain01\\User01` or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the `Get-Credential` cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory account object by providing one of the following property values. The identifier in parentheses is the Lightweight Directory Access Protocol (LDAP) display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A Security Identifier (objectSid) - A SAM account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an account object instance. Derived types such as the following are also accepted: - Microsoft.ActiveDirectory.Management.ADUser - Microsoft.ActiveDirectory.Management.ADComputer - Microsoft.ActiveDirectory.Management.ADServiceAccount ## Parameter properties Type:Microsoft.ActiveDirectory.Management.ADAccount Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter will not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you\u0027re working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory snapshot instance. Specify the Active Directory Domain Services instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet isn\u0027t run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## Microsoft.ActiveDirectory.Management.ADAccount An account object is received by the Identity parameter. Derived types, such as the following, are also accepted: - Microsoft.ActiveDirectory.Management.ADUser - Microsoft.ActiveDirectory.Management.ADComputer - Microsoft.ActiveDirectory.Management.ADServiceAccount ## Outputs ## None ## Notes - This cmdlet doesn\u0027t work with an Active Directory snapshot. - This cmdlet doesn\u0027t work with a read-only domain controller. ## Related Links - Clear-ADAccountExpiration - Disable-ADAccount - Get-ADAccountAuthorizationGroup - Search-ADAccount - Set-ADAccountControl - Set-ADAccountExpiration - Set-ADAccountPassword - Unlock-ADAccount ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "enable-adaccount"
                                 ],
                        "last_updated":  "2025-07-28T17:14:46Z",
                        "title":  "Enable-ADAccount (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/disable-adaccount",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Disable-ADAccount"
                                    ],
                        "id":  "microsoft_docs_disable-adaccount",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AuthType",
                                                            "Credential",
                                                            "Partition",
                                                            "Server"
                                                        ],
                                         "cmdlet_name":  "Disable-ADAccount"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Disable-ADAccount (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Disable-ADAccount Module: ActiveDirectory Module ## Disables an Active Directory account. ## Syntax ## Default (Default) ```powershellDisable-ADAccount [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Identity] \u003cADAccount\u003e [-Partition \u003cString\u003e] [-PassThru] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The `Disable-ADAccount` cmdlet disables an Active Directory user, computer, or service account. The Identity parameter specifies the Active Directory user, computer service account, or other service account that you want to disable. You can identify an account by its distinguished name, GUID, security identifier (SID), or Security Accounts Manager (SAM) account name. You can also set the Identity parameter to an object variable such as `$\u003clocalADAccountObject\u003e`, or you can pass an account object through the pipeline to the Identity parameter. For example, you can use the `Get-ADUser` cmdlet to retrieve a user account object and then pass the object through the pipeline to the `Disable-ADAccount` cmdlet. Similarly, you can use `Get-ADComputer` and `Search-ADAccount` to retrieve account objects. For Active Directory Lightweight Directory Services (AD LDS) environments, the Partition parameter must be specified except in the following two conditions: - The cmdlet is run from an Active Directory provider drive. - A default naming context or partition is defined for the AD LDS environment. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. ## Examples ## Example 1: Disable an account by identity ```powershellDisable-ADAccount -Identity PattiFul ``` This command disables the account with identity SAMAccountName `PattiFul`. ## Example 2: Disable an account by Distinguished Name ```powershellDisable-ADAccount -Identity \u0027CN=Patti Fuller,OU=Finance,OU=Users,DC=FABRIKAM,DC=COM\u0027 ``` This command disables the account with DistinguishedName `CN=Patti Fuller,OU=Finance,OU=Users,DC=FABRIKAM,DC=COM`. ## Example 3: Disable all accounts in an organizational unit ```powershellGet-ADUser -Filter \u0027Name -like \"*\"\u0027 -SearchBase \"OU=Finance,OU=Users,DC=FABRIKAM,DC=COM\" | Disable-ADAccount ``` This command disables all accounts in the organizational unit `OU=Finance,OU=Users,DC=FABRIKAM,DC=COM`. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - `Negotiate` or `0` - `Basic` or `1` The default authentication method is `Negotiate`. A Secure Sockets Layer (SSL) connection is required for the `Basic` authentication method. ## Parameter properties Type:Microsoft.ActiveDirectory.Management.ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as `User1` or `Domain01\\User01` or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the `Get-Credential` cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory account object by providing one of the following property values. The identifier in parentheses is the Lightweight Directory Access Protocol (LDAP) display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A Security Identifier (objectSid) - A SAM Account Name (SAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an account object instance. Derived types such as the following are also accepted: - Microsoft.ActiveDirectory.Management.ADServiceAccount - Microsoft.ActiveDirectory.Management.ADComputer - Microsoft.ActiveDirectory.Management.ADUser ## Parameter properties Type:Microsoft.ActiveDirectory.Management.ADAccount Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter will not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you\u0027re working. By default, this cmdlet doesn\u0027t generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory snapshot instance. Specify the Active Directory Domain Services instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet isn\u0027t run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## Microsoft.ActiveDirectory.Management.ADAccount An account object is received by the Identity parameter. Derived types, such as the following are also accepted: - Microsoft.ActiveDirectory.Management.ADUser - Microsoft.ActiveDirectory.Management.ADComputer - Microsoft.ActiveDirectory.Management.ADServiceAccount ## Outputs ## None ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. ## Related Links - Clear-ADAccountExpiration - Enable-ADAccount - Get-ADAccountAuthorizationGroup - Search-ADAccount - Set-ADAccountControl - Set-ADAccountExpiration - Set-ADAccountPassword - Unlock-ADAccount - AD DS Administration Cmdlets in Windows PowerShell ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "disable-adaccount"
                                 ],
                        "last_updated":  "2025-07-28T17:14:47Z",
                        "title":  "Disable-ADAccount (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroup",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Get-ADGroup"
                                    ],
                        "id":  "microsoft_docs_get-adgroup",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "Filter",
                                                            "AuthType",
                                                            "Credential",
                                                            "Properties",
                                                            "ResultPageSize",
                                                            "ResultSetSize",
                                                            "SearchBase",
                                                            "SearchScope",
                                                            "Server",
                                                            "Partition",
                                                            "LDAPFilter"
                                                        ],
                                         "cmdlet_name":  "Get-ADGroup"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Get-ADGroup (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Get-ADGroup Module: ActiveDirectory Module ## Gets one or more Active Directory groups. ## Syntax ## Filter (Default) ```powershellGet-ADGroup -Filter \u003cString\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Properties \u003cString[]\u003e] [-ResultPageSize \u003cInt32\u003e] [-ResultSetSize \u003cInt32\u003e] [-SearchBase \u003cString\u003e] [-SearchScope \u003cADSearchScope\u003e] [-Server \u003cString\u003e] [-ShowMemberTimeToLive] [\u003cCommonParameters\u003e] ``` ## Identity ```powershellGet-ADGroup [-Identity] \u003cADGroup\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Partition \u003cString\u003e] [-Properties \u003cString[]\u003e] [-Server \u003cString\u003e] [-ShowMemberTimeToLive] [\u003cCommonParameters\u003e] ``` ## LdapFilter ```powershellGet-ADGroup -LDAPFilter \u003cString\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Properties \u003cString[]\u003e] [-ResultPageSize \u003cInt32\u003e] [-ResultSetSize \u003cInt32\u003e] [-SearchBase \u003cString\u003e] [-SearchScope \u003cADSearchScope\u003e] [-Server \u003cString\u003e] [-ShowMemberTimeToLive] [\u003cCommonParameters\u003e] ``` ## Description The Get-ADGroup cmdlet gets a group or performs a search to retrieve multiple groups from an Active Directory. The Identity parameter specifies the Active Directory group to get. You can identify a group by its distinguished name (DN), GUID, security identifier (SID), or Security Accounts Manager (SAM) account name. You can also specify group object variable, such as `$\u003clocalGroupObject\u003e`. To search for and retrieve more than one group, use the Filter or LDAPFilter parameters. The Filter parameter uses the PowerShell Expression Language to write query strings for Active Directory. PowerShell Expression Language syntax provides rich type conversion support for value types received by the Filter parameter. For more information about the Filter parameter syntax, type `Get-Help about_ActiveDirectory_Filter`. If you have existing Lightweight Directory Access Protocol (LDAP) query strings, you can use the LDAPFilter parameter. This cmdlet gets a default set of group object properties. To get additional properties use the Properties parameter. For more information about the how to determine the properties for group objects, see the Properties parameter description. ## Examples ## Example 1: Get a group by SAM account name ```powershellPS C:\\\u003e Get-ADGroup -Identity Administrators DistinguishedName : CN=Administrators,CN=Builtin,DC=Fabrikam,DC=com GroupCategory : Security GroupScope : DomainLocal Name : Administrators ObjectClass : group ObjectGUID : 02ce3874-dd86-41ba-bddc-013f34019978 SamAccountName : Administrators SID : S-1-5-32-544 ``` This command gets the group with the SAM account name Administrators. ## Example 2: Get a group by SID ```powershellPS C:\\\u003e Get-ADGroup -Identity S-1-5-32-544 -Properties member DistinguishedName : CN=Administrators,CN=Builtin,DC=Fabrikam,DC=com GroupCategory : Security GroupScope : DomainLocal member : {CN=Domain Admins,CN=Users,DC=Fabrikam,DC=com, CN=Enterprise Admins,CN=Users,DC=Fabrikam,DC=com, CN=LabAdmin,CN=Users,DC=Fabrikam,DC=com, C N=Administrator,CN=Users,DC=Fabrikam,DC=com} Name : Administrators ObjectClass : group ObjectGUID : 02ce3874-dd86-41ba-bddc-013f34019978 SamAccountName : Administrators SID : S-1-5-32-544 ``` This command gets the group with SID S-1-5-32-544 and the property member. ## Example 3: Get a group and filter the results ```powershellPS C:\\\u003e Get-ADGroup -Filter \u0027GroupCategory -eq \"Security\" -and GroupScope -ne \"DomainLocal\"\u0027 ``` This command gets all groups that have a GroupCategory of Security but do not have a GroupScope of DomainLocal. ## Example 4: Get a group from a specified search base and filter the results ```powershellPS C:\\\u003e Get-ADGroup -Server localhost:60000 -Filter \"GroupScope -eq \u0027DomainLocal\u0027\" -SearchBase \"DC=AppNC\" DistinguishedName : CN=AlphaGroup,OU=AccountDeptOU,DC=AppNC GroupCategory : Security GroupScope : DomainLocal Name : AlphaGroup ObjectClass : group ObjectGUID : 6498c9fb-7c62-48fe-9972-1461f7f3dec2 SID : S-1-*********-*********-**********-**********-**********-********* DistinguishedName : CN=BranchOffice1,OU=AccountDeptOU,DC=AppNC GroupCategory : Security GroupScope : DomainLocal Name : BranchOffice1 ObjectClass : group ObjectGUID : 0b7504c5-482b-4a73-88f5-8a76960e4568 SID : S-1-*********-*********-**********-**********-**********-********** DistinguishedName : CN=AccountLeads,OU=AccountDeptOU,DC=AppNC GroupCategory : Distribution GroupScope : DomainLocal Name : AccountLeads ObjectClass : group ObjectGUID : b20c032b-2de9-401a-b48c-341854a37254 SID : S-1-*********-*********-**********-**********-**********-********* ``` This command gets all the DomainLocal groups from the AppNC partition of the AD LDS instance. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Filter Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Expression Language syntax. The PowerShell Expression Language syntax provides rich type-conversion support for value types received by the Filter parameter. The syntax uses an in-order representation, which means that the operator is placed between the operand and the value. For more information about the Filter parameter, type `Get-Help about_ActiveDirectory_Filter`. Syntax: The following syntax uses Backus-Naur form to show how to use the PowerShell Expression Language for this parameter. \u003cfilter\u003e ::= \"{\" \u003cFilterComponentList\u003e \"}\" \u003cFilterComponentList\u003e ::= \u003cFilterComponent\u003e | \u003cFilterComponent\u003e \u003cJoinOperator\u003e \u003cFilterComponent\u003e | \u003cNotOperator\u003e \u003cFilterComponent\u003e \u003cFilterComponent\u003e ::= \u003cattr\u003e \u003cFilterOperator\u003e \u003cvalue\u003e | \"(\" \u003cFilterComponent\u003e \")\" \u003cFilterOperator\u003e ::= \"-eq\" | \"-le\" | \"-ge\" | \"-ne\" | \"-lt\" | \"-gt\"| \"-approx\" | \"-bor\" | \"-band\" | \"-recursivematch\" | \"-like\" | \"-notlike\" \u003cJoinOperator\u003e ::= \"-and\" | \"-or\" \u003cNotOperator\u003e ::= \"-not\" \u003cattr\u003e ::= \u003cPropertyName\u003e | \u003cLDAPDisplayName of the attribute\u003e \u003cvalue\u003e::= \u003ccompare this value with an \u003cattr\u003e by using the specified \u003cFilterOperator\u003e\u003e For a list of supported types for \u003cvalue\u003e, type `Get-Help about_ActiveDirectory_ObjectModel`. Note: PowerShell wildcards other than *, such as ?, are not supported by the Filter syntax. Note: To query using LDAP query strings, use the LDAPFilter parameter. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory group object by providing one of the following values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A security accounts manager account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADGroup Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -LDAPFilter Specifies an LDAP query string that is used to filter Active Directory objects. You can use this parameter to run your existing LDAP queries. The Filter parameter syntax supports the same functionality as the LDAP syntax. For more information, see the Filter parameter description or type `Get-Help about_ActiveDirectory_Filter`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets LdapFilter Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services (AD DS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition will be set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter does not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Properties Specifies the properties of the output object to retrieve from the server. Use this parameter to retrieve properties that are not included in the default set. Specify properties for this parameter as a comma-separated list of names. To display all of the attributes that are set on the object, specify * (asterisk). To specify an individual extended property, use the name of the property. For properties that are not default or extended properties, you must specify the LDAP display name of the attribute. To retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the Get-Member cmdlet. ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False Aliases:Property ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ResultPageSize Specifies the number of objects to include in one page for an AD DS query. The default is 256 objects per page. ## Parameter properties Type:Int32 Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ResultSetSize Specifies the maximum number of objects to return for an AD DS query. If you want to receive all of the objects, set this parameter to $Null (null value). You can use Ctrl+C to stop the query and return of objects. The default is $Null. ## Parameter properties Type:Int32 Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SearchBase Specifies an Active Directory path to search under. When you run a cmdlet from an Active Directory provider drive, the default value of this parameter is the current path of the drive. When you run a cmdlet outside of an Active Directory provider drive against an AD DS target, the default value of this parameter is the default naming context of the target domain. When you run a cmdlet outside of an Active Directory provider drive against an AD LDS target, the default value is the default naming context of the target LDS instance if one has been specified by setting the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. If no default naming context has been specified for the target AD LDS instance, then this parameter has no default value. When the value of the SearchBase parameter is set to an empty string and you are connected to a GC port, all partitions are searched. If the value of the SearchBase parameter is set to an empty string and you are not connected to a GC port, an error is thrown. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SearchScope Specifies the scope of an Active Directory search. The acceptable values for this parameter are: - Base or 0 - OneLevel or 1 - Subtree or 2 A Base query searches only the current path or object. A OneLevel query searches the immediate children of that path or object. A Subtree query searches the current path or object and all children of that path or object. ## Parameter properties Type:ADSearchScope Default value:None Accepted values:Base, OneLevel, Subtree Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance. Specify the Active Directory Domain Services instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ShowMemberTimeToLive Indicates that this cmdlet displays Time to Live (TTL) values for group members. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADGroup A group object is received by the Identity parameter. ## Outputs ## Microsoft.ActiveDirectory.Management.ADGroup Returns one or more group objects. The Get-ADGroup cmdlet returns a default set of ADGroup property values. To retrieve additional ADGroup properties, use the Properties parameter. To view the properties for an ADGroup object, see the following examples. To run these examples, replace \u003cgroup\u003e with a group identifier such as Administrators. To get a list of the default set of properties of an ADGroup object, use the following command: `Get-ADGroup`\u003cgroup\u003e`| Get-Member` To get a list of all the properties of an ADGroup object, use the following command: `Get-ADGroup`\u003cgroup\u003e`-Properties * | Get-Member` ## Related Links - New-ADGroup - Remove-ADGroup - Set-ADGroup ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "get-adgroup"
                                 ],
                        "last_updated":  "2025-07-28T17:14:48Z",
                        "title":  "Get-ADGroup (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/set-adgroup",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Set-ADGroup"
                                    ],
                        "id":  "microsoft_docs_set-adgroup",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "Add",
                                                            "AuthType",
                                                            "Clear",
                                                            "Credential",
                                                            "Description",
                                                            "DisplayName",
                                                            "GroupCategory",
                                                            "GroupScope",
                                                            "HomePage",
                                                            "ManagedBy",
                                                            "Partition",
                                                            "Remove",
                                                            "Replace",
                                                            "SamAccountName",
                                                            "Server",
                                                            "Instance"
                                                        ],
                                         "cmdlet_name":  "Set-ADGroup"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Set-ADGroup (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Set-ADGroup Module: ActiveDirectory Module ## Modifies an Active Directory group. ## Syntax ## Identity ```powershellSet-ADGroup [-Identity] \u003cADGroup\u003e [-WhatIf] [-Confirm] [-Add \u003cHashtable\u003e] [-AuthType \u003cADAuthType\u003e] [-Clear \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-GroupCategory \u003cADGroupCategory\u003e] [-GroupScope \u003cADGroupScope\u003e] [-HomePage \u003cString\u003e] [-ManagedBy \u003cADPrincipal\u003e] [-Partition \u003cString\u003e] [-PassThru] [-Remove \u003cHashtable\u003e] [-Replace \u003cHashtable\u003e] [-SamAccountName \u003cString\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Instance ```powershellSet-ADGroup -Instance \u003cADGroup\u003e [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-PassThru] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The Set-ADGroup cmdlet modifies the properties of an Active Directory group. You can modify commonly used property values by using the cmdlet parameters. Property values that are not associated with cmdlet parameters can be modified by using the Add, Replace, Clear, and Remove parameters. The Identity parameter specifies the Active Directory group to modify. You can identify a group by its distinguished name, GUID, security identifier, or Security Account Manager (SAM) account name. You can also set the Identity parameter to an object variable such as `$\u003clocalGroupObject\u003e`, or you can pass a group object through the pipeline to the Identity parameter. For example, you can use the Get-ADGroup cmdlet to get a group object and then pass the object through the pipeline to the Set-ADGroup cmdlet. The Instance parameter provides a way to update a group object by applying the changes made to a copy of the object. When you set the Instance parameter to a copy of an Active Directory group object that has been modified, the Set-ADGroup cmdlet makes the same changes to the original group object. To get a copy of the object to modify, use the Get-ADGroup cmdlet. The Identity parameter is not allowed when you use the Instance parameter. For more information about the Instance parameter, see the Instance parameter description. ## Examples ## Example 1: Set a property for a group ```powershellPS C:\\\u003e Set-ADGroup -Server localhost:60000 -Identity \"CN=AccessControl,DC=AppNC\" -Description \"Access Group\" -Passthru DistinguishedName : CN=AccessControl,DC=AppNC GroupCategory : Security GroupScope : DomainLocal Name : AccessControl ObjectClass : group ObjectGUID : d65f5e8f-36da-4390-9840-8b9fde6282fc SID : S-1-*********-*********-2782881406-1264922549-3814061485-1557022459 ``` This command sets the Description property of the group named AccessControl to Access Group on an Active Directory Application Mode (ADAM) instance. ## Example 2: Set the description for filtered groups ```powershellPS C:\\\u003e Get-ADGroup -Filter \u0027name -like \"Access*\"\u0027 | Set-ADGroup -Description \"Access Group\" ``` This command modifies the Description property on all groups that have a name that starts with Access by using the pipeline operator. ## Example 3: Set a property by specifying an instance ```powershellPS C:\\\u003e $Group = Get-ADGroup -Server localhost:60000 -Identity \"CN=AccessControl,DC=AppNC\" PS C:\\\u003e $Group.Description = \"Access Group\" PS C:\\\u003e Set-ADGroup -Instance $Group -Passthru DistinguishedName : CN=AccessControl,DC=AppNC GroupCategory : Security GroupScope : DomainLocal Name : AccessControl ObjectClass : group ObjectGUID : d65f5e8f-36da-4390-9840-8b9fde6282fc SID : S-1-*********-*********-2782881406-1264922549-3814061485-1557022459 ``` This example sets the Description property on the AccessControl group by using the Instance parameter. ## Parameters ## -Add Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is: `-Add @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Replace, Clear, and Remove parameters together, the operations are performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Clear Specifies an array of object properties that are cleared in the directory. Use this parameter to clear one or more values of a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can modify more than one property by specifying a comma-separated list. The format for this parameter is: `-Clear Attribute1LDAPDisplayName, Attribute2LDAPDisplayName` When you use the Add, Replace, Clear, and Remove parameters together, the operations are performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Description Specifies a description of the object. This parameter sets the value of the Description property for the object. The Lightweight Directory Access Protocol (LDAP) display name (ldapDisplayName) for this property is description. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -DisplayName Specifies the display name of the object. This parameter sets the DisplayName property of the object. The LDAP display name (ldapDisplayName) for this property is displayName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -GroupCategory Specifies the category of the group. The acceptable values for this parameter are: - Distribution or 0 - Security or 1 This parameter sets the GroupCategory property of the group. This parameter value combined with other group values sets the LDAP display name (ldapDisplayName) attribute named groupType. ## Parameter properties Type:ADGroupCategory Default value:None Accepted values:Distribution, Security Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -GroupScope Specifies the group scope of the group. The acceptable values for this parameter are: - DomainLocal or 0 - Global or 1 - Universal or 2 This parameter sets the GroupScope property of a group object to the specified value. The LDAP display name of this property is groupType. ## Parameter properties Type:ADGroupScope Default value:None Accepted values:DomainLocal, Global, Universal Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -HomePage Specifies the URL of the home page of the object. This parameter sets the homePage property of an Active Directory object. The LDAP display name (ldapDisplayName) for this property is wWWHomePage. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory group object by providing one of the following values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A SAM account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADGroup Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Instance Specifies a modified copy of a group object to use to update the actual Active Directory group object. When this parameter is used, any modifications made to the modified copy of the object are also made to the corresponding Active Directory object. The cmdlet only updates the object properties that have changed. The Instance parameter can only update group objects that have been retrieved by using the Get-ADGroup cmdlet. When you specify the Instance parameter, you cannot specify other parameters that set properties on the object. ## Parameter properties Type:ADGroup Default value:None Supports wildcards:False DontShow:False ## Parameter sets Instance Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ManagedBy Specifies the user or group that manages the object by providing one of the following property values. Note: The identifier in parentheses is the LDAP display name for the property. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - SAM account name (sAMAccountName) This parameter sets the Active Directory attribute with an LDAP display name of managedBy. ## Parameter properties Type:ADPrincipal Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services (AD DS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter does not take a default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Remove Specifies that the cmdlet remove values of an object property. Use this parameter to remove one or more values of a property that cannot be modified using a cmdlet parameter. To remove an object property, you must use the LDAP display name. You can remove more than one property by specifying a semicolon-separated list. The format for this parameter is: `-Remove @{Attribute1LDAPDisplayName=value[]; Attribute2LDAPDisplayName=value[]}` When you use the Add, Replace, Clear, and Remove parameters together, the operations will be performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Replace Specifies values for an object property that will replace the current values. Use this parameter to replace one or more values of a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values, and more than one property by separating them using a semicolon. The format for this parameter is: `-Replace @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Remove, Replace, and Clear parameters together, the operations will be performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SamAccountName Specifies the Security Account Manager (SAM) account name of the user, group, computer, or service account. The maximum length of the description is 256 characters. To be compatible with older operating systems, create a SAM account name that is 20 characters or less. This parameter sets the SAMAccountName for an account object. The LDAP display name (ldapDisplayName) for this property is sAMAccountName. Note: If the string value provided is not terminated with a $ (dollar sign) character, the system adds one if necessary. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services (AD DS) instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services (AD LDS), AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADGroup A group object is received by the Identity parameter. A group object that was retrieved by using the Get-ADGroup cmdlet and then modified is received by the Instance parameter. ## Outputs ## None or Microsoft.ActiveDirectory.Management.ADGroup Returns the modified group object when the PassThru parameter is specified. By default, this cmdlet does not generate any output. ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. ## Related Links - Add-ADGroupMember - Add-ADPrincipalGroupMembership - Get-ADGroup - Get-ADGroupMember - Get-ADPrincipalGroupMembership - New-ADGroup - Remove-ADGroup - Remove-ADGroupMember - Remove-ADPrincipalGroupMembership ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "set-adgroup"
                                 ],
                        "last_updated":  "2025-07-28T17:14:49Z",
                        "title":  "Set-ADGroup (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adgroup",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "New-ADGroup"
                                    ],
                        "id":  "microsoft_docs_new-adgroup",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AuthType",
                                                            "Credential",
                                                            "Description",
                                                            "DisplayName",
                                                            "GroupCategory",
                                                            "HomePage",
                                                            "Instance",
                                                            "ManagedBy",
                                                            "OtherAttributes",
                                                            "Path",
                                                            "SamAccountName",
                                                            "Server"
                                                        ],
                                         "cmdlet_name":  "New-ADGroup"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# New-ADGroup (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## New-ADGroup Module: ActiveDirectory Module ## Creates an Active Directory group. ## Syntax ## Default (Default) ```powershellNew-ADGroup [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-GroupCategory \u003cADGroupCategory\u003e] [-GroupScope] \u003cADGroupScope\u003e [-HomePage \u003cString\u003e] [-Instance \u003cADGroup\u003e] [-ManagedBy \u003cADPrincipal\u003e] [-Name] \u003cString\u003e [-OtherAttributes \u003cHashtable\u003e] [-PassThru] [-Path \u003cString\u003e] [-SamAccountName \u003cString\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The New-ADGroup cmdlet creates an Active Directory group object. Many object properties are defined by setting cmdlet parameters. Properties that cannot be set by cmdlet parameters can be set using the OtherAttributes parameter. The Name and GroupScope parameters specify the name and scope of the group and are required to create a new group. You can define the new group as a security or distribution group by setting the GroupType parameter. The Path parameter specifies the container or organizational unit (OU) for the group. The following methods explain different ways to create an object by using this cmdlet. Method 1: Use the New-ADGroup cmdlet, specify the required parameters, and set any additional property values by using the cmdlet parameters. Method 2: Use a template to create the new object. To do this, create a new group object or retrieve a copy of an existing group object and set the Instance parameter to this object. The object provided to the Instance parameter is used as a template for the new object. You can override property values from the template by setting cmdlet parameters. For more information, see the Instance parameter description for this cmdlet. Method 3: Use the Import-Csv cmdlet with the New-ADGroup cmdlet to create multiple Active Directory group objects. To do this, use the Import-CSV cmdlet to create the custom objects from a comma-separated value (CSV) file that contains a list of object properties. Then pass these objects through the pipeline to the New-ADGroup cmdlet to create the group objects. ## Examples ## Example 1: Create a group and set its properties ```powershellPS C:\\\u003e New-ADGroup -Name \"RODC Admins\" -SamAccountName RODCAdmins -GroupCategory Security -GroupScope Global -DisplayName \"RODC Administrators\" -Path \"CN=Users,DC=Fabrikam,DC=Com\" -Description \"Members of this group are RODC Administrators\" ``` This command creates a group named RODC Admins in the container CN=Users,DC=Fabrikam,DC=Com and set the GroupCategory, DisplayName, GroupScope, and Description properties on the new object. ## Example 2: Create a group using existing property values ```powershellPS C:\\\u003e Get-ADGroup FabrikamBranch1 -Properties Description | New-ADGroup -Name \"Branch1Employees\" -SamAccountName \"Branch1Employees\" -GroupCategory Distribution -PassThru GroupScope : Universal Name : Branch1Employees GroupCategory : Distribution SamAccountName : Branch1Employees ObjectClass : group ObjectGUID : 8eebce44-5df7-4bed-a98b-b987a702103e SID : S-1-5-21-********-**********-**********-1117 DistinguishedName : CN=Branch1Employees,CN=Users,DC=Fabrikam,DC=com ``` This command creates a new group using the property values from a current group. ## Example 3: Create a group on an LDS instance ```powershellPS C:\\\u003e New-ADGroup -Server localhost:60000 -Path \"OU=AccountDeptOU,DC=AppNC\" -Name \"AccountLeads\" -GroupScope DomainLocal -GroupCategory Distribution ``` This command creates a group named AccountLeads on an AD LDS instance. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Description Specifies a description of the object. This parameter sets the value of the Description property for the object. The Lightweight Directory Access Protocol (LDAP) display name (ldapDisplayName) for this property is description. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -DisplayName Specifies the display name of the object. This parameter sets the DisplayName property of the object. The LDAP display name (ldapDisplayName) for this property is displayName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -GroupCategory Specifies the category of the group. The acceptable values for this parameter are: - Distribution or 0 - Security or 1 This parameter sets the GroupCategory property of the group. This parameter value combined with other group values sets the LDAP display name (ldapDisplayName) attribute named groupType. ## Parameter properties Type:ADGroupCategory Default value:None Accepted values:Distribution, Security Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -GroupScope Specifies the group scope of the group. The acceptable values for this parameter are: - DomainLocal or 0 - Global or 1 - Universal or 2 This parameter sets the GroupScope property of a group object to the specified value. The LDAP display name of this property is groupType. ## Parameter properties Type:ADGroupScope Default value:None Accepted values:DomainLocal, Global, Universal Supports wildcards:False DontShow:False ## Parameter sets (All) Position:2 Mandatory:True Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -HomePage Specifies the URL of the home page of the object. This parameter sets the homePage property of an Active Directory object. The LDAP display name (ldapDisplayName) for this property is wWWHomePage. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Instance Specifies an instance of a group object to use as a template for a new group object. You can use an instance of an existing group object as a template or you can construct a new group object by using the Windows PowerShell command line or by using a script. Method 1: Use an existing group object as a template for a new object. Use the Get-ADGroup cmdlet to retrieve a group object then pass this object to the Instance parameter of the New-ADGroup cmdlet to create a new group object. You can override property values of the new object by setting the appropriate parameters. Method 2: Create a new ADGroup object and set the property values by using the Windows PowerShell command line interface. Then pass this object to the Instance parameter of the New-ADGroup cmdlet to create the new group object. Note: Specified attributes are not validated, so attempting to set attributes that do not exist or cannot be set raises an error. ## Parameter properties Type:ADGroup Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ManagedBy Specifies the user or group that manages the object by providing one of the following property values. Note: The identifier in parentheses is the LDAP display name for the property. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - SAM account name (sAMAccountName) ## Parameter properties Type:ADPrincipal Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Name Specifies the name of the object. This parameter sets the Name property of the Active Directory object. The LDAP display name (ldapDisplayName) of this property is name. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:1 Mandatory:True Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OtherAttributes Specifies object attribute values for attributes that are not represented by cmdlet parameters. You can set one or more parameters at the same time with this parameter. If an attribute takes more than one value, you can assign multiple values. To identify an attribute, specify the LDAP display name (ldapDisplayName) defined for it in the Active Directory schema. To specify a single value for an attribute: `-OtherAttributes @{\u0027AttributeLDAPDisplayName\u0027=value}` To specify multiple values for an attribute `-OtherAttributes @{\u0027AttributeLDAPDisplayName\u0027=value1,value2,...}` You can specify values for more than one attribute by using semicolons to separate attributes. The following syntax shows how to set values for multiple attributes: `-OtherAttributes @{\u0027Attribute1LDAPDisplayName\u0027=value; \u0027Attribute2LDAPDisplayName\u0027=value1,value2;...}` ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Path Specifies the X.500 path of the Organizational Unit (OU) or container where the new object is created. In many cases, a default value is used for the Path parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services (AD DS) environments, a default value for Path is set in the following cases: - If the cmdlet is run from an Active Directory module for Windows PowerShell provider drive, the parameter is set to the current path of the provider drive. - If the cmdlet has a default path, this is used. For example: in New-ADUser, the Path parameter defaults to the Users container. - If none of the previous cases apply, the default value of Path is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Path is set in the following cases: - If the cmdlet is run from an Active Directory PowerShell provider drive, the parameter is set to the current path of the provider drive. - If the cmdlet has a default path, this is used. For example: in New-ADUser, the Path parameter defaults to the Users container. - If the target AD LDS instance has a default naming context, the default value of Path is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Path parameter does not take a default value. Note: The Active Directory Provider cmdlets, such as New-Item, Remove-Item, Remove-ItemProperty, Rename-Item, and Set-ItemProperty, also contain a Path property. However, for the provider cmdlets, the Path parameter identifies the path of the actual object and not the container as with the Active Directory cmdlets. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -SamAccountName Specifies the Security Account Manager (SAM) account name of the user, group, computer, or service account. The maximum length of the description is 256 characters. To be compatible with older operating systems, create a SAM account name that is 20 characters or less. This parameter sets the SAMAccountName for an account object. The LDAP display name (ldapDisplayName) for this property is sAMAccountName. Note: If the string value provided is not terminated with a $ (dollar sign) character, the system adds one if necessary. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance. Specify the Active Directory Domain Services instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADGroup A group object that is a template for the new group object is received by the Instance parameter. ## Outputs ## None or Microsoft.ActiveDirectory.Management.ADGroup Returns the new group object when the PassThru parameter is specified. By default, this cmdlet does not generate any output. ## Notes - This cmdlet does not work with an Active Directory Snapshot. - This cmdlet does not work with a read-only domain controller. ## Related Links - Get-ADGroup - Remove-ADGroup - Set-ADGroup ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "new-adgroup"
                                 ],
                        "last_updated":  "2025-07-28T17:14:50Z",
                        "title":  "New-ADGroup (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-adgroup",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Remove-ADGroup"
                                    ],
                        "id":  "microsoft_docs_remove-adgroup",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AuthType",
                                                            "Credential",
                                                            "Partition",
                                                            "Server"
                                                        ],
                                         "cmdlet_name":  "Remove-ADGroup"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Remove-ADGroup (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Remove-ADGroup Module: ActiveDirectory Module ## Removes an Active Directory group. ## Syntax ## Default (Default) ```powershellRemove-ADGroup [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Identity] \u003cADGroup\u003e [-Partition \u003cString\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The Remove-ADGroup cmdlet removes an Active Directory group object. You can use this cmdlet to remove security and distribution groups. The Identity parameter specifies the Active Directory group to remove. You can identify a group by its distinguished name, GUID, security identifier, Security Account Manager (SAM) account name, or canonical name. You can also set the Identity parameter to an object variable such as `$\u003clocalADGroupObject\u003e`, or you can pass an object through the pipeline to the Identity parameter. For example, you can use the Get-ADGroup cmdlet to retrieve a group object and then pass the object through the pipeline to the Remove-ADGroup cmdlet. If the ADGroup is being identified by its distinguished name, the Partition parameter is automatically determined. For Active Directory Lightweight Directory Services (AD LDS) environments, the Partition parameter must be specified except in the following two conditions: - The cmdlet is run from an Active Directory provider drive. - A default naming context or partition is defined for the AD LDS environment. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. ## Examples ## Example 1: Remove a group by name ```powershellPS C:\\\u003e Remove-ADGroup -Identity SanjaysReports Confirm Are you sure you want to perform this action? Performing operation \"Remove\" on Target \"CN=SanjayReports,DC=Fabrikam,DC=com\". [Y] Yes [A] Yes to All [N] No [L] No to All [S] Suspend [?] Help (default is \"Y\"): ``` This command removes the group that has SAM account name SanjaysReports. ## Example 2: Get filtered groups and remove them ```powershellPS C:\\\u003e Get-ADGroup -Filter \u0027Name -like \"Sanjay*\"\u0027 | Remove-ADGroup Confirm Are you sure you want to perform this action? Performing operation \"Remove\" on Target \"CN=SanjaysReports,DC=Fabrikam,DC=com\". [Y] Yes [A] Yes to All [N] No [L] No to All [S] Suspend [?] Help (default is \"Y\"): ``` This command gets all groups whose name starts with Sanjay and then removes them. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory group object by providing one of the following values. The identifier in parentheses is the Lightweight Directory Access Protocol (LDAP) display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A Security Account Manager account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADGroup Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value will be used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In AD DS environments, a default value for Partition will be set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition will be set to the default partition or naming context of the target domain. In AD LDS environments, a default value for Partition will be set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition will be set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter will not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance. Specify the Active Directory Domain Services instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADGroup A group object is received by the Identity parameter. ## Outputs ## None ## Notes - This cmdlet does not work with an Active Directory Snapshot. - This cmdlet does not work with a read-only domain controller. - By default, this cmdlet has the Confirm parameter set, which prompts you to confirm before a removal of the specified object type can occur. To bypass prompting for confirmation before removal, you can specify `-Confirm:$False` when using this cmdlet. ## Related Links - Add-ADGroupMember - Get-ADGroup - Get-ADGroupMember - New-ADGroup - Remove-ADGroupMember - Set-ADGroup ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "remove-adgroup"
                                 ],
                        "last_updated":  "2025-07-28T17:14:51Z",
                        "title":  "Remove-ADGroup (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adgroupmember",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Add-ADGroupMember"
                                    ],
                        "id":  "microsoft_docs_add-adgroupmember",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AuthType",
                                                            "Credential",
                                                            "MemberTimeToLive",
                                                            "Partition",
                                                            "Server"
                                                        ],
                                         "cmdlet_name":  "Add-ADGroupMember"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Add-ADGroupMember (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Add-ADGroupMember Module: ActiveDirectory Module ## Adds one or more members to an Active Directory group. ## Syntax ## Default (Default) ```powershellAdd-ADGroupMember [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Identity] \u003cADGroup\u003e [-Members] \u003cADPrincipal[]\u003e [-MemberTimeToLive \u003cTimeSpan\u003e] [-Partition \u003cString\u003e] [-PassThru] [-Server \u003cString\u003e] [-DisablePermissiveModify] [\u003cCommonParameters\u003e] ``` ## Description The `Add-ADGroupMember` cmdlet adds one or more users, groups, service accounts, or computers as new members of an Active Directory group. The Identity parameter specifies the Active Directory group that receives the new members. You can identify a group by its distinguished name, GUID, security identifier, or Security Account Manager (SAM) account name. You can also specify group object variable, such as `$\u003clocalGroupObject\u003e`, or pass a group object through the pipeline to the Identity parameter. For example, you can use the `Get-ADGroup` cmdlet to get a group object and then pass the object through the pipeline to the `Add-ADGroupMember` cmdlet. The Members parameter specifies the new members to add to a group. You can identify a new member by its distinguished name, GUID, security identifier, or SAM account name. You can also specify user, computer, and group object variables, such as `$\u003clocalUserObject\u003e`. If you are specifying more than one new member, use a comma-separated list. You cannot pass user, computer, or group objects through the pipeline to this cmdlet. To add user, computer, or group objects to a group by using the pipeline, use the `Add-ADPrincipalGroupMembership` cmdlet. For Active Directory Lightweight Directory Services (AD LDS) environments, the Partition parameter must be specified except in the following two conditions: - The cmdlet is run from an Active Directory provider drive. - A default naming context or partition is defined for the AD LDS environment. - To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. ## Examples ## EXAMPLE 1 ```powershellAdd-ADGroupMember -Identity SvcAccPSOGroup -Members SQL01, SQL02 ``` This command adds the user accounts with the SAM account names `SQL01` and `SQL02` to the group `SvcAccPSOGroup`. ## EXAMPLE 2 ```powershell$params = @{ Server = \u0027localhost:60000\u0027 SearchBase = \u0027OU=AccountDeptOU,DC=AppNC\u0027 Filter = \"name -like \u0027AccountLeads\u0027\" } Get-ADGroup @params | Add-ADGroupMember -Members \u0027CN=PattiFuller,OU=AccountDeptOU,DC=AppNC\u0027 ``` This command gets a group from the organizational unit `OU=AccountDeptOU,DC=AppNC` in the AD LDS instance `localhost:60000` that has the name `AccountLeads`, and then pipes it to `Add-ADGroupMember`, which then adds the user account with the distinguished name `CN=PattiFuller,OU=AccountDeptOU,DC=AppNC` to it. ## EXAMPLE 3 ```powershell$userParams = @{ Identity = \u0027CN=Chew David,OU=UserAccounts,DC=NORTHAMERICA,DC=FABRIKAM,DC=COM\u0027 Server = \u0027northamerica.fabrikam.com\u0027 } $User = Get-ADUser @userParams $groupParams = @{ Identity = \u0027CN=AccountLeads,OU=UserAccounts,DC=EUROPE,DC=FABRIKAM,DC=COM\u0027 Server = \u0027europe.fabrikam.com\u0027 } $Group = Get-ADGroup @groupParams Add-ADGroupMember -Identity $Group -Members $User -Server \"europe.fabrikam.com\" ``` This command adds the user `CN=Chew David,OU=UserAccounts` from the North America domain to the group `CN=AccountLeads,OU=UserAccounts` in the Europe domain. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - `Negotiate` or `0` - `Basic` or `1` The default authentication method is `Negotiate`. A Secure Sockets Layer (SSL) connection is required for the `Basic` authentication method. ## Parameter properties Type:Microsoft.ActiveDirectory.Management.ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as `User1` or `Domain01\\User01` or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the `Get-Credential` cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -DisablePermissiveModify Group membership updates use permissive modify by default. This suppresses an error when adding a member that is already member of the group. When this parameter is used, an error \"The specified account name is already a member of the group\" is returned. This parameter is available in Windows Server 2019 with the September 2020 Updates. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory group object by providing one of the following values. The identifier in parentheses is the Lightweight Directory Access Protocol (LDAP) display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - Security Accounts Manager account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:Microsoft.ActiveDirectory.Management.ADGroup Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Members Specifies an array of user, group, and computer objects in a comma-separated list to add to a group. To identify each object, use one of the following property values. The identifier in parentheses is the LDAP display name. The acceptable values for this parameter are: - Distinguished name - GUID (objectGUID) - Security identifier (objectSid) - SAM account name (sAMAccountName) You can also provide objects to this parameter directly. The following examples show how to specify this parameter. This example specifies a user and group to add by specifying the distinguished name and the SAM account name properties. `-Members \"CN=SaraDavis,CN=employees,CN=Users,DC=contoso,DC=com\", \"saradavisreports\"` This example specifies a user and a group object that are defined in the current Windows PowerShell session as input for the parameter. `-Members $userObject, $GroupObject` The objects specified for this parameter are processed as Microsoft.ActiveDirectory.Management.ADPrincipal objects. Derived types, such as the following are also received by this parameter. - Microsoft.ActiveDirectory.Management.ADUser - Microsoft.ActiveDirectory.Management.ADComputer - Microsoft.ActiveDirectory.Management.ADServiceAccount - Microsoft.ActiveDirectory.Management.ADGroup You cannot pass objects through the pipeline to this parameter. ## Parameter properties Type: Microsoft.ActiveDirectory.Management.ADPrincipal[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:1 Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -MemberTimeToLive Specifies a Time to Live (TTL) for the new group members. ## Parameter properties Type:TimeSpan Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter will not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you\u0027re working. By default, this cmdlet doesn\u0027t generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory snapshot instance. Specify the Active Directory Domain Services instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet isn\u0027t run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## Microsoft.ActiveDirectory.Management.ADGroup A group object is received by the Identity parameter. ## Outputs ## None or Microsoft.ActiveDirectory.Management.ADGroup Returns the modified group object when the PassThru parameter is specified. By default, this cmdlet does not generate any output. ## Notes - This cmdlet does not work with a read-only domain controller. - This cmdlet does not work with an Active Directory snapshot. - This cmdlet will allow you to add a group as a member of itself which could lead to unstable behavior. ## Related Links - Add-ADPrincipalGroupMembership - Get-ADGroup - Get-ADGroupMember - Get-ADPrincipalGroupMembership - Remove-ADGroupMember - Remove-ADPrincipalGroupMembership ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "add-adgroupmember"
                                 ],
                        "last_updated":  "2025-07-28T17:14:51Z",
                        "title":  "Add-ADGroupMember (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-adgroupmember",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Remove-ADGroupMember"
                                    ],
                        "id":  "microsoft_docs_remove-adgroupmember",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AuthType",
                                                            "Credential",
                                                            "Partition",
                                                            "Server"
                                                        ],
                                         "cmdlet_name":  "Remove-ADGroupMember"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Remove-ADGroupMember (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Remove-ADGroupMember Module: ActiveDirectory Module ## Removes one or more members from an Active Directory group. ## Syntax ## Default (Default) ```powershellRemove-ADGroupMember [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Identity] \u003cADGroup\u003e [-Members] \u003cADPrincipal[]\u003e [-Partition \u003cString\u003e] [-PassThru] [-Server \u003cString\u003e] [-DisablePermissiveModify] [\u003cCommonParameters\u003e] ``` ## Description The Remove-ADGroupMember cmdlet removes one or more users, groups, service accounts, or computers from an Active Directory group. The Identity parameter specifies the Active Directory group that contains the members to remove. You can identify a group by its distinguished name, GUID, security identifier, or Security Account Manager (SAM) account name. You can also specify a group object variable, such as `$\u003clocalGroupObject\u003e`, or pass a group object through the pipeline to the Identity parameter. For example, you can use the Get-ADGroup cmdlet to retrieve a group object and then pass the object through the pipeline to the Remove-ADGroupMember cmdlet. The Members parameter specifies the users, computers and groups to remove from the group specified by the Identity parameter. You can identify a user, computer or group by its distinguished name, GUID, security identifier, or SAM account name. You can also specify user, computer, and group object variables, such as `$\u003clocalUserObject\u003e`. If you are specifying more than one new member, use a comma-separated list. You cannot pass user, computer, or group objects through the pipeline to this cmdlet. To remove user, computer, or group objects from a group by using the pipeline, use the Remove-ADPrincipalGroupMembership cmdlet. For Active Directory Lightweight Directory Services (AD LDS) environments, the Partition parameter must be specified except in the following two conditions: - The cmdlet is run from an Active Directory provider drive. - A default naming context or partition is defined for the AD LDS environment. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. ## Examples ## Example 1: Remove a member from a group ```powershellPS C:\\\u003e Remove-ADGroupMember -Identity DocumentReaders -Members DavidChew Confirm Are you sure you want to perform this action? Performing operation \"Set\" on Target \"CN=DocumentReaders,CN=Users,DC=Fabrikam,DC=com\". [Y] Yes [A] Yes to All [N] No [L] No to All [S] Suspend [?] Help (default is \"Y\"): ``` This command removes the user with the SAM account name DavidChew from the group DocumentReaders. ## Example 2: Remove multiple members from a group ```powershellPS C:\\\u003e Remove-ADGroupMember -Identity \"DocumentReaders\" -Members administrator,DavidChew ``` This command removes the users with SAM account name administrator and DavidChew from the group DocumentReaders. ## Example 3: Remove a distinguished user from a group ```powershellPS C:\\\u003e Get-ADGroup -Server localhost:60000 -Identity CN=AccessControl,DC=AppNC | Remove-ADGroupMember -Members CN=GlenJohn,DC=AppNC Confirm Are you sure you want to perform this action? Performing operation \"Set\" on Target \"CN=AccessControl,DC=AppNC\". [Y] Yes [A] Yes to All [N] No [L] No to All [S] Suspend [?] Help (default is \"Y\"): ``` This command removes the user with the distinguished name CN=GlenJohn,DC=AppNC from the group AccessControl on an AD LDS instance using the pipeline. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:True Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -DisablePermissiveModify Group membership updates use permissive modify by default. This suppresses an error when removing a member that is not member of the group. When this parameter is used, an error \"The specified account name is not a member of the group\" is returned. This parameter is available in Windows Server 2019 with the September 2020 Updates. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -DisablePermissiveModify Group membership updates use permissive modify by default. This suppresses an error when removing a member that is not member of the group. When this parameter is used, an error \"The specified account name is not a member of the group\" is returned. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory group object by providing one of the following values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A Security Account Manager account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADGroup Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Members Specifies an array of user, group, and computer objects in a comma-separated list to remove from a group. To identify each object, use one of the following property values. Note: The identifier in parentheses is the LDAP display name. The acceptable values for this parameter are: - Distinguished name - GUID (objectGUID) - Security identifier (objectSid) - SAM account name (sAMAccountName) You can also provide objects to this parameter directly. The following examples show how to specify this parameter. This example specifies a user and group to remove by specifying the distinguished name and the SAM account name properties. `-Members \"CN=SaraDavis,CN=employees,CN=Users,DC=contoso,DC=com\", \"saradavisreports\"` This example specifies a user and a group object that are defined in the current Windows PowerShell session as input for the parameter. `-Members $userObject, $GroupObject` The objects specified for this parameter are processed as Microsoft.ActiveDirectory.Management.ADPrincipal objects. Derived types, such as the following, are also received by this parameter. - Microsoft.ActiveDirectory.Management.ADUser - Microsoft.ActiveDirectory.Management.ADComputer - Microsoft.ActiveDirectory.Management.ADServiceAccount - Microsoft.ActiveDirectory.Management.ADGroup You cannot pass objects through the pipeline to this parameter. ## Parameter properties Type: ADPrincipal[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:1 Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services (AD DS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter does not take a default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADGroup A group object is received by the Identity parameter. ## Outputs ## None or Microsoft.ActiveDirectory.Management.ADGroup Returns the modified group object when the PassThru parameter is specified. By default, this cmdlet does not generate any output. ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. - By default, this cmdlet has the Confirm parameter set, which prompts you to confirm before a removal of the specified object type can occur. To bypass prompting for confirmation before removal, you can specify `-Confirm:$False` when using this cmdlet. ## Related Links - Add-ADGroupMember - Add-ADPrincipalGroupMembership - Get-ADGroup - Get-ADGroupMember - Get-ADPrincipalGroupMembership - Remove-ADPrincipalGroupMembership ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "remove-adgroupmember"
                                 ],
                        "last_updated":  "2025-07-28T17:14:52Z",
                        "title":  "Remove-ADGroupMember (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adcomputer",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Get-ADComputer"
                                    ],
                        "id":  "microsoft_docs_get-adcomputer",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "Filter",
                                                            "AuthType",
                                                            "Credential",
                                                            "Properties",
                                                            "ResultPageSize",
                                                            "ResultSetSize",
                                                            "SearchBase",
                                                            "SearchScope",
                                                            "Server",
                                                            "Partition",
                                                            "LDAPFilter"
                                                        ],
                                         "cmdlet_name":  "Get-ADComputer"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Get-ADComputer (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Get-ADComputer Module: ActiveDirectory Module ## Gets one or more Active Directory computers. ## Syntax ## Filter (Default) ```powershellGet-ADComputer -Filter \u003cString\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Properties \u003cString[]\u003e] [-ResultPageSize \u003cInt32\u003e] [-ResultSetSize \u003cInt32\u003e] [-SearchBase \u003cString\u003e] [-SearchScope \u003cADSearchScope\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Identity ```powershellGet-ADComputer [-Identity] \u003cADComputer\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Partition \u003cString\u003e] [-Properties \u003cString[]\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## LdapFilter ```powershellGet-ADComputer -LDAPFilter \u003cString\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Properties \u003cString[]\u003e] [-ResultPageSize \u003cInt32\u003e] [-ResultSetSize \u003cInt32\u003e] [-SearchBase \u003cString\u003e] [-SearchScope \u003cADSearchScope\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The `Get-ADComputer` cmdlet gets a computer or performs a search to retrieve multiple computers. The Identity parameter specifies the Active Directory computer to retrieve. You can identify a computer by its distinguished name, GUID, security identifier (SID) or Security Accounts Manager (SAM) account name. You can also set the parameter to a computer object variable, such as `$\u003clocalComputerobject\u003e` or pass a computer object through the pipeline to the Identity parameter. To search for and retrieve more than one computer, use the Filter or LDAPFilter parameters. The Filter parameter uses the PowerShell Expression Language to write query strings for Active Directory. PowerShell Expression Language syntax provides rich type conversion support for value types received by the Filter parameter. For more information about the Filter parameter syntax, type `Get-Help` about_ActiveDirectory_Filter. If you have existing Lightweight Directory Access Protocol (LDAP) query strings, you can use the LDAPFilter parameter. This cmdlet retrieves a default set of computer object properties. To retrieve additional properties use the Properties parameter. For more information about the how to determine the properties for computer objects, see the Properties parameter description. ## Examples ## Example 1: Get specific computer that shows all properties ```powershellGet-ADComputer -Identity \"User01-SRV1\" -Properties * ``` ```powershell AccountExpirationDate : accountExpires : 9223372036854775807 AccountLockoutTime : AccountNotDelegated : False AllowReversiblePasswordEncryption : False BadLogonCount : CannotChangePassword : False CanonicalName : fabrikam.com/Computers/User01-srv1 Certificates : {} CN : User01-srv1 codePage : 0 countryCode : 0 Created : 3/16/2009 4:15:00 PM createTimeStamp : 3/16/2009 4:15:00 PM Deleted : Description : DisplayName : DistinguishedName : CN= User01-srv1,CN=Computers,DC=fabrikam,DC=com DNSHostName : User01-srv1 DoesNotRequirePreAuth : False dSCorePropagationData : {3/16/2009 4:21:51 PM, 12/31/1600 4:00:01 PM} Enabled : True HomedirRequired : False HomePage : instanceType : 0 IPv4Address : IPv6Address : isCriticalSystemObject : False isDeleted : LastBadPasswordAttempt : LastKnownParent : LastLogonDate : localPolicyFlags : 0 Location : NA/HQ/Building A LockedOut : False ManagedBy : CN=SQL Administrator 01,OU=UserAccounts,OU=Managed,DC=fabrikam,DC=com MemberOf : {} MNSLogonAccount : False Modified : 3/16/2009 4:23:01 PM modifyTimeStamp : 3/16/2009 4:23:01 PM msDS-User-Account-Control-Computed : 0 Name : User01-srv1 nTSecurityDescriptor : System.DirectoryServices.ActiveDirectorySecurity ObjectCategory : CN=Computer,CN=Schema,CN=Configuration,DC=fabrikam,DC=com ObjectClass : computer ObjectGUID : 828306a3-8ccd-410e-9537-e6616662c0b0 objectSid : S-1-5-21-********-**********-**********-1130 OperatingSystem : OperatingSystemHotfix : OperatingSystemServicePack : OperatingSystemVersion : PasswordExpired : False PasswordLastSet : PasswordNeverExpires : False PasswordNotRequired : False PrimaryGroup : CN=Domain Computers,CN=Users,DC=fabrikam,DC=com primaryGroupID : 515 ProtectedFromAccidentalDeletion : False pwdLastSet : 0 SamAccountName : User01-srv1$ sAMAccountType : ********* sDRightsEffective : 0 ServiceAccount : {} servicePrincipalName : {MSOLAPSVC.3/User01-SRV1.fabrikam.com:analyze, MSSQLSVC/User01-SRV1.fabrikam.com:1456} ServicePrincipalNames : {MSOLAPSVC.3/User01-SRV1.fabrikam.com:analyze, MSSQLSVC/User01-SRV1.fabrikam.com:1456} SID : S-1-5-21-********-**********-**********-1130 SIDHistory : {} TrustedForDelegation : False TrustedToAuthForDelegation : False UseDESKeyOnly : False userAccountControl : 4096 userCertificate : {} UserPrincipalName : uSNChanged : 36024 uSNCreated : 35966 whenChanged : 3/16/2009 4:23:01 PM whenCreated : 3/16/2009 4:15:00 PM ``` This command gets a specific computer showing all the properties. ## Example 2: Get all computers with a name starting with a particular string ```powershellGet-ADComputer -Filter \u0027Name -like \"User01*\"\u0027 -Properties IPv4Address | Format-Table Name, DNSHostName, IPv4Address -AutoSize ``` ```powershellname dnshostname ipv4address ---- ----------- ----------- User01-SRV1 User01-SRV1.User01.com ************* User01-SRV2 User01-SRV2.User01.com ************ ``` This command gets all the computers with a name starting with a particular string and shows the name, DNS hostname, and IPv4 address. ## Example 3: Gets all computers that have changed their password in specific time frame ```powershell$Date = [DateTime]::Today.AddDays(-90) Get-ADComputer -Filter \u0027PasswordLastSet -ge $Date\u0027 -Properties PasswordLastSet | Format-Table Name, PasswordLastSet ``` ```powershellName PasswordLastSet ---- --------------- USER01-SRV4 3/12/2009 6:40:37 PM USER01-SRV5 3/12/2009 7:05:45 PM ``` This command gets all the computers that have changed their password in the last 90 days. ## Example 4: Get computer accounts in a specific location using an LDAPFilter ```powershellGet-ADComputer -LDAPFilter \"(name=*laptop*)\" -SearchBase \"CN=Computers,DC= User01,DC=com\" ``` ```powershellname ---- pattiful-laptop davidche-laptop ``` This command gets the computer accounts in the location `CN=Computers,DC=User01,DC=com` that are listed as laptops by using an LDAPFilter. ## Example 5: Get all computer accounts using a filter ```powershellGet-ADComputer -Filter * ``` This command gets all computer accounts. ## Example 6: Get all computers with a name starting with Computer01 or Computer02 ```powershellGet-ADComputer -Filter \u0027Name -like \"Computer01*\" -or Name -like \"Computer02*\"\u0027 -Properties IPv4Address | Format-Table Name, DNSHostName, IPv4Address -AutoSize ``` ```powershellname dnshostname ipv4address ---- ----------- ----------- Computer01-SRV1 Computer01-SRV1.Computer01.com ************* Computer02-SRV2 Computer02-SRV2.Computer02.com ************ ``` ## Example 7: Get all computers with a name starting with a string AND password last set before 30 days ```powershell$Date = [DateTime]::Today.AddDays(-30) Get-ADComputer -Filter \u0027Name -like \"Computer01*\" -and PasswordLastSet -ge $Date\u0027 -Properties IPv4Address | Format-Table Name, DNSHostName, IPv4Address -AutoSize ``` ```powershellname dnshostname ipv4address ---- ----------- ----------- Computer01-SRV1 Computer01-SRV1.Computer01.com ************* ``` This command shows the name, DNS hostname, and IPv4 address. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as `User1` or `Domain01\\User01` or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the `Get-Credential` cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, the cmdlet returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Filter Specifies a query string that retrieves Active Directory objects. This string uses the Windows PowerShell Expression Language syntax. The Windows PowerShell Expression Language syntax provides rich type-conversion support for value types received by the Filter parameter. The syntax uses an in-order representation, which means that the operator is placed between the operand and the value. For more information about the Filter parameter, type `Get-Help` about_ActiveDirectory_Filter. Syntax: The following syntax uses Backus-Naur form to show how to use the Windows PowerShell Expression Language for this parameter. \u003cfilter\u003e ::= \"{\" \u003cFilterComponentList\u003e \"}\" \u003cFilterComponentList\u003e ::= \u003cFilterComponent\u003e | \u003cFilterComponent\u003e \u003cJoinOperator\u003e \u003cFilterComponent\u003e | \u003cNotOperator\u003e \u003cFilterComponent\u003e \u003cFilterComponent\u003e ::= \u003cattr\u003e \u003cFilterOperator\u003e \u003cvalue\u003e | \"(\" \u003cFilterComponent\u003e \")\" \u003cFilterOperator\u003e ::= \"-eq\" | \"-le\" | \"-ge\" | \"-ne\" | \"-lt\" | \"-gt\"| \"-approx\" | \"-bor\" | \"-band\" | \"-recursivematch\" | \"-like\" | \"-notlike\" \u003cJoinOperator\u003e ::= \"-and\" | \"-or\" \u003cNotOperator\u003e ::= \"-not\" \u003cattr\u003e ::= \u003cPropertyName\u003e | \u003cLDAPDisplayName of the attribute\u003e \u003cvalue\u003e::= \u003ccompare this value with an \u003cattr\u003e by using the specified \u003cFilterOperator\u003e\u003e For a list of supported types for \u003cvalue\u003e, type `Get-Help about_ActiveDirectory_ObjectModel`. Note Wildcards other than `*`, such as `?`, are not supported by the Filter syntax. Note To query using LDAP query strings, use the LDAPFilter parameter. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory computer object by providing one of the following property values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (`objectGUID`) - A security identifier (`objectSid`) - A Security Accounts Manager account name (`sAMAccountName`) The cmdlet searches the default naming context or partition to find the object. If the identifier given is a distinguished name, the partition to search is computed from that distinguished name. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to a computer object instance. ## Parameter properties Type:ADComputer Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -LDAPFilter Specifies an LDAP query string that is used to filter Active Directory objects. You can use this parameter to run your existing LDAP queries. The Filter parameter syntax supports the same functionality as the LDAP syntax. For more information, see the Filter parameter description or type `Get-Help` about_ActiveDirectory_Filter. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets LdapFilter Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter will not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Properties Specifies the properties of the output object to retrieve from the server. Use this parameter to retrieve properties that are not included in the default set. Specify properties for this parameter as a comma-separated list of names. To display all of the attributes that are set on the object, specify * (asterisk). To specify an individual extended property, use the name of the property. For properties that are not default or extended properties, you must specify the LDAP display name of the attribute. To retrieve properties and display them for an object, you can use the `Get-*` cmdlet associated with the object and pass the output to the `Get-Member` cmdlet. ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False Aliases:Property ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ResultPageSize Specifies the number of objects to include in one page for an Active Directory Domain Services query. The default is 256 objects per page. ## Parameter properties Type:Int32 Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ResultSetSize Specifies the maximum number of objects to return for an Active Directory Domain Services query. If you want to receive all of the objects, set this parameter to $Null (null value). You can use Ctrl+C to stop the query and return of objects. The default is $Null. ## Parameter properties Type:Int32 Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SearchBase Specifies an Active Directory path to search under. When you run a cmdlet from an Active Directory provider drive, the default value of this parameter is the current path of the drive. When you run a cmdlet outside of an Active Directory provider drive against an Active Directory Domain Services target, the default value of this parameter is the default naming context of the target domain. When you run a cmdlet outside of an Active Directory provider drive against an AD LDS target, the default value is the default naming context of the target AD LDS instance if one has been specified by setting the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. If no default naming context has been specified for the target AD LDS instance, then this parameter has no default value. When the value of the SearchBase parameter is set to an empty string and you are connected to a global catalog port, all partitions are searched. If the value of the SearchBase parameter is set to an empty string and you are not connected to a global catalog port, an error is thrown. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SearchScope Specifies the scope of an Active Directory search. The acceptable values for this parameter are: - Base or 0 - OneLevel or 1 - Subtree or 2 A Base query searches only the current path or object. A OneLevel query searches the immediate children of that path or object. A Subtree query searches the current path or object and all children of that path or object. ## Parameter properties Type:ADSearchScope Default value:None Accepted values:Base, OneLevel, Subtree Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory snapshot instance. Specify the Active Directory Domain Services instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADComputer A computer object is received by the Identity parameter. ## Outputs ## Microsoft.ActiveDirectory.Management.ADComputer Returns one or more computer objects. This Get-ADComputer cmdlet returns a default set of ADComputer property values. To retrieve additional ADComputer properties, use the Properties parameter of this cmdlet. To view the properties for an ADComputer object, see the following examples. To run these examples, replace `\u003ccomputer\u003e` with a computer identifier such as the SAM account name of your local computer. To get a list of the default set of properties of an ADComputer object, use the following command: `Get-ADComputer`\u003ccomputer\u003e`| Get-Member` To get a list of all the properties of an ADComputer object, use the following command: `Get-ADComputer`\u003ccomputer\u003e`-Properties ALL | Get-Member` ## Notes - This cmdlet doesn\u0027t work with AD LDS with its default schema. By default the AD LDS schema doesn\u0027t have a computer class, but if the schema is extended to include it, this cmdlet will work with LDS. ## Related Links - Add-ADComputerServiceAccount - Get-ADComputerServiceAccount - New-ADComputer - Remove-ADComputer - Remove-ADComputerServiceAccount - Set-ADComputer - AD DS Administration Cmdlets in Windows PowerShell ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "get-adcomputer"
                                 ],
                        "last_updated":  "2025-07-28T17:14:53Z",
                        "title":  "Get-ADComputer (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/set-adcomputer",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Set-ADComputer"
                                    ],
                        "id":  "microsoft_docs_set-adcomputer",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AccountExpirationDate",
                                                            "AccountNotDelegated",
                                                            "Add",
                                                            "AllowReversiblePasswordEncryption",
                                                            "AuthenticationPolicy",
                                                            "AuthenticationPolicySilo",
                                                            "AuthType",
                                                            "CannotChangePassword",
                                                            "Certificates",
                                                            "ChangePasswordAtLogon",
                                                            "Clear",
                                                            "CompoundIdentitySupported",
                                                            "Credential",
                                                            "Description",
                                                            "DisplayName",
                                                            "DNSHostName",
                                                            "Enabled",
                                                            "HomePage",
                                                            "KerberosEncryptionType",
                                                            "Location",
                                                            "ManagedBy",
                                                            "OperatingSystem",
                                                            "OperatingSystemHotfix",
                                                            "OperatingSystemServicePack",
                                                            "OperatingSystemVersion",
                                                            "Partition",
                                                            "PasswordNeverExpires",
                                                            "PasswordNotRequired",
                                                            "PrincipalsAllowedToDelegateToAccount",
                                                            "Remove",
                                                            "Replace",
                                                            "SAMAccountName",
                                                            "Server",
                                                            "ServicePrincipalNames",
                                                            "TrustedForDelegation",
                                                            "UserPrincipalName",
                                                            "Instance"
                                                        ],
                                         "cmdlet_name":  "Set-ADComputer"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Set-ADComputer (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Set-ADComputer Module: ActiveDirectory Module ## Modifies an Active Directory computer object. ## Syntax ## Identity ```powershellSet-ADComputer [-Identity] \u003cADComputer\u003e [-WhatIf] [-Confirm] [-AccountExpirationDate \u003cDateTime\u003e] [-AccountNotDelegated \u003cBoolean\u003e] [-Add \u003cHashtable\u003e] [-AllowReversiblePasswordEncryption \u003cBoolean\u003e] [-AuthenticationPolicy \u003cADAuthenticationPolicy\u003e] [-AuthenticationPolicySilo \u003cADAuthenticationPolicySilo\u003e] [-AuthType \u003cADAuthType\u003e] [-CannotChangePassword \u003cBoolean\u003e] [-Certificates \u003cHashtable\u003e] [-ChangePasswordAtLogon \u003cBoolean\u003e] [-Clear \u003cString[]\u003e] [-CompoundIdentitySupported \u003cBoolean\u003e] [-Credential \u003cPSCredential\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-DNSHostName \u003cString\u003e] [-Enabled \u003cBoolean\u003e] [-HomePage \u003cString\u003e] [-KerberosEncryptionType \u003cADKerberosEncryptionType\u003e] [-Location \u003cString\u003e] [-ManagedBy \u003cADPrincipal\u003e] [-OperatingSystem \u003cString\u003e] [-OperatingSystemHotfix \u003cString\u003e] [-OperatingSystemServicePack \u003cString\u003e] [-OperatingSystemVersion \u003cString\u003e] [-Partition \u003cString\u003e] [-PassThru] [-PasswordNeverExpires \u003cBoolean\u003e] [-PasswordNotRequired \u003cBoolean\u003e] [-PrincipalsAllowedToDelegateToAccount \u003cADPrincipal[]\u003e] [-Remove \u003cHashtable\u003e] [-Replace \u003cHashtable\u003e] [-SAMAccountName \u003cString\u003e] [-Server \u003cString\u003e] [-ServicePrincipalNames \u003cHashtable\u003e] [-TrustedForDelegation \u003cBoolean\u003e] [-UserPrincipalName \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Instance ```powershellSet-ADComputer -Instance \u003cADComputer\u003e [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-PassThru] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The Set-ADComputer cmdlet modifies the properties of an Active Directory computer object. You can modify commonly used property values by using the cmdlet parameters. Property values that are not associated with cmdlet parameters can be modified by using the Add, Replace, Clear, and Remove parameters. The Identity parameter specifies the Active Directory computer to modify. You can identify a computer by its distinguished name, GUID, security identifier (SID) or Security Accounts Manager (SAM) account name. You can also set the Identity parameter to an object variable such as `$\u003clocalComputerobject\u003e`, or you can pass an object through the pipeline to the Identity parameter. For example, you can use the Get-ADComputer cmdlet to retrieve a computer object and then pass the object through the pipeline to Set-ADComputer. The Instance parameter provides a way to update a computer by applying the changes made to a copy of the computer object. When you set the Instance parameter to a copy of an Active Directory computer object that has been modified, the Set-ADComputer cmdlet makes the same changes to the original computer object. To get a copy of the object to modify, use the Get-ADComputer object. When you specify the Instance parameter you should not pass the Identity parameter. For more information about the Instance parameter, see the Instance parameter description. ## Examples ## Example 1: Modify the SPN value for a specified Active Directory computer ```powershellPS C:\\\u003e Set-ADComputer -Identity \"USER01-SRV1\" -ServicePrincipalName @{Replace=\"MSSQLSVC/USER01-SRV1.USER01.COM:1456\",\"MSOLAPSVC.3/USER01-SRV1.USER01.COM:analyze\"} ``` This command modifies the service principal name (SPN) value for the computer specified by the Identity parameter. ## Example 2: Set the location for a specified Active Directory computer ```powershellPS C:\\\u003e Set-ADComputer -Identity \"USER02-SRV1\" -Location \"NA/HQ/Building A\" ``` This command sets the location for the computer specified by the Identity parameter. ## Example 3: Set an attribute for a specified Active Directory computer using a SAM account name ```powershellPS C:\\\u003e Set-ADComputer -Identity \"USER03-SRV1\" -ManagedBy \"CN=SQL Administrator 01,OU=UserAccounts,OU=Managed,DC=USER03,DC=COM\" ``` This command sets the ManagedBy attribute value for the computer specified by the Identity parameter using the SAM account name of the user. ## Example 4: Set multiple attributes of an Active Directory computer ```powershellPS C:\\\u003e $Comp = Get-ADComputer -Identity \"USER04-SRV1\" PS C:\\\u003e $Comp.Location = \"NA/HQ/Building A\" PS C:\\\u003e $Comp.ManagedBy = \"CN=SQL Administrator 01,OU=UserAccounts,OU=Managed,DC=USER04,DC=COM\" PS C:\\\u003e Set-ADComputer -Instance $Comp ``` This command sets the Location and ManagedBy attributes of a computer. ## Parameters ## -AccountExpirationDate Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The Active Directory Lightweight Directory Services (LDAP) display name (ldapDisplayName) for this property is accountExpires. Use the DateTime syntax when you specify this parameter. Time is assumed to be local time unless otherwise specified. When a time value is not specified, the time is assumed to 12:00:00 AM local time. When a date is not specified, the date is assumed to be the current date. ## Parameter properties Type:DateTime Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AccountNotDelegated Specifies whether the security context of the user is delegated to a service. When this parameter is set to true, the security context of the account is not delegated to a service even when the service account is set as trusted for Kerberos delegation. This parameter sets the AccountNotDelegated property for an Active Directory account. This parameter also sets the ADS_UF_NOT_DELEGATED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Add Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon.. The format for this parameter is: `-Add @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Replace, Clear, and Remove parameters together, the operations are performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AllowReversiblePasswordEncryption Specifies whether reversible password encryption is allowed for the account. This parameter sets the AllowReversiblePasswordEncryption property of the account. This parameter also sets the ADS_UF_ENCRYPTED_TEXT_PASSWORD_ALLOWED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthenticationPolicy Specifies an Active Directory Domain Services authentication policy object. Specify the authentication policy object in one of the following formats: - distinguished name - GUID - Name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicy Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthenticationPolicySilo Specifies an Active Directory Domain Services authentication policy silo object. Specify the authentication policy silo object in one of the following formats: - Distinguished Name - GUID - Name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicySilo Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -CannotChangePassword Specifies whether the account password can be changed. This parameter sets the CannotChangePassword property of an account. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Certificates Modifies the DER-encoded X.509v3 certificates of the account. These certificates include the public key certificates issued to this account by the Microsoft Certificate Service. This parameter sets the Certificates property of the account object. The LDAP Display Name (ldapDisplayName) for this property is userCertificate. Syntax: To add values: `-Certificates @{Add=value1,value2,...}` To remove values: `-Certificates @{Remove=value3,value4,...}` To replace values: `-Certificates @{Replace=value1,value2,...}` To clear all values: `-Certificates $null` You can specify more than one operation by using a list separated by semicolons. For example, use the following syntax to add and remove Certificate values `-Certificates @{Add=value1,value2,...;Remove=value3,value4,...}` The operators are applied in the following sequence: ..Remove ..Add ..Replace ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ChangePasswordAtLogon Specifies whether a password must be changed during the next logon attempt. The acceptable values for this parameter are: - $False or 0 - $True or 1 This parameter cannot be set to $True or 1 for an account that also has the PasswordNeverExpires property set to $True. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Clear Specifies an array of object properties that are cleared in the directory. Use this parameter to clear one or more values of a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can modify more than one property by specifying a comma-separated list. The format for this parameter is: -Clear Attribute1LDAPDisplayName, Attribute2LDAPDisplayName When you use the Add, Replace, Clear, and Remove parameters together, the operations are performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -CompoundIdentitySupported Specifies whether an account supports Kerberos service tickets which includes the authorization data for the user\u0027s device. This value sets the compound identity supported flag of the Active Directory msDS-SupportedEncryptionTypes attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 Warning: Domain-joined Windows systems and services such as clustering manage their own msDS-SupportedEncryptionTypes attribute. Therefore any changes to the flag on the msDS-SupportedEncryptionTypes attribute is overwritten by the service or system which manages the setting. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Description Specifies a description of the object. This parameter sets the value of the Description property for the object. The LDAP display name (ldapDisplayName) for this property is description. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -DisplayName Specifies the display name of the object. This parameter sets the DisplayName property of the object. The LDAP display name (ldapDisplayName) for this property is displayName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -DNSHostName Specifies the fully qualified domain name (FQDN) of the computer. This parameter sets the DNSHostName property for a computer object. The LDAP display name for this property is dNSHostName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Enabled Specifies if an account is enabled. An enabled account requires a password. This parameter sets the Enabled property for an account object. This parameter also sets the ADS_UF_ACCOUNTDISABLE flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -HomePage Specifies the URL of the home page of the object. This parameter sets the homePage property of an Active Directory object. The LDAP display name (ldapDisplayName) for this property is wWWHomePage. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory computer object by providing one of the following property values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A Distinguished Name - A GUID (objectGUID) - A Security Identifier (objectSid) - A Security Accounts Manager Account Name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If the identifier given is a distinguished name, the partition to search is computed from that distinguished name. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to a computer object instance. ## Parameter properties Type:ADComputer Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Instance Specifies a modified copy of a computer object to use to update the actual Active Directory computer object. When this parameter is used, any modifications made to the modified copy of the object are also made to the corresponding Active Directory object. The cmdlet only updates the object properties that have changed. The Instance parameter can only update computer objects that have been retrieved by using the Get-ADComputer cmdlet. When you specify the Instance parameter, you cannot specify other parameters that set properties on the object. ## Parameter properties Type:ADComputer Default value:None Supports wildcards:False DontShow:False ## Parameter sets Instance Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -KerberosEncryptionType Specifies whether an account supports Kerberos encryption types which are used during creation of service tickets. This value sets the encryption types supported flags of the Active Directory msDS-SupportedEncryptionTypes attribute. The acceptable values for this parameter are: - None - DES - RC4 - AES128 - AES256 None will remove all encryption types from the account which may result in the Key Distribution Center (KDC) being unable to issue service tickets for services using the account. Data Encryption Standard (DES) is a weak encryption type which is not supported by default since Windows 7 and Windows Server 2008 R2. Warning: Domain-joined Windows systems and services such as clustering manage their own msDS-SupportedEncryptionTypes attribute. Therefore any changes to the flag on the msDS-SupportedEncryptionTypes attribute is overwritten by the service or system which manages the setting. ## Parameter properties Type:ADKerberosEncryptionType Default value:None Accepted values:None, DES, RC4, AES128, AES256 Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Location Specifies the location of the computer, such as an office number. This parameter sets the Location property of a computer. The LDAP display name (ldapDisplayName) of this property is location. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ManagedBy Specifies the user or group that manages the object by providing one of the following property values. Note: The identifier in parentheses is the LDAP display name for the property. The acceptable values for this parameter are: - A Distinguished Name - A GUID (objectGUID) - A Security Identifier (objectSid) - A SAM Account Name (sAMAccountName) This parameter sets the Active Directory attribute with an LDAP display name of managedBy. ## Parameter properties Type:ADPrincipal Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -OperatingSystem Specifies an operating system name. This parameter sets the OperatingSystem property of the computer object. The LDAP display name (ldapDisplayName) for this property is operatingSystem. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -OperatingSystemHotfix Specifies an operating system hotfix name. This parameter sets the operatingSystemHotfix property of the computer object. The LDAP display name for this property is operatingSystemHotfix. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -OperatingSystemServicePack Specifies the name of an operating system service pack. This parameter sets the OperatingSystemServicePack property of the computer object. The LDAP display name (ldapDisplayName) for this property is operatingSystemServicePack. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -OperatingSystemVersion Specifies an operating system version. This parameter sets the OperatingSystemVersion property of the computer object. The LDAP display name (ldapDisplayName) for this property is operatingSystemVersion. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter will not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PasswordNeverExpires Specifies whether the password of an account can expire. This parameter sets the PasswordNeverExpires property of an account object. This parameter also sets the ADS_UF_DONT_EXPIRE_PASSWD flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 Note: This parameter cannot be set to $True or 1 for an account that also has the ChangePasswordAtLogon property set to \u0026True. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PasswordNotRequired Specifies whether the account requires a password. This parameter sets the PasswordNotRequired property of an account, such as a user or computer account. This parameter also sets the ADS_UF_PASSWD_NOTREQD flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PrincipalsAllowedToDelegateToAccount Specifies the accounts which can act on the behalf of users to services running as this computer account. This parameter sets the msDS-AllowedToActOnBehalfOfOtherIdentity attribute of a computer account object. `Running Set-ADComputer without specifying the first principal will cause it to get overridden` ## Parameter properties Type: ADPrincipal[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Remove Specifies that the cmdlet remove values of an object property. Use this parameter to remove one or more values of a property that cannot be modified using a cmdlet parameter. To remove an object property, you must use the LDAP display name. You can remove more than one property by specifying a semicolon-separated list. The format for this parameter is: `-Remove @{Attribute1LDAPDisplayName=value[]; Attribute2LDAPDisplayName=value[]}` When you use the Add, Replace, Clear, and Remove parameters together, the parameters are applied in the following sequence: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Replace Specifies values for an object property that will replace the current values. Use this parameter to replace one or more values of a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values, and more than one property by separating them using a semicolon. The format for this parameter is: `-Replace @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Remove, Replace, and Clear parameters together, the operations will be performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SAMAccountName Specifies the Security Account Manager (SAM) account name of the user, group, computer, or service account. The maximum length of the description is 256 characters. To be compatible with older operating systems, create a SAM account name that is 20 characters or less. This parameter sets the SAMAccountName for an account object. The LDAP display name (ldapDisplayName) for this property is sAMAccountName. Note: If the string value provided is not terminated with a $ character, the system adds one if needed. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services, or Active Directory snapshot instance. Specify the Active Directory Domain Services instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ServicePrincipalNames Specifies the service principal names for the account. This parameter sets the ServicePrincipalNames property of the account. The LDAP display name (ldapDisplayName) for this property is servicePrincipalName. This parameter uses the following syntax to add remove, replace or clear service principal name values: Syntax: To add values: `-ServicePrincipalNames @{Add=value1,value2,...}` To remove values: `-ServicePrincipalNames @{Remove=value3,value4,...}` To replace values: `-ServicePrincipalNames @{Replace=value1,value2,...}` To clear all values: `-ServicePrincipalNames $null` You can specify more than one change by using a list separated by semicolons. For example, use the following syntax to add and remove service principal names. `@{Add=value1,value2,...;Remove=value3,value4,...}` The operators are applied in the following sequence: ..Remove ..Add ..Replace The following example shows how to add and remove service principal names. `-ServicePrincipalNames @{Add=\"SQLservice/accounting.corp.contoso.com:1456\";Remove=\"SQLservice/finance.corp.contoso.com:1456\"}` ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -TrustedForDelegation Specifies whether an account is trusted for Kerberos delegation. A service that runs under an account that is trusted for Kerberos delegation can assume the identity of a client requesting the service. This parameter sets the TrustedForDelegation property of an account object. This value also sets the ADS_UF_TRUSTED_FOR_DELEGATION flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -UserPrincipalName Specifies a user principal name (UPN) in the format `\u003cuser\u003e@\u003cDNS-domain-name\u003e`. A user principal name (UPN) is a friendly name assigned by an administrator that is shorter than the LDAP distinguished name used by the system and easier to remember. The UPN is independent of the user object\u0027s distinguished name, so a user object can be moved or renamed without affecting the user logon name. When logging on using a UPN, users no longer have to choose a domain from a list on the logon dialog box. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## Microsoft.ActiveDirectory.Management.ADComputer A computer object is received by the Identity parameter. A computer object that was retrieved by using the Get-ADComputer cmdlet and then modified is received by the Instance parameter. ## Outputs ## None or Microsoft.ActiveDirectory.Management.ADComputer Returns the modified computer object when the PassThru parameter is specified. By default, this cmdlet does not generate any output. ## Notes - This cmdlet does not work with AD LDS. - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. - This cmdlet does not work when connected to Global Catalog port. ## Related Links - Add-ADComputerServiceAccount - Get-ADComputer - Get-ADComputerServiceAccount - New-ADComputer - Remove-ADComputer - Remove-ADComputerServiceAccount - AD DS Administration Cmdlets in Windows PowerShell ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "set-adcomputer"
                                 ],
                        "last_updated":  "2025-07-28T17:14:54Z",
                        "title":  "Set-ADComputer (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adcomputer",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "New-ADComputer"
                                    ],
                        "id":  "microsoft_docs_new-adcomputer",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AccountExpirationDate",
                                                            "AccountNotDelegated",
                                                            "AccountPassword",
                                                            "AllowReversiblePasswordEncryption",
                                                            "AuthenticationPolicy",
                                                            "AuthenticationPolicySilo",
                                                            "AuthType",
                                                            "CannotChangePassword",
                                                            "Certificates",
                                                            "ChangePasswordAtLogon",
                                                            "CompoundIdentitySupported",
                                                            "Credential",
                                                            "Description",
                                                            "DisplayName",
                                                            "DNSHostName",
                                                            "Enabled",
                                                            "HomePage",
                                                            "Instance",
                                                            "KerberosEncryptionType",
                                                            "Location",
                                                            "ManagedBy",
                                                            "OperatingSystem",
                                                            "OperatingSystemHotfix",
                                                            "OperatingSystemServicePack",
                                                            "OperatingSystemVersion",
                                                            "OtherAttributes",
                                                            "PasswordNeverExpires",
                                                            "PasswordNotRequired",
                                                            "Path",
                                                            "PrincipalsAllowedToDelegateToAccount",
                                                            "SAMAccountName",
                                                            "Server",
                                                            "ServicePrincipalNames",
                                                            "TrustedForDelegation",
                                                            "UserPrincipalName"
                                                        ],
                                         "cmdlet_name":  "New-ADComputer"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# New-ADComputer (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## New-ADComputer Module: ActiveDirectory Module ## Creates a new Active Directory computer object. ## Syntax ## Default (Default) ```powershellNew-ADComputer [-WhatIf] [-Confirm] [-AccountExpirationDate \u003cDateTime\u003e] [-AccountNotDelegated \u003cBoolean\u003e] [-AccountPassword \u003cSecureString\u003e] [-AllowReversiblePasswordEncryption \u003cBoolean\u003e] [-AuthenticationPolicy \u003cADAuthenticationPolicy\u003e] [-AuthenticationPolicySilo \u003cADAuthenticationPolicySilo\u003e] [-AuthType \u003cADAuthType\u003e] [-CannotChangePassword \u003cBoolean\u003e] [-Certificates \u003cX509Certificate[]\u003e] [-ChangePasswordAtLogon \u003cBoolean\u003e] [-CompoundIdentitySupported \u003cBoolean\u003e] [-Credential \u003cPSCredential\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-DNSHostName \u003cString\u003e] [-Enabled \u003cBoolean\u003e] [-HomePage \u003cString\u003e] [-Instance \u003cADComputer\u003e] [-KerberosEncryptionType \u003cADKerberosEncryptionType\u003e] [-Location \u003cString\u003e] [-ManagedBy \u003cADPrincipal\u003e] [-Name] \u003cString\u003e [-OperatingSystem \u003cString\u003e] [-OperatingSystemHotfix \u003cString\u003e] [-OperatingSystemServicePack \u003cString\u003e] [-OperatingSystemVersion \u003cString\u003e] [-OtherAttributes \u003cHashtable\u003e] [-PassThru] [-PasswordNeverExpires \u003cBoolean\u003e] [-PasswordNotRequired \u003cBoolean\u003e] [-Path \u003cString\u003e] [-PrincipalsAllowedToDelegateToAccount \u003cADPrincipal[]\u003e] [-SAMAccountName \u003cString\u003e] [-Server \u003cString\u003e] [-ServicePrincipalNames \u003cString[]\u003e] [-TrustedForDelegation \u003cBoolean\u003e] [-UserPrincipalName \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The New-ADComputer cmdlet creates a new Active Directory computer object. This cmdlet does not join a computer to a domain. You can set commonly used computer property values by using the cmdlet parameters. Property values that are not associated with cmdlet parameters can be modified by using the OtherAttributes parameter. You can use this cmdlet to provision a computer account before the computer is added to the domain. These pre-created computer objects can be used with offline domain join, unsecure domain join, and RODC domain join scenarios. The Path parameter specifies the container or organizational unit (OU) for the new computer. When you do not specify the Path parameter, the cmdlet creates a computer account in the default container for computer objects in the domain. The following methods explain different ways to create an object by using this cmdlet. Method 1: Use the New-ADComputer cmdlet, specify the required parameters, and set any additional property values by using the cmdlet parameters. Method 2: Use a template to create the new object. To do this, create a new computer object or retrieve a copy of an existing computer object and set the Instance parameter to this object. The object provided to the Instance parameter is used as a template for the new object. You can override property values from the template by setting cmdlet parameters. Method 3: Use the Import-Csv cmdlet with the Add-ADComputerServiceAccount cmdlet to create multiple Active Directory computer objects. To do this, use the Import-Csv cmdlet to create the custom objects from a comma-separated value (CSV) file that contains a list of object properties. Then pass these objects to the New-ADComputer cmdlet by using the pipeline operator to create the computer objects. ## Examples ## Example 1: Create a new computer account in an organization unit ```powershellPS C:\\\u003e New-ADComputer -Name \"USER02-SRV2\" -SamAccountName \"USER02-SRV2\" -Path \"OU=ApplicationServers,OU=ComputerAccounts,OU=Managed,DC=USER02,DC=COM\" ``` This command creates a new computer account in the OU OU=ApplicationServers,OU=ComputerAccounts,OU=Managed,DC=USER02,DC=COM. ## Example 2: Create a new computer account under an organization unit in a specified region ```powershellPS C:\\\u003e New-ADComputer -Name \"USER01-SRV3\" -SamAccountName \"USER01-SRV3\" -Path \"OU=ApplicationServers,OU=ComputerAccounts,OU=Managed,DC=USER01,DC=COM\" -Enabled $True -Location \"Redmond,WA\" ``` This command creates a new computer account under a particular OU, which is enabled and located in Redmond, WA. ## Example 3: Create a new computer account from a template ```powershellPS C:\\\u003e $TemplateComp = Get-ADComputer -Name \"LabServer-00\" -Properties \"Location\",\"OperatingSystem\",\"OperatingSystemHotfix\",\"OperatingSystemServicePack\",\"OperatingSystemVersion\" PS C:\\\u003e New-ADComputer -Instance $TemplateComp -Name \"LabServer-01\" ``` This example creates a new computer account from a template object. ## Parameters ## -AccountExpirationDate Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The Lightweight Directory Access Protocol (LDAP) display name (ldapDisplayName) for this property is accountExpires. Use the DateTime syntax when you specify this parameter. Time is assumed to be local time unless otherwise specified. When a time value is not specified, the time is assumed to 12:00:00 AM local time. When a date is not specified, the date is assumed to be the current date. ## Parameter properties Type:DateTime Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AccountNotDelegated Specifies whether the security context of the user is delegated to a service. When this parameter is set to true, the security context of the account is not delegated to a service even when the service account is set as trusted for Kerberos delegation. This parameter sets the AccountNotDelegated property for an Active Directory account. This parameter also sets the ADS_UF_NOT_DELEGATED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AccountPassword Specifies a new password value for an account. This value is stored as an encrypted string. The following conditions apply based on the manner in which the password parameter is used: - $Null password is specified: random password is set and the account is enabled unless it is requested to be disabled. - No password is specified: random password is set and the account is enabled unless it is requested to be disabled. - User password is specified: password is set and the account is enabled unless it is requested to be disabled, unless the password you provided does not meet password policy or was not set for other reasons, at which point the account is disabled. Notes: Computer accounts, by default, are created with a 240-character random password. If you provide a password, an attempt is made to set that password. However, this can fail due to password policy restrictions. The computer account is created and you can use Set-ADAccountPassword to set the password on that account. In order to ensure that accounts remain secure, computer accounts will never be enabled unless a valid password is set (either a randomly-generated or user-provided one) or PasswordNotRequired is set to $True. The account is created if the password fails for any reason. The new ADComputer object will always either be disabled or have a user-requested or randomly-generated password. There is no way to create an enabled computer account object with a password that violates domain password policy, such as an empty password. ## Parameter properties Type:SecureString Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AllowReversiblePasswordEncryption Specifies whether reversible password encryption is allowed for the account. This parameter sets the AllowReversiblePasswordEncryption property of the account. This parameter also sets the ADS_UF_ENCRYPTED_TEXT_PASSWORD_ALLOWED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AuthenticationPolicy Specifies an Active Directory Domain Services authentication policy object. Specify the authentication policy object in one of the following formats: - A distinguished Name - A GUID - A name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicy Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AuthenticationPolicySilo Specifies an Active Directory Domain Services authentication policy silo object. Specify the authentication policy silo object in one of the following formats: - A distinguished name - A GUID - A name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicySilo Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -CannotChangePassword Specifies whether the account password can be changed. This parameter sets the CannotChangePassword property of an account. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Certificates Specifies the DER-encoded X.509v3 certificates of the account. These certificates include the public key certificates issued to this account by the Microsoft Certificate Service. This parameter sets the Certificates property of the account object. The LDAP display name (ldapDisplayName) for this property is userCertificate. ## Parameter properties Type: X509Certificate[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -ChangePasswordAtLogon Specifies whether a password must be changed during the next logon attempt. The acceptable values for this parameter are: - $False or 0 - $True or 1 This parameter cannot be set to $True or 1 for an account that also has the PasswordNeverExpires property set to $True. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -CompoundIdentitySupported Specifies whether an account supports Kerberos service tickets which includes the authorization data for the user\u0027s device. This value sets the compound identity supported flag of the Active Directory msDS-SupportedEncryptionTypes attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 Warning: Domain-joined Windows systems and services such as clustering manage their own msDS-SupportedEncryptionTypes attribute. Therefore any changes to the flag on the msDS-SupportedEncryptionTypes attribute is overwritten by the service or system which manages the setting. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Description Specifies a description of the object. This parameter sets the value of the Description property for the object. The LDAP display name (ldapDisplayName) for this property is description. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -DisplayName Specifies the display name of the object. This parameter sets the DisplayName property of the object. The LDAP display name (ldapDisplayName) for this property is displayName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -DNSHostName Specifies the fully qualified domain name (FQDN) of the computer. This parameter sets the DNSHostName property for a computer object. The LDAP display name for this property is dNSHostName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Enabled Specifies if an account is enabled. An enabled account requires a password. This parameter sets the Enabled property for an account object. This parameter also sets the ADS_UF_ACCOUNTDISABLE flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -HomePage Specifies the URL of the home page of the object. This parameter sets the homePage property of an Active Directory object. The LDAP display name (ldapDisplayName) for this property is wWWHomePage. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Instance Specifies an instance of a computer object to use as a template for a new computer object. You can use an instance of an existing computer object as a template or you can construct a new computer object by using the Windows PowerShell command line or by using a script. Method 1: Use an existing computer object as a template for a new object. To retrieve an instance of an existing computer object use Get-ADComputer. Then provide this object to the Instance parameter of the New-ADComputer cmdlet to create a new computer object. You can override property values of the new object by setting the appropriate parameters. Method 2: Create a new ADcomputer object and set the property values by using the Windows PowerShell command line interface. Then pass this object to the Instance parameter of the New-ADComputer cmdlet to create the new Active Directory computer object. Note: Specified attributes are not validated, so attempting to set attributes that do not exist or cannot be set will raise an error. ## Parameter properties Type:ADComputer Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -KerberosEncryptionType Specifies whether an account supports Kerberos encryption types which are used during creation of service tickets. This value sets the encryption types supported flags of the Active Directory msDS-SupportedEncryptionTypes attribute. The acceptable values for this parameter are: - None - DES - RC4 - AES128 - AES256 None will remove all encryption types from the account which may result in the KDC being unable to issue service tickets for services using the account. DES is a weak encryption type which is not supported by default since Windows 7 and Windows Server 2008 R2. Warning: Domain-joined Windows systems and services such as clustering manage their own msDS-SupportedEncryptionTypes attribute. Therefore any changes to the flag on the msDS-SupportedEncryptionTypes attribute is overwritten by the service or system which manages the setting. ## Parameter properties Type:ADKerberosEncryptionType Default value:None Accepted values:None, DES, RC4, AES128, AES256 Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Location Specifies the location of the computer, such as an office number. This parameter sets the Location property of a computer. The LDAP display name (ldapDisplayName) of this property is location. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -ManagedBy Specifies the user or group that manages the object by providing one of the following property values. Note: The identifier in parentheses is the LDAP display name for the property. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A SAM account name (sAMAccountName) This parameter sets the Active Directory attribute with an LDAP display name of managedBy. ## Parameter properties Type:ADPrincipal Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Name Specifies the name of the object. This parameter sets the Name property of the Active Directory object. The LDAP display name (ldapDisplayName) of this property is name. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:1 Mandatory:True Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OperatingSystem Specifies an operating system name. This parameter sets the OperatingSystem property of the computer object. The LDAP Display Name (ldapDisplayName) for this property is operatingSystem. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OperatingSystemHotfix Specifies an operating system hotfix name. This parameter sets the operatingSystemHotfix property of the computer object. The LDAP display name for this property is operatingSystemHotfix. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OperatingSystemServicePack Specifies the name of an operating system service pack. This parameter sets the OperatingSystemServicePack property of the computer object. The LDAP display name (ldapDisplayName) for this property is operatingSystemServicePack. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OperatingSystemVersion Specifies an operating system version. This parameter sets the OperatingSystemVersion property of the computer object. The LDAP display name (ldapDisplayName) for this property is operatingSystemVersion. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OtherAttributes Specifies object attribute values for attributes that are not represented by cmdlet parameters. You can set one or more parameters at the same time with this parameter. If an attribute takes more than one value, you can assign multiple values. To identify an attribute, specify the LDAP display name (ldapDisplayName) defined for it in the Active Directory schema. Syntax: To specify a single value for an attribute: `-OtherAttributes @{\u0027AttributeLDAPDisplayName\u0027=value}` To specify multiple values for an attribute `-OtherAttributes @{\u0027AttributeLDAPDisplayName\u0027=value1,value2,...}` You can specify values for more than one attribute by using semicolons to separate attributes. The following syntax shows how to set values for multiple attributes: `-OtherAttributes @{\u0027Attribute1LDAPDisplayName\u0027=value; \u0027Attribute2LDAPDisplayName\u0027=value1,value2;...}` ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PasswordNeverExpires Specifies whether the password of an account can expire. This parameter sets the PasswordNeverExpires property of an account object. This parameter also sets the ADS_UF_DONT_EXPIRE_PASSWD flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 Note: This parameter cannot be set to $True or 1 for an account that also has the ChangePasswordAtLogon property set to $True. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PasswordNotRequired Specifies whether the account requires a password. This parameter sets the PasswordNotRequired property of an account, such as a user or computer account. This parameter also sets the ADS_UF_PASSWD_NOTREQD flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Path Specifies the X.500 path of the Organizational Unit (OU) or container where the new object is created. In many cases, a default value is used for the Path parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services environments, a default value for Path is set in the following cases: - If the cmdlet is run from an Active Directory module for Windows PowerShell provider drive, the parameter is set to the current path of the provider drive. - If the cmdlet has a default path, this value is used. For example: in New-ADUser, the Path parameter would default to the Users container. - If none of the previous cases apply, the default value of Path is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Path is set in the following cases: - If the cmdlet is run from an Active Directory module for Windows PowerShell provider drive, the parameter is set to the current path of the provider drive. - If the cmdlet has a default path, this value is used. For example: in New-ADUser, the Path parameter would default to the Users container. - If the target AD LDS instance has a default naming context, the default value of Path is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Path parameter will not take any default value. Note: The Active Directory Provider cmdlets, such as New-Item, Remove-Item, Remove-ItemProperty, Rename-Item, and Set-ItemProperty, also contain a Path property. However, for the provider cmdlets, the Path parameter identifies the path of the actual object and not the container as with the Active Directory cmdlets. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PrincipalsAllowedToDelegateToAccount Specifies the accounts which can act on the behalf of users to services running as this computer account. This parameter sets the msDS-AllowedToActOnBehalfOfOtherIdentity attribute of a computer account object. ## Parameter properties Type: ADPrincipal[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -SAMAccountName Specifies the Security Account Manager (SAM) account name of the user, group, computer, or service account. The maximum length of the description is 256 characters. To be compatible with older operating systems, create a SAM account name that is 15 characters or less. This parameter sets the SAMAccountName for an account object. The LDAP display name (ldapDisplayName) for this property is sAMAccountName. Note: If the SAMAccountName string provided does not end with a $, a $ will be appended if needed. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory snapshot instance. Specify the Active Directory Domain Services instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ServicePrincipalNames Specifies the service principal names for the account. This parameter sets the ServicePrincipalNames property of the account. The LDAP display name (ldapDisplayName) for this property is servicePrincipalName. To enter multiple values, use the following syntax: `\u003cvalue1\u003e,\u003cvalue2\u003e,...\u003cvalueX\u003e`. If the values contain spaces or otherwise require quotation marks, use the following syntax: `\"\u003cvalue1\u003e\",\"\u003cvalue2\u003e\",...\"\u003cvalueX\u003e\"`.\" ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -TrustedForDelegation Specifies whether an account is trusted for Kerberos delegation. A service that runs under an account that is trusted for Kerberos delegation can assume the identity of a client requesting the service. This parameter sets the TrustedForDelegation property of an account object. This value also sets the ADS_UF_TRUSTED_FOR_DELEGATION flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -UserPrincipalName Specifies a user principal name (UPN) in the format `\u003cuser\u003e@\u003cDNS-domain-name\u003e`. A UPN is a friendly name assigned by an administrator that is shorter than the LDAP distinguished name used by the system and easier to remember. The UPN is independent of the user object\u0027s distinguished name, so a user object can be moved or renamed without affecting the user logon name. When logging on using a UPN, users no longer have to choose a domain from a list on the logon dialog box. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADComputer A computer object that is a template for the new computer object is received by the Instance parameter. ## Outputs ## None or Microsoft.ActiveDirectory.Management.ADComputer Returns the new computer object when the PassThru parameter is specified. By default, this cmdlet does not generate any output. ## Notes - This cmdlet does not work with AD LDS. - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. ## Related Links - Add-ADComputerServiceAccount - Get-ADComputer - Get-ADComputerServiceAccount - Remove-ADComputer - Remove-ADComputerServiceAccount - Set-ADComputer - Set-ADAccountPassword - New-ADUser - AD DS Administration Cmdlets in Windows PowerShell ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "new-adcomputer"
                                 ],
                        "last_updated":  "2025-07-28T17:14:55Z",
                        "title":  "New-ADComputer (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-adcomputer",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Remove-ADComputer"
                                    ],
                        "id":  "microsoft_docs_remove-adcomputer",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AuthType",
                                                            "Credential",
                                                            "Partition",
                                                            "Server"
                                                        ],
                                         "cmdlet_name":  "Remove-ADComputer"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Remove-ADComputer (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Remove-ADComputer Module: ActiveDirectory Module ## Removes an Active Directory computer. ## Syntax ## Default (Default) ```powershellRemove-ADComputer [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Identity] \u003cADComputer\u003e [-Partition \u003cString\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The Remove-ADComputer cmdlet removes an Active Directory computer. The Identity parameter specifies the Active Directory computer to remove. You can identify a computer by its distinguished name, GUID, security identifier (SID), or Security Accounts Manager (SAM) account name. You can also set the Identity parameter to a computer object variable, such as `$\u003clocalComputerobject\u003e`, or you can pass a computer object through the pipeline to the Identity parameter. For example, you can use the Get-ADComputer cmdlet to retrieve a computer object and then pass the object through the pipeline to the Remove-ADComputer cmdlet. ## Examples ## Example 1: Remove a specified computer from Active Directory ```powershellPS C:\\\u003e Remove-ADComputer -Identity \"USER04-SRV4\" ``` This command removes a specified computer from Active Directory. ## Example 2: Remove all computers from a specified location using a filter ```powershellPS C:\\\u003e Get-ADComputer -Filter \u0027Location -eq \"NA/HQ/Building A\"\u0027 | Remove-ADComputer Confirm Are you sure you want to perform this action? Performing operation \"Remove\" on Target \"CN=LabServer-01,CN=Computers,DC=Fabrikam,DC=com\". [Y] Yes [A] Yes to All [N] No [L] No to All [S] Suspend [?] Help (default is \"Y\"): a ``` This command removes all computers in the location specified by using the Filter parameter. ## Example 3: Remove all computers from a specified location using a filter ```powershellPS C:\\\u003e Get-ADComputer -Filter \u0027Location -eq \"NA/HQ/Building A\"\u0027 | Remove-ADComputer -Confirm:$False ``` This command removes all computers from the location specified by using the Filter parameter. The command does not prompt you for confirmation. ## Example 4: Remove a computer and all leaf objects that are located under a specified directory ```powershellPS C:\\\u003e Get-ADComputer -Identity \"USER01-SRV4\" | Remove-ADObject -Recursive ``` This command removes a computer and all leaf objects that are located underneath it in the directory. Note that only a few computer objects create child objects, such as servers running the Clustering service. This example can be useful for removing those objects and any child objects owned by and associated with them. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:True Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory computer object by providing one of the following property values. The identifier in parentheses is the Lightweight Directory Access Protocol (LDAP) display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A Security Accounts Manager account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If the identifier given is a distinguished name, the partition to search is computed from that distinguished name. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to a computer object instance. ## Parameter properties Type:ADComputer Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter will not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory snapshot instance. Specify the Active Directory Domain Services instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADComputer A computer object is received by the Identity parameter. ## Outputs ## None ## Notes - This cmdlet does not work with AD LDS. - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. - By default, this cmdlet has the Confirm parameter set, which prompts you to confirm before a removal of the specified object type can occur. To bypass prompting for confirmation before removal, you can specify `-Confirm:$False` when using this cmdlet. ## Related Links - Add-ADComputerServiceAccount - Get-ADComputer - Get-ADComputerServiceAccount - New-ADComputer - Remove-ADComputerServiceAccount - Set-ADComputer - AD DS Administration Cmdlets in Windows PowerShell ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "remove-adcomputer"
                                 ],
                        "last_updated":  "2025-07-28T17:14:56Z",
                        "title":  "Remove-ADComputer (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adorganizationalunit",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Get-ADOrganizationalUnit"
                                    ],
                        "id":  "microsoft_docs_get-adorganizationalunit",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "Filter",
                                                            "AuthType",
                                                            "Credential",
                                                            "Properties",
                                                            "ResultPageSize",
                                                            "ResultSetSize",
                                                            "SearchBase",
                                                            "SearchScope",
                                                            "Server",
                                                            "Partition",
                                                            "LDAPFilter"
                                                        ],
                                         "cmdlet_name":  "Get-ADOrganizationalUnit"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Get-ADOrganizationalUnit (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Get-ADOrganizationalUnit Module: ActiveDirectory Module ## Gets one or more Active Directory organizational units. ## Syntax ## Filter (Default) ```powershellGet-ADOrganizationalUnit -Filter \u003cString\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Properties \u003cString[]\u003e] [-ResultPageSize \u003cInt32\u003e] [-ResultSetSize \u003cInt32\u003e] [-SearchBase \u003cString\u003e] [-SearchScope \u003cADSearchScope\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Identity ```powershellGet-ADOrganizationalUnit [-Identity] \u003cADOrganizationalUnit\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Partition \u003cString\u003e] [-Properties \u003cString[]\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## LdapFilter ```powershellGet-ADOrganizationalUnit -LDAPFilter \u003cString\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Properties \u003cString[]\u003e] [-ResultPageSize \u003cInt32\u003e] [-ResultSetSize \u003cInt32\u003e] [-SearchBase \u003cString\u003e] [-SearchScope \u003cADSearchScope\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The Get-ADOrganizationalUnit cmdlet gets an organizational unit (OU) object or performs a search to get multiple OUs. The Identity parameter specifies the Active Directory OU to get. You can identify an OU by its distinguished name or GUID. You can also set the parameter to an OU object variable, such as `$\u003clocalOrganizationalunitObject\u003e` or pass an OU object through the pipeline to the Identity parameter. To search for and retrieve more than one OU, use the Filter or LDAPFilter parameters. The Filter parameter uses the PowerShell Expression Language to write query strings for Active Directory. PowerShell Expression Language syntax provides rich type conversion support for value types received by the Filter parameter. For more information about the Filter parameter syntax, type `Get-Help about_ActiveDirectory_Filter`. If you have existing Lightweight Directory Access Protocol (LDAP) query strings, you can use the LDAPFilter parameter. This cmdlet gets a default set of OU object properties. To get additional properties, use the Properties parameter. For more information about the how to determine the properties for computer objects, see the Properties parameter description. ## Examples ## Example 1: Get all of the OUs in a domain ```powershellPS C:\\\u003e Get-ADOrganizationalUnit -Filter \u0027Name -like \"*\"\u0027 | Format-Table Name, DistinguishedName -A ``` ```powershellName DistinguishedName ---- ----------------- Domain Controllers OU=Domain Controllers,DC=FABRIKAM,DC=COM UserAccounts OU=UserAccounts,DC=FABRIKAM,DC=COM Sales OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM Marketing OU=Marketing,OU=UserAccounts,DC=FABRIKAM,DC=COM Production OU=Production,OU=UserAccounts,DC=FABRIKAM,DC=COM HumanResources OU=HumanResources,OU=UserAccounts,DC=FABRIKAM,DC=COM NorthAmerica OU=NorthAmerica,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM SouthAmerica OU=SouthAmerica,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM Europe OU=Europe,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM AsiaPacific OU=AsiaPacific,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM Finance OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM Corporate OU=Corporate,OU=UserAccounts,DC=FABRIKAM,DC=COM ApplicationServers OU=ApplicationServers,DC=FABRIKAM,DC=COM Groups OU=Groups,OU=Managed,DC=FABRIKAM,DC=COM PasswordPolicyGroups OU=PasswordPolicyGroups,OU=Groups,OU=Managed,DC=FABRIKAM,DC=COM Managed OU=Managed,DC=FABRIKAM,DC=COM ServiceAccounts OU=ServiceAccounts,OU=Managed,DC=FABRIKAM,DC=COM ``` This command gets all of the OUs in a domain. ## Example 2: Get an OU by its distinguished name ```powershellPS C:\\\u003e Get-ADOrganizationalUnit -Identity \u0027OU=AsiaPacific,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 | Format-Table Name,Country,PostalCode,City,StreetAddress,State -A ``` ```powershellName Country PostalCode City StreetAddress State ---- ------- ---------- ---- ------------- ----- AsiaPacific AU 4171 Balmoral 45 Martens Place QLD ``` This command gets the OU with the distinguished name OU=AsiaPacific,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM. ## Example 3: Get child OUs ```powershellPS C:\\\u003e Get-ADOrganizationalUnit -LDAPFilter \u0027(name=*)\u0027 -SearchBase \u0027OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 -SearchScope OneLevel | Format-Table Name,Country,PostalCode,City,StreetAddress,State ``` ```powershellName Country PostalCode City StreetAddress State ---- ------- ---------- ---- ------------- ----- AsiaPacific AU 4171 Balmoral 45 Martens Place QLD Europe UK NG34 0NI QUARRINGTON 22 Station Rd NorthAmerica US 02142 Cambridge 1634 Randolph Street MA ``` This command gets OUs underneath the Sales OU using an LDAP filter. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Filter Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Expression Language syntax. The PowerShell Expression Language syntax provides rich type-conversion support for value types received by the Filter parameter. The syntax uses an in-order representation, which means that the operator is placed between the operand and the value. For more information about the Filter parameter, type `Get-Help about_ActiveDirectory_Filter`. Syntax: The following syntax uses Backus-Naur form to show how to use the PowerShell Expression Language for this parameter. \u003cfilter\u003e ::= \"{\" \u003cFilterComponentList\u003e \"}\" \u003cFilterComponentList\u003e ::= \u003cFilterComponent\u003e | \u003cFilterComponent\u003e \u003cJoinOperator\u003e \u003cFilterComponent\u003e | \u003cNotOperator\u003e \u003cFilterComponent\u003e \u003cFilterComponent\u003e ::= \u003cattr\u003e \u003cFilterOperator\u003e \u003cvalue\u003e | \"(\" \u003cFilterComponent\u003e \")\" \u003cFilterOperator\u003e ::= \"-eq\" | \"-le\" | \"-ge\" | \"-ne\" | \"-lt\" | \"-gt\"| \"-approx\" | \"-bor\" | \"-band\" | \"-recursivematch\" | \"-like\" | \"-notlike\" \u003cJoinOperator\u003e ::= \"-and\" | \"-or\" \u003cNotOperator\u003e ::= \"-not\" \u003cattr\u003e ::= \u003cPropertyName\u003e | \u003cLDAPDisplayName of the attribute\u003e \u003cvalue\u003e::= \u003ccompare this value with an \u003cattr\u003e by using the specified \u003cFilterOperator\u003e\u003e For a list of supported types for \u003cvalue\u003e, type `Get-Help about_ActiveDirectory_ObjectModel`. Note: PowerShell wildcards other than *, such as ?, are not supported by the Filter syntax. Note: To query using LDAP query strings, use the LDAPFilter parameter. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory organizational unit object by providing one of the following values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A Security Account Manager account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADOrganizationalUnit Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -LDAPFilter Specifies an LDAP query string that is used to filter Active Directory objects. You can use this parameter to run your existing LDAP queries. The Filter parameter syntax supports the same functionality as the LDAP syntax. For more information, see the Filter parameter description or type `Get-Help about_ActiveDirectory_Filter`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets LdapFilter Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter will not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Properties Specifies the properties of the output object to retrieve from the server. Use this parameter to retrieve properties that are not included in the default set. Specify properties for this parameter as a comma-separated list of names. To display all of the attributes that are set on the object, specify * (asterisk). To specify an individual extended property, use the name of the property. For properties that are not default or extended properties, you must specify the LDAP display name of the attribute. To retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the Get-Member cmdlet. ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False Aliases:Property ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ResultPageSize Specifies the number of objects to include in one page for an AD DS query. The default is 256 objects per page. ## Parameter properties Type:Int32 Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ResultSetSize Specifies the maximum number of objects to return for an AD DS query. If you want to receive all of the objects, set this parameter to $Null (null value). You can use Ctrl+C to stop the query and return of objects. The default is $Null. ## Parameter properties Type:Int32 Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SearchBase Specifies an Active Directory path to search. When you run a cmdlet from an Active Directory provider drive, the default value of this parameter is the current path of the drive. When you run a cmdlet outside of an Active Directory provider drive against an AD DS target, the default value of this parameter is the default naming context of the target domain. When you run a cmdlet outside of an Active Directory provider drive against an AD LDS target, the default value is the default naming context of the target AD LDS instance if one has been specified by setting the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. If no default naming context has been specified for the target AD LDS instance, then this parameter has no default value. When the value of the SearchBase parameter is set to an empty string and you are connected to a global catalog (GC) port, all partitions are searched. If the value of the SearchBase parameter is set to an empty string and you are not connected to a GC port, an error is thrown. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SearchScope Specifies the scope of an Active Directory search. The acceptable values for this parameter are: - Base or 0 - OneLevel or 1 - Subtree or 2 A Base query searches only the current path or object. A OneLevel query searches the immediate children of that path or object. A Subtree query searches the current path or object and all children of that path or object. ## Parameter properties Type:ADSearchScope Default value:None Accepted values:Base, OneLevel, Subtree Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADOrganizationalUnit An OU object is received by the Identity parameter. ## Outputs ## Microsoft.ActiveDirectory.Management.ADOrganizationalUnit Returns one or more OU objects. This cmdlet returns a default set of ADOrganizational property values. To retrieve additional ADOrganizational properties, use the Properties parameter. To view the properties for an ADOrganizational object, see the following examples. To run these examples, replace \u003corganizational unit\u003e with an OU identifier such as the distinguished name of an OU. To get a list of the default set of properties of an ADOrganizational object, use the following command: `Get-ADOrganizationalUnit`\u003corganizational unit\u003e`| Get-Member` To get a list of all the properties of an ADOrganizational object, use the following command: `Get-ADOrganizationalUnit`\u003corganizational unit\u003e`-Properties * | Get-Member` ## Related Links - New-ADOrganizationalUnit - Remove-ADOrganizationalUnit - Set-ADOrganizationalUnit ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "get-adorganizationalunit"
                                 ],
                        "last_updated":  "2025-07-28T17:14:57Z",
                        "title":  "Get-ADOrganizationalUnit (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/set-adorganizationalunit",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Set-ADOrganizationalUnit"
                                    ],
                        "id":  "microsoft_docs_set-adorganizationalunit",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "Add",
                                                            "AuthType",
                                                            "City",
                                                            "Clear",
                                                            "Country",
                                                            "Credential",
                                                            "Description",
                                                            "DisplayName",
                                                            "ManagedBy",
                                                            "Partition",
                                                            "PostalCode",
                                                            "ProtectedFromAccidentalDeletion",
                                                            "Remove",
                                                            "Replace",
                                                            "Server",
                                                            "State",
                                                            "StreetAddress",
                                                            "Instance"
                                                        ],
                                         "cmdlet_name":  "Set-ADOrganizationalUnit"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Set-ADOrganizationalUnit (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Set-ADOrganizationalUnit Module: ActiveDirectory Module ## Modifies an Active Directory organizational unit. ## Syntax ## Identity ```powershellSet-ADOrganizationalUnit [-Identity] \u003cADOrganizationalUnit\u003e [-WhatIf] [-Confirm] [-Add \u003cHashtable\u003e] [-AuthType \u003cADAuthType\u003e] [-City \u003cString\u003e] [-Clear \u003cString[]\u003e] [-Country \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-ManagedBy \u003cADPrincipal\u003e] [-Partition \u003cString\u003e] [-PassThru] [-PostalCode \u003cString\u003e] [-ProtectedFromAccidentalDeletion \u003cBoolean\u003e] [-Remove \u003cHashtable\u003e] [-Replace \u003cHashtable\u003e] [-Server \u003cString\u003e] [-State \u003cString\u003e] [-StreetAddress \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Instance ```powershellSet-ADOrganizationalUnit -Instance \u003cADOrganizationalUnit\u003e [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-PassThru] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The Set-ADOrganizationalUnit cmdlet modifies the properties of an Active Directory organizational unit (OU). You can modify commonly used property values by using the cmdlet parameters. Property values that are not associated with cmdlet parameters can be modified by using the Add, Remove, Replace, and Clear parameters. The Identity parameter specifies the Active Directory organizational unit to modify. You can identify an organizational unit by its distinguished name or GUID. You can also set the Identity parameter to an object variable such as `$\u003clocalADOrganizationalUnitObject\u003e`, or you can pass an object through the pipeline to the Identity parameter. For example, you can use the Get-ADOrganizationalUnit cmdlet to retrieve an organizational unit object and then pass the object through the pipeline to the Set-ADOrganizationalUnit cmdlet. The Instance parameter provides a way to update an organizational unit object by applying the changes made to a copy of the object. When you set the Instance parameter to a copy of an Active Directory organizational unit object that has been modified, the Set-ADOrganizationalUnit cmdlet makes the same changes to the original organizational unit object. To get a copy of the object to modify, use the Get-ADOrganizationalUnit object. When you specify the Instance parameter you should not pass the Identity parameter. For more information about the Instance parameter, see the Instance parameter description. For Active Directory Lightweight Directory Services (AD LDS) environments, the Partition parameter must be specified except in the following two conditions: - The cmdlet is run from an Active Directory provider drive. - A default naming context or partition is defined for the AD LDS environment. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. ## Examples ## Example 1: Set a description for an OU ```powershellPS C:\\\u003e Set-ADOrganizationalUnit -Identity \"OU=UserAccounts,DC=FABRIKAM,DC=COM\" -Description \"This Organizational Unit holds all of the users accounts of FABRIKAM.COM\" ``` This command sets the description of the OU with the distinguished name OU=UserAccounts,DC=FABRIKAM,DC=COM. ## Example 2: Set the ProtectedFromAccidentalDeletion property for an OU ```powershellPS C:\\\u003e Set-ADOrganizationalUnit -Identity \"OU=UserAccounts,DC=FABRIKAM,DC=COM\" -ProtectedFromAccidentalDeletion $false ``` This command sets the ProtectedFromAccidentalDeletion property to $False on the OU with distinguished name OU=UserAccounts,DC=FABRIKAM,DC=COM. ## Example 3: Set location properties for an OU ```powershellPS C:\\\u003e Set-ADOrganizationalUnit -Identity \"OU=AsiaPacific,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM\" -Country \"AU\" -StreetAddress \"45 Martens Place\" -City Balmoral -State QLD -PostalCode 4171 -Replace @{co=\"Australia\"} ``` This command sets the Country, City, State, PostalCode, and Country properties on the OU OU=AsiaPacific,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM. ## Example 4: Set a property for an OU in an AD LDS instance ```powershellPS C:\\\u003e Set-ADOrganizationalUnit -Identity \"OU=Managed,DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\" -Country \"UK\" ``` This command sets the Country property of the OU OU=Managed,DC=AppNC in an AD LDS instance. ## Example 5: Set a property for a piped OU ```powershellPS C:\\\u003e Get-ADOrganizationalUnit -Identity \"AccountingDepartment\" | Set-ADOrganizationalUnit -ManagedBy \"PattiFullerGroup\" ``` This command modifies the ManagedBy property for the AccountingDepartment OU. The command uses the Get-ADOrganizationalUnit cmdlet to get the AccountingDepartment OU, and then passes the object to the current cmdlet by using the pipeline operator. ## Example 6: Set a property for a local OU to modify an Active Directory OU ```powershellPS C:\\\u003e $OrganizationalUnit = Get-ADOrganizationalUnit -Identity \"AccountingDepartment\" PS C:\\\u003e $OrganizationalUnit.ManagedBy = \"PattiFullerGroup\" PS C:\\\u003e Set-ADOrganizationalUnit -Instance $OrganizationalUnit ``` This example modifies the ManagedBy property for the AccountingDepartment OU. The example modifies a local instance of the AccountingDepartment OU and then specifies the Instance parameter for the Set-ADOrganizationalUnit cmdlet as the local instance. ## Parameters ## -Add Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is: `-Add @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Remove, Replace, and Clear parameters together, the operations are performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -City Specifies the town or city. This parameter sets the City property of an OU object. The LDAP display name (ldapDisplayName) of this property is l. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Clear Specifies an array of object properties that are cleared in the directory. Use this parameter to clear one or more values of a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can modify more than one property by specifying a comma-separated list. The format for this parameter is: `-Clear Attribute1LDAPDisplayName, Attribute2LDAPDisplayName` When you use the Add, Remove, Replace, and Clear parameters together, the operations are performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Country Specifies the country or region code. This parameter sets the Country property of an OU object. The LDAP display name (ldapDisplayName) of this property is c. This value is not used by Windows 2000. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Description Specifies a description of the object. This parameter sets the value of the Description property for the OU object. The LDAP display name (ldapDisplayName) for this property is description. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -DisplayName Specifies the display name of the object. This parameter sets the DisplayName property of the OU object. The LDAP display name (ldapDisplayName) for this property is displayName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory object by providing one of the following property values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. Derived types, such as the following are also accepted: - Microsoft.ActiveDirectory.Management.ADGroup - Microsoft.ActiveDirectory.Management.ADUser - Microsoft.ActiveDirectory.Management.ADComputer - Microsoft.ActiveDirectory.Management.ADServiceAccount - Microsoft.ActiveDirectory.Management.ADFineGrainedPasswordPolicy - Microsoft.ActiveDirectory.Management.ADDomain ## Parameter properties Type:ADOrganizationalUnit Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Instance Specifies a modified copy of an OU object to use to update the actual Active Directory OU object. When this parameter is used, any modifications made to the modified copy of the object are also made to the corresponding Active Directory object. The cmdlet only updates the object properties that have changed. The Instance parameter can only update organizational unit objects that have been retrieved by using the Get-ADOrganizationalUnit cmdlet. When you specify the Instance parameter, you cannot specify other parameters that set properties on the object. ## Parameter properties Type:ADOrganizationalUnit Default value:None Supports wildcards:False DontShow:False ## Parameter sets Instance Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ManagedBy Specifies the user or group that manages the object by providing one of the following property values. Note: The identifier in parentheses is the LDAP display name for the property. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A SAM account name (sAMAccountName) This parameter sets the Active Directory attribute with an LDAP display name of managedBy. ## Parameter properties Type:ADPrincipal Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and when a default value can be determined, no further rules are evaluated. In AD DS environments, a default value for Partition are set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In AD LDS environments, a default value for Partition will be set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter does not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PostalCode Specifies the postal code or zip code. This parameter sets the PostalCode property of an OU object. The LDAP display name (ldapDisplayName) of this property is postalCode. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ProtectedFromAccidentalDeletion Specifies whether to prevent the object from being deleted. When this property is set to $True, you cannot delete the corresponding object without changing the value of the property. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Remove Specifies that the cmdlet remove values of an object property. Use this parameter to remove one or more values of a property that cannot be modified using a cmdlet parameter. To remove an object property, you must use the LDAP display name. You can remove more than one property by specifying a semicolon-separated list. The format for this parameter is: `-Remove @{Attribute1LDAPDisplayName=value[]; Attribute2LDAPDisplayName=value[]}` When you use the Add, Remove, Replace, and Clear parameters together, the operations are performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Replace Specifies values for an object property that will replace the current values. Use this parameter to replace one or more values of a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values, and more than one property by separating them using a semicolon. The format for this parameter is: `-Replace @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Remove, Replace, and Clear parameters together, the operations will be performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -State Specifies the state or province. This parameter sets the State property of an OU object. The LDAP display name (ldapDisplayName) of this property is st. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -StreetAddress Specifies the street address. This parameter sets the StreetAddress property of an OU object. The LDAP display name (ldapDisplayName) of this property is street. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADOrganizationalUnit An OU object is received by the Identity parameter. An organizational unit object that was retrieved by using the Get-ADOrganizationalUnit cmdlet and then modified is received by the Instance parameter. ## Outputs ## None or Microsoft.ActiveDirectory.Management.ADOrganizationalUnit Returns the modified OU object when the PassThru parameter is specified. By default, this cmdlet does not generate any output. ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. ## Related Links - Get-ADOrganizationalUnit - New-ADOrganizationalUnit - Remove-ADOrganizationalUnit ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "set-adorganizationalunit"
                                 ],
                        "last_updated":  "2025-07-28T17:14:57Z",
                        "title":  "Set-ADOrganizationalUnit (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adorganizationalunit",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "New-ADOrganizationalUnit"
                                    ],
                        "id":  "microsoft_docs_new-adorganizationalunit",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AuthType",
                                                            "City",
                                                            "Country",
                                                            "Credential",
                                                            "Description",
                                                            "DisplayName",
                                                            "Instance",
                                                            "ManagedBy",
                                                            "OtherAttributes",
                                                            "Path",
                                                            "PostalCode",
                                                            "ProtectedFromAccidentalDeletion",
                                                            "Server",
                                                            "State",
                                                            "StreetAddress"
                                                        ],
                                         "cmdlet_name":  "New-ADOrganizationalUnit"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# New-ADOrganizationalUnit (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## New-ADOrganizationalUnit Module: ActiveDirectory Module ## Creates an Active Directory organizational unit. ## Syntax ## Default (Default) ```powershellNew-ADOrganizationalUnit [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-City \u003cString\u003e] [-Country \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-Instance \u003cADOrganizationalUnit\u003e] [-ManagedBy \u003cADPrincipal\u003e] [-Name] \u003cString\u003e [-OtherAttributes \u003cHashtable\u003e] [-PassThru] [-Path \u003cString\u003e] [-PostalCode \u003cString\u003e] [-ProtectedFromAccidentalDeletion \u003cBoolean\u003e] [-Server \u003cString\u003e] [-State \u003cString\u003e] [-StreetAddress \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The New-ADOrganizationalUnit cmdlet creates an Active Directory organizational unit (OU). You can set commonly used OU property values by using the cmdlet parameters. Property values that are not associated with cmdlet parameters can be set by using the OtherAttributes parameter. You must set the Name parameter to create a new OU. If you do not specify the Path parameter, the cmdlet creates an OU under the default naming context (NC) head for the domain. The following methods describe how to create an object by using this cmdlet. Method 1: Use the New-ADOrganizationalUnit cmdlet, specify the required parameters, and set any additional property values by using the cmdlet parameters. Method 2: Use a template to create the new object. To do this, create a new OU object or get a copy of an existing OU object and set the Instance parameter to this object. The object provided to the Instance parameter is used as a template for the new object. You can override property values from the template by setting cmdlet parameters. For more information, see the Instance parameter description for this cmdlet. Method 3: Use the Import-Csv cmdlet with the New-ADOrganizationalUnit cmdlet to create multiple Active Directory OU objects. To do this, use the Import-Csv cmdlet to create the custom objects from a comma-separated value (CSV) file that contains a list of object properties. Then pass these objects through the pipeline to the New-ADOrganizationalUnit cmdlet to create the OU objects. ## Examples ## Example 1: Create an OU ```powershellPS C:\\\u003e New-ADOrganizationalUnit -Name \"UserAccounts\" -Path \"DC=FABRIKAM,DC=COM\" ``` This command creates an OU named UserAccounts that is protected from accidental deletion. Note that accidental protection is implicit. ## Example 2: Create an OU that is not protected from accidental deletion ```powershellPS C:\\\u003e New-ADOrganizationalUnit -Name \"UserAccounts\" -Path \"DC=FABRIKAM,DC=COM\" -ProtectedFromAccidentalDeletion $False ``` This command creates an OU named UserAccounts that is not protected from accidental deletion. ## Example 3: Create an OU that is protected from accidental deletion ```powershellPS C:\\\u003e New-ADOrganizationalUnit -Name \"UserAccounts\" -Path \"DC=FABRIKAM,DC=COM\" -OtherAttributes @{seeAlso=\"CN=HumanResourceManagers,OU=Groups,OU=Managed,DC=Fabrikam,DC=com\";managedBy=\"CN=TomC,DC=FABRIKAM,DC=COM\"} ``` This command creates an OU named UserAccounts that is protected from accidental deletion. The seeAlso and managedBy properties are set to specified values. ## Example 4: Create an OU from a template OU ```powershellPS C:\\\u003e $OuTemplate = Get-ADOrganizationalUnit -Identity \"OU=UserAccounts,DC=Fabrikam,DC=com\" -Properties seeAlso,managedBy PS C:\\\u003e New-ADOrganizationalUnit -Name \"TomCReports\" -Instance $OuTemplate ``` This command uses the data from the OU OU=UserAccounts,DC=Fabrikam,DC=com as a template for another OU. ## Example 5: Create an OU in an AD LDS instance ```powershellPS C:\\\u003e New-ADOrganizationalUnit -Name \"Managed\" -Path \"DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\" ``` This command creates an OU named Managed in an AD LDS instance. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -City Specifies the town or city. This parameter sets the City property of an OU object. The Lightweight Directory Access Protocol (LDAP) display name (ldapDisplayName) of this property is `l`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Country Specifies the country or region code. This parameter sets the Country property of an OU object. The LDAP display name (ldapDisplayName) of this property is `c`. This value is not used by Windows 2000. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as `User1` or `Domain01\\User01` or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Description Specifies a description of the object. This parameter sets the value of the Description property for the OU object. The LDAP display name (ldapDisplayName) for this property is `description`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -DisplayName Specifies the display name of the object. This parameter sets the DisplayName property of the OU object. The LDAP display name (ldapDisplayName) for this property is `displayName`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Instance Specifies an instance of an OU object to use as a template for a new OU object. You can use an instance of an existing OU object as a template or you can construct a new OU object by using the Windows PowerShell command line or by using a script. Method 1: Use an existing OU object as a template for a new object. To retrieve an instance of an existing OU object use Get-ADOrganizationalUnit. Then provide this object to the Instance parameter of the New-ADOrganizationalUnit cmdlet to create a new OU object. You can override property values of the new object by setting the appropriate parameters. Method 2: Create a new ADOrganizationalUnit object and set the property values by using the Windows PowerShell command line interface. Then pass this object to the Instance parameter of the New-ADOrganizationalUnit cmdlet to create the new Active Directory OU object. Note Specified attributes are not validated, so attempting to set attributes that do not exist or cannot be set raises an error. ## Parameter properties Type:ADOrganizationalUnit Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ManagedBy Specifies the user or group that manages the object by providing one of the following property values. Note: The identifier in parentheses is the LDAP display name for the property. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A SAM account name (sAMAccountName) This parameter sets the Active Directory attribute with an LDAP display name of `managedBy`. ## Parameter properties Type:ADPrincipal Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Name Specifies the name of the object. This parameter sets the Name property of the OU object. The LDAP display name (ldapDisplayName) of this property is `name`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:1 Mandatory:True Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OtherAttributes Specifies object attribute values for attributes that are not represented by cmdlet parameters. You can set one or more parameters at the same time with this parameter. If an attribute takes more than one value, you can assign multiple values. To identify an attribute, specify the LDAP display name (ldapDisplayName) defined for it in the Active Directory schema. To specify a single value for an attribute: `-OtherAttributes @{\u0027AttributeLDAPDisplayName\u0027=value}` To specify multiple values for an attribute, separate the values with a comma: `-OtherAttributes @{\u0027AttributeLDAPDisplayName\u0027=value1,value2,...}` To specify values for multiple attributes, separate the attributes with a semi-colon: `-OtherAttributes @{\u0027Attribute1LDAPDisplayName\u0027=value; \u0027Attribute2LDAPDisplayName\u0027=value1,value2;...}` ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Path Specifies the X.500 path of the OU or container where the new object is created. In many cases, a default value is used for the Path parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and when a default value can be determined, no further rules are evaluated. In Active Directory Domain Services (AD DS) environments, a default value for Path is set in the following cases: - If the cmdlet is run from an Active Directory PowerShell provider drive, the parameter is set to the current path of the provider drive. - If the cmdlet has a default path, this is used. For example: in New-ADUser, the Path parameter defaults to the Users container. - If none of the previous cases apply, the default value of Path is set to the default partition or naming context of the target domain. In AD LDS environments, a default value for Path is set in the following cases: - If the cmdlet is run from an Active Directory module for PowerShell provider drive, the parameter is set to the current path of the provider drive. - If the cmdlet has a default path, this is used. For example: in New-ADUser, the Path parameter defaults to the Users container. - If the target AD LDS instance has a default naming context, the default value of Path is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Path parameter does not take any default value. Note The Active Directory Provider cmdlets, such as New-Item, Remove-Item, Remove-ItemProperty, Rename-Item, and Set-ItemProperty, also contain a Path property. However, for the Active Directory Provider cmdlets, the Path parameter identifies the path of the actual object rather than the container. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PostalCode Specifies the postal code or zip code. This parameter sets the PostalCode property of an OU object. The LDAP display name (ldapDisplayName) of this property is `postalCode`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -ProtectedFromAccidentalDeletion Indicates whether to prevent the object from being deleted. When this property is set to $True, you cannot delete the corresponding object without changing the value of the property. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Server Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -State Specifies a state or province. This parameter sets the State property of an OU object. The LDAP display name (ldapDisplayName) of this property is `st`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -StreetAddress Specifies a street address. This parameter sets the StreetAddress property of an OU object. The LDAP display name (ldapDisplayName) of this property is `street`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADOrganizationalUnit An OU object that is a template for the new OU object is received by the Instance parameter. ## Outputs ## None or Microsoft.ActiveDirectory.Management.ADOrganizationalUnit Returns the new OU object when the PassThru parameter is specified. By default, this cmdlet does not generate any output. ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. ## Related Links - Get-ADOrganizationalUnit - Remove-ADOrganizationalUnit - Set-ADOrganizationalUnit ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "new-adorganizationalunit"
                                 ],
                        "last_updated":  "2025-07-28T17:14:58Z",
                        "title":  "New-ADOrganizationalUnit (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-adorganizationalunit",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Remove-ADOrganizationalUnit"
                                    ],
                        "id":  "microsoft_docs_remove-adorganizationalunit",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AuthType",
                                                            "Credential",
                                                            "Partition",
                                                            "Server"
                                                        ],
                                         "cmdlet_name":  "Remove-ADOrganizationalUnit"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Remove-ADOrganizationalUnit (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Remove-ADOrganizationalUnit Module: ActiveDirectory Module ## Removes an Active Directory organizational unit. ## Syntax ## Default (Default) ```powershellRemove-ADOrganizationalUnit [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Identity] \u003cADOrganizationalUnit\u003e [-Partition \u003cString\u003e] [-Recursive] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The Remove-ADOrganizationalUnit cmdlet removes an Active Directory organizational unit (OU). The Identity parameter specifies the organizational unit to remove. You can identify an organizational unit by its distinguished name or GUID. You can also set the parameter to an organizational unit object variable, such as `$\u003clocalOrganizationUnitObject\u003e` or you can pass an object through the pipeline to the Identity parameter. For example, you can use the Get-ADOrganizationalUnit cmdlet to retrieve the object and then pass the object through the pipeline to the Remove-ADOrganizationalUnit cmdlet. If the object you want to remove has child objects, you must specify the Recursive parameter. If the ProtectedFromAccidentalDeletion property of the organizational unit object is set to true, the cmdlet returns a terminating error. For AD LDS environments, the Partition parameter must be specified except in the following two conditions: - The cmdlet is run from an Active Directory provider drive. - A default naming context or partition is defined for the AD LDS environment. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. ## Examples ## Example 1: Remove an OU and its children ```powershellPS C:\\\u003e Remove-ADOrganizationalUnit -Identity \"OU=Accounting,DC=FABRIKAM,DC=COM\" -Recursive Are you sure you want to remove the item and all its children? Performing recursive remove on Target: \u0027OU=Accounting,DC=Fabrikam,DC=com\u0027. [Y] Yes [A] Yes to All [N] No [L] No to All [S] Suspend [?] Help (default is \"Y\"):y ``` This command removes an OU and all of its children. If the OU is protected from deletion, then the OU and its children are not deleted. If the OU is not protected but any of the children are, then the OU and its children are deleted. ## Example 2: Remove on OU by its GUID ```powershellPS C:\\\u003e Remove-ADOrganizationalUnit -Identity \"1b228aa5-2c14-48b8-ad8a-2685dc22e055\" -Confirm:$False ``` This command removes an OU that is specified by its objectGUID and suppresses the confirmation prompt. ## Example 3: Remove a specified OU ```powershellPS C:\\\u003e Remove-ADOrganizationalUnit -Identity \"OU=Accounting,DC=FABRIKAM,DC=COM\" Confirm Are you sure you want to perform this action? Performing operation \"Remove\" on Target \"OU=Accounting,DC=Fabrikam,DC=com\". [Y] Yes [A] Yes to All [N] No [L] No to All [S] Suspend [?] Help (default is \"Y\"):y ``` This command removes the Accounting OU. ## Example 4: Remove an OU from an AD LDS instance ```powershellPS C:\\\u003e Remove-ADOrganizationalUnit -Identity \"OU=Managed,DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\" -Confirm:$False ``` This command removes an OU from an AD LDS instance. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory group object by providing one of the following values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: -A distinguished name -A GUID (objectGUID) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADOrganizationalUnit Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Identity parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated. In Active Directory Domain Services (AD DS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter does not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Recursive Indicates that this cmdlet removes the OU and any child items it contains. You must specify this parameter to remove an OU that is not empty. Note: Specifying this parameter removes all child objects of an OU that are marked with ProtectedFromAccidentalDeletion. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADOrganizationalUnit An ADOrganizationalUnit object is received by the Identity parameter. ## Outputs ## None ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. - By default, this cmdlet has the Confirm parameter set, which prompts you to confirm before a removal of the specified object type can occur. To bypass prompting for confirmation before removal, you can specify `-Confirm:$False` when using this cmdlet. ## Related Links - Get-ADOrganizationalUnit - New-ADOrganizationalUnit - Set-ADOrganizationalUnit ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "remove-adorganizationalunit"
                                 ],
                        "last_updated":  "2025-07-28T17:14:59Z",
                        "title":  "Remove-ADOrganizationalUnit (ActiveDirectory)"
                    }
                ],
    "scraped_at":  "2025-07-28T17:14:43Z"
}
