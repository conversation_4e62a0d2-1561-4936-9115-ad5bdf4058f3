﻿{
    "source":  "microsoft_docs_clean",
    "schema_version":  "1.0",
    "total_entries":  3,
    "entries":  [
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Get-ADUser"
                                    ],
                        "id":  "microsoft_docs_get-aduser",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "Filter",
                                                            "AuthType",
                                                            "Credential",
                                                            "Properties",
                                                            "ResultPageSize",
                                                            "ResultSetSize",
                                                            "SearchBase",
                                                            "SearchScope",
                                                            "Server",
                                                            "Partition",
                                                            "LDAPFilter"
                                                        ],
                                         "cmdlet_name":  "Get-ADUser"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Get-ADUser (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Get-ADUser Module: ActiveDirectory Module ## Gets one or more Active Directory users. ## Syntax ## Filter (Default) ```powershellGet-ADUser -Filter \u003cString\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Properties \u003cString[]\u003e] [-ResultPageSize \u003cInt32\u003e] [-ResultSetSize \u003cInt32\u003e] [-SearchBase \u003cString\u003e] [-SearchScope \u003cADSearchScope\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Identity ```powershellGet-ADUser [-Identity] \u003cADUser\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Partition \u003cString\u003e] [-Properties \u003cString[]\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## LdapFilter ```powershellGet-ADUser -LDAPFilter \u003cString\u003e [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-Properties \u003cString[]\u003e] [-ResultPageSize \u003cInt32\u003e] [-ResultSetSize \u003cInt32\u003e] [-SearchBase \u003cString\u003e] [-SearchScope \u003cADSearchScope\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The Get-ADUser cmdlet gets a specified user object or performs a search to get multiple user objects. The Identity parameter specifies the Active Directory user to get. You can identify a user by its distinguished name (DN), GUID, security identifier (SID), or Security Account Manager (SAM) account name. You can also set the parameter to a user object variable such as `$\u003clocalUserObject\u003e` or pass a user object through the pipeline to the Identity parameter. To search for and retrieve more than one user, use the Filter or LDAPFilter parameters. The Filter parameter uses the PowerShell Expression Language to write query strings for Active Directory. PowerShell Expression Language syntax provides rich type-conversion support for value types received by the Filter parameter. For more information about the Filter parameter syntax, type `Get-Help about_ActiveDirectory_Filter`. If you have existing Lightweight Directory Access Protocol (LDAP) query strings, you can use the LDAPFilter parameter. This cmdlet retrieves a default set of user object properties. To retrieve additional properties use the Properties parameter. For more information about how to determine the properties for user objects, see the Properties parameter description. ## Examples ## Example 1: Get all of the users in a container ```powershellPS C:\\\u003e Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\" ``` This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM. ## Example 2: Get a filtered list of users ```powershellPS C:\\\u003e Get-ADUser -Filter \u0027Name -like \"*SvcAccount\"\u0027 | Format-Table Name,SamAccountName -A ``` ```powershellName SamAccountName ---- -------------- SQL01 SvcAccount SQL01 SQL02 SvcAccount SQL02 IIS01 SvcAccount IIS01 ``` This command gets all users that have a name that ends with SvcAccount. ## Example 3: Get all of the properties for a specified user ```powershellPS C:\\\u003e Get-ADUser -Identity ChewDavid -Properties * ``` ```powershellSurname : David Name : Chew David UserPrincipalName : GivenName : David Enabled : False SamAccountName : ChewDavid ObjectClass : user SID : S-1-5-21-**********-**********-**********-3544 ObjectGUID : e1418d64-096c-4cb0-b903-ebb66562d99d DistinguishedName : CN=Chew David,OU=NorthAmerica,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM ``` This command gets all of the properties of the user with the SAM account name ChewDavid. ## Example 4: Get a specified user ```powershellPS C:\\\u003e Get-ADUser -Filter \"Name -eq \u0027ChewDavid\u0027\" -SearchBase \"DC=AppNC\" -Properties \"mail\" -Server lds.Fabrikam.com:50000 ``` This command gets the user with the name ChewDavid in the Active Directory Lightweight Directory Services (AD LDS) instance. ## Example 5: Get all enabled user accounts ```powershellC:\\PS\u003e Get-ADUser -LDAPFilter \u0027(!userAccountControl:1.2.840.113556.1.4.803:=2)\u0027 ``` This command gets all enabled user accounts in Active Directory using an LDAP filter. ## Parameters ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the Get-Credential cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Filter Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Expression Language syntax. The PowerShell Expression Language syntax provides rich type-conversion support for value types received by the Filter parameter. The syntax uses an in-order representation, which means that the operator is placed between the operand and the value. For more information about the Filter parameter, type `Get-Help about_ActiveDirectory_Filter`. Syntax: The following syntax uses Backus-Naur form to show how to use the PowerShell Expression Language for this parameter. \u003cfilter\u003e ::= \"{\" \u003cFilterComponentList\u003e \"}\" \u003cFilterComponentList\u003e ::= \u003cFilterComponent\u003e | \u003cFilterComponent\u003e \u003cJoinOperator\u003e \u003cFilterComponent\u003e | \u003cNotOperator\u003e \u003cFilterComponent\u003e \u003cFilterComponent\u003e ::= \u003cattr\u003e \u003cFilterOperator\u003e \u003cvalue\u003e | \"(\" \u003cFilterComponent\u003e \")\" \u003cFilterOperator\u003e ::= \"-eq\" | \"-le\" | \"-ge\" | \"-ne\" | \"-lt\" | \"-gt\"| \"-approx\" | \"-bor\" | \"-band\" | \"-recursivematch\" | \"-like\" | \"-notlike\" \u003cJoinOperator\u003e ::= \"-and\" | \"-or\" \u003cNotOperator\u003e ::= \"-not\" \u003cattr\u003e ::= \u003cPropertyName\u003e | \u003cLDAPDisplayName of the attribute\u003e \u003cvalue\u003e::= \u003ccompare this value with an \u003cattr\u003e by using the specified \u003cFilterOperator\u003e\u003e For a list of supported types for \u003cvalue\u003e, type `Get-Help about_ActiveDirectory_ObjectModel`. Note: For String parameter type, PowerShell will cast the filter query to a string while processing the command. When using a string variable as a value in the filter component, make sure that it complies with the PowerShell Quoting Rules. For example, if the filter expression is double-quoted, the variable should be enclosed using single quotation marks: Get-ADUser -Filter \"Name -like \u0027$UserName\u0027\". On the contrary, if curly braces are used to enclose the filter, the variable should not be quoted at all: Get-ADUser -Filter {Name -like $UserName}. Note: PowerShell wildcards other than *, such as ?, are not supported by the Filter syntax. Note: To query using LDAP query strings, use the LDAPFilter parameter. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory user object by providing one of the following property values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A SAM account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -LDAPFilter Specifies an LDAP query string that is used to filter Active Directory objects. You can use this parameter to run your existing LDAP queries. The Filter parameter syntax supports the same functionality as the LDAP syntax. For more information, see the Filter parameter description or type `Get-Help about_ActiveDirectory_Filter`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets LdapFilter Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first, and when a default value can be determined, no further rules are evaluated. In AD DS environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In AD LDS environments, a default value for Partition is set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter does not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Properties Specifies the properties of the output object to retrieve from the server. Use this parameter to retrieve properties that are not included in the default set. Specify properties for this parameter as a comma-separated list of names. To display all of the attributes that are set on the object, specify * (asterisk). To specify an individual extended property, use the name of the property. For properties that are not default or extended properties, you must specify the LDAP display name of the attribute. To retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the Get-Member cmdlet. ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False Aliases:Property ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ResultPageSize Specifies the number of objects to include in one page for an Active Directory Domain Services query. The default is 256 objects per page. ## Parameter properties Type:Int32 Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ResultSetSize Specifies the maximum number of objects to return for an Active Directory Domain Services query. If you want to receive all of the objects, set this parameter to $Null (null value). You can use Ctrl+C to stop the query and return of objects. The default is $Null. ## Parameter properties Type:Int32 Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SearchBase Specifies an Active Directory path to search under. When you run a cmdlet from an Active Directory provider drive, the default value of this parameter is the current path of the drive. When you run a cmdlet outside of an Active Directory provider drive against an AD DS target, the default value of this parameter is the default naming context of the target domain. When you run a cmdlet outside of an Active Directory provider drive against an AD LDS target, the default value is the default naming context of the target LDS instance if one has been specified by setting the msDS-defaultNamingContext property of the Active Directory directory service agent (DSA) object (nTDSDSA) for the AD LDS instance. If no default naming context has been specified for the target AD LDS instance, then this parameter has no default value. When the value of the SearchBase parameter is set to an empty string and you are connected to a GC port, all partitions are searched. If the value of the SearchBase parameter is set to an empty string and you are not connected to a GC port, an error is thrown. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SearchScope Specifies the scope of an Active Directory search. The acceptable values for this parameter are: - Base or 0 - OneLevel or 1 - Subtree or 2 A SearchScope with a Base value searches only for the given user. If an OU is specified in the SearchBase parameter, no user will be returned by, for example, a specified Filter statement. A OneLevel query searches the immediate children of that path or object. This option only works when an OU is given as the SearchBase. If a user is given, no results are returned. A Subtree query searches the current path or object and all children of that path or object. ## Parameter properties Type:ADSearchScope Default value:None Accepted values:Base, OneLevel, Subtree Supports wildcards:False DontShow:False ## Parameter sets Filter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False LdapFilter Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance. Domain name values: - Fully qualified domain name (FQDN) - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for the Server parameter is determined by one of the following methods in the order that they are listed: - By using Server value from objects passed through the pipeline. - By using the server information associated with the Active Directory PowerShell provider drive, when running under that drive. - By using the domain of the computer running PowerShell. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADUser A user object is received by the Identity parameter. ## Outputs ## Microsoft.ActiveDirectory.Management.ADUser Returns one or more user objects. This cmdlet returns a default set of ADUser property values. To retrieve additional ADUser properties, use the Properties parameter. To get a list of the default set of properties of an ADUser object, use the following command: `Get-ADUser`\u003cuser\u003e`| Get-Member` To get a list of the most commonly used properties of an ADUser object, use the following command: `Get-ADUser`\u003cuser\u003e`-Properties Extended | Get-Member` To get a list of all the properties of an ADUser object, use the following command: `Get-ADUser`\u003cuser\u003e`-Properties * | Get-Member` ## Notes - This cmdlet does not work with an Active Directory snapshot. ## Related Links - New-ADUser - Remove-ADUser - Set-ADUser ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "get-aduser"
                                 ],
                        "last_updated":  "2025-07-28T17:13:30Z",
                        "title":  "Get-ADUser (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/set-aduser",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Set-ADUser"
                                    ],
                        "id":  "microsoft_docs_set-aduser",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AccountExpirationDate",
                                                            "AccountNotDelegated",
                                                            "Add",
                                                            "AllowReversiblePasswordEncryption",
                                                            "AuthenticationPolicy",
                                                            "AuthenticationPolicySilo",
                                                            "AuthType",
                                                            "CannotChangePassword",
                                                            "Certificates",
                                                            "ChangePasswordAtLogon",
                                                            "City",
                                                            "Clear",
                                                            "Company",
                                                            "CompoundIdentitySupported",
                                                            "Country",
                                                            "Credential",
                                                            "Department",
                                                            "Description",
                                                            "DisplayName",
                                                            "Division",
                                                            "EmailAddress",
                                                            "EmployeeID",
                                                            "EmployeeNumber",
                                                            "Enabled",
                                                            "Fax",
                                                            "GivenName",
                                                            "HomeDirectory",
                                                            "HomeDrive",
                                                            "HomePage",
                                                            "HomePhone",
                                                            "Initials",
                                                            "KerberosEncryptionType",
                                                            "LogonWorkstations",
                                                            "Manager",
                                                            "MobilePhone",
                                                            "Office",
                                                            "OfficePhone",
                                                            "Organization",
                                                            "OtherName",
                                                            "Partition",
                                                            "PasswordNeverExpires",
                                                            "PasswordNotRequired",
                                                            "POBox",
                                                            "PostalCode",
                                                            "PrincipalsAllowedToDelegateToAccount",
                                                            "ProfilePath",
                                                            "Remove",
                                                            "Replace",
                                                            "SamAccountName",
                                                            "ScriptPath",
                                                            "Server",
                                                            "ServicePrincipalNames",
                                                            "SmartcardLogonRequired",
                                                            "State",
                                                            "StreetAddress",
                                                            "Surname",
                                                            "Title",
                                                            "TrustedForDelegation",
                                                            "UserPrincipalName",
                                                            "Instance"
                                                        ],
                                         "cmdlet_name":  "Set-ADUser"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Set-ADUser (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## Set-ADUser Module: ActiveDirectory Module ## Modifies an Active Directory user. ## Syntax ## Identity ```powershellSet-ADUser [-Identity] \u003cADUser\u003e [-WhatIf] [-Confirm] [-AccountExpirationDate \u003cDateTime\u003e] [-AccountNotDelegated \u003cBoolean\u003e] [-Add \u003cHashtable\u003e] [-AllowReversiblePasswordEncryption \u003cBoolean\u003e] [-AuthenticationPolicy \u003cADAuthenticationPolicy\u003e] [-AuthenticationPolicySilo \u003cADAuthenticationPolicySilo\u003e] [-AuthType \u003cADAuthType\u003e] [-CannotChangePassword \u003cBoolean\u003e] [-Certificates \u003cHashtable\u003e] [-ChangePasswordAtLogon \u003cBoolean\u003e] [-City \u003cString\u003e] [-Clear \u003cString[]\u003e] [-Company \u003cString\u003e] [-CompoundIdentitySupported \u003cBoolean\u003e] [-Country \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Department \u003cString\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-Division \u003cString\u003e] [-EmailAddress \u003cString\u003e] [-EmployeeID \u003cString\u003e] [-EmployeeNumber \u003cString\u003e] [-Enabled \u003cBoolean\u003e] [-Fax \u003cString\u003e] [-GivenName \u003cString\u003e] [-HomeDirectory \u003cString\u003e] [-HomeDrive \u003cString\u003e] [-HomePage \u003cString\u003e] [-HomePhone \u003cString\u003e] [-Initials \u003cString\u003e] [-KerberosEncryptionType \u003cADKerberosEncryptionType\u003e] [-LogonWorkstations \u003cString\u003e] [-Manager \u003cADUser\u003e] [-MobilePhone \u003cString\u003e] [-Office \u003cString\u003e] [-OfficePhone \u003cString\u003e] [-Organization \u003cString\u003e] [-OtherName \u003cString\u003e] [-Partition \u003cString\u003e] [-PassThru] [-PasswordNeverExpires \u003cBoolean\u003e] [-PasswordNotRequired \u003cBoolean\u003e] [-POBox \u003cString\u003e] [-PostalCode \u003cString\u003e] [-PrincipalsAllowedToDelegateToAccount \u003cADPrincipal[]\u003e] [-ProfilePath \u003cString\u003e] [-Remove \u003cHashtable\u003e] [-Replace \u003cHashtable\u003e] [-SamAccountName \u003cString\u003e] [-ScriptPath \u003cString\u003e] [-Server \u003cString\u003e] [-ServicePrincipalNames \u003cHashtable\u003e] [-SmartcardLogonRequired \u003cBoolean\u003e] [-State \u003cString\u003e] [-StreetAddress \u003cString\u003e] [-Surname \u003cString\u003e] [-Title \u003cString\u003e] [-TrustedForDelegation \u003cBoolean\u003e] [-UserPrincipalName \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Instance ```powershellSet-ADUser -Instance \u003cADUser\u003e [-WhatIf] [-Confirm] [-AuthType \u003cADAuthType\u003e] [-Credential \u003cPSCredential\u003e] [-PassThru] [-SamAccountName \u003cString\u003e] [-Server \u003cString\u003e] [\u003cCommonParameters\u003e] ``` ## Description The `Set-ADUser` cmdlet modifies the properties of an Active Directory user. You can modify commonly used property values by using the cmdlet parameters. You can set property values that are not associated with cmdlet parameters by using the Add, Remove, Replace, and Clear parameters. The Identity parameter specifies the Active Directory user to modify. You can identify a user by its distinguished name, GUID, security identifier (SID), or Security Account Manager (SAM) account name. You can also set the Identity parameter to an object variable such as `$\u003clocalUserObject\u003e`, or you can pass an object through the pipeline to the Identity parameter. For example, you can use the Get-ADUser cmdlet to retrieve a user object and then pass the object through the pipeline to the Set-ADUser cmdlet. The Instance parameter provides a way to update a user object by applying the changes made to a copy of the object. When you set the Instance parameter to a copy of an Active Directory user object that has been modified, the Set-ADUser cmdlet makes the same changes to the original user object. To get a copy of the object to modify, use the Get-ADUser object. The Identity parameter is not allowed when you use the Instance parameter. For more information about the Instance parameter, see the Instance parameter description. Accounts created with the New-ADUser cmdlet are disabled if no password is provided. For AD LDS environments, the Partition parameter must be specified except in the following two conditions: - The cmdlet is run from an Active Directory provider drive. - A default naming context or partition is defined for the AD LDS environment. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. ## Examples ## Example 1: Set properties for a user ```powershell$params = @{ Identity = \u0027ChewDavid\u0027 HomePage = \u0027http://fabrikam.com/employees/ChewDavid\u0027 LogonWorkstations = \u0027ChewDavid-DSKTOP,ChewDavid-LPTOP\u0027 } Set-ADUser @params ``` This command sets the specified user\u0027s homepage property to http://fabrikam.com/employees/ChewDavid and the LogonWorkstations property to ChewDavid-DSKTOP,ChewDavid-LPTOP. ## Example 2: Set properties for multiple users ```powershellPS C:\\\u003e Get-ADUser -Filter \u0027Name -like \"*\"\u0027 -SearchBase \u0027OU=HumanResources,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 -Properties DisplayName | % {Set-ADUser $_ -DisplayName ($_.Surname + \u0027 \u0027 + $_.GivenName)} ``` This command gets all the users in the directory that are located in the OU=HumanResources,OU=UserAccounts,DC=FABRIKAM,DC=COM organizational unit. The command sets the DisplayName property on these user objects to the concatenation of the Surname property and the GivenName property. ## Example 3: Set properties ```powershellPS C:\\\u003e Set-ADUser -Identity GlenJohn -Replace @{title=\"director\";mail=\"<EMAIL>\"} ``` This command sets the specified user\u0027s title property to director and the mail <NAME_EMAIL>. ## Example 4: Modify a user otherMailbox property ```powershellPS C:\\\u003e Set-ADUser -Identity GlenJohn -Remove @{otherMailbox=\"glen.john\"} -Add @{url=\"fabrikam.com\"} -Replace @{title=\"manager\"} -Clear description ``` This command modifies the user with the SAM account name GlenJohn\u0027s object by removing glen.john from the otherMailbox property, adding fabrikam.com to the url property, replacing the title property with manager, and clearing the description property. ## Example 5: Set user properties to a local instance ```powershellPS C:\\\u003e $User = Get-ADUser -Identity GlenJohn -Properties mail,department PS C:\\\u003e $User.mail = \"<EMAIL>\" PS C:\\\u003e $User.department = \"Accounting\" PS C:\\\u003e Set-ADUser -Instance $User ``` This example sets the mail and department properties on the user object with the SAM account name GlenJohn by using the Instance parameter. ## Example 6: Set attributes for a user ```powershellPS C:\\\u003e $Hours = New-Object byte[] 21 PS C:\\\u003e $Hours[5] = 255; $Hours[8] = 255; $Hours[11] = 255; $Hours[14] = 255; $Hours[17] = 255; PS C:\\\u003e $Hours[6] = 1; $Hours[9] = 1; $Hours[12] = 1; $Hours[15] = 1; $Hours[18] = 1; PS C:\\\u003e $ReplaceHashTable = New-Object HashTable PS C:\\\u003e $ReplaceHashTable.Add(\"logonHours\", $Hours) PS C:\\\u003e $ReplaceHashTable.Add(\"description\", \"Sarah Davis can only logon from Monday through Friday from 8:00 AM to 5:00 PM\") PS C:\\\u003e Set-ADUser -Identity \"SarahDavis\" -Replace $ReplaceHashTable ``` This example sets the user logon hours to Monday through Friday from 8:00 AM to 5:00 PM and adds a description. It updates the logonHours attribute with the specified byte array and the description attribute with the specified string. ## Example 7: Set a property for a user ```powershellPS C:\\\u003e $Manager = Get-ADUser -Identity GlenJohn -Server Corp-DC01 PS C:\\\u003e Set-ADUser -Identity ChewDavid -Manager $Manager -Server Branch-DC02 ``` This example sets the Manager property for the user with the SAM account name of ChewDavid where the manager, GlenJohn, is a user in another domain. ## Example 8: Get a user and set a property ```powershellPS C:\\\u003e Get-ADUser -Identity \"DavidChew\" | Set-ADUser -Manager \"ElisaDaugherty\" ``` This command modifies the Manager property for the DavidChew user. The command uses the Get-ADUser cmdlet to get the user DavidChew, and then passes the object to the current cmdlet by using the pipeline operator. ## Parameters ## -AccountExpirationDate Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires. Use the DateTime syntax when you specify this parameter. Time is assumed to be local time unless otherwise specified. When a time value is not specified, the time is assumed to 12:00:00 AM local time. When a date is not specified, the date is assumed to be the current date. ## Parameter properties Type:DateTime Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AccountNotDelegated Indicates whether the security context of the user is delegated to a service. When this parameter is set to $True, the security context of the account is not delegated to a service even when the service account is set as trusted for Kerberos delegation. This parameter sets the AccountNotDelegated property for an Active Directory account. This parameter also sets the ADS_UF_NOT_DELEGATED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Add Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values, and more than one property by separating them using a semicolon. If any of the properties have a null or empty value the cmdlet will return an error. The format for this parameter is: `-Add @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Remove, Replace, and Clear parameters together, the operations will be performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AllowReversiblePasswordEncryption Indicates whether reversible password encryption is allowed for the account. This parameter sets the AllowReversiblePasswordEncryption property of the account. This parameter also sets the ADS_UF_ENCRYPTED_TEXT_PASSWORD_ALLOWED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthenticationPolicy Specifies an Active Directory Domain Services authentication policy object. Specify the authentication policy object in one of the following formats: - Distinguished name - GUID - Name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicy Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthenticationPolicySilo Specifies an Active Directory Domain Services authentication policy silo object. Specify the authentication policy silo object in one of the following formats: - Distinguished name - GUID - Name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicySilo Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - Negotiate or 0 - Basic or 1 The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -CannotChangePassword Indicates whether the account password can be changed. This parameter sets the CannotChangePassword property of an account. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Certificates Specifies an array of certificates. The cmdlet modifies the DER-encoded X.509v3 certificates of the account. These certificates include the public key certificates issued to this account by the Microsoft Certificate Service. This parameter sets the Certificates property of the account object. The Lightweight Directory Access Protocol (LDAP) display name (ldapDisplayName) for this property is userCertificate. To add values: `-Certificates @{Add=value1,value2,...}` To remove values: `-Certificates @{Remove=value3,value4,...}` To replace values: `-Certificates @{Replace=value1,value2,...}` To clear all values: `-Certificates $Null` You can specify more than one operation by using a list separated by semicolons. For example, use the following syntax to add and remove Certificates values: `-Certificates @{Add=value1;Remove=value3}` The operators are applied in the following sequence: - Remove - Add - Replace ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ChangePasswordAtLogon Indicates whether a password must be changed during the next logon attempt. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -City Specifies the user\u0027s town or city. This parameter sets the City property of a user object. The LDAP display name (ldapDisplayName) of this property is l. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Clear Specifies an array of object properties that are cleared in the directory. Use this parameter to clear one or more values of a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can modify more than one property by specifying a comma-separated list. The format for this parameter is: `-Clear Attribute1LDAPDisplayName, Attribute2LDAPDisplayName` When you use the Add, Remove, Replace, and Clear parameters together, the operations are performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Company Specifies the user\u0027s company. This parameter sets the Company property of a user object. The LDAP display name (ldapDisplayName) of this property is company. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -CompoundIdentitySupported Indicates whether an account supports Kerberos service tickets which includes the authorization data for the user\u0027s device. This value sets the compound identity supported flag of the Active Directory msDS-SupportedEncryptionTypes attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 Warning Domain-joined Windows systems and services such as clustering manage their own msDS-SupportedEncryptionTypes attribute. Therefore any changes to the flag on the msDS-SupportedEncryptionTypes attribute are overwritten by the service or system that manages the setting. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Country Specifies the country or region code for the user\u0027s language of choice. This parameter sets the Country property of a user object. The LDAP display name (ldapDisplayName) of this property is c. This value is not used by Windows 2000. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as `User1` or `Domain01\\User01` or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the `Get-Credential` cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Department Specifies the user\u0027s department. This parameter sets the Department property of a user object. The LDAP display name (ldapDisplayName) of this property is department. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Description Specifies a description of the object. This parameter sets the value of the Description property for the user object. The LDAP display name (ldapDisplayName) for this property is description. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -DisplayName Specifies the display name of the object. This parameter sets the DisplayName property of the user object. The LDAP display name (ldapDisplayName) for this property is displayName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Division Specifies the user\u0027s division. This parameter sets the Division property of a user object. The LDAP display name (ldapDisplayName) of this property is division. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -EmailAddress Specifies the user\u0027s e-mail address. This parameter sets the EmailAddress property of a user object. The LDAP display name (ldapDisplayName) of this property is mail. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -EmployeeID Specifies the user\u0027s employee ID. This parameter sets the EmployeeID property of a user object. The LDAP display name (ldapDisplayName) of this property is employeeID. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -EmployeeNumber Specifies the user\u0027s employee number. This parameter sets the EmployeeNumber property of a user object. The LDAP display name (ldapDisplayName) of this property is employeeNumber. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Enabled Indicates whether an account is enabled. An enabled account requires a password. This parameter sets the Enabled property for an account object. This parameter also sets the ADS_UF_ACCOUNTDISABLE flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Fax Specifies the user\u0027s fax phone number. This parameter sets the Fax property of a user object. The LDAP display name (ldapDisplayName) of this property is facsimileTelephoneNumber. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -GivenName Specifies the user\u0027s given name. This parameter sets the GivenName property of a user object. The LDAP display name (ldapDisplayName) of this property is givenName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -HomeDirectory Specifies a user\u0027s home directory. This parameter sets the HomeDirectory property of a user object. The LDAP display name (ldapDisplayName) for this property is homeDirectory. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -HomeDrive Specifies a drive that is associated with the UNC path defined by the HomeDirectory property. The drive letter is specified as `\u003cDriveLetter\u003e`: where `\u003cDriveLetter\u003e` indicates the letter of the drive to associate. The `\u003cDriveLetter\u003e` must be a single, uppercase letter and the colon is required. This parameter sets the HomeDrive property of the user object. The LDAP display name (ldapDisplayName) for this property is homeDrive. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -HomePage Specifies the URL of the home page of the object. This parameter sets the homePage property of an Active Directory object. The LDAP display name (ldapDisplayName) for this property is wWWHomePage. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -HomePhone Specifies the user\u0027s home telephone number. This parameter sets the HomePhone property of a user. The LDAP display name (ldapDisplayName) of this property is homePhone. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Identity Specifies an Active Directory user object by providing one of the following property values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A SAM account name (sAMAccountName) The cmdlet searches the default naming context or partition to find the object. If two or more objects are found, the cmdlet returns a non-terminating error. This parameter can also get this object through the pipeline or you can set this parameter to an object instance. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:0 Mandatory:True Value from pipeline:True Value from pipeline by property name:False Value from remaining arguments:False ## -Initials Specifies the initials that represent part of a user\u0027s name. You can use this value for the user\u0027s middle initial. This parameter sets the Initials property of a user. The LDAP display name (ldapDisplayName) of this property is initials. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Instance Specifies an ADUser object that identifies the Active Directory user object that should be modified and the set of changes that should be made to that object. When this parameter is specified, any modifications made to the ADUser object are also made to the corresponding Active Directory object. The cmdlet only updates the object properties that have changed. The ADUser object specified as the value of the Instance parameter must have been retrieved by using the `Get-ADUser` cmdlet. When you specify the Instance parameter, you cannot specify other parameters that set individual properties on the object. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets Instance Position:Named Mandatory:True Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -KerberosEncryptionType Specifies whether an account supports Kerberos encryption types which are used during creation of service tickets. This value sets the encryption types supported flags of the Active Directory msDS-SupportedEncryptionTypes attribute. The acceptable values for this parameter are: - `None` - `DES` - `RC4` - `AES128` - `AES256` `None` removes all encryption types from the account, resulting in the KDC being unable to issue service tickets for services using the account. DES is a weak encryption type that is not supported by default since Windows 7 and Windows Server 2008 R2. Warning Domain-joined Windows systems and services such as clustering manage their own msDS-SupportedEncryptionTypes attribute. Therefore any changes to the flag on the msDS-SupportedEncryptionTypes attribute are overwritten by the service or system that manages the setting. ## Parameter properties Type:ADKerberosEncryptionType Default value:None Accepted values:None, DES, RC4, AES128, AES256 Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -LogonWorkstations Specifies the computers that the user can access. To specify more than one computer, create a single comma-separated list. You can identify a computer by using the Security Account Manager (SAM) account name (sAMAccountName) or the DNS host name of the computer. The SAM account name is the same as the NetBIOS name of the computer. The LDAP display name (ldapDisplayName) for this property is userWorkStations. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Manager Specifies the user\u0027s manager. This parameter sets the Manager property of a user object. This parameter is set by providing one of the following property values. Note: The identifier in parentheses is the LDAP display name for the property. The acceptable values for this parameter are: - A distinguished name - A GUID (objectGUID) - A security identifier (objectSid) - A SAM account name (sAMAccountName) The LDAP display name (ldapDisplayName) of this property is manager. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -MobilePhone Specifies the user\u0027s mobile phone number. This parameter sets the MobilePhone property of a user object. The LDAP display name (ldapDisplayName) of this property is mobile. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Office Specifies the location of the user\u0027s office or place of business. This parameter sets the Office property of a user object. The LDAP display name (ldapDisplayName) of this property is physicalDeliveryOfficeName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -OfficePhone Specifies the user\u0027s office telephone number. This parameter sets the OfficePhone property of a user object. The LDAP display name (ldapDisplayName) of this property is telephoneNumber. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Organization Specifies the user\u0027s organization. This parameter sets the Organization property of a user object. The LDAP display name (ldapDisplayName) of this property is o. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -OtherName Specifies a name in addition to a user\u0027s given name and surname, such as the user\u0027s middle name. This parameter sets the OtherName property of a user object. The LDAP display name (ldapDisplayName) of this property is middleName. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Partition Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter. In many cases, a default value is used for the Partition parameter if no value is specified. The rules for determining the default value are given below. Note that rules listed first are evaluated first and when a default value can be determined, no further rules are evaluated. In AD DS environments, a default value for Partition are set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. In AD LDS environments, a default value for Partition will be set in the following cases: - If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name. - If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive. - If the target AD LDS instance has a default naming context, the default value of Partition is set to the default naming context. To specify a default naming context for an AD LDS environment, set the msDS-defaultNamingContext property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Partition parameter does not take any default value. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PasswordNeverExpires Specifies whether the password of an account can expire. This parameter sets the PasswordNeverExpires property of an account object. This parameter also sets the ADS_UF_DONT_EXPIRE_PASSWD flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$False` or `0` - `$True` or `1` Note This parameter cannot be set to `$True` or `1` for an account that also has the ChangePasswordAtLogon property set to `$True`. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PasswordNotRequired Specifies whether the account requires a password. This parameter sets the PasswordNotRequired property of an account, such as a user or computer account. This parameter also sets the ADS_UF_PASSWD_NOTREQD flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$False` or `0` - `$True` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -POBox Specifies the user\u0027s post office box number. This parameter sets the POBox property of a user object. The LDAP display name (ldapDisplayName) of this property is postOfficeBox. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PostalCode Specifies the postal code or zip code. This parameter sets the PostalCode property of a user object. The LDAP display name (ldapDisplayName) of this property is `postalCode`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PrincipalsAllowedToDelegateToAccount Specifies an array of principal objects. This parameter sets the msDS-AllowedToActOnBehalfOfOtherIdentity attribute of a computer account object. ## Parameter properties Type: ADPrincipal[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ProfilePath Specifies a path to the user\u0027s profile. This value can be a local absolute path or a Universal Naming Convention (UNC) path. This parameter sets the ProfilePath property of the user object. The LDAP display name (ldapDisplayName) for this property is profilePath. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Remove Specifies that the cmdlet remove values of an object property. Use this parameter to remove one or more values of a property that cannot be modified using a cmdlet parameter. To remove an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values, and more than one property by separating them using a semicolon. If any of the properties have a null or empty value the cmdlet will return an error. The format for this parameter is: `-Remove @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Remove, Replace, and Clear parameters together, the parameters are applied in the following sequence: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Replace Specifies values for an object property that will replace the current values. Use this parameter to replace one or more values of a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values, and more than one property by separating them using a semicolon. If any of the properties have a null or empty value the cmdlet will return an error. The format for this parameter is: `-Replace @{Attribute1LDAPDisplayName=value1, value2, ...; Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}` When you use the Add, Remove, Replace, and Clear parameters together, the operations will be performed in the following order: - Remove - Add - Replace - Clear ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SamAccountName Specifies the Security Account Manager (SAM) account name of the user, group, computer, or service account. The maximum length of the description is 256 characters. To be compatible with older operating systems, create a SAM account name that is 20 characters or less. This parameter sets the SAMAccountName for an account object. The LDAP display name (ldapDisplayName) for this property is `sAMAccountName`. Note If the string value provided is not terminated with a `$` character, the system adds one if needed. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ScriptPath Specifies a path to the user\u0027s log on script. This value can be a local absolute path or a Universal Naming Convention (UNC) path. This parameter sets the ScriptPath property of the user. The LDAP display name (ldapDisplayName) for this property is scriptPath. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Server Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ServicePrincipalNames Specifies the service principal names for the account. This parameter sets the ServicePrincipalNames property of the account. The LDAP display name (ldapDisplayName) for this property is `servicePrincipalName`. This parameter uses the following syntax to add, remove, replace or clear service principal name values. Syntax: To add values: `-ServicePrincipalNames @{Add=value1,value2,...}` To remove values: `-ServicePrincipalNames @{Remove=value3,value4,...}` To replace values: `-ServicePrincipalNames @{Replace=value1,value2,...}` To clear all values: `-ServicePrincipalNames $null` You can specify more than one change by using a list separated by semicolons. For example, use the following syntax to add and remove service principal names. `@{Add=value1,value2,...};@{Remove=value3,value4,...}` The operators will be applied in the following sequence: - Remove - Add - Replace The following example shows how to add and remove service principal names. `-ServicePrincipalNames-@{Add=\"SQLservice\\accounting.corp.contoso.com:1456\"};{Remove=\"SQLservice\\finance.corp.contoso.com:1456\"}` ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -SmartcardLogonRequired Indicates whether a smart card is required to logon. This parameter sets the SmartCardLoginRequired property for a user. This parameter also sets the ADS_UF_SMARTCARD_REQUIRED flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - $False or 0 - $True or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -State Specifies the user\u0027s state or province. This parameter sets the State property of a user object. The LDAP display name (ldapDisplayName) of this property is st. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -StreetAddress Specifies the user\u0027s street address. This parameter sets the StreetAddress property of a user object. The LDAP display name (ldapDisplayName) of this property is streetAddress. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Surname Specifies the user\u0027s last name or surname. This parameter sets the Surname property of a user object. The LDAP display name (ldapDisplayName) of this property is sn. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Title Specifies the user\u0027s title. This parameter sets the Title property of a user object. The LDAP display name (ldapDisplayName) of this property is title. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -TrustedForDelegation Specifies whether an account is trusted for Kerberos delegation. A service that runs under an account that is trusted for Kerberos delegation can assume the identity of a client requesting the service. This parameter sets the TrustedForDelegation property of an account object. This value also sets the ADS_UF_TRUSTED_FOR_DELEGATION flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$False` or `0` - `$True` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -UserPrincipalName Specifies a user principal name (UPN) in the format `\u003cuser\u003e@\u003cDNS-domain-name\u003e`. A UPN is a friendly name assigned by an administrator that is shorter than the LDAP distinguished name used by the system and easier to remember. The UPN is independent of the user object\u0027s distinguished name, so a user object can be moved or renamed without affecting the user logon name. When logging on using a UPN, users don\u0027t have to choose a domain from a list on the logon dialog box. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets Identity Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADUser A user object is received by the Identity parameter. A user object that was retrieved by using the `Get-ADUser` cmdlet and then modified is received by the Instance parameter. ## Outputs ## None or Microsoft.ActiveDirectory.Management.ADUser Returns the modified user object when the PassThru parameter is specified. By default, this cmdlet does not generate any output. ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. ## Related Links - Get-ADUser - New-ADUser - Remove-ADUser - Set-ADAccountControl ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "set-aduser"
                                 ],
                        "last_updated":  "2025-07-28T17:13:31Z",
                        "title":  "Set-ADUser (ActiveDirectory)"
                    },
                    {
                        "source":  {
                                       "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-aduser",
                                       "type":  "microsoft_docs",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "New-ADUser"
                                    ],
                        "id":  "microsoft_docs_new-aduser",
                        "metadata":  {
                                         "module":  "ActiveDirectory",
                                         "parameters":  [
                                                            "AccountExpirationDate",
                                                            "AccountNotDelegated",
                                                            "AccountPassword",
                                                            "AllowReversiblePasswordEncryption",
                                                            "AuthenticationPolicy",
                                                            "AuthenticationPolicySilo",
                                                            "AuthType",
                                                            "CannotChangePassword",
                                                            "Certificates",
                                                            "ChangePasswordAtLogon",
                                                            "City",
                                                            "Company",
                                                            "CompoundIdentitySupported",
                                                            "Country",
                                                            "Credential",
                                                            "Department",
                                                            "Description",
                                                            "DisplayName",
                                                            "Division",
                                                            "EmailAddress",
                                                            "EmployeeID",
                                                            "EmployeeNumber",
                                                            "Enabled",
                                                            "Fax",
                                                            "GivenName",
                                                            "HomeDirectory",
                                                            "HomeDrive",
                                                            "HomePage",
                                                            "HomePhone",
                                                            "Initials",
                                                            "Instance",
                                                            "KerberosEncryptionType",
                                                            "LogonWorkstations",
                                                            "Manager",
                                                            "MobilePhone",
                                                            "Office",
                                                            "OfficePhone",
                                                            "Organization",
                                                            "OtherAttributes",
                                                            "OtherName",
                                                            "PasswordNeverExpires",
                                                            "PasswordNotRequired",
                                                            "Path",
                                                            "POBox",
                                                            "PostalCode",
                                                            "PrincipalsAllowedToDelegateToAccount",
                                                            "ProfilePath",
                                                            "SamAccountName",
                                                            "ScriptPath",
                                                            "Server",
                                                            "ServicePrincipalNames",
                                                            "SmartcardLogonRequired",
                                                            "State",
                                                            "StreetAddress",
                                                            "Surname",
                                                            "Title",
                                                            "TrustedForDelegation",
                                                            "Type",
                                                            "UserPrincipalName"
                                                        ],
                                         "cmdlet_name":  "New-ADUser"
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# New-ADUser (ActiveDirectory)\n\n## Documentation\n\nTable of contents Exit editor mode Ask Learn Ask Learn Focus mode Table of contents Read in English Add Add to plan Edit ## Share via Facebook x.com LinkedIn Email Print Note Access to this page requires authorization. You can try signing in or changing directories. Access to this page requires authorization. You can try changing directories. ## New-ADUser Module: ActiveDirectory Module ## Creates an Active Directory user. ## Syntax ## Default (Default) ```powershellNew-ADUser [-AccountExpirationDate \u003cDateTime\u003e] [-AccountNotDelegated \u003cBoolean\u003e] [-AccountPassword \u003cSecureString\u003e] [-AllowReversiblePasswordEncryption \u003cBoolean\u003e] [-AuthenticationPolicy \u003cADAuthenticationPolicy\u003e] [-AuthenticationPolicySilo \u003cADAuthenticationPolicySilo\u003e] [-AuthType \u003cADAuthType\u003e] [-CannotChangePassword \u003cBoolean\u003e] [-Certificates \u003cX509Certificate[]\u003e] [-ChangePasswordAtLogon \u003cBoolean\u003e] [-City \u003cString\u003e] [-Company \u003cString\u003e] [-CompoundIdentitySupported \u003cBoolean\u003e] [-Country \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Department \u003cString\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-Division \u003cString\u003e] [-EmailAddress \u003cString\u003e] [-EmployeeID \u003cString\u003e] [-EmployeeNumber \u003cString\u003e] [-Enabled \u003cBoolean\u003e] [-Fax \u003cString\u003e] [-GivenName \u003cString\u003e] [-HomeDirectory \u003cString\u003e] [-HomeDrive \u003cString\u003e] [-HomePage \u003cString\u003e] [-HomePhone \u003cString\u003e] [-Initials \u003cString\u003e] [-Instance \u003cADUser\u003e] [-KerberosEncryptionType \u003cADKerberosEncryptionType\u003e] [-LogonWorkstations \u003cString\u003e] [-Manager \u003cADUser\u003e] [-MobilePhone \u003cString\u003e] [-Name] \u003cString\u003e [-Office \u003cString\u003e] [-OfficePhone \u003cString\u003e] [-Organization \u003cString\u003e] [-OtherAttributes \u003cHashtable\u003e] [-OtherName \u003cString\u003e] [-PassThru] [-PasswordNeverExpires \u003cBoolean\u003e] [-PasswordNotRequired \u003cBoolean\u003e] [-Path \u003cString\u003e] [-POBox \u003cString\u003e] [-PostalCode \u003cString\u003e] [-PrincipalsAllowedToDelegateToAccount \u003cADPrincipal[]\u003e] [-ProfilePath \u003cString\u003e] [-SamAccountName \u003cString\u003e] [-ScriptPath \u003cString\u003e] [-Server \u003cString\u003e] [-ServicePrincipalNames \u003cString[]\u003e] [-SmartcardLogonRequired \u003cBoolean\u003e] [-State \u003cString\u003e] [-StreetAddress \u003cString\u003e] [-Surname \u003cString\u003e] [-Title \u003cString\u003e] [-TrustedForDelegation \u003cBoolean\u003e] [-Type \u003cString\u003e] [-UserPrincipalName \u003cString\u003e] [-WhatIf] [-Confirm] [\u003cCommonParameters\u003e] ``` ## Description The `New-ADUser` cmdlet creates an Active Directory user. You can set commonly used user property values by using the cmdlet parameters. You can set property values that are not associated with cmdlet parameters by using the OtherAttributes parameter. When using this parameter, be sure to place single quotes around the attribute name. You must specify the SamAccountName parameter to create a user. You can use the `New-ADUser` cmdlet to create different types of user accounts such as iNetOrgPerson accounts. To do this in Active Directory Domain Services (AD DS), set the Type parameter to the Lightweight Directory Access Protocol (LDAP) display name for the type of account you want to create. This type can be any class in the Active Directory schema that is a subclass of user and that has an object category of person. The Path parameter specifies the container or organizational unit (OU) for the new user. When you do not specify the Path parameter, the cmdlet creates a user object in the default container for user objects in the domain. The following methods explain different ways to create an object by using this cmdlet. - Method 1: Use the `New-ADUser` cmdlet, specify the required parameters, and set any additional property values by using the cmdlet parameters. - Method 2: Use a template to create the new object. To do this, create a new user object or retrieve a copy of an existing user object and set the Instance parameter to this object. The object provided to the Instance parameter is used as a template for the new object. You can override property values from the template by setting cmdlet parameters. For examples and more information, see the Instance parameter description for this cmdlet. - Method 3: Use the `Import-Csv` cmdlet with the `New-ADUser` cmdlet to create multiple Active Directory user objects. To do this, use the `Import-Csv` cmdlet to create the custom objects from a comma-separated value (CSV) file that contains a list of object properties. Then pass these objects through the pipeline to the `New-ADUser` cmdlet to create the user objects. ## Examples ## Example 1: Create a user with an imported certificate ```powershell$splat = @{ Name = \u0027ChewDavid\u0027 Certificate = (New-Object System.Security.Cryptography.X509Certificates.X509Certificate -ArgumentList \u0027Export.cer\u0027) } New-ADUser @splat ``` This command creates a user named `ChewDavid` with a certificate imported from the file `Export.cer`. ## Example 2: Create a user and set properties ```powershellNew-ADUser -Name \u0027ChewDavid\u0027 -OtherAttributes @{ \u0027title\u0027=\u0027director\u0027 \u0027mail\u0027=\<EMAIL>\u0027 } ``` This command creates a new user named ChewDavid and sets the title and mail properties on the new object. ## Example 3: Create an inetOrgPerson user ```powershellNew-ADUser -Name \u0027ChewDavid\u0027 -Type iNetOrgPerson -Path \u0027DC=AppNC\u0027 -Server lds.Fabrikam.com:50000 ``` This command creates an inetOrgPerson-class user named ChewDavid on an AD LDS instance. ## Example 4: Create a user and set password ```powershell$splat = @{ Name = \u0027ChewDavid\u0027 AccountPassword = (Read-Host -AsSecureString \u0027AccountPassword\u0027) Enabled = $true } New-ADUser @splat ``` This command creates a new user named ChewDavid and sets the account password. ## Parameters ## -AccountExpirationDate Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is `accountExpires`. Use the DateTime syntax when you specify this parameter. Time is assumed to be local time unless otherwise specified. When a time value is not specified, the time is assumed to 12:00:00 AM local time. When a date is not specified, the date is assumed to be the current date. ## Parameter properties Type:DateTime Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AccountNotDelegated Indicates whether the security context of the user is delegated to a service. When this parameter is set to `$true`, the security context of the account is not delegated to a service even when the service account is set as trusted for Kerberos delegation. This parameter sets the AccountNotDelegated property for an Active Directory account. This parameter also sets the ADS_UF_NOT_DELEGATED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - `$false` or 0 - `$true` or 1 ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AccountPassword Specifies a new password value for an account. This value is stored as an encrypted string. The following conditions apply based on the manner in which the password parameter is used: - $Null password is specified: No password is set and the account is disabled unless it is requested to be enabled. - No password is specified: No password is set and the account is disabled unless it is requested to be enabled. - User password is specified: Password is set and the account is disabled unless it is requested to be enabled. User accounts, by default, are created without a password. If you provide a password, an attempt will be made to set that password however, this can fail due to password policy restrictions. The user account will still be created and you may use `Set-ADAccountPassword` to set the password on that account. In order to ensure that accounts remain secure, user accounts will never be enabled unless a valid password is set or PasswordNotRequired is set to `$true`. The account is created if the password fails for any reason. ## Parameter properties Type:SecureString Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AllowReversiblePasswordEncryption Indicates whether reversible password encryption is allowed for the account. This parameter sets the AllowReversiblePasswordEncryption property of the account. This parameter also sets the ADS_UF_ENCRYPTED_TEXT_PASSWORD_ALLOWED flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AuthenticationPolicy Specifies an Active Directory Domain Services authentication policy object. Specify the authentication policy object in one of the following formats: - Distinguished name - GUID - Name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicy Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AuthenticationPolicySilo Specifies an Active Directory Domain Services authentication policy silo object. Specify the authentication policy silo object in one of the following formats: - Distinguished name - GUID - Name This parameter can also get this object through the pipeline or you can set this parameter to an object instance. The cmdlet searches the default naming context or partition to find the object. If the cmdlet finds two or more objects, the cmdlet returns a non-terminating error. ## Parameter properties Type:ADAuthenticationPolicySilo Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -AuthType Specifies the authentication method to use. The acceptable values for this parameter are: - `Negotiate` or `0` - `Basic` or `1` The default authentication method is Negotiate. A Secure Sockets Layer (SSL) connection is required for the Basic authentication method. ## Parameter properties Type:ADAuthType Default value:None Accepted values:Negotiate, Basic Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -CannotChangePassword Indicates whether the account password can be changed. This parameter sets the CannotChangePassword property of an account. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Certificates Specifies the DER-encoded X.509v3 certificates of the account. These certificates include the public key certificates issued to this account by the Microsoft Certificate Service. This parameter sets the Certificates property of the account object. The LDAP display name (ldapDisplayName) for this property is `userCertificate`. ## Parameter properties Type: X509Certificate[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -ChangePasswordAtLogon Indicates whether a password must be changed during the next logon attempt. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` This parameter cannot be set to `$true` or 1 for an account that also has the PasswordNeverExpires property set to `$true`. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -City Specifies the user\u0027s town or city. This parameter sets the City property of a user object. The LDAP display name (ldapDisplayName) of this property is `l`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Company Specifies the user\u0027s company. This parameter sets the Company property of a user object. The LDAP display name (ldapDisplayName) of this property is `company`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -CompoundIdentitySupported Specifies whether an account supports Kerberos service tickets which includes the authorization data for the user\u0027s device. This value sets the compound identity supported flag of the Active Directory `msDS-SupportedEncryptionTypes` attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` Warning Domain-joined Windows systems and services such as clustering manage their own `msDS-SupportedEncryptionTypes` attribute. Therefore any changes to the flag on the `msDS-SupportedEncryptionTypes` attribute are overwritten by the service or system that manages the setting. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Confirm Prompts you for confirmation before running the cmdlet. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:cf ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Country Specifies the country or region code for the user\u0027s language of choice. This parameter sets the Country property of a user object. The LDAP display name (ldapDisplayName) of this property is `c`. This value is not used by Windows 2000. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Credential Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default. To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a PSCredential object. If you specify a user name for this parameter, the cmdlet prompts for a password. You can also create a PSCredential object by using a script or by using the `Get-Credential` cmdlet. You can then set the Credential parameter to the PSCredential object. If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error. ## Parameter properties Type:PSCredential Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -Department Specifies the user\u0027s department. This parameter sets the Department property of a user object. The LDAP display name (ldapDisplayName) of this property is `department`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Description Specifies a description of the object. This parameter sets the value of the Description property for the user object. The LDAP display name (ldapDisplayName) for this property is `description`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -DisplayName Specifies the display name of the object. This parameter sets the DisplayName property of the user object. The LDAP display name (ldapDisplayName) for this property is `displayName`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Division Specifies the user\u0027s division. This parameter sets the Division property of a user object. The LDAP display name (ldapDisplayName) of this property is `division`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -EmailAddress Specifies the user\u0027s e-mail address. This parameter sets the EmailAddress property of a user object. The LDAP display name (ldapDisplayName) of this property is `mail`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -EmployeeID Specifies the user\u0027s employee ID. This parameter sets the EmployeeID property of a user object. The LDAP display name (ldapDisplayName) of this property is `employeeID`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -EmployeeNumber Specifies the user\u0027s employee number. This parameter sets the EmployeeNumber property of a user object. The LDAP display name (ldapDisplayName) of this property is `employeeNumber`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Enabled Specifies if an account is enabled. An enabled account requires a password. This parameter sets the Enabled property for an account object. This parameter also sets the ADS_UF_ACCOUNTDISABLE flag of the Active Directory User Account Control (UAC) attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Fax Specifies the user\u0027s fax phone number. This parameter sets the Fax property of a user object. The LDAP display name (ldapDisplayName) of this property is `facsimileTelephoneNumber`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -GivenName Specifies the user\u0027s given name. This parameter sets the GivenName property of a user object. The LDAP display name (ldapDisplayName) of this property is `givenName`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -HomeDirectory Specifies a user\u0027s home directory. This parameter sets the HomeDirectory property of a user object. The LDAP display name (ldapDisplayName) for this property is `homeDirectory`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -HomeDrive Specifies a drive that is associated with the UNC path defined by the HomeDirectory property. The drive letter is specified as `\u003cDriveLetter\u003e:` where `\u003cDriveLetter\u003e` indicates the letter of the drive to associate. The `\u003cDriveLetter\u003e` must be a single, uppercase letter and the colon is required. This parameter sets the HomeDrive property of the user object. The LDAP display name (ldapDisplayName) for this property is `homeDrive`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -HomePage Specifies the URL of the home page of the object. This parameter sets the homePage property of a user object. The LDAP display name (ldapDisplayName) for this property is `wWWHomePage`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -HomePhone Specifies the user\u0027s home telephone number. This parameter sets the HomePhone property of a user object. The LDAP display name (ldapDisplayName) of this property is `homePhone`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Initials Specifies the initials that represent part of a user\u0027s name. You can use this value for the user\u0027s middle initial. This parameter sets the Initials property of a user object. The LDAP display name (ldapDisplayName) of this property is `initials`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Instance Specifies an instance of a user object to use as a template for a new user object. You can use an instance of an existing user object as a template or you can construct a new user object for template use. You can construct a new user object using the Windows PowerShell command line or by using a script. - Method 1: Use an existing user object as a template for a new object. To retrieve an instance of an existing user object, use a cmdlet such as `Get-ADUser`. Then provide this object to the Instance parameter of the `New-ADUser` cmdlet to create a new user object. You can override property values of the new object by setting the appropriate parameters. - Method 2: Create a new ADUser object and set the property values by using the Windows PowerShell command line interface. Then pass this object to the Instance parameter of the `New-ADUser` cmdlet to create the new Active Directory user object. Note Specified attributes are not validated, so attempting to set attributes that do not exist or cannot be set raises an error. ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -KerberosEncryptionType Specifies whether an account supports Kerberos encryption types which are used during creation of service tickets. This value sets the encryption types supported flags of the Active Directory `msDS-SupportedEncryptionTypes` attribute. Possible values for this parameter are: - `None` - `DES` - `RC4` - `AES128` - `AES256` `None` removes all encryption types from the account, resulting in the KDC being unable to issue service tickets for services using the account. `DES` is a weak encryption type that is not supported by default since Windows 7 and Windows Server 2008 R2. Warning Domain-joined Windows systems and services such as clustering manage their own `msDS-SupportedEncryptionTypes` attribute. Therefore any changes to the flag on the `msDS-SupportedEncryptionTypes` attribute are overwritten by the service or system that manages the setting. ## Parameter properties Type:ADKerberosEncryptionType Default value:None Accepted values:None, DES, RC4, AES128, AES256 Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -LogonWorkstations Specifies the computers that the user can access. To specify more than one computer, create a single comma-separated list. You can identify a computer by using the Security Account Manager (SAM) account name (sAMAccountName) or the DNS host name of the computer. The SAM account name is the same as the NetBIOS name of the computer. The LDAP display name (ldapDisplayName) for this property is `userWorkStations`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Manager Specifies the user\u0027s manager. This parameter sets the Manager property of a user object. This parameter is set by providing one of the following property values. The identifier in parentheses is the LDAP display name for the property. The acceptable values for this parameter are: - A distinguished name - A GUID (`objectGUID`) - A security identifier (`objectSid`) - A SAM account name (`sAMAccountName`) ## Parameter properties Type:ADUser Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -MobilePhone Specifies the user\u0027s mobile phone number. This parameter sets the MobilePhone property of a user object. The LDAP display name (ldapDisplayName) of this property is `mobile`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Name Specifies the name of the object. This parameter sets the Name property of a user object. The LDAP display name (ldapDisplayName) of this property is `name`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:1 Mandatory:True Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Office Specifies the location of the user\u0027s office or place of business. This parameter sets the Office property of a user object. The LDAP display name (ldapDisplayName) of this property is `physicalDeliveryOfficeName`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OfficePhone Specifies the user\u0027s office telephone number. This parameter sets the OfficePhone property of a user object. The LDAP display name (ldapDisplayName) of this property is `telephoneNumber`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Organization Specifies the user\u0027s organization. This parameter sets the Organization property of a user object. The LDAP display name (ldapDisplayName) of this property is `o`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -OtherAttributes Specifies object attribute values for attributes that are not represented by cmdlet parameters. You can set one or more parameters at the same time with this parameter. If an attribute takes more than one value, you can assign multiple values. To identify an attribute, specify the LDAP display name (ldapDisplayName) defined for it in the Active Directory schema. To specify a single value for an attribute: `-OtherAttributes @{\u0027AttributeLDAPDisplayName\u0027=value}` To specify multiple values for an attribute: `-OtherAttributes @{\u0027AttributeLDAPDisplayName\u0027=value1,value2,...}` To specify values for multiple attributes: `-OtherAttributes @{\u0027Attribute1LDAPDisplayName\u0027=value; \u0027Attribute2LDAPDisplayName\u0027=value1,value2;...}` ## Parameter properties Type:Hashtable Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -OtherName Specifies a name in addition to a user\u0027s given name and surname, such as the user\u0027s middle name. This parameter sets the OtherName property of a user object. The LDAP display name (ldapDisplayName) of this property is `middleName`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PassThru Returns an object representing the item with which you are working. By default, this cmdlet does not generate any output. ## Parameter properties Type:SwitchParameter Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -PasswordNeverExpires Specifies whether the password of an account can expire. This parameter sets the PasswordNeverExpires property of an account object. This parameter also sets the ADS_UF_DONT_EXPIRE_PASSWD flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` This parameter cannot be set to `$true` or `1` for an account that also has the ChangePasswordAtLogon property set to `$true`. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PasswordNotRequired Specifies whether the account requires a password. A password is not required for a new account. This parameter sets the PasswordNotRequired property of an account object. ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Path Specifies the X.500 path of the OU or container where the new object is created. In many cases, a default value is used for the Path parameter if no value is specified. The rules for determining the default value are given below. The rules listed first are evaluated first and when a default value can be determined, no further rules are evaluated. In Active Directory Domain Services (AD DS) environments, a default value for Path is set in the following cases: - If the cmdlet is run from an Active Directory PowerShell provider drive, the parameter is set to the current path of the provider drive. - If the cmdlet has a default path, this is used. For example: in New-ADUser, the Path parameter defaults to the Users container. - If none of the previous cases apply, the default value of Path is set to the default partition or naming context of the target domain. In AD LDS environments, a default value for Path is set in the following cases: - If the cmdlet is run from an Active Directory module for PowerShell provider drive, the parameter is set to the current path of the provider drive. - If the cmdlet has a default path, this is used. For example: in `New-ADUser`, the Path parameter defaults to the Users container. - If the target AD LDS instance has a default naming context, the default value of Path is set to the default naming context. To specify a default naming context for an AD LDS environment, set the `msDS-defaultNamingContext` property of the Active Directory directory service agent object (nTDSDSA) for the AD LDS instance. - If none of the previous cases apply, the Path parameter does not take any default value. Note The Active Directory Provider cmdlets, such as `New-Item`, `Remove-Item`, `Remove-ItemProperty`, `Rename-Item`, and `Set-ItemProperty` also contain a Path property. However, for the Active Directory Provider cmdlets, the Path parameter identifies the path of the actual object rather than the container. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -POBox Specifies the user\u0027s post office box number. This parameter sets the POBox property of a user object. The LDAP display name (ldapDisplayName) of this property is `postOfficeBox`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PostalCode Specifies the user\u0027s postal code or zip code. This parameter sets the PostalCode property of a user object. The LDAP display name (ldapDisplayName) of this property is `postalCode`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -PrincipalsAllowedToDelegateToAccount Specifies an array of principal objects. This parameter sets the `msDS-AllowedToActOnBehalfOfOtherIdentity` attribute of a computer account object. ## Parameter properties Type: ADPrincipal[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -ProfilePath Specifies a path to the user\u0027s profile. This value can be a local absolute path or a Universal Naming Convention (UNC) path. This parameter sets the ProfilePath property of the user object. The LDAP display name (ldapDisplayName) for this property is `profilePath`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -SamAccountName Specifies the Security Account Manager (SAM) account name of the user, group, computer, or service account. The maximum length of the description is 256 characters. To be compatible with older operating systems, create a SAM account name that is 20 characters or less. This parameter sets the SAMAccountName for an account object. The LDAP display name (ldapDisplayName) for this property is `sAMAccountName`. Note Information the user should notice even if skimmingIf the string value provided is not terminated with a `$` character, the system adds one if needed. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -ScriptPath Specifies a path to the user\u0027s log on script. This value can be a local absolute path or a Universal Naming Convention (UNC) path. This parameter sets the ScriptPath property of the user object. The LDAP display name (ldapDisplayName) for this property is `scriptPath`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Server Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance. Specify the AD DS instance in one of the following ways: Domain name values: - Fully qualified domain name - NetBIOS name Directory server values: - Fully qualified directory server name - NetBIOS name - Fully qualified directory server name and port The default value for this parameter is determined by one of the following methods in the order that they are listed: - By using the Server value from objects passed through the pipeline - By using the server information associated with the AD DS Windows PowerShell provider drive, when the cmdlet runs in that drive - By using the domain of the computer running Windows PowerShell ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## -ServicePrincipalNames Specifies the service principal names for the account. This parameter sets the ServicePrincipalNames property of the account. The LDAP display name (ldapDisplayName) for this property is servicePrincipalName. To enter multiple values, use the following syntax: `\u003cvalue1\u003e,\u003cvalue2\u003e,...\u003cvalueX\u003e`. If the values contain spaces or otherwise require quotation marks, use the following syntax: `\u0027\u003cvalue1\u003e\u0027,\u0027\u003cvalue2\u003e\u0027,...\u0027\u003cvalueX\u003e\u0027`. ## Parameter properties Type: String[] Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -SmartcardLogonRequired Specifies whether a smart card is required to logon. This parameter sets the SmartCardLoginRequired property for a user object. This parameter also sets the ADS_UF_SMARTCARD_REQUIRED flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -State Specifies the user\u0027s or Organizational Unit\u0027s state or province. This parameter sets the State property of a user object. The LDAP display name (ldapDisplayName) of this property is `st`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -StreetAddress Specifies the user\u0027s street address. This parameter sets the StreetAddress property of a user object. The LDAP display name (ldapDisplayName) of this property is streetAddress. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Surname Specifies the user\u0027s last name or surname. This parameter sets the Surname property of a user object. The LDAP display name (ldapDisplayName) of this property is `sn`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Title Specifies the user\u0027s title. This parameter sets the Title property of a user object. The LDAP display name (ldapDisplayName) of this property is `title`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -TrustedForDelegation Indicates whether an account is trusted for Kerberos delegation. A service that runs under an account that is trusted for Kerberos delegation can assume the identity of a client requesting the service. This parameter sets the TrustedForDelegation property of an account object. This value also sets the ADS_UF_TRUSTED_FOR_DELEGATION flag of the Active Directory User Account Control attribute. The acceptable values for this parameter are: - `$false` or `0` - `$true` or `1` ## Parameter properties Type:Boolean Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -Type Specifies the type of object to create. Set the Type parameter to the LDAP display name of the Active Directory schema class that represents the type of object that you want to create. The selected type must be a subclass of the User schema class. If this parameter is not specified it defaults to `User`. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -UserPrincipalName Specifies a user principal name (UPN) in the format `\u003cuser\u003e@\u003cDNS-domain-name\u003e`. A UPN is a friendly name assigned by an administrator that is shorter than the LDAP distinguished name used by the system and easier to remember. The UPN is independent of the user object\u0027s distinguished name, so a user object can be moved or renamed without affecting the user logon name. When signing on using a UPN, users no longer have to choose a domain from a list on the logon dialog box. ## Parameter properties Type:String Default value:None Supports wildcards:False DontShow:False ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:True Value from remaining arguments:False ## -WhatIf Shows what would happen if the cmdlet runs. The cmdlet is not run. ## Parameter properties Type:SwitchParameter Default value:False Supports wildcards:False DontShow:False Aliases:wi ## Parameter sets (All) Position:Named Mandatory:False Value from pipeline:False Value from pipeline by property name:False Value from remaining arguments:False ## CommonParameters This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters. ## Inputs ## None or Microsoft.ActiveDirectory.Management.ADUser A user object that is a template for the new user object is received by the Instance parameter. ## Outputs ## None By default, this cmdlet does not generate any output. ## Microsoft.ActiveDirectory.Management.ADUser Returns the new user object when the PassThru parameter is specified. ## Notes - This cmdlet does not work with an Active Directory snapshot. - This cmdlet does not work with a read-only domain controller. ## Related Links - Get-ADUser - Remove-ADUser - Set-ADUser - Set-ADAccountPassword ## Feedback Was this page helpful? Yes No",
                        "tags":  [
                                     "powershell",
                                     "active-directory",
                                     "microsoft-docs",
                                     "new-aduser"
                                 ],
                        "last_updated":  "2025-07-28T17:13:32Z",
                        "title":  "New-ADUser (ActiveDirectory)"
                    }
                ],
    "scraped_at":  "2025-07-28T17:13:30Z"
}
