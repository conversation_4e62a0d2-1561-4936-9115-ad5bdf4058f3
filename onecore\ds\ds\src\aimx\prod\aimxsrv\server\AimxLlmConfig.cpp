/*++
    Copyright (c) Microsoft Corporation. All rights reserved.

    Module Name:
        AimxLlmConfig.cpp

    Abstract:
        Configuration management for AIMX service.

    Author:
        <PERSON> (SNAKE FIGHTER) (lindakup) 07/07/2025

--*/
#include "pch.hxx"
#include "AimxConstants.h"
#include "AimxCommon.h"
#include "memory.h"
#include "AimxLlmConfig.h"

#include "AimxLlmConfig.cpp.tmh"

std::wstring
AimxLlmConfig::ReadAndConstructLlmEndpointUrlFromRegistry(
    HKEY hKey
    )
/*++
    Routine Description:
        Reads the FoundryLocalPort from the registry, validates it, and constructs the endpoint URL.
        Returns an empty string on error.

    Arguments:
        hKey - Handle to the registry key where the FoundryLocalPort is stored.

    Return Value:
        A string containing the constructed endpoint URL or an empty string on error.
--*/
{
    LONG lResult = 0;
    DWORD size = 0;
    std::wstring emptyString = L"";
    DWORD type = 0;
    lResult = RegQueryValueExW(
        hKey,
        AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT,
        nullptr, // lpReserved
        &type,
        nullptr, // lpData
        &size);
    if (lResult != ERROR_SUCCESS || size == 0 || type != REG_SZ)
    {
        TraceErr(
            AimxLlmInfer,
            "Failed to query size for %ws (error %ld, size %lu, type %lu)",
            AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT,
            lResult, size, type);
        if (lResult == ERROR_FILE_NOT_FOUND)
        {
            TraceErr(AimxLlmInfer, "Registry key %ws not found - Foundry local AI may not be configured",
                AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT);
        }
        return emptyString;
    }

    std::wstring portStr(size / sizeof(wchar_t), L'\0');
    lResult = RegQueryValueExW(
        hKey,
        AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT,
        nullptr, // lpReserved
        &type,
        (LPBYTE)&portStr[0],
        &size);
    if (lResult != ERROR_SUCCESS)
    {
        TraceErr(
            AimxLlmInfer,
            "Failed to read %ws (error %ld)",
            AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT,
            lResult);
        return emptyString;
    }

    size_t len = wcsnlen(portStr.c_str(), portStr.size());
    portStr.resize(len);
    portStr.erase(0, portStr.find_first_not_of(L" \t\n\r"));
    portStr.erase(portStr.find_last_not_of(L" \t\n\r") + 1);
    int port = 0;
    try
    {
        port = std::stoi(portStr);
    }
    catch (...)
    {
        TraceErr(AimxLlmInfer, L"FoundryLocalPort value is not a valid integer: %ws", portStr.c_str());
        return emptyString;
    }
    if (port < AimxConstants::Network::AIMX_MIN_PORT || port > AimxConstants::Network::AIMX_MAX_PORT)
    {
        TraceErr(AimxLlmInfer, L"FoundryLocalPort %d is out of valid range [%d, %d]", port,
            AimxConstants::Network::AIMX_MIN_PORT, AimxConstants::Network::AIMX_MAX_PORT);
        return emptyString;
    }

    // if we got here the port is valid, construct the endpoint URL
    std::wstring endpointUrl =
        std::wstring(AimxConstants::Network::AIMX_DEFAULT_LLM_SCHEME) +
        #if 1
        // RUPO_TODO: remove this hardcoding
        // keep this as is until we have the GPU installed in the test VM.
        L"************" + L":" +
        #else
        AimxConstants::Network::AIMX_DEFAULT_LLM_HOST + L":" +
        #endif
        portStr +
        AimxConstants::Network::AIMX_LLM_ENDPOINT_PATH;

    TraceInfo(AimxLlmInfer, L"Constructed endpointUrl: %ws", endpointUrl.c_str());
    return endpointUrl;
}

HRESULT
AimxLlmConfig::LoadLlmConfigFromRegistry()
/*++
    Routine Description:
        Loads the LLM configuration from the Windows registry.
        Reads model, temperature, topK, and topP values and populates g_LlmConfig.
        If values are missing, defaults are used.

        g_LlmConfig must already be initialized and allocated. 

    Arguments:
        None.

    Return Value:
        HRESULT indicating success or failure.
--*/
{
    HRESULT hr = S_OK;
    LONG lResult = 0;
    HKEY hKey = nullptr;
    DWORD size = 0;
    DWORD dwType = 0;
    std::wstring endpointUrl;
    std::wstring modelValue = L"";
    float temperature = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_TEMPERATURE_VALUE;
    int topK = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_TOP_K_VALUE;
    float topP = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_TOP_P_VALUE;
    std::wstring tempStr, topKStr, topPStr;

    lResult = RegOpenKeyExW(
        HKEY_LOCAL_MACHINE,
        AimxConstants::Registry::AIMX_REGISTRY_PARAMETERS,
        0,
        KEY_READ,
        &hKey);

    if (lResult != ERROR_SUCCESS)
    {
        TraceErr(
            AimxLlmInfer,
            "Failed to open registry key %ws (lResult: %ld)",
            AimxConstants::Registry::AIMX_REGISTRY_PARAMETERS,
            lResult);
        return HRESULT_FROM_WIN32(lResult);
    }

    TraceInfo(AimxLlmInfer, "Successfully opened registry key: %ws", AimxConstants::Registry::AIMX_REGISTRY_PARAMETERS);

    // Read and construct endpointUrl from FoundryLocalPort
    TraceInfo(AimxLlmInfer, "Reading FoundryLocalPort from registry...");
    endpointUrl = ReadAndConstructLlmEndpointUrlFromRegistry(hKey);
    if (endpointUrl.empty())
    {
        TraceErr(AimxLlmInfer, "Failed to construct endpoint URL from registry - FoundryLocalPort may be missing or invalid");
        hr = E_INVALIDARG;
        goto Exit;
    }
    TraceInfo(AimxLlmInfer, "Successfully constructed endpoint URL: %ws", endpointUrl.c_str());

    // Read the model ID from the registry. Sizing pass.
    size = 0;
    dwType = 0;
    lResult = RegQueryValueExW(
        hKey,
        AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELID,
        nullptr, // lpReserved
        &dwType,
        nullptr, // lpData
        &size);
    
    if (lResult == ERROR_SUCCESS && size > 0 && dwType == REG_SZ)
    {
        // Allocate enough space for the model value
        if (size % sizeof(wchar_t) != 0)
        {
            TraceErr(
                AimxLlmInfer,
                "Invalid size for %ws: %ld (not a multiple of wchar_t size)",
                AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELID,
                size);
            hr = E_INVALIDARG;
            goto Exit;
        }
        modelValue.resize(size / sizeof(wchar_t));
    }
    else // failure to read or not found
    {
        TraceInfo(AimxService, "Model ID not found in registry.");
        hr = HRESULT_FROM_WIN32(lResult);
        goto Exit;
    }

    // Now get the actual model value
    if (!modelValue.empty())
    {
        modelValue.assign(size / sizeof(wchar_t), L'\0');
        lResult = RegQueryValueExW(
            hKey,
            AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELID,
            nullptr, // lpReserved
            &dwType,
            (LPBYTE)&modelValue[0],
            &size);
        if (lResult == ERROR_SUCCESS)
        {
            // Remove any trailing nulls
            size_t len = wcsnlen(modelValue.c_str(), modelValue.size());
            modelValue.resize(len);
            TraceInfo(AimxLlmInfer, "Loaded model: %ws", modelValue.c_str());
        }
        else
        {
            TraceErr(
                AimxService,
                "Failed to read %ws (error %ld)",
                AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELID,
                lResult);
            hr = HRESULT_FROM_WIN32(lResult);
            goto Exit;
        }
    }

    //
    // Now read the temperature, topK, and topP values as REG_SZ (string), parse and convert,
    // use defaults if missing / invalid.
    //
    // Temperature
    size = 0;
    dwType = 0;
    lResult = RegQueryValueExW(
        hKey,
        AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_LLM_TEMPERATURE,
        nullptr, // lpReserved
        &dwType,
        nullptr, // lpData
        &size);
    if (lResult == ERROR_SUCCESS && size > 0 && dwType == REG_SZ)
    {
        tempStr.resize(size / sizeof(wchar_t));
        lResult = RegQueryValueExW(
            hKey,
            AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_LLM_TEMPERATURE,
            nullptr,
            &dwType,
            (LPBYTE)&tempStr[0],
            &size);
        if (lResult == ERROR_SUCCESS)
        {
            size_t len = wcsnlen(tempStr.c_str(), tempStr.size());
            tempStr.resize(len);
            try {
                temperature = std::stof(tempStr);
                TraceInfo(AimxLlmInfer, "Loaded temperature: %f", temperature);
            } catch (...) {
                TraceErr(AimxLlmInfer, L"Invalid temperature value in registry: %ws", tempStr.c_str());
                TraceInfo(AimxLlmInfer, "Using default temperature: %f", temperature);
            }
        }
        else {
            TraceInfo(AimxLlmInfer, "Using default temperature: %f", temperature);
        }
    }
    else {
        TraceInfo(AimxLlmInfer, "Using default temperature: %f", temperature);
    }

    // TopK
    size = 0;
    dwType = 0;
    lResult = RegQueryValueExW(
        hKey,
        AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_LLM_TOP_K,
        nullptr, // lpReserved
        &dwType,
        nullptr, // lpData
        &size);
    if (lResult == ERROR_SUCCESS && size > 0 && dwType == REG_SZ)
    {
        topKStr.resize(size / sizeof(wchar_t));
        lResult = RegQueryValueExW(
            hKey,
            AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_LLM_TOP_K,
            nullptr,
            &dwType,
            (LPBYTE)&topKStr[0],
            &size);
        if (lResult == ERROR_SUCCESS)
        {
            size_t len = wcsnlen(topKStr.c_str(), topKStr.size());
            topKStr.resize(len);
            try {
                topK = std::stoi(topKStr);
                TraceInfo(AimxLlmInfer, "Loaded topK: %d", topK);
            } catch (...) {
                TraceErr(AimxLlmInfer, L"Invalid topK value in registry: %ws", topKStr.c_str());
                TraceInfo(AimxLlmInfer, "Using default topK: %d", topK);
            }
        }
        else {
            TraceInfo(AimxLlmInfer, "Using default topK: %d", topK);
        }
    }
    else {
        TraceInfo(AimxLlmInfer, "Using default topK: %d", topK);
    }

    // TopP
    size = 0;
    dwType = 0;
    lResult = RegQueryValueExW(
        hKey,
        AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_LLM_TOP_P,
        nullptr, // lpReserved
        &dwType,
        nullptr, // lpData
        &size);
    if (lResult == ERROR_SUCCESS && size > 0 && dwType == REG_SZ)
    {
        topPStr.resize(size / sizeof(wchar_t));
        lResult = RegQueryValueExW(
            hKey,
            AimxConstants::Registry::AIMX_REGISTRY_KEYNAME_LLM_TOP_P,
            nullptr,
            &dwType,
            (LPBYTE)&topPStr[0],
            &size);
        if (lResult == ERROR_SUCCESS)
        {
            size_t len = wcsnlen(topPStr.c_str(), topPStr.size());
            topPStr.resize(len);
            try {
                topP = std::stof(topPStr);
                TraceInfo(AimxLlmInfer, "Loaded topP: %f", topP);
            } catch (...) {
                TraceErr(AimxLlmInfer, L"Invalid topP value in registry: %ws", topPStr.c_str());
                TraceInfo(AimxLlmInfer, "Using default topP: %f", topP);
            }
        }
        else
        {
            TraceInfo(AimxLlmInfer, "Using default topP: %f", topP);
        }
    }
    else
    {
        TraceInfo(AimxLlmInfer, "Using default topP: %f", topP);
    }

    // Success. Save everything in the global state.
    endpointUrl_ = endpointUrl;
    model_ = modelValue;
    temperature_ = temperature;
    topK_ = topK;
    topP_ = topP;

Exit:
    if (hKey)
    {
        RegCloseKey(hKey);
    }
    return hr;
}
