# Check what commands are in the dataset
$datasetPath = "e:\os\src\onecore\ds\ds\src\aimx\prod\netRag\data\ad_powershell_comprehensive_dataset.json"

Write-Host "Checking dataset at: $datasetPath" -ForegroundColor Green

if (Test-Path $datasetPath) {
    $dataset = Get-Content $datasetPath | ConvertFrom-Json
    Write-Host "Total commands in dataset: $($dataset.commands.Count)" -ForegroundColor Yellow
    
    Write-Host "`nFirst 10 commands:" -ForegroundColor Cyan
    $dataset.commands | Select-Object -First 10 | ForEach-Object { 
        Write-Host "  - $($_.command_name)" -ForegroundColor White
    }
    
    Write-Host "`nLooking for Enable commands:" -ForegroundColor Cyan
    $enableCommands = $dataset.commands | Where-Object { $_.command_name -like '*Enable*' }
    if ($enableCommands) {
        $enableCommands | ForEach-Object { 
            Write-Host "  - $($_.command_name)" -ForegroundColor White
        }
    } else {
        Write-Host "  No Enable commands found" -ForegroundColor Red
    }
    
    Write-Host "`nLooking for New-ADUser command:" -ForegroundColor Cyan
    $newUserCommand = $dataset.commands | Where-Object { $_.command_name -eq 'New-ADUser' }
    if ($newUserCommand) {
        Write-Host "  ✅ Found New-ADUser command" -ForegroundColor Green
    } else {
        Write-Host "  ❌ New-ADUser command not found" -ForegroundColor Red
    }
    
    Write-Host "`nLooking for user-related commands:" -ForegroundColor Cyan
    $userCommands = $dataset.commands | Where-Object { $_.command_name -like '*User*' }
    Write-Host "Found $($userCommands.Count) user-related commands:" -ForegroundColor Yellow
    $userCommands | Select-Object -First 5 | ForEach-Object { 
        Write-Host "  - $($_.command_name)" -ForegroundColor White
    }
    
} else {
    Write-Host "Dataset file not found!" -ForegroundColor Red
}
