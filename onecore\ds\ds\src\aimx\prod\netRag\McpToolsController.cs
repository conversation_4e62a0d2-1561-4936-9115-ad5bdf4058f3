using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace netRag;

/// <summary>
/// API controller for managing MCP tools
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class McpToolsController : ControllerBase
{
    private readonly McpToolService _mcpToolService;
    private readonly McpServiceConfig _config;
    private readonly ILogger<McpToolsController> _logger;

    public McpToolsController(
        McpToolService mcpToolService, 
        McpServiceConfig config,
        ILogger<McpToolsController> logger)
    {
        _mcpToolService = mcpToolService;
        _config = config;
        _logger = logger;
    }

    /// <summary>
    /// Register a new MCP tool
    /// </summary>
    /// <param name="request">Tool registration request</param>
    /// <returns>Success status</returns>
    [HttpPost("register")]
    public async Task<IActionResult> RegisterTool([FromBody] RegisterToolRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Id) || string.IsNullOrEmpty(request.Name))
            {
                return BadRequest(new { error = "Tool ID and Name are required" });
            }

            var success = await _mcpToolService.RegisterToolAsync(request);
            
            if (success)
            {
                _logger.LogInformation("Tool registered successfully: {ToolId}", request.Id);
                return Ok(new { success = true, message = "Tool registered successfully", toolId = request.Id });
            }
            else
            {
                return BadRequest(new { error = "Failed to register tool. Check tool ID format (GUID)" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering tool: {ToolId}", request.Id);
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Search for MCP tools based on a query
    /// </summary>
    /// <param name="request">Search request</param>
    /// <returns>List of matching tools with relevance scores</returns>
    [HttpPost("search")]
    public async Task<IActionResult> SearchTools([FromBody] SearchToolsRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Query))
            {
                return BadRequest(new { error = "Search query is required" });
            }

            // Apply configuration limits
            if (request.TopK <= 0 || request.TopK > _config.MaxTopK)
            {
                request.TopK = _config.DefaultTopK;
            }

            if (request.MinScore < 0.0f)
            {
                request.MinScore = _config.DefaultMinScore;
            }

            var response = await _mcpToolService.SearchToolsAsync(request);
            
            _logger.LogInformation("Search completed: {Query} returned {Count} tools", 
                request.Query, response.Tools.Count);
            
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching tools: {Query}", request.Query);
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Get a specific tool by ID
    /// </summary>
    /// <param name="toolId">Tool ID (GUID)</param>
    /// <returns>Tool details</returns>
    [HttpGet("{toolId}")]
    public async Task<IActionResult> GetTool(string toolId)
    {
        try
        {
            if (string.IsNullOrEmpty(toolId))
            {
                return BadRequest(new { error = "Tool ID is required" });
            }

            var tool = await _mcpToolService.GetToolAsync(toolId);
            
            if (tool == null)
            {
                return NotFound(new { error = "Tool not found", toolId });
            }

            return Ok(tool);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tool: {ToolId}", toolId);
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Get statistics about registered tools
    /// </summary>
    /// <returns>Statistics about the tool database</returns>
    [HttpGet("statistics")]
    public async Task<IActionResult> GetStatistics()
    {
        try
        {
            var stats = await _mcpToolService.GetStatisticsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting statistics");
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Health check endpoint
    /// </summary>
    /// <returns>Service health status</returns>
    [HttpGet("health")]
    public IActionResult HealthCheck()
    {
        return Ok(new 
        { 
            status = "healthy", 
            timestamp = DateTime.UtcNow,
            service = "MCP Tools RAG Service",
            version = "1.0.0",
            config = new
            {
                defaultTopK = _config.DefaultTopK,
                maxTopK = _config.MaxTopK,
                host = _config.Host,
                port = _config.Port
            }
        });
    }

    /// <summary>
    /// Register multiple MCP tools in batch
    /// </summary>
    /// <param name="requests">List of tool registration requests</param>
    /// <returns>Batch operation results</returns>
    [HttpPost("batch/register")]
    public async Task<IActionResult> RegisterToolsBatch([FromBody] List<RegisterToolRequest> requests)
    {
        try
        {
            if (requests == null || !requests.Any())
            {
                return BadRequest(new { error = "At least one tool registration request is required" });
            }

            var results = new List<object>();
            var successCount = 0;
            var failureCount = 0;

            foreach (var request in requests)
            {
                try
                {
                    var success = await _mcpToolService.RegisterToolAsync(request);
                    
                    if (success)
                    {
                        successCount++;
                        results.Add(new { toolId = request.Id, success = true, message = "Registered successfully" });
                    }
                    else
                    {
                        failureCount++;
                        results.Add(new { toolId = request.Id, success = false, error = "Registration failed" });
                    }
                }
                catch (Exception ex)
                {
                    failureCount++;
                    results.Add(new { toolId = request.Id, success = false, error = ex.Message });
                    _logger.LogError(ex, "Error in batch registration for tool: {ToolId}", request.Id);
                }
            }

            _logger.LogInformation("Batch registration completed: {Success} successful, {Failed} failed", 
                successCount, failureCount);

            return Ok(new 
            { 
                totalRequests = requests.Count,
                successCount,
                failureCount,
                results 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in batch tool registration");
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Remove a specific MCP tool by ID
    /// </summary>
    /// <param name="toolId">Tool ID (GUID)</param>
    /// <returns>Success status</returns>
    [HttpDelete("{toolId}")]
    public async Task<IActionResult> RemoveTool(string toolId)
    {
        try
        {
            if (string.IsNullOrEmpty(toolId))
            {
                return BadRequest(new { error = "Tool ID is required" });
            }

            var removed = await _mcpToolService.RemoveToolAsync(toolId);

            if (removed)
            {
                _logger.LogInformation("Tool removed successfully: {ToolId}", toolId);
                return Ok(new { success = true, message = "Tool removed successfully", toolId });
            }
            else
            {
                return NotFound(new { error = "Tool not found", toolId });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing tool: {ToolId}", toolId);
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Remove all tools (batch operation)
    /// </summary>
    /// <returns>Success status with count of removed tools</returns>
    [HttpDelete("all")]
    public async Task<IActionResult> RemoveAllTools()
    {
        try
        {
            var removedCount = await _mcpToolService.RemoveAllToolsAsync();

            _logger.LogInformation("Removed {Count} tools from the system", removedCount);
            return Ok(new { success = true, message = $"Removed {removedCount} tools", removedCount });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing all tools");
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }

    /// <summary>
    /// Search for tools with multiple queries in batch
    /// </summary>
    /// <param name="requests">List of search requests</param>
    /// <returns>Batch search results</returns>
    [HttpPost("batch/search")]
    public async Task<IActionResult> SearchToolsBatch([FromBody] List<SearchToolsRequest> requests)
    {
        try
        {
            if (requests == null || !requests.Any())
            {
                return BadRequest(new { error = "At least one search request is required" });
            }

            var results = new List<SearchToolsResponse>();

            foreach (var request in requests)
            {
                try
                {
                    if (string.IsNullOrEmpty(request.Query))
                    {
                        results.Add(new SearchToolsResponse 
                        { 
                            Query = request.Query,
                            Tools = new List<ToolSearchResult>()
                        });
                        continue;
                    }

                    // Apply configuration limits
                    if (request.TopK <= 0 || request.TopK > _config.MaxTopK)
                    {
                        request.TopK = _config.DefaultTopK;
                    }

                    if (request.MinScore < 0.0f)
                    {
                        request.MinScore = _config.DefaultMinScore;
                    }

                    var response = await _mcpToolService.SearchToolsAsync(request);
                    results.Add(response);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in batch search for query: {Query}", request.Query);
                    results.Add(new SearchToolsResponse 
                    { 
                        Query = request.Query,
                        Tools = new List<ToolSearchResult>()
                    });
                }
            }

            _logger.LogInformation("Batch search completed for {Count} queries", requests.Count);

            return Ok(new 
            { 
                totalQueries = requests.Count,
                results 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in batch tool search");
            return StatusCode(500, new { error = "Internal server error", details = ex.Message });
        }
    }
}
