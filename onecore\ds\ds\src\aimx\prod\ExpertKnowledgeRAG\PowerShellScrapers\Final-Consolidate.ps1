# Final Data Consolidation Script
# Merges the latest clean data files into a single knowledge base
param(
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "./ScrapedData/consolidated_clean_knowledge_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
)

Write-Host "Final Data Consolidation - Clean Knowledge Base" -ForegroundColor Cyan
Write-Host "Merging latest clean data files..." -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Gray

try {
    # Define the latest clean data files
    $dataFiles = @(
        "./ScrapedData/simple_stackoverflow_20250728_171419.json",  # 50 entries
        "./ScrapedData/clean_microsoft_docs_20250728_171443.json"   # 20 entries
    )
    
    $consolidatedKnowledgeBase = @{
        schema_version = "1.0"
        consolidated_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        description = "Consolidated PowerShell Active Directory knowledge base for RAG"
        sources = @()
        total_entries = 0
        entries = @()
        statistics = @{
            total_cmdlets = 0
            total_code_examples = 0
            sources_count = 0
            avg_content_length = 0
        }
    }
    
    $allEntries = @()
    $processedFiles = 0
    
    foreach ($file in $dataFiles) {
        if (Test-Path $file) {
            Write-Host "Processing file: $(Split-Path $file -Leaf)" -ForegroundColor Yellow
            
            try {
                $data = Get-Content $file -Raw | ConvertFrom-Json
                
                if ($data.entries) {
                    $validEntries = $data.entries | Where-Object { 
                        $_.content -and $_.content.Length -gt 100 
                    }
                    
                    Write-Host "  Loaded $($validEntries.Count) valid entries" -ForegroundColor Green
                    
                    # Add source info
                    $consolidatedKnowledgeBase.sources += @{
                        file = $(Split-Path $file -Leaf)
                        source_type = $data.source
                        entries_count = $validEntries.Count
                        scraped_at = $data.scraped_at
                    }
                    
                    $allEntries += $validEntries
                    $processedFiles++
                }
                else {
                    Write-Host "  No entries found in file" -ForegroundColor Yellow
                }
            }
            catch {
                Write-Host "  Failed to process file: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        else {
            Write-Host "File not found: $file" -ForegroundColor Red
        }
    }
    
    Write-Host "`nTotal entries loaded: $($allEntries.Count)" -ForegroundColor Green
    
    # Simple deduplication by ID
    $uniqueEntries = @{}
    $duplicatesRemoved = 0
    
    foreach ($entry in $allEntries) {
        if ($uniqueEntries.ContainsKey($entry.id)) {
            $duplicatesRemoved++
            Write-Host "  Duplicate removed: $($entry.id)" -ForegroundColor Yellow
        }
        else {
            $uniqueEntries[$entry.id] = $entry
        }
    }
    
    $finalEntries = $uniqueEntries.Values
    Write-Host "Removed $duplicatesRemoved duplicates, final count: $($finalEntries.Count)" -ForegroundColor Green
    
    # Calculate statistics
    $allCmdlets = @()
    $allCodeExamples = @()
    $totalContentLength = 0
    
    foreach ($entry in $finalEntries) {
        if ($entry.cmdlets) {
            $allCmdlets += $entry.cmdlets
        }
        if ($entry.code_examples) {
            $allCodeExamples += $entry.code_examples
        }
        if ($entry.content) {
            $totalContentLength += $entry.content.Length
        }
    }
    
    $uniqueCmdlets = $allCmdlets | Select-Object -Unique
    $avgContentLength = if ($finalEntries.Count -gt 0) { [math]::Round($totalContentLength / $finalEntries.Count) } else { 0 }
    
    # Update consolidated knowledge base
    $consolidatedKnowledgeBase.entries = $finalEntries
    $consolidatedKnowledgeBase.total_entries = $finalEntries.Count
    $consolidatedKnowledgeBase.statistics.total_cmdlets = $uniqueCmdlets.Count
    $consolidatedKnowledgeBase.statistics.total_code_examples = $allCodeExamples.Count
    $consolidatedKnowledgeBase.statistics.sources_count = $processedFiles
    $consolidatedKnowledgeBase.statistics.avg_content_length = $avgContentLength
    
    # Save consolidated knowledge base
    $consolidatedKnowledgeBase | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "`nConsolidation completed successfully!" -ForegroundColor Green
    Write-Host "=" * 60 -ForegroundColor Gray
    Write-Host "Final Statistics:" -ForegroundColor Cyan
    Write-Host "  Total entries: $($consolidatedKnowledgeBase.total_entries)" -ForegroundColor White
    Write-Host "  Unique cmdlets: $($consolidatedKnowledgeBase.statistics.total_cmdlets)" -ForegroundColor White
    Write-Host "  Code examples: $($consolidatedKnowledgeBase.statistics.total_code_examples)" -ForegroundColor White
    Write-Host "  Sources processed: $($consolidatedKnowledgeBase.statistics.sources_count)" -ForegroundColor White
    Write-Host "  Average content length: $($consolidatedKnowledgeBase.statistics.avg_content_length) characters" -ForegroundColor White
    Write-Host "  Output saved to: $OutputPath" -ForegroundColor Green
    
    # Show sample cmdlets
    if ($uniqueCmdlets.Count -gt 0) {
        $sampleCmdlets = $uniqueCmdlets | Select-Object -First 10
        Write-Host "`nSample cmdlets covered:" -ForegroundColor Cyan
        foreach ($cmdlet in $sampleCmdlets) {
            Write-Host "  - $cmdlet" -ForegroundColor Gray
        }
        if ($uniqueCmdlets.Count -gt 10) {
            Write-Host "  ... and $($uniqueCmdlets.Count - 10) more" -ForegroundColor Gray
        }
    }
    
    # Show sample entries
    if ($finalEntries.Count -gt 0) {
        Write-Host "`nSample entries:" -ForegroundColor Cyan
        $sampleEntries = $finalEntries | Select-Object -First 3
        foreach ($entry in $sampleEntries) {
            Write-Host "  - $($entry.title) ($($entry.content.Length) chars)" -ForegroundColor Gray
        }
    }
    
    Write-Host "`nClean knowledge base ready for RAG usage!" -ForegroundColor Green
    Write-Host "   All content is offline-ready with no external dependencies." -ForegroundColor Gray
}
catch {
    Write-Host "Consolidation failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Full error: $($_.Exception)" -ForegroundColor Red
    exit 1
}
