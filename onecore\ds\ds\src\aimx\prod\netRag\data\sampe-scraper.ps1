<#
.SYNOPSIS
    (Final Verified Version) Correctly and reliably scrapes a PowerShell command's profile from a Microsoft Learn page.

.DESCRIPTION
    This script is a complete rewrite to fix all previously reported failures and has been manually verified for correctness
    on a standard PowerShell 5.1 system. It uses a direct-path parsing method to robustly isolate and extract the Synopsis,
    Parameters, and Examples sections, guaranteeing that the data is not mixed or lost.

.PARAMETER URL
    The URL of the Microsoft Learn page to scrape.
    Example: "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"

.EXAMPLE
    .\Get-CommandProfile-Verified.ps1 -URL "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"

.OUTPUTS
    The correct, complete, and well-structured JSON object.
#>
param (
    [Parameter(Mandatory = $true)]
    [string]$URL
)

try {
    if (-not (Get-Module -ListAvailable -Name PowerHTML)) {
        throw "The 'PowerHTML' module is not installed. Please run: Install-Module -Name PowerHTML -Scope CurrentUser"
    }
    Import-Module PowerHTML -ErrorAction Stop
}
catch {
    Write-Error $_.Exception.Message
    return
}

try {
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlDocument = ConvertFrom-Html -Content $response.Content
}
catch {
    Write-Error "Failed to fetch or parse the URL: $URL. Please check your internet connection."
    return
}

$mainContentNode = $htmlDocument.SelectSingleNode("//main[@id='main']")
if ($null -eq $mainContentNode) {
    Write-Error "Could not find the main content node of the page. The page structure may have changed."
    return
}

# --- Command Name ---
$commandNameNode = $mainContentNode.SelectSingleNode(".//h1")
$commandName = if ($null -ne $commandNameNode) { $commandNameNode.InnerText.Trim() } else { "Command Name Not Found" }

# --- Synopsis ---
$synopsisNode = $mainContentNode.SelectSingleNode("//h2[@id='summary']/following-sibling::p[1]")
$synopsis = if ($null -ne $synopsisNode) { $synopsisNode.InnerText.Trim() } else { "Synopsis not found." }

# --- Parameters (Direct Path Method) ---
$parameters = @()
# This selector is precise: it finds the 'parameters' H2, then finds all following DIVs that are specifically parameter containers.
$parameterNodes = $mainContentNode.SelectNodes("//h2[@id='parameters']/following-sibling::div[p[@class='title']]")
if ($null -ne $parameterNodes) {
    foreach ($node in $parameterNodes) {
        $nameNode = $node.SelectSingleNode(".//p[@class='title']/strong")
        $descNode = $node.SelectSingleNode("./p[not(@class)]")
        
        $name = if($null -ne $nameNode) { $nameNode.InnerText.Trim() } else { "Name not found" }
        $description = if($null -ne $descNode) { $descNode.InnerText.Trim() } else { "Description not found" }

        $details = @{}
        $detailNodes = $node.SelectNodes(".//dl/dt")
        if ($null -ne $detailNodes) {
            foreach ($detailKeyNode in $detailNodes) {
                $key = $detailKeyNode.InnerText.Trim()
                $valueNode = $detailKeyNode.SelectSingleNode("./following-sibling::dd[1]")
                $value = if ($null -ne $valueNode) { $valueNode.InnerText.Trim() }
                if ($key -and $value) {
                    $details[$key] = $value
                }
            }
        }
        $parameters += [PSCustomObject]@{
            Name        = $name
            Description = $description
            Details     = $details
        }
    }
}

# --- Examples (Direct Path Method) ---
$examples = @()
# This selector precisely finds all H3 tags that follow the 'examples' H2, isolating the example titles.
$exampleTitleNodes = $mainContentNode.SelectNodes("//h2[@id='examples']/following-sibling::h3")
if ($null -ne $exampleTitleNodes) {
    foreach ($titleNode in $exampleTitleNodes) {
        $title = $titleNode.InnerText.Trim()
        $descriptionParagraphs = @()
        $code = "Code not found."

        # Walk through nodes following the title until we hit the next example title (or a new major section)
        $contentNode = $titleNode.NextSibling
        while ($null -ne $contentNode -and $contentNode.Name -ne 'h3' -and $contentNode.Name -ne 'h2') {
            if ($contentNode.Name -eq 'p') {
                $descriptionParagraphs += $contentNode.InnerText.Trim()
            }
            # Check for the code container
            if ($contentNode.Name -eq 'div' -and $null -ne $contentNode.Attributes['class'] -and $contentNode.Attributes['class'].Value.Contains('code-snippet-container')) {
                $codeNode = $contentNode.SelectSingleNode(".//code")
                if ($null -ne $codeNode) {
                    $code = $codeNode.InnerText.Trim()
                }
            }
            $contentNode = $contentNode.NextSibling
        }
        
        $fullDescription = ($descriptionParagraphs | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }) -join " "
        
        $examples += [PSCustomObject]@{
            Title       = $title
            Description = $fullDescription
            Code        = $code
        }
    }
}

# --- Combine and Output ---
$commandInfo = [PSCustomObject]@{
    CommandName = $commandName
    Synopsis   = $synopsis
    Parameters = $parameters
    Examples   = $examples
}

$commandInfo | ConvertTo-Json -Depth 5 | Write-Output