# Test the improved step generation (should be fewer, better steps)
param(
    [string]$BaseUrl = "http://localhost:8082"
)

Write-Host "=== TESTING IMPROVED STEP GENERATION ===" -ForegroundColor Green
Write-Host "Should generate FEWER, BETTER steps (not always 5!)" -ForegroundColor Yellow

$testCases = @(
    @{
        Input = "Create a new user account for <PERSON>"
        Expected = "Should be 1-2 steps, not 5!"
    },
    @{
        Input = "Reset password for user <PERSON>"
        Expected = "Should be 1 step only!"
    },
    @{
        Input = "Add user Bob <PERSON> to Finance group"
        Expected = "Should be 1 step only!"
    },
    @{
        Input = "Create user <PERSON> and add to HR group"
        Expected = "Should be 2 steps only!"
    }
)

foreach ($test in $testCases) {
    Write-Host "`n" + "=" * 80 -ForegroundColor Cyan
    Write-Host "TEST: $($test.Input)" -ForegroundColor Cyan
    Write-Host "EXPECTED: $($test.Expected)" -ForegroundColor Yellow
    Write-Host "=" * 80
    
    try {
        $requestBody = @{
            requestId = [System.Guid]::NewGuid().ToString()
            userInput = $test.Input
        } | ConvertTo-Json
        
        Write-Host "Sending request..." -ForegroundColor Gray
        $startTime = Get-Date
        
        $response = Invoke-RestMethod -Uri "$BaseUrl/api/IntentPlanning/analyze" -Method POST -Body $requestBody -ContentType "application/json"
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        Write-Host "`nRESULTS:" -ForegroundColor White
        Write-Host "  Success: $($response.success)" -ForegroundColor $(if($response.success) { "Green" } else { "Red" })
        Write-Host "  Steps Generated: $($response.primaryWorkflow.steps.Count)" -ForegroundColor $(if($response.primaryWorkflow.steps.Count -le 3) { "Green" } else { "Red" })
        Write-Host "  Response Time: $([math]::Round($duration))ms" -ForegroundColor Gray
        
        if ($response.success -and $response.primaryWorkflow.steps.Count -gt 0) {
            Write-Host "`nGENERATED STEPS:" -ForegroundColor White
            for ($i = 0; $i -lt $response.primaryWorkflow.steps.Count; $i++) {
                $step = $response.primaryWorkflow.steps[$i]
                Write-Host "  $($i + 1). $($step.description)" -ForegroundColor White
                Write-Host "     Command: $($step.command)" -ForegroundColor Gray
            }
            
            # Analysis
            if ($response.primaryWorkflow.steps.Count -le 3) {
                Write-Host "`n✅ GOOD: Reasonable number of steps!" -ForegroundColor Green
            } else {
                Write-Host "`n❌ BAD: Too many steps for this simple task!" -ForegroundColor Red
            }
        } else {
            Write-Host "`n❌ FAILED: No workflow generated" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "`n❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 500
}

Write-Host "`n" + "=" * 80 -ForegroundColor Green
Write-Host "ANALYSIS COMPLETE" -ForegroundColor Green
Write-Host "=" * 80
Write-Host "The LLM should now generate fewer, more focused steps!" -ForegroundColor Yellow
Write-Host "Simple tasks like 'reset password' should be 1 step, not 5!" -ForegroundColor White
