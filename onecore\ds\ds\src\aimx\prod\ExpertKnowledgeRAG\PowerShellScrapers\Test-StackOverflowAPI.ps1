# Test Stack Overflow API connectivity
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing Stack Overflow API Connectivity" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    
    # Test Stack Overflow API
    Write-Host "`nTesting Stack Overflow API..." -ForegroundColor Yellow
    
    $baseUrl = "https://api.stackexchange.com/2.3/"
    $tags = "powershell;active-directory"
    $apiUrl = "${baseUrl}questions?order=desc&sort=votes&tagged=${tags}&site=stackoverflow&pagesize=5&filter=withbody"
    
    Write-Host "API URL: $apiUrl" -ForegroundColor Gray
    
    $response = Invoke-WebRequestWithRetry -Uri $apiUrl -MaxRetries 2
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Stack Overflow API accessible" -ForegroundColor Green
        Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
        Write-Host "  Content length: $($response.Content.Length)" -ForegroundColor Gray
        
        # Parse JSON response
        $data = $response.Content | ConvertFrom-Json
        
        if ($data.items) {
            Write-Host "✅ Found $($data.items.Count) questions" -ForegroundColor Green
            Write-Host "  Quota remaining: $($data.quota_remaining)" -ForegroundColor Gray
            
            # Show first few questions
            Write-Host "`nFirst few questions:" -ForegroundColor Yellow
            $count = 0
            foreach ($question in $data.items) {
                if ($count -ge 3) { break }
                Write-Host "  Q$($count + 1): $($question.title)" -ForegroundColor Cyan
                Write-Host "    Score: $($question.score), Views: $($question.view_count)" -ForegroundColor Gray
                Write-Host "    URL: $($question.link)" -ForegroundColor DarkGray
                $count++
            }
        } else {
            Write-Host "❌ No questions found in response" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Stack Overflow API returned status: $($response.StatusCode)" -ForegroundColor Red
    }
    
    Write-Host "`n✅ Stack Overflow API test completed!" -ForegroundColor Green
}
catch {
    Write-Host "`n❌ Stack Overflow API test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
}
