﻿{
    "ScrapedAt":  "2025-07-28T16:10:08Z",
    "BatchId":  "20250728_161008",
    "Patterns":  [
                     {
                         "Syntax":  [
                                        "Remove-ADReplicationSiteLinkBridge\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType \u003cADAuthType\u003e]\n    [-Credential \u003cPSCredential\u003e]\n    [-Identity] \u003cADReplicationSiteLinkBridge\u003e\n    [-Server \u003cString\u003e]\n    [\u003cCommonParameters\u003e]"
                                    ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:10:07Z",
                         "Synopsis":  "",
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_general_ad_88bcabfe",
                         "Operation":  "delete",
                         "Notes":  "",
                         "UpdatedAt":  "2025-07-28T16:10:07Z",
                         "Parameters":  [
                                            "Confirm",
                                            "Credential",
                                            "Identity",
                                            "Server"
                                        ],
                         "BestPractices":  [

                                           ],
                         "Title":  "Microsoft Docs: Remove-ADReplicationSiteLinkBridge",
                         "CodeTemplate":  "Remove-ADReplicationSiteLinkBridge\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType \u003cADAuthType\u003e]\n    [-Credential \u003cPSCredential\u003e]\n    [-Identity] \u003cADReplicationSiteLinkBridge\u003e\n    [-Server \u003cString\u003e]\n    [\u003cCommonParameters\u003e]",
                         "CommonMistakes":  [

                                            ],
                         "Examples":  [
                                          "Remove-ADReplicationSiteLinkBridge\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType \u003cADAuthType\u003e]\n    [-Credential \u003cPSCredential\u003e]\n    [-Identity] \u003cADReplicationSiteLinkBridge\u003e\n    [-Server \u003cString\u003e]\n    [\u003cCommonParameters\u003e]",
                                          "PS C:\\\u003e Remove-ADReplicationSiteLinkBridge -Identity \"NorthAmerica-Asia\"",
                                          "PS C:\\\u003e Get-ADReplicationSiteLinkBridge -Filter \"SiteLinksIncluded -eq \u0027Europe-Asia\u0027\" | Remove-ADReplicationSiteLinkBridge"
                                      ],
                         "Description":  "",
                         "Domain":  "general_ad",
                         "RequiredParameters":  [

                                                ],
                         "RelatedLinks":  [

                                          ],
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "general_ad",
                                      "delete"
                                  ],
                         "Abstract":  " \u003c!DOCTYPE html\u003e\n\t\t\u003chtml\n\t\t\tclass=\"layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus\"\n\t\t\tlang=\"en-us\"\n\t\t\tdir=\"ltr\"\n\t\t\tdata-authenticated=\"false\"\n\t\t\tdata-auth-status-determined=\"false\"\n\t\t\tdata-target=\"docs\"\n\t\t\tx-ms-format-detection=\"none\"\n\t\t\u003e\n\t\t\t\n\t\t\u003chead\u003e\n\t\t\t\u003ctitle\u003eRemove-ADReplicationSiteLinkBridge (ActiveDirectory) | Microsoft Learn\u003c/title\u003e\n\t\t\t\u003cmeta charset=\"utf-8\" /\u003e\n\t\t\t\u003cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" /\u003e\n\t\t\t\u003cmeta nam...",
                         "Sources":  [
                                         {
                                             "SourceType":  "microsoft_docs",
                                             "CredibilityScore":  0.95,
                                             "PublishedAt":  "2025-07-28T16:10:07Z",
                                             "Url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-adreplicationsitelinkbridge",
                                             "ScrapedAt":  "2025-07-28T16:10:07Z",
                                             "Id":  "16f0156a-8cb1-473c-a9b1-9bdaa02ccd65",
                                             "Author":  "Microsoft",
                                             "Title":  "Microsoft Docs: Remove-ADReplicationSiteLinkBridge"
                                         }
                                     ],
                         "Content":  " \u003c!DOCTYPE html\u003e\n\t\t\u003chtml\n\t\t\tclass=\"layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus\"\n\t\t\tlang=\"en-us\"\n\t\t\tdir=\"ltr\"\n\t\t\tdata-authenticated=\"false\"\n\t\t\tdata-auth-status-determined=\"false\"\n\t\t\tdata-target=\"docs\"\n\t\t\tx-ms-format-detection=\"none\"\n\t\t\u003e\n\t\t\t\n\t\t\u003chead\u003e\n\t\t\t\u003ctitle\u003eRemove-ADReplicationSiteLinkBridge (ActiveDirectory) | Microsoft Learn\u003c/title\u003e\n\t\t\t\u003cmeta charset=\"utf-8\" /\u003e\n\t\t\t\u003cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" /\u003e\n\t\t\t\u003cmeta name=\"color-scheme\" content=\"light dark\" /\u003e\n\n\t\t\t\u003cmeta name=\"description\" content=\"Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.\" /\u003e\n\t\t\t\u003clink rel=\"canonical\" href=\"https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-adreplicationsitelinkbridge?view=windowsserver2025-ps\" /\u003e \n\n\t\t\t\u003c!-- Non-customizable open graph and sharing-related metadata --\u003e\n\t\t\t\u003cmeta name=\"twitter:card\" content=\"summary_large_image\" /\u003e\n\t\t\t\u003cmeta name=\"twitter:site\" content=\"@MicrosoftLearn\" /\u003e\n\t\t\t\u003cmeta property=\"og:type\" content=\"website\" /\u003e\n\t\t\t\u003cmeta property=\"og:image:alt\" content=\"Microsoft Learn\" /\u003e\n\t\t\t\u003cmeta property=\"og:image\" content=\"https://learn.microsoft.com/en-us/media/open-graph-image.png\" /\u003e\n\t\t\t\u003c!-- Page specific open graph and sharing-related metadata --\u003e\n\t\t\t\u003cmeta property=\"og:title\" content=\"Remove-ADReplicationSiteLinkBridge (ActiveDirectory)\" /\u003e\n\t\t\t\u003cmeta property=\"og:url\" content=\"https://learn.microsoft.com/en-us/powershell/module/activedirectory/remove-adreplicationsitelinkbridge?view=windowsserver2025-ps\" /\u003e\n\t\t\t\u003cmeta property=\"og:description\" content=\"Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.\" /\u003e\n\t\t\t\u003cmeta name=\"platform_id\" content=\"be567795-4669-b2d1-0030-d4b1d21762fe\" /\u003e \n\t\t\t\u003cmeta name=\"locale\" content=\"en-us\" /\u003e\n\t\t\t \u003cmeta name=\"adobe-target\" content=\"true\" /\u003e\n\t\t\t\u003cmeta name=\"uhfHeaderId\" content=\"MSDocsHeader-M365-IT\" /\u003e\n\n\t\t\t\u003cmeta name=\"page_type\" content=\"powershell\" /\u003e\n\n\t\t\t\u003c!--page specific meta tags--\u003e\n\t\t\t\n\n\t\t\t\u003c!-- custom meta tags --\u003e\n\t\t\t\n\t\t\u003cmeta name=\"uid\" content=\"ActiveDirectory.Remove-ADReplicationSiteLinkBridge\" /\u003e\n\t\n\t\t\u003cmeta name=\"module\" content=\"ActiveDirectory\" /\u003e\n\t\n\t\t\u003cmeta name=\"schema\" content=\"PowerShellCmdlet1\" /\u003e\n\t\n\t\t\u003cmeta name=\"ROBOTS\" content=\"INDEX, FOLLOW\" /\u003e\n\t\n\t\t\u003cmeta name=\"apiPlatform\" content=\"powershell\" /\u003e\n\t\n\t\t\u003cmeta name=\"archive_url\" content=\"https://learn.microsoft.com/previous-versions/powershell/windows/get-started\" /\u003e\n\t\n\t\t\u003cmeta name=\"author\" content=\"robinharwood\" /\u003e\n\t\n\t\t\u003cmeta name=\"breadcrumb_path\" content=\"/powershell/windows/bread/toc.json\" /\u003e\n\t\n\t\t\u003cmeta name=\"feedback_product_url\" content=\"https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332\" /\u003e\n\t\n\t\t\u003cmeta name=\"feedback_system\" content=\"Standard\" /\u003e\n\t\n\t\t\u003cmeta name=\"manager\" content=\"tedhudek\" /\u003e\n\t\n\t\t\u003cmeta name=\"ms.author\" content=\"roharwoo\" /\u003e\n\t\n\t\t\u003cmeta name=\"ms.devlang\" content=\"powershell\" /\u003e\n\t\n\t\t\u003cmeta name=\"ms.service\" content=\"windows-11\" /\u003e\n\t\n\t\t\u003cmeta name=\"ms.topic\" content=\"reference\" /\u003e\n\t\n\t\t\u003cmeta name=\"products\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/56936876-97d9-45cc-ad1b-9d63320447c8\" /\u003e\n\t\n\t\t\u003cmeta name=\"products\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/56754133-c3c3-4a9f-af19-71bdbe19fccf\" /\u003e\n\t\n\t\t\u003cmeta name=\"document type\" content=\"cmdlet\" /\u003e\n\t\n\t\t\u003cmeta name=\"external help file\" content=\"Microsoft.ActiveDirectory.Management.dll-Help.xml\" /\u003e\n\t\n\t\t\u003cmeta name=\"HelpUri\" content=\"https://learn.microsoft.com/powershell/module/activedirectory/remove-adreplicationsitelinkbridge?view=windowsserver2025-ps\u0026amp;wt.mc_id=ps-gethelp\" /\u003e\n\t\n\t\t\u003cmeta name=\"Module Name\" content=\"ActiveDirectory\" /\u003e\n\t\n\t\t\u003cmeta name=\"ms.date\" content=\"2016-12-27T00:00:00Z\" /\u003e\n\t\n\t\t\u003cmeta name=\"PlatyPS schema version\" content=\"2024-05-01T00:00:00Z\" /\u003e\n\t\n\t\t\u003cmeta name=\"document_id\" content=\"64e7fbb4-2ca3-1556-9cc9-82aa188b9bf9\" /\u003e\n\t\n\t\t\u003cmeta name=\"document_version_independent_id\" content=\"39d9cfbb-3caf-cbba-1960-ef56866a57ff\" /\u003e\n\t\n\t\t\u003cmeta name=\"updated_at\" content=\"2025-05-14T22:44:00Z\" /\u003e\n\t\n\t\t\u003cmeta name=\"original_content_git_url\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\" /\u003e\n\t\n\t\t\u003cmeta name=\"gitcommit\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/0ef3f225d29e26d1cf3119f37dfff70bb6165746/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\" /\u003e\n\t\n\t\t\u003cmeta name=\"git_commit_id\" content=\"0ef3f225d29e26d1cf3119f37dfff70bb6165746\" /\u003e\n\t\n\t\t\u003cmeta name=\"monikers\" content=\"windowsserver2025-ps\" /\u003e\n\t\n\t\t\u003cmeta name=\"default_moniker\" content=\"windowsserver2025-ps\" /\u003e\n\t\n\t\t\u003cmeta name=\"site_name\" content=\"Docs\" /\u003e\n\t\n\t\t\u003cmeta name=\"depot_name\" content=\"TechNet.windows-powershell\" /\u003e\n\t\n\t\t\u003cmeta name=\"in_right_rail\" content=\"h2h3\" /\u003e\n\t\n\t\t\u003cmeta name=\"page_kind\" content=\"command\" /\u003e\n\t\n\t\t\u003cmeta name=\"toc_rel\" content=\"../windowsserver2025-ps/toc.json\" /\u003e\n\t\n\t\t\u003cmeta name=\"feedback_help_link_type\" content=\"\" /\u003e\n\t\n\t\t\u003cmeta name=\"feedback_help_link_url\" content=\"\" /\u003e\n\t\n\t\t\u003cmeta name=\"config_moniker_range\" content=\"WindowsServer2025-ps\" /\u003e\n\t\n\t\t\u003cmeta name=\"asset_id\" content=\"module/activedirectory/remove-adreplicationsitelinkbridge\" /\u003e\n\t\n\t\t\u003cmeta name=\"moniker_range_name\" content=\"ffb05b7b47577225af7c7b6a20151268\" /\u003e\n\t\n\t\t\u003cmeta name=\"item_type\" content=\"Content\" /\u003e\n\t\n\t\t\u003cmeta name=\"source_path\" content=\"docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\" /\u003e\n\t\n\t\t\u003cmeta name=\"github_feedback_content_git_url\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\" /\u003e\n\t \n\t\t\u003cmeta name=\"cmProducts\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/bcbcbad5-4208-4783-8035-8481272c98b8\" data-source=\"generated\" /\u003e\n\t\n\t\t\u003cmeta name=\"spProducts\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/43b2e5aa-8a6d-4de2-a252-692232e5edc8\" data-source=\"generated\" /\u003e\n\t\n\n\t\t\t\u003c!-- assets and js globals --\u003e\n\t\t\t\n\t\t\t\u003clink rel=\"stylesheet\" href=\"/static/assets/0.4.03126.7002-3880ccdd/styles/site-ltr.css\" /\u003e\n\t\t\t\u003clink rel=\"preconnect\" href=\"//mscom.demdex.net\" crossorigin /\u003e\n\t\t\t\t\t\t\u003clink rel=\"dns-prefetch\" href=\"//target.microsoft.com\" /\u003e\n\t\t\t\t\t\t\u003clink rel=\"dns-prefetch\" href=\"//microsoftmscompoc.tt.omtrdc.net\" /\u003e\n\t\t\t\t\t\t\u003clink\n\t\t\t\t\t\t\trel=\"preload\"\n\t\t\t\t\t\t\tas=\"script\"\n\t\t\t\t\t\t\thref=\"/static/third-party/adobe-target/at-js/2.9.0/at.js\"\n\t\t\t\t\t\t\tintegrity=\"sha384-1/viVM50hgc33O2gOgkWz3EjiD/Fy/ld1dKYXJRUyjNYVEjSUGcSN+iPiQF7e4cu\"\n\t\t\t\t\t\t\tcrossorigin=\"anonymous\"\n\t\t\t\t\t\t\tid=\"adobe-target-script\"\n\t\t\t\t\t\t\ttype=\"application/javascript\"\n\t\t\t\t\t\t/\u003e\n\t\t\t\u003cscript src=\"https://wcpstatic.microsoft.com/mscc/lib/v2/wcp-consent.js\"\u003e\u003c/script\u003e\n\t\t\t\u003cscript src=\"https://js.monitor.azure.com/scripts/c/ms.jsll-4.min.js\"\u003e\u003c/script\u003e\n\t\t\t\u003cscript src=\"/_themes/docs.theme/master/en-us/_themes/global/deprecation.js\"\u003e\u003c/script\u003e\n\n\t\t\t\u003c!-- msdocs global object --\u003e\n\t\t\t\u003cscript id=\"msdocs-script\"\u003e\n\t\tvar msDocs = {\n  \"environment\": {\n    \"accessLevel\": \"online\",\n    \"azurePortalHostname\": \"portal.azure.com\",\n    \"reviewFeatures\": false,\n    \"supportLevel\": \"production\",\n    \"systemContent\": true,\n    \"siteName\": \"learn\",\n    \"legacyHosting\": false\n  },\n  \"data\": {\n    \"contentLocale\": \"en-us\",\n    \"contentDir\": \"ltr\",\n    \"userLocale\": \"en-us\",\n    \"userDir\": \"ltr\",\n    \"pageTemplate\": \"Reference\",\n    \"brand\": \"\",\n    \"context\": {},\n    \"standardFeedback\": true,\n    \"showFeedbackReport\": false,\n    \"feedbackHelpLinkType\": \"\",\n    \"feedbackHelpLinkUrl\": \"\",\n    \"feedbackSystem\": \"Standard\",\n    \"feedbackGitHubRepo\": \"\",\n    \"feedbackProductUrl\": \"https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332\",\n    \"extendBreadcrumb\": true,\n    \"isEditDisplayable\": true,\n    \"isPrivateUnauthorized\": false,\n    \"hideViewSource\": false,\n    \"isPermissioned\": false,\n    \"hasRecommendations\": false,\n    \"contributors\": []\n  },\n  \"functions\": {}\n};;\n\t\u003c/script\u003e\n\n\t\t\t\u003c!-- base scripts, msdocs global should be before this --\u003e\n\t\t\t\u003cscript src=\"/static/assets/0.4.03126.7002-3880ccdd/scripts/en-us/index-docs.js\"\u003e\u003c/script\u003e\n\t\t\t\n\n\t\t\t\u003c!-- json-ld --\u003e\n\t\t\t\n\t\t\u003c/head\u003e\n\t\n\t\t\t\u003cbody\n\t\t\t\tid=\"body\"\n\t\t\t\tdata-bi-name=\"body\"\n\t\t\t\tclass=\"layout-body \"\n\t\t\t\tlang=\"en-us\"\n\t\t\t\tdir=\"ltr\"\n\t\t\t\u003e\n\t\t\t\t\u003cheader class=\"layout-body-header\"\u003e\n\t\t\u003cdiv class=\"header-holder has-default-focus\"\u003e\n\t\t\t\n\t\t\u003ca\n\t\t\thref=\"#main\"\n\t\t\t\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\t\n\t\t\u003e\n\t\t\tSkip to main content\n\t\t\u003c/a\u003e\n\t\n\t\t\u003ca\n\t\t\thref=\"#side-doc-outline\"\n\t\t\t\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\t\n\t\t\u003e\n\t\t\tSkip to in-page navigation\n\t\t\u003c/a\u003e\n\t\n\t\t\u003ca\n\t\t\thref=\"#\"\n\t\t\tdata-skip-to-ask-learn\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\thidden\n\t\t\u003e\n\t\t\tSkip to Ask Learn chat experience\n\t\t\u003c/a\u003e\n\t\n\n\t\t\t\u003cdiv hidden id=\"cookie-consent-holder\" data-test-id=\"cookie-consent-container\"\u003e\u003c/div\u003e\n\t\t\t\u003c!-- Unsupported browser warning --\u003e\n\t\t\t\u003cdiv\n\t\t\t\tid=\"unsupported-browser\"\n\t\t\t\tstyle=\"background-color: white; color: black; padding: 16px; border-bottom: 1px solid grey;\"\n\t\t\t\thidden\n\t\t\t\u003e\n\t\t\t\t\u003cdiv style=\"max-width: 800px; margin: 0 auto;\"\u003e\n\t\t\t\t\t\u003cp style=\"font-size: 24px\"\u003eThis browser is no longer supported.\u003c/p\u003e\n\t\t\t\t\t\u003cp style=\"font-size: 16px; margin-top: 16px;\"\u003e\n\t\t\t\t\t\tUpgrade to Microsoft Edge to take advantage of the latest features, security updates, and technical support.\n\t\t\t\t\t\u003c/p\u003e\n\t\t\t\t\t\u003cdiv style=\"margin-top: 12px;\"\u003e\n\t\t\t\t\t\t\u003ca\n\t\t\t\t\t\t\thref=\"https://go.microsoft.com/fwlink/p/?LinkID=2092881 \"\n\t\t\t\t\t\t\tstyle=\"background-color: #0078d4; border: 1px solid #0078d4; color: white; padding: 6px 12px; border-radius: 2px; display: inline-block;\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\tDownload Microsoft Edge\n\t\t\t\t\t\t\u003c/a\u003e\n\t\t\t\t\t\t\u003ca\n\t\t\t\t\t\t\thref=\"https://learn.microsoft.com/en-us/lifecycle/faq/internet-explorer-microsoft-edge\"\n\t\t\t\t\t\t\tstyle=\"background-color: white; padding: 6px 12px; border: 1px solid #505050; color: #171717; border-radius: 2px; display: inline-block;\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\tMore info about Internet Explorer and Microsoft Edge\n\t\t\t\t\t\t\u003c/a\u003e\n\t\t\t\t\t\u003c/div\u003e\n\t\t\t\t\u003c/div\u003e\n\t\t\t\u003c/div\u003e\n\t\t\t\u003c!-- site header --\u003e\n\t\t\t\u003cheader\n\t\t\t\tid=\"ms--site-header\"\n\t\t\t\tdata-test-id=\"site-header-wrapper\"\n\t\t\t\trole=\"banner\"\n\t\t\t\titemscope=\"itemscope\"\n\t\t\t\titemtype=\"http://schema.org/Organization\"\n\t\t\t\u003e\n\t\t\t\t\u003cdiv\n\t\t\t\t\tid=\"ms--mobile-nav\"\n\t\t\t\t\tclass=\"site-header display-none-tablet padding-inline-none gap-none\"\n\t\t\t\t\tdata-bi-name=\"mobile-header\"\n\t\t\t\t\tdata-test-id=\"mobile-header\"\n\t\t\t\t\u003e\u003c/div\u003e\n\t\t\t\t\u003cdiv\n\t\t\t\t\tid=\"ms--primary-nav\"\n\t\t\t\t\tclass=\"site-header display-none display-flex-tablet\"\n\t\t\t\t\tdata-bi-name=\"L1-header\"\n\t\t\t\t\tdata-test-id=\"primary-header\"\n\t\t\t\t\u003e\u003c/div\u003e\n\t\t\t\t\u003cdiv\n\t\t\t\t\tid=\"ms--secondary-nav\"\n\t\t\t\t\tclass=\"site-header display-none display-flex-tablet\"\n\t\t\t\t\tdata-bi-name=\"L2-header\"\n\t\t\t\t\tdata-test-id=\"secondary-header\"\n\t\t\t\t\u003e\u003c/div\u003e\n\t\t\t\u003c/header\u003e\n\t\t\t\n\t\t\u003c!-- banner --\u003e\n\t\t\u003cdiv data-banner\u003e\n\t\t\t\u003cdiv id=\"disclaimer-holder\"\u003e\u003c/div\u003e\n\t\t\t\n\t\t\u003c/div\u003e\n\t\t\u003c!-- banner end --\u003e\n\t\n\t\t\u003c/div\u003e\n\t\u003c/header\u003e\n\t\t\t\t \u003csection\n\t\t\t\t\tid=\"layout-body-menu\"\n\t\t\t\t\tclass=\"layout-body-menu display-flex\"\n\t\t\t\t\tdata-bi-name=\"menu\"\n\t\t\t  \u003e\n\t\t\t\t\t\u003cdiv\n\t\tid=\"left-container\"\n\t\tclass=\"left-container display-none display-block-tablet padding-inline-sm padding-bottom-sm width-full\"\n\t\u003e\n\t\t\u003cnav\n\t\t\tid=\"affixed-left-container\"\n\t\t\tclass=\"margin-top-sm-tablet position-sticky display-flex flex-direction-column\"\n\t\t\taria-label=\"Primary\"\n\t\t\u003e\u003c/nav\u003e\n\t\u003c/div\u003e\n\t\t\t  \u003c/section\u003e\n\n\t\t\t\t\u003cmain\n\t\t\t\t\tid=\"main\"\n\t\t\t\t\trole=\"main\"\n\t\t\t\t\tclass=\"layout-body-main \"\n\t\t\t\t\tdata-bi-name=\"content\"\n\t\t\t\t\tlang=\"en-us\"\n\t\t\t\t\tdir=\"ltr\"\n\t\t\t\t\u003e\n\t\t\t\t\t\n\t\t\t\u003cdiv\n\t\tid=\"ms--content-header\"\n\t\tclass=\"content-header default-focus border-bottom-none\"\n\t\tdata-bi-name=\"content-header\"\n\t\u003e\n\t\t\u003cdiv class=\"content-header-controls margin-xxs margin-inline-sm-tablet\"\u003e\n\t\t\t\u003cbutton\n\t\t\t\ttype=\"button\"\n\t\t\t\tclass=\"contents-button button button-sm margin-right-xxs\"\n\t\t\t\tdata-bi-name=\"contents-expand\"\n\t\t\t\taria-haspopup=\"true\"\n\t\t\t\tdata-contents-button\n\t\t\t\u003e\n\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\u003cspan class=\"docon docon-menu\"\u003e\u003c/span\u003e\u003c/span\u003e\n\t\t\t\t\u003cspan class=\"contents-expand-title\"\u003e Table of contents \u003c/span\u003e\n\t\t\t\u003c/button\u003e\n\t\t\t\u003cbutton\n\t\t\t\ttype=\"button\"\n\t\t\t\tclass=\"ap-collapse-behavior ap-expanded button button-sm\"\n\t\t\t\tdata-bi-name=\"ap-collapse\"\n\t\t\t\taria-controls=\"action-panel\"\n\t\t\t\u003e\n\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\u003cspan class=\"docon docon-exit-mode\"\u003e\u003c/span\u003e\u003c/span\u003e\n\t\t\t\t\u003cspan\u003eExit editor mode\u003c/span\u003e\n\t\t\t\u003c/button\u003e\n\t\t\u003c/div\u003e\n\t\u003c/div\u003e\n\t\t\t\u003cdiv data-main-column class=\"padding-sm padding-top-none padding-top-sm-tablet\"\u003e\n\t\t\t\t\u003cdiv\u003e\n\t\t\t\t\t\n\t\t\u003cdiv id=\"article-header\" class=\"background-color-body margin-bottom-xs display-none-print\"\u003e\n\t\t\t\u003cdiv class=\"display-flex align-items-center justify-content-space-between\"\u003e\n\t\t\t\t\n\t\t\u003cdetails\n\t\t\tid=\"article-header-breadcrumbs-overflow-popover\"\n\t\t\tclass=\"popover\"\n\t\t\tdata-for=\"article-header-breadcrumbs\"\n\t\t\u003e\n\t\t\t\u003csummary\n\t\t\t\tclass=\"button button-clear button-primary button-sm inner-focus\"\n\t\t\t\taria-label=\"All breadcrumbs\"\n\t\t\t\u003e\n\t\t\t\t\u003cspan class=\"icon\"\u003e\n\t\t\t\t\t\u003cspan class=\"docon docon-more\"\u003e\u003c/span\u003e\n\t\t\t\t\u003c/span\u003e\n\t\t\t\u003c/summary\u003e\n\t\t\t\u003cdiv id=\"article-header-breadcrumbs-overflow\" class=\"popover-content padding-none\"\u003e\u003c/div\u003e\n\t\t\u003c/details\u003e\n\n\t\t\u003cbread-crumbs\n\t\t\tid=\"article-header-breadcrumbs\"\n\t\t\tdata-test-id=\"article-header-breadcrumbs\"\n\t\t\tclass=\"overflow-hidden flex-grow-1 margin-right-sm margin-right-md-tablet margin-right-lg-desktop margin-left-negative-xxs padding-left-xxs\"\n\t\t\u003e\u003c/bread-crumbs\u003e\n\t \n\t\t\u003cdiv\n\t\t\tid=\"article-header-page-actions\"\n\t\t\tclass=\"opacity-none margin-left-auto display-flex flex-wrap-no-wrap align-items-stretch\"\n\t\t\u003e\n\t\t\t\n\t\t\u003cbutton\n\t\t\tclass=\"button button-sm border-none inner-focus display-none-tablet flex-shrink-0 \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-modal-entry-mobile\"\n\t\t\tdata-ask-learn-modal-entry\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\taria-label=\"Ask Learn\"\n\t\t\thidden\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon font-size-lg\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t\t\u003cbutton\n\t\t\tclass=\"button button-sm display-none display-inline-flex-tablet display-none-desktop flex-shrink-0 margin-right-xxs \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-modal-entry-tablet\"\n\t\t\tdata-ask-learn-modal-entry\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\thidden\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon font-size-lg\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan\u003eAsk Learn\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t\t\u003cbutton\n\t\t\tclass=\"button button-sm display-none flex-shrink-0 display-inline-flex-desktop margin-right-xxs\t \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-flyout-entry\"\n\t\t\tdata-ask-learn-flyout-entry\n\t\t\tdata-flyout-button=\"toggle\"\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\taria-controls=\"ask-learn-flyout\"\n\t\t\thidden\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon font-size-lg\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan\u003eAsk Learn\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t \n\t\t\u003cbutton\n\t\t\ttype=\"button\"\n\t\t\tid=\"ms--focus-mode-button\"\n\t\t\tdata-focus-mode\n\t\t\tdata-bi-name=\"focus-mode-entry\"\n\t\t\tclass=\"button button-sm flex-shrink-0 margin-right-xxs display-none display-inline-flex-desktop\"\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon font-size-lg\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-glasses\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan\u003eFocus mode\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t \n\n\t\t\t\u003cdetails class=\"popover popover-right\" id=\"article-header-page-actions-overflow\"\u003e\n\t\t\t\t\u003csummary\n\t\t\t\t\tclass=\"justify-content-flex-start button button-clear button-sm button-primary inner-focus\"\n\t\t\t\t\taria-label=\"More actions\"\n\t\t\t\t\ttitle=\"More actions\"\n\t\t\t\t\u003e\n\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-more-vertical\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003cdiv class=\"popover-content\"\u003e\n\t\t\t\t\t\n\t\t\u003cbutton\n\t\t\tdata-page-action-item=\"overflow-mobile\"\n\t\t\ttype=\"button\"\n\t\t\tclass=\"button-block button-sm has-inner-focus button button-clear display-none-tablet justify-content-flex-start text-align-left\"\n\t\t\tdata-bi-name=\"contents-expand\"\n\t\t\tdata-contents-button\n\t\t\tdata-popover-close\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-editor-list-bullet\" aria-hidden=\"true\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan class=\"contents-expand-title\"\u003eTable of contents\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t \n\t\t\u003ca\n\t\t\tid=\"lang-link-overflow\"\n\t\t\tclass=\"button-sm has-inner-focus button button-clear button-block justify-content-flex-start text-align-left\"\n\t\t\tdata-bi-name=\"language-toggle\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-read-in-link\n\t\t\thref=\"#\"\n\t\t\thidden\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\" data-read-in-link-icon\u003e\n\t\t\t\t\u003cspan class=\"docon docon-locale-globe\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan data-read-in-link-text\u003eRead in English\u003c/span\u003e\n\t\t\u003c/a\u003e\n\t \n\t\t\u003cbutton\n\t\t\ttype=\"button\"\n\t\t\tclass=\"collection button button-clear button-sm button-block justify-content-flex-start text-align-left inner-focus\"\n\t\t\tdata-list-type=\"collection\"\n\t\t\tdata-bi-name=\"collection\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-popover-close\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-circle-addition\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan class=\"collection-status\"\u003eAdd\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t\n\t\t\t\t\t\n\t\t\u003cbutton\n\t\t\ttype=\"button\"\n\t\t\tclass=\"collection button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus\"\n\t\t\tdata-list-type=\"plan\"\n\t\t\tdata-bi-name=\"plan\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-popover-close\n\t\t\thidden\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-circle-addition\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan class=\"plan-status\"\u003eAdd to plan\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t  \n\t\t\u003ca\n\t\t\tdata-contenteditbtn\n\t\t\tclass=\"button button-clear button-block button-sm inner-focus justify-content-flex-start text-align-left text-decoration-none\"\n\t\t\tdata-bi-name=\"edit\"\n\t\t\t\n\t\t\thref=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\"\n\t\t\tdata-original_content_git_url=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\"\n\t\t\tdata-original_content_git_url_template=\"{repo}/blob/{branch}/docset/winserver2025-ps/ActiveDirectory/Remove-ADReplicationSiteLinkBridge.md\"\n\t\t\tdata-pr_repo=\"\"\n\t\t\tdata-pr_branch=\"\"\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-edit-outline\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan\u003eEdit\u003c/span\u003e\n\t\t\u003c/a\u003e\n\t\n\t\t\t\t\t\n\t\t\u003chr class=\"margin-block-xxs\" /\u003e\n\t\t\u003ch4 class=\"font-size-sm padding-left-xxs\"\u003eShare via\u003c/h4\u003e\n\t\t\n\t\t\t\t\t\u003ca\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-facebook\"\n\t\t\t\t\t\tdata-bi-name=\"facebook\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon color-primary\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-facebook-share\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eFacebook\u003c/span\u003e\n\t\t\t\t\t\u003c/a\u003e\n\n\t\t\t\t\t\u003ca\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-twitter\"\n\t\t\t\t\t\tdata-bi-name=\"twitter\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon color-text\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-xlogo-share\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003ex.com\u003c/span\u003e\n\t\t\t\t\t\u003c/a\u003e\n\n\t\t\t\t\t\u003ca\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-linkedin\"\n\t\t\t\t\t\tdata-bi-name=\"linkedin\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon color-primary\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-linked-in-logo\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eLinkedIn\u003c/span\u003e\n\t\t\t\t\t\u003c/a\u003e\n\t\t\t\t\t\u003ca\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-email\"\n\t\t\t\t\t\tdata-bi-name=\"email\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon color-primary\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-mail-message\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eEmail\u003c/span\u003e\n\t\t\t\t\t\u003c/a\u003e\n\t\t\t  \n\t \n\t\t\u003chr class=\"margin-block-xxs\" /\u003e\n\t\t\u003cbutton\n\t\t\tclass=\"button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus\"\n\t\t\ttype=\"button\"\n\t\t\tdata-bi-name=\"print\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-popover-close\n\t\t\tdata-print-page\n\t\t\tdata-check-hidden=\"true\"\n\t\t\u003e\n\t\t\t\u003cspan class=\"icon color-primary\" aria-hidden=\"true\"\u003e\n\t\t\t\t\u003cspan class=\"docon docon-print\"\u003e\u003c/span\u003e\n\t\t\t\u003c/span\u003e\n\t\t\t\u003cspan\u003ePrint\u003c/span\u003e\n\t\t\u003c/button\u003e\n\t\n\t\t\t\t\u003c/div\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003c/div\u003e\n\t\n\t\t\t\u003c/div\u003e\n\t\t\u003c/div\u003e\n\t\n\t\t\t\t\t\u003c!-- azure disclaimer --\u003e\n\t\t\t\t\t\n\t\t\t\t\t\u003c!-- privateUnauthorizedTemplate is hidden by default --\u003e\n\t\t\t\t\t\n\t\t\u003cdiv unauthorized-private-section data-bi-name=\"permission-content-unauthorized-private\" hidden\u003e\n\t\t\t\u003chr class=\"hr margin-top-xs margin-bottom-sm\" /\u003e\n\t\t\t\u003cdiv class=\"notification notification-info\"\u003e\n\t\t\t\t\u003cdiv class=\"notification-content\"\u003e\n\t\t\t\t\t\u003cp class=\"margin-top-none notification-title\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-exclamation-circle-solid\" aria-hidden=\"true\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eNote\u003c/span\u003e\n\t\t\t\t\t\u003c/p\u003e\n\t\t\t\t\t\u003cp class=\"margin-top-none authentication-determined not-authenticated\"\u003e\n\t\t\t\t\t\tAccess to this page requires authorization. You can try \u003ca class=\"docs-sign-in\" href=\"#\" data-bi-name=\"permission-content-sign-in\"\u003esigning in\u003c/a\u003e or \u003ca  class=\"docs-change-directory\" data-bi-name=\"permisson-content-change-directory\"\u003echanging directories\u003c/a\u003e.\n\t\t\t\t\t\u003c/p\u003e\n\t\t\t\t\t\u003cp class=\"margin-top-none authentication-determined authenticated\"\u003e\n\t\t\t\t\t\tAccess to this page requires authorization. You can try \u003ca class=\"docs-change-directory\" data-bi-name=\"permisson-content-change-directory\"\u003echanging directories\u003c/a\u003e.\n\t\t\t\t\t\u003c/p\u003e\n\t\t\t\t\u003c/div\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/div\u003e\n\t\n\t\t\t\t\t\u003cdiv class=\"content\"\u003e\u003c/div\u003e\n\t\t\t\t\t \n\t\t\t\t\t\u003cdiv class=\"content\"\u003e\u003ch1 data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\" class=\"margin-bottom-xs\"\u003eRemove-ADReplication\u003cwbr\u003eSite\u003cwbr\u003eLink\u003cwbr\u003eBridge\u003c/h1\u003e\n\n\t\u003cdiv class=\"margin-block-xxs\"\u003e\n\t\t\u003cul class=\"metadata page-metadata align-items-center\" data-bi-name=\"page info\"\u003e\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\u003c/ul\u003e\n\t\u003c/div\u003e\n\n\u003cdiv class=\"metadata\" data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003e\n\t\t\u003cdl class=\"attributeList\"\u003e\n\t\t\t\u003cdt\u003eModule:\u003c/dt\u003e\n\t\t\t\u003cdd\u003e\u003ca href=\"./?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eActiveDirectory Module\u003c/a\u003e\u003c/dd\u003e\n\t\t\u003c/dl\u003e\n\u003c/div\u003e\n\n\u003cnav id=\"center-doc-outline\" class=\"doc-outline is-hidden-desktop display-none-print margin-bottom-sm\" data-bi-name=\"intopic toc\" aria-label=\"\"\u003e\n  \u003ch2 class=\"title is-6 margin-block-xs\"\u003e\u003c/h2\u003e\n\u003c/nav\u003e\n\n\n\t\u003cdiv class=\"margin-block-sm\" data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003e\n\t\t\u003cp\u003eDeletes a replication site link bridge from Active Directory.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\n\t\u003ch2 id=\"syntax\" data-chunk-ids=\"default\"\u003eSyntax\u003c/h2\u003e\n\t\u003ch3 id=\"default\" data-chunk-ids=\"default\"\u003e\n\t\tDefault (Default)\n\t\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"default\"\u003e\n\t\t\u003cpre\u003e\u003ccode class=\"lang-Syntax\"\u003eRemove-ADReplicationSiteLinkBridge\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType \u0026lt;ADAuthType\u0026gt;]\n    [-Credential \u0026lt;PSCredential\u0026gt;]\n    [-Identity] \u0026lt;ADReplicationSiteLinkBridge\u0026gt;\n    [-Server \u0026lt;String\u0026gt;]\n    [\u0026lt;CommonParameters\u0026gt;]\n\u003c/code\u003e\u003c/pre\u003e\n\n\t\u003c/div\u003e\n\n\n\t\u003ch2 id=\"description\" data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003eDescription\u003c/h2\u003e\n\t\u003cdiv data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003e\n\t\t\u003cp\u003eThe \u003cstrong\u003eRemove-ADReplicationSiteLinkBridge\u003c/strong\u003e cmdlet deletes a replication site link bridge from Active Directory.\nA site link bridge connects two or more site links and enables transitivity between site links.\nEach site link in a bridge must have a site in common with another site link in the bridge.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\n\t\u003ch2 id=\"examples\" data-chunk-ids=\"example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges\"\u003eExamples\u003c/h2\u003e\n\t\u003ch3 id=\"example-1-remove-a-site-link-bridge\" data-chunk-ids=\"example-1-remove-a-site-link-bridge\"\u003eExample 1: Remove a site link bridge\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"example-1-remove-a-site-link-bridge\"\u003e\n\t\t\u003cpre\u003e\u003ccode\u003ePS C:\\\u0026gt; Remove-ADReplicationSiteLinkBridge -Identity \"NorthAmerica-Asia\"\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis command removes the site link bridge named NorthAmerica-Asia.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\t\u003ch3 id=\"example-2-remove-a-filtered-list-of-site-link-bridges\" data-chunk-ids=\"example-2-remove-a-filtered-list-of-site-link-bridges\"\u003eExample 2: Remove a filtered list of site link bridges\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"example-2-remove-a-filtered-list-of-site-link-bridges\"\u003e\n\t\t\u003cpre\u003e\u003ccode\u003ePS C:\\\u0026gt; Get-ADReplicationSiteLinkBridge -Filter \"SiteLinksIncluded -eq \u0027Europe-Asia\u0027\" | Remove-ADReplicationSiteLinkBridge\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis command gets the site link bridges that include Europe-Asia and removes them.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\n\t\u003ch2 id=\"parameters\" data-chunk-ids=\"authtype,confirm,credential,identity,server,whatif\"\u003eParameters\u003c/h2\u003e\n\t\t\u003ch3 id=\"-authtype\" data-chunk-ids=\"authtype\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Auth\u003cwbr\u003eType\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the authentication method to use.\nThe acceptable values for this parameter are:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eNegotiate or 0\u003c/li\u003e\n\u003cli\u003eBasic or 1\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eThe default authentication method is Negotiate.\u003c/p\u003e\n\u003cp\u003eA Secure Sockets Layer (SSL) connection is required for the Basic authentication method.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"authtype-properties\" data-chunk-ids=\"authtype\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"authtype\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eADAuthType\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eAccepted values:\u003c/td\u003e\u003ctd\u003eNegotiate, Basic\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"authtype-sets\" data-chunk-ids=\"authtype\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"authtype\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-confirm\" data-chunk-ids=\"confirm\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Confirm\u003c/h3\u003e\n\t\t\u003cp\u003ePrompts you for confirmation before running the cmdlet.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"confirm-properties\" data-chunk-ids=\"confirm\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"confirm\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eSwitchParameter\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eAliases:\u003c/td\u003e\u003ctd\u003ecf\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"confirm-sets\" data-chunk-ids=\"confirm\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"confirm\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-credential\" data-chunk-ids=\"credential\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Credential\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the user account credentials to use to perform this task.\nThe default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive.\nIf the cmdlet is run from such a provider drive, the account associated with the drive is the default.\u003c/p\u003e\n\u003cp\u003eTo specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a \u003cstrong\u003ePSCredential\u003c/strong\u003e object.\nIf you specify a user name for this parameter, the cmdlet prompts for a password.\u003c/p\u003e\n\u003cp\u003eYou can also create a \u003cstrong\u003ePSCredential\u003c/strong\u003e object by using a script or by using the \u003cstrong\u003eGet-Credential\u003c/strong\u003e cmdlet.\nYou can then set the \u003cem\u003eCredential\u003c/em\u003e parameter to the \u003cstrong\u003ePSCredential\u003c/strong\u003e object.\u003c/p\u003e\n\u003cp\u003eIf the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"credential-properties\" data-chunk-ids=\"credential\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"credential\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003ePSCredential\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"credential-sets\" data-chunk-ids=\"credential\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"credential\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-identity\" data-chunk-ids=\"identity\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Identity\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies an Active Directory object by providing one of the following property values.\nThe identifier in parentheses is the LDAP display name for the attribute.\nThe acceptable values for this parameter are:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eA distinguished name\u003c/li\u003e\n\u003cli\u003eA GUID (objectGUID)\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eThe cmdlet searches the default naming context or partition to find the object.\nIf two or more objects are found, the cmdlet returns a non-terminating error.\u003c/p\u003e\n\u003cp\u003eThis parameter can also get this object through the pipeline or you can set this parameter to an object instance.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"identity-properties\" data-chunk-ids=\"identity\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"identity\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eADReplicationSiteLinkBridge\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"identity-sets\" data-chunk-ids=\"identity\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"identity\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003e0\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-server\" data-chunk-ids=\"server\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Server\u003c/h3\u003e\n\t\t\u003cp\u003eThe default authentication method is Negotiate.\u003c/p\u003e\n\u003cp\u003eA Secure Sockets Layer (SSL) connection is required for the Basic authentication method.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"server-properties\" data-chunk-ids=\"server\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"server\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"server-sets\" data-chunk-ids=\"server\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"server\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-whatif\" data-chunk-ids=\"whatif\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-What\u003cwbr\u003eIf\u003c/h3\u003e\n\t\t\u003cp\u003eShows what would happen if the cmdlet runs.\nThe cmdlet is not run.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"whatif-properties\" data-chunk-ids=\"whatif\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"whatif\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eSwitchParameter\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eAliases:\u003c/td\u003e\u003ctd\u003ewi\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"whatif-sets\" data-chunk-ids=\"whatif\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"whatif\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"common-parameters\" data-no-chunk=\"\"\u003eCommonParameters\u003c/h3\u003e\n\t\t\u003cdiv data-no-chunk=\"\"\u003e\n\t\t\t\u003cp\u003eThis cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\n\u003ca href=\"https://go.microsoft.com/fwlink/?LinkID=113216\" data-linktype=\"external\"\u003eabout_CommonParameters\u003c/a\u003e.\u003c/p\u003e\n\n\t\t\u003c/div\u003e\n\n\t\u003ch2 id=\"inputs\" data-chunk-ids=\"inputs\"\u003eInputs\u003c/h2\u003e\n\t\t\t\u003ch3 id=\"input-1\" data-chunk-ids=\"inputs\" class=\"break-text font-size-xl\"\u003e\u003cspan class=\"no-loc xref\"\u003eNone or Microsoft.ActiveDirectory.Management.ADReplicationSiteLinkBridge\u003c/span\u003e\n\u003c/h3\u003e\n\t\t\t\u003cdiv data-chunk-ids=\"inputs\"\u003e\n\t\t\t\t\u003cp\u003eA site link bridge object is received by the \u003cem\u003eIdentity\u003c/em\u003e parameter.\u003c/p\u003e\n\n\t\t\t\u003c/div\u003e\n\n\t\u003ch2 id=\"outputs\" data-chunk-ids=\"outputs\"\u003eOutputs\u003c/h2\u003e\n\t\t\t\u003ch3 id=\"output-1\" data-chunk-ids=\"outputs\" class=\"break-text font-size-xl\"\u003e\u003cspan class=\"no-loc xref\"\u003eNone\u003c/span\u003e\n\u003c/h3\u003e\n\t\t\t\u003cdiv data-chunk-ids=\"outputs\"\u003e\n\t\t\t\t\n\t\t\t\u003c/div\u003e\n\n\t\u003ch2 id=\"notes\" data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003eNotes\u003c/h2\u003e\n\t\u003cdiv data-chunk-ids=\"inputs,outputs,default,example-1-remove-a-site-link-bridge,example-2-remove-a-filtered-list-of-site-link-bridges,authtype,confirm,credential,identity,server,whatif\"\u003e\n\t\t\u003cul\u003e\n\u003cli\u003eBy default, this cmdlet has the \u003cem\u003eConfirm\u003c/em\u003e parameter set, which prompts you to confirm before a removal of the specified object type can occur. To bypass prompting for confirmation before removal, you can specify \u003ccode\u003e-Confirm:$False\u003c/code\u003e when using this cmdlet.\u003c/li\u003e\n\u003c/ul\u003e\n\n\t\u003c/div\u003e\n\n\t\u003ch2 id=\"related-links\" data-no-chunk=\"\"\u003eRelated Links\u003c/h2\u003e\n\t\u003cul data-no-chunk=\"\"\u003e\n\t\t\t\u003cli\u003e\u003ca href=\"get-adreplicationsitelinkbridge?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eGet-ADReplicationSiteLinkBridge\u003c/a\u003e\u003c/li\u003e\n\t\t\t\u003cli\u003e\u003ca href=\"new-adreplicationsitelinkbridge?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eNew-ADReplicationSiteLinkBridge\u003c/a\u003e\u003c/li\u003e\n\t\t\t\u003cli\u003e\u003ca href=\"set-adreplicationsitelinkbridge?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eSet-ADReplicationSiteLinkBridge\u003c/a\u003e\u003c/li\u003e\n\t\u003c/ul\u003e\n\u003c/div\u003e\n\t\t\t\t\t\n\t\t\u003cdiv\n\t\t\tid=\"ms--inline-notifications\"\n\t\t\tclass=\"margin-block-xs\"\n\t\t\tdata-bi-name=\"inline-notification\"\n\t\t\u003e\u003c/div\u003e\n\t \n\t\t\u003cdiv\n\t\t\tid=\"assertive-live-region\"\n\t\t\trole=\"alert\"\n\t\t\taria-live=\"assertive\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t\u003e\u003c/div\u003e\n\t\t\u003cdiv\n\t\t\tid=\"polite-live-region\"\n\t\t\trole=\"status\"\n\t\t\taria-live=\"polite\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t\u003e\u003c/div\u003e\n\t\n\t\t\t\t\t\n\t\t\u003c!-- feedback section --\u003e\n\t\t\u003csection\n\t\t\tid=\"site-user-feedback-footer\"\n\t\t\tclass=\"font-size-sm margin-top-md display-none-print display-none-desktop\"\n\t\t\tdata-test-id=\"site-user-feedback-footer\"\n\t\t\tdata-bi-name=\"site-feedback-section\"\n\t\t\u003e\n\t\t\t\u003chr class=\"hr\" /\u003e\n\t\t\t\u003ch2 id=\"ms--feedback\" class=\"title is-3\"\u003eFeedback\u003c/h2\u003e\n\t\t\t\u003cdiv class=\"display-flex flex-wrap-wrap align-items-center\"\u003e\n\t\t\t\t\u003cp class=\"font-weight-semibold margin-xxs margin-left-none\"\u003e\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\u003c/p\u003e\n\t\t\t\t\u003cdiv class=\"buttons\"\u003e\n\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-yes\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-like\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eYes\u003c/span\u003e\n\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-no\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-dislike\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eNo\u003c/span\u003e\n\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\u003c/div\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/section\u003e\n\t\t\u003c!-- end feedback section --\u003e\n\t\n\t\t\t\t\u003c/div\u003e\n\t\t\t\t\n\t\t\t\u003c/div\u003e\n\t\t\t\n\t\t\u003cdiv\n\t\t\tid=\"action-panel\"\n\t\t\trole=\"region\"\n\t\t\taria-label=\"Action Panel\"\n\t\t\tclass=\"action-panel\"\n\t\t\ttabindex=\"-1\"\n\t\t\u003e\u003c/div\u003e\n\t\n\t\t\n\t\t\t\t\u003c/main\u003e\n\t\t\t\t\u003caside\n\t\t\t\t\tid=\"layout-body-aside\"\n\t\t\t\t\tclass=\"layout-body-aside \"\n\t\t\t\t\tdata-bi-name=\"aside\"\n\t\t\t  \u003e\n\t\t\t\t\t\n\t\t\u003cdiv\n\t\t\tid=\"ms--additional-resources\"\n\t\t\tclass=\"right-container padding-sm display-none display-block-desktop height-full\"\n\t\t\tdata-bi-name=\"pageactions\"\n\t\t\trole=\"complementary\"\n\t\t\taria-label=\"Additional resources\"\n\t\t\u003e\n\t\t\t\u003cdiv id=\"affixed-right-container\" data-bi-name=\"right-column\"\u003e\n\t\t\t\t\n\t\t\u003cnav\n\t\t\tid=\"side-doc-outline\"\n\t\t\tclass=\"doc-outline border-bottom padding-bottom-xs margin-bottom-xs\"\n\t\t\tdata-bi-name=\"intopic toc\"\n\t\t\taria-label=\"In this article\"\n\t\t\u003e\n\t\t\t\u003ch3\u003eIn this article\u003c/h3\u003e\n\t\t\u003c/nav\u003e\n\t\n\t\t\t\t\u003c!-- Feedback --\u003e\n\t\t\t\t\n\t\t\u003csection\n\t\t\tid=\"ms--site-user-feedback-right-rail\"\n\t\t\tclass=\"font-size-sm display-none-print\"\n\t\t\tdata-test-id=\"site-user-feedback-right-rail\"\n\t\t\tdata-bi-name=\"site-feedback-right-rail\"\n\t\t\u003e\n\t\t\t\u003cp class=\"font-weight-semibold margin-bottom-xs\"\u003eWas this page helpful?\u003c/p\u003e\n\t\t\t\u003cdiv class=\"buttons\"\u003e\n\t\t\t\t\u003cbutton\n\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-yes\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\u003e\n\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-like\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\u003cspan\u003eYes\u003c/span\u003e\n\t\t\t\t\u003c/button\u003e\n\t\t\t\t\u003cbutton\n\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-no\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\u003e\n\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-dislike\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\u003cspan\u003eNo\u003c/span\u003e\n\t\t\t\t\u003c/button\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/section\u003e\n\t\n\t\t\t\u003c/div\u003e\n\t\t\u003c/div\u003e\n\t\n\t\t\t  \u003c/aside\u003e \u003csection\n\t\t\t\t\tid=\"layout-body-flyout\"\n\t\t\t\t\tclass=\"layout-body-flyout \"\n\t\t\t\t\tdata-bi-name=\"flyout\"\n\t\t\t  \u003e\n\t\t\t\t\t \u003cdiv\n\tclass=\"height-full border-left background-color-body-medium\"\n\tid=\"ask-learn-flyout\"\n\u003e\u003c/div\u003e\n\t\t\t  \u003c/section\u003e \u003cdiv class=\"layout-body-footer \" data-bi-name=\"layout-footer\"\u003e\n\t\t\u003cfooter\n\t\t\tid=\"footer\"\n\t\t\tdata-test-id=\"footer\"\n\t\t\tdata-bi-name=\"footer\"\n\t\t\tclass=\"footer-layout has-padding has-default-focus border-top  uhf-container\"\n\t\t\trole=\"contentinfo\"\n\t\t\u003e\n\t\t\t\u003cdiv class=\"display-flex gap-xs flex-wrap-wrap is-full-height padding-right-lg-desktop\"\u003e\n\t\t\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"#\"\n\t\t\tdata-bi-name=\"select-locale\"\n\t\t\tclass=\"locale-selector-link flex-shrink-0 button button-sm button-clear external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003e\u003cspan class=\"icon\" aria-hidden=\"true\"\n\t\t\t\t\u003e\u003cspan class=\"docon docon-world\"\u003e\u003c/span\u003e\u003c/span\n\t\t\t\u003e\u003cspan class=\"local-selector-link-text\"\u003een-us\u003c/span\u003e\u003c/a\n\t\t\u003e\n\t\n\t\t\t\t\u003cdiv class=\"ccpa-privacy-link\" data-ccpa-privacy-link hidden\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://aka.ms/yourcaliforniaprivacychoices\"\n\t\t\tdata-bi-name=\"your-privacy-choices\"\n\t\t\tclass=\"button button-sm button-clear flex-shrink-0 external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003e\n\t\t\u003csvg\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\tviewBox=\"0 0 30 14\"\n\t\t\txml:space=\"preserve\"\n\t\t\theight=\"16\"\n\t\t\twidth=\"43\"\n\t\t\taria-hidden=\"true\"\n\t\t\tfocusable=\"false\"\n\t\t\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#fff\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7zm-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#06f\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M24.6 4c.******* 0 .8L22.5 7l2.2 2.2c.******* 0 .8-.2.2-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0-.2-.2-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0z\"\n\t\t\t\tstyle=\"fill:#fff\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M12.7 4.1c.*******.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0z\"\n\t\t\t\tstyle=\"fill:#06f\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\u003c/svg\u003e\n\t\n\t\t\t\u003cspan\u003eYour Privacy Choices\u003c/span\u003e\u003c/a\n\t\t\u003e\n\t\n\t\u003c/div\u003e\n\t\t\t\t\u003cdiv class=\"flex-shrink-0\"\u003e\n\t\t\u003cdiv class=\"dropdown has-caret-up\"\u003e\n\t\t\t\u003cbutton\n\t\t\t\tdata-test-id=\"theme-selector-button\"\n\t\t\t\tclass=\"dropdown-trigger button button-clear button-sm has-inner-focus theme-dropdown-trigger\"\n\t\t\t\taria-controls=\"{{ themeMenuId }}\"\n\t\t\t\taria-expanded=\"false\"\n\t\t\t\ttitle=\"Theme\"\n\t\t\t\tdata-bi-name=\"theme\"\n\t\t\t\u003e\n\t\t\t\t\u003cspan class=\"icon\"\u003e\n\t\t\t\t\t\u003cspan class=\"docon docon-sun\" aria-hidden=\"true\"\u003e\u003c/span\u003e\n\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003cspan\u003eTheme\u003c/span\u003e\n\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\u003c/span\u003e\n\t\t\t\u003c/button\u003e\n\t\t\t\u003cdiv class=\"dropdown-menu\" id=\"{{ themeMenuId }}\" role=\"menu\"\u003e\n\t\t\t\t\u003cul class=\"theme-selector padding-xxs\" data-test-id=\"theme-dropdown-menu\"\u003e\n\t\t\t\t\t\u003cli class=\"theme display-block\"\u003e\n\t\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"light\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"theme-light margin-right-xxs\"\u003e\n\t\t\t\t\t\t\t\t\u003cspan\n\t\t\t\t\t\t\t\t\tclass=\"theme-selector-icon border display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\t\t\u003csvg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\"\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect width=\"22\" height=\"14\" class=\"has-fill-body-background\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\u003c/svg\u003e\n\t\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003cspan role=\"menuitem\"\u003e Light \u003c/span\u003e\n\t\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003c/li\u003e\n\t\t\t\t\t\u003cli class=\"theme display-block\"\u003e\n\t\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"dark\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"theme-dark margin-right-xxs\"\u003e\n\t\t\t\t\t\t\t\t\u003cspan\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\t\t\u003csvg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\"\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect width=\"22\" height=\"14\" class=\"has-fill-body-background\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\u003c/svg\u003e\n\t\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003cspan role=\"menuitem\"\u003e Dark \u003c/span\u003e\n\t\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003c/li\u003e\n\t\t\t\t\t\u003cli class=\"theme display-block\"\u003e\n\t\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"high-contrast\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"theme-high-contrast margin-right-xxs\"\u003e\n\t\t\t\t\t\t\t\t\u003cspan\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\t\t\u003csvg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\"\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect width=\"22\" height=\"14\" class=\"has-fill-body-background\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\u003c/svg\u003e\n\t\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003cspan role=\"menuitem\"\u003e High contrast \u003c/span\u003e\n\t\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003c/li\u003e\n\t\t\t\t\u003c/ul\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/div\u003e\n\t\u003c/div\u003e\n\t\t\t\u003c/div\u003e\n\t\t\t\u003cul class=\"links\" data-bi-name=\"footerlinks\"\u003e\n\t\t\t\t\u003cli class=\"manage-cookies-holder\" hidden=\"\"\u003e\u003c/li\u003e\n\t\t\t\t\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/principles-for-ai-generated-content\"\n\t\t\tdata-bi-name=\"aiDisclaimer\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eAI Disclaimer\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/previous-versions/\"\n\t\t\tdata-bi-name=\"archivelink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003ePrevious Versions\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e \u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://techcommunity.microsoft.com/t5/microsoft-learn-blog/bg-p/MicrosoftLearnBlog\"\n\t\t\tdata-bi-name=\"bloglink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eBlog\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e \u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/contribute\"\n\t\t\tdata-bi-name=\"contributorGuide\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eContribute\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://go.microsoft.com/fwlink/?LinkId=521839\"\n\t\t\tdata-bi-name=\"privacy\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003ePrivacy\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/legal/termsofuse\"\n\t\t\tdata-bi-name=\"termsofuse\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eTerms of Use\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://www.microsoft.com/legal/intellectualproperty/Trademarks/\"\n\t\t\tdata-bi-name=\"trademarks\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eTrademarks\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\n\t\t\t\t\u003cli\u003e\u0026copy; Microsoft 2025\u003c/li\u003e\n\t\t\t\u003c/ul\u003e\n\t\t\u003c/footer\u003e\n\t\u003c/footer\u003e\n\t\t\t\u003c/body\u003e\n\t\t\u003c/html\u003e"
                     }
                 ],
    "SourceType":  "enhanced_microsoft_docs",
    "TotalPatterns":  1
}
