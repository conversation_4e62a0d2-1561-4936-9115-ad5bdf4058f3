# Test script for the new LLM-driven workflow implementation
# This tests all 4 tasks requested by the user:
# 1. Complete comprehensive testing with positive and negative inputs
# 2. Optimize operation extraction and workflow planning
# 3. Enhanced debugging and logging
# 4. Expand tool coverage

param(
    [string]$BaseUrl = "http://localhost:8082"
)

Write-Host "=== TESTING NEW LLM-DRIVEN WORKFLOW SYSTEM ===" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "=" * 60

# Test scenarios covering different IT operations
$testScenarios = @(
    @{
        Name = "User Creation (Positive)"
        Input = "Create a new user account for <PERSON> in the Sales department"
        ExpectedSuccess = $true
        Category = "user_management"
    },
    @{
        Name = "Group Management (Positive)"
        Input = "Add user bob.wilson to the Finance group"
        ExpectedSuccess = $true
        Category = "group_management"
    },
    @{
        Name = "Password Reset (Positive)"
        Input = "Reset password for user alice.johnson"
        ExpectedSuccess = $true
        Category = "user_management"
    },
    @{
        Name = "Computer Management (Positive)"
        Input = "Join computer DESKTOP-001 to the domain"
        ExpectedSuccess = $true
        Category = "computer_management"
    },
    @{
        Name = "Security Audit (Positive)"
        Input = "Check which users have admin privileges in the domain"
        ExpectedSuccess = $true
        Category = "security_operations"
    },
    @{
        Name = "Non-IT Request (Negative)"
        Input = "Order pizza for the team meeting"
        ExpectedSuccess = $false
        Category = "non_it"
    },
    @{
        Name = "Vague Request (Negative)"
        Input = "Fix the computer"
        ExpectedSuccess = $false
        Category = "unclear"
    },
    @{
        Name = "Complex Multi-Step (Positive)"
        Input = "Create user sarah.connor, add her to IT group, and set her as domain admin"
        ExpectedSuccess = $true
        Category = "user_management"
    }
)

$successCount = 0
$totalTests = $testScenarios.Count

foreach ($scenario in $testScenarios) {
    Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
    Write-Host "TEST: $($scenario.Name)" -ForegroundColor Cyan
    Write-Host "Input: '$($scenario.Input)'" -ForegroundColor White
    Write-Host "Expected Success: $($scenario.ExpectedSuccess)" -ForegroundColor White
    Write-Host "-" * 60

    try {
        # Create request
        $requestBody = @{
            userInput = $scenario.Input
            requestId = [System.Guid]::NewGuid().ToString()
            context = @{
                source = "test_script"
                timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            }
        } | ConvertTo-Json -Depth 3

        Write-Host "Sending request..." -ForegroundColor Yellow
        
        # Measure response time
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        
        $response = Invoke-RestMethod -Uri "$BaseUrl/api/IntentPlanning/analyze" -Method POST -Body $requestBody -ContentType "application/json"
        
        $stopwatch.Stop()
        $responseTime = $stopwatch.ElapsedMilliseconds

        Write-Host "Response received in ${responseTime}ms" -ForegroundColor Green

        # Analyze results
        Write-Host "`nRESULTS:" -ForegroundColor Yellow
        Write-Host "  Success: $($response.success)" -ForegroundColor $(if ($response.success) { "Green" } else { "Red" })
        Write-Host "  Scope Filter: $($response.isItRequest)" -ForegroundColor White
        Write-Host "  Intent Category: $($response.intentCategory)" -ForegroundColor White
        Write-Host "  Confidence: $($response.userGoal.confidence)" -ForegroundColor White
        
        if ($response.userGoal) {
            Write-Host "  Primary Objective: '$($response.userGoal.primaryObjective)'" -ForegroundColor White
            Write-Host "  Success Criteria: '$($response.userGoal.successCriteria)'" -ForegroundColor White
        }

        if ($response.primaryWorkflow -and $response.primaryWorkflow.steps) {
            Write-Host "  Workflow Steps: $($response.primaryWorkflow.steps.Count)" -ForegroundColor White
            Write-Host "  Risk Level: $($response.primaryWorkflow.riskLevel)" -ForegroundColor White
            Write-Host "  Requires Approval: $($response.primaryWorkflow.approvalRequired)" -ForegroundColor White
            
            if ($response.primaryWorkflow.steps.Count -gt 0) {
                Write-Host "`n  GENERATED STEPS:" -ForegroundColor Cyan
                for ($i = 0; $i -lt $response.primaryWorkflow.steps.Count; $i++) {
                    $step = $response.primaryWorkflow.steps[$i]
                    Write-Host "    $($i + 1). $($step.description)" -ForegroundColor White
                    Write-Host "       Tool: $($step.toolId)" -ForegroundColor Gray
                    Write-Host "       Operation: $($step.operation)" -ForegroundColor Gray
                    if ($step.parameters.command) {
                        Write-Host "       Command: $($step.parameters.command)" -ForegroundColor Gray
                    }
                }
            }
        } else {
            Write-Host "  Workflow Steps: 0 (No workflow generated)" -ForegroundColor Red
        }

        if ($response.errors -and $response.errors.Count -gt 0) {
            Write-Host "`n  ERRORS:" -ForegroundColor Red
            foreach ($error in $response.errors) {
                Write-Host "    - $error" -ForegroundColor Red
            }
        }

        if ($response.errorMessage) {
            Write-Host "  Error Message: $($response.errorMessage)" -ForegroundColor Red
        }

        # Determine if test passed
        $testPassed = $false
        if ($scenario.ExpectedSuccess) {
            # For positive tests, expect success and workflow generation
            $testPassed = $response.success -and $response.primaryWorkflow -and $response.primaryWorkflow.steps.Count -gt 0
        } else {
            # For negative tests, expect either failure or no workflow
            $testPassed = -not $response.success -or -not $response.isItRequest -or ($response.primaryWorkflow.steps.Count -eq 0)
        }

        if ($testPassed) {
            Write-Host "`n✅ TEST PASSED" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "`n❌ TEST FAILED" -ForegroundColor Red
            Write-Host "   Expected Success: $($scenario.ExpectedSuccess), Got Success: $($response.success)" -ForegroundColor Red
            Write-Host "   Expected Workflow: $($scenario.ExpectedSuccess), Got Steps: $($response.primaryWorkflow.steps.Count)" -ForegroundColor Red
        }

    } catch {
        Write-Host "`n❌ TEST ERROR: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "   Full Error: $($_.Exception.ToString())" -ForegroundColor Red
    }

    Start-Sleep -Milliseconds 500  # Brief pause between tests
}

# Final summary
Write-Host "`n" + "=" * 60 -ForegroundColor Green
Write-Host "FINAL RESULTS" -ForegroundColor Green
Write-Host "=" * 60
Write-Host "Tests Passed: $successCount / $totalTests" -ForegroundColor $(if ($successCount -eq $totalTests) { "Green" } else { "Yellow" })
Write-Host "Success Rate: $([math]::Round(($successCount / $totalTests) * 100, 1))%" -ForegroundColor $(if ($successCount -eq $totalTests) { "Green" } else { "Yellow" })

if ($successCount -eq $totalTests) {
    Write-Host "`n🎉 ALL TESTS PASSED! The new LLM-driven workflow system is working perfectly!" -ForegroundColor Green
} elseif ($successCount -gt ($totalTests * 0.7)) {
    Write-Host "`n✅ GOOD RESULTS! Most tests passed. Some fine-tuning may be needed." -ForegroundColor Yellow
} else {
    Write-Host "`n⚠️  NEEDS IMPROVEMENT! Several tests failed. System requires debugging." -ForegroundColor Red
}

Write-Host "`nTest completed at $(Get-Date)" -ForegroundColor Gray
