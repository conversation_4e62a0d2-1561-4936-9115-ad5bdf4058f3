/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    IntentResolutionEngine.cpp

Abstract:

    Implementation of the Intent Resolution Engine component that handles
    hierarchical intent classification and resolution for IT administration tasks.

Author:

    AI Assistant 07/27/2025

--*/

#include "pch.hxx"
#include "IntentResolutionEngine.h"
#include "SystemPromptManager.h"
#include "LLMInfer.h"
#include "StringUtils.h"
#include "IntentResolutionEngine.cpp.tmh"

// Global instance
static std::unique_ptr<IntentResolutionEngine> g_intentEngine;

HRESULT
IntentResolutionEngine::Initialize()
/*++

Routine Description:
    Initialize the Intent Resolution Engine with resolution rules and patterns.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    
    TraceInfo(AimxIntentEngine, "Initializing Intent Resolution Engine");
    
    try
    {
        // Initialize resolution rules
        hr = InitializeResolutionRules();
        if (FAILED(hr))
        {
            TraceErr(AimxIntentEngine, "Failed to initialize resolution rules: %!HRESULT!", hr);
            return hr;
        }
        
        // Initialize parameter extraction patterns
        hr = InitializeParameterPatterns();
        if (FAILED(hr))
        {
            TraceErr(AimxIntentEngine, "Failed to initialize parameter patterns: %!HRESULT!", hr);
            return hr;
        }
        
        // Initialize clarification templates
        hr = InitializeClarificationTemplates();
        if (FAILED(hr))
        {
            TraceErr(AimxIntentEngine, "Failed to initialize clarification templates: %!HRESULT!", hr);
            return hr;
        }
        
        TraceInfo(AimxIntentEngine, "Intent Resolution Engine initialized successfully");
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxIntentEngine, "Exception during initialization: %s", e.what());
        hr = E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxIntentEngine, "Unknown exception during initialization");
        hr = E_FAIL;
    }
    
    return hr;
}

void
IntentResolutionEngine::Uninitialize()
/*++

Routine Description:
    Uninitialize and cleanup the Intent Resolution Engine.

Arguments:
    None.

Return Value:
    None.

--*/
{
    TraceInfo(AimxIntentEngine, "Uninitializing Intent Resolution Engine");
    
    m_resolutionRules.clear();
    m_parameterPatterns.clear();
    m_clarificationTemplates.clear();
    m_categoryKeywords.clear();
    m_intentKeywords.clear();
    m_preferredStrategies.clear();
}

HRESULT
IntentResolutionEngine::ResolveIntent(
    _In_ const std::wstring& userInput,
    _In_ const CONTEXT_ENRICHMENT_DATA& context,
    _Out_ INTENT_RESOLUTION_RESULT& result
    )
/*++

Routine Description:
    Main entry point for resolving user intent from natural language input.

Arguments:
    userInput - The user's natural language input
    context - Context enrichment data
    result - Output intent resolution result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    
    TraceInfo(AimxIntentEngine, "Resolving intent for input: %ws", userInput.c_str());
    
    try
    {
        // Step 1: Classify intent category
        double categoryConfidence = 0.0;
        hr = ClassifyIntentCategory(userInput, result.category, categoryConfidence);
        if (FAILED(hr))
        {
            TraceErr(AimxIntentEngine, "Failed to classify intent category: %!HRESULT!", hr);
            return hr;
        }
        
        // Step 2: Resolve specific intent within category
        double specificConfidence = 0.0;
        hr = ResolveSpecificIntent(userInput, result.category, result.specificIntent, specificConfidence);
        if (FAILED(hr))
        {
            TraceErr(AimxIntentEngine, "Failed to resolve specific intent: %!HRESULT!", hr);
            return hr;
        }
        
        // Step 3: Extract parameters
        hr = ExtractParameters(userInput, result.specificIntent, context, result.parameters);
        if (FAILED(hr))
        {
            TraceErr(AimxIntentEngine, "Failed to extract parameters: %!HRESULT!", hr);
            return hr;
        }
        
        // Step 4: Recommend execution strategy
        hr = RecommendExecutionStrategy(result.specificIntent, result.parameters.extractedParameters, context, result.recommendedStrategy);
        if (FAILED(hr))
        {
            TraceErr(AimxIntentEngine, "Failed to recommend execution strategy: %!HRESULT!", hr);
            return hr;
        }
        
        // Step 5: Get suggested commands
        hr = GetSuggestedCommands(result.specificIntent, result.parameters.extractedParameters, result.suggestedCommands);
        if (FAILED(hr))
        {
            TraceErr(AimxIntentEngine, "Failed to get suggested commands: %!HRESULT!", hr);
            return hr;
        }
        
        // Step 6: Generate clarification prompt if needed
        if (result.parameters.requiresClarification)
        {
            hr = GenerateClarificationPrompt(result.specificIntent, result.parameters.missingRequiredParameters, result.clarificationPrompt);
            if (FAILED(hr))
            {
                TraceErr(AimxIntentEngine, "Failed to generate clarification prompt: %!HRESULT!", hr);
                return hr;
            }
        }
        
        // Calculate overall confidence score
        result.confidenceScore = (categoryConfidence + specificConfidence + result.parameters.confidenceScore) / 3.0;
        
        TraceInfo(AimxIntentEngine, "Intent resolved successfully. Category: %s, Specific: %s, Confidence: %.2f",
                 ToString(result.category), ToString(result.specificIntent), result.confidenceScore);
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxIntentEngine, "Exception during intent resolution: %s", e.what());
        hr = E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxIntentEngine, "Unknown exception during intent resolution");
        hr = E_FAIL;
    }
    
    return hr;
}

HRESULT
IntentResolutionEngine::ClassifyIntentCategory(
    _In_ const std::wstring& userInput,
    _Out_ INTENT_CATEGORY& category,
    _Out_ double& confidence
    )
/*++

Routine Description:
    Classify the user input into one of the primary intent categories.

Arguments:
    userInput - The user's natural language input
    category - Output intent category
    confidence - Output confidence score

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;
    
    // Try rule-based classification first (faster)
    hr = ClassifyIntentByRules(userInput, category, confidence);
    if (SUCCEEDED(hr) && confidence > 0.8)
    {
        TraceInfo(AimxIntentEngine, "High-confidence rule-based classification: %s (%.2f)", 
                 ToString(category), confidence);
        return hr;
    }
    
    // Fall back to LLM-based classification for ambiguous cases
    hr = ClassifyIntentByLLM(userInput, category, confidence);
    if (FAILED(hr))
    {
        TraceErr(AimxIntentEngine, "Both rule-based and LLM classification failed: %!HRESULT!", hr);
        category = INTENT_CATEGORY::UNKNOWN_INTENT;
        confidence = 0.0;
        return hr;
    }
    
    TraceInfo(AimxIntentEngine, "LLM-based classification: %s (%.2f)", 
             ToString(category), confidence);
    
    return hr;
}

HRESULT
IntentResolutionEngine::InitializeResolutionRules()
/*++

Routine Description:
    Initialize the intent resolution rules for all supported categories.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxIntentEngine, "Initializing intent resolution rules");
    
    // USER_MANAGEMENT category keywords
    m_categoryKeywords[INTENT_CATEGORY::USER_MANAGEMENT] = {
        "user", "account", "password", "reset", "unlock", "create", "modify", "delete",
        "username", "email", "login", "logon", "authentication", "credential"
    };
    
    // GROUP_MANAGEMENT category keywords
    m_categoryKeywords[INTENT_CATEGORY::GROUP_MANAGEMENT] = {
        "group", "membership", "member", "add", "remove", "distribution", "security",
        "nested", "scope", "universal", "global", "domain local"
    };
    
    // AD_OPERATIONS category keywords
    m_categoryKeywords[INTENT_CATEGORY::AD_OPERATIONS] = {
        "domain", "controller", "dc", "replication", "replicate", "fsmo", "schema",
        "trust", "forest", "site", "subnet", "partition", "naming context"
    };
    
    // SECURITY_OPERATIONS category keywords
    m_categoryKeywords[INTENT_CATEGORY::SECURITY_OPERATIONS] = {
        "permission", "access", "acl", "security", "audit", "certificate", "ca",
        "kerberos", "authentication", "authorization", "privilege", "rights"
    };
    
    // MONITORING_OPERATIONS category keywords
    m_categoryKeywords[INTENT_CATEGORY::MONITORING_OPERATIONS] = {
        "event", "log", "monitor", "performance", "health", "status", "service",
        "process", "cpu", "memory", "disk", "network", "alert", "notification"
    };
    
    // NETWORK_OPERATIONS category keywords
    m_categoryKeywords[INTENT_CATEGORY::NETWORK_OPERATIONS] = {
        "network", "ping", "connectivity", "dns", "dhcp", "ip", "subnet", "gateway",
        "firewall", "port", "protocol", "tcp", "udp", "traceroute", "nslookup"
    };
    
    // Initialize specific intent keywords
    m_intentKeywords[SPECIFIC_INTENT::SEARCH_USER] = {
        "find", "search", "lookup", "get", "show", "display", "list"
    };
    
    m_intentKeywords[SPECIFIC_INTENT::RESET_PASSWORD] = {
        "reset", "change", "update", "modify", "password", "pwd"
    };
    
    m_intentKeywords[SPECIFIC_INTENT::UNLOCK_ACCOUNT] = {
        "unlock", "enable", "unblock", "locked", "disabled", "blocked"
    };
    
    m_intentKeywords[SPECIFIC_INTENT::CHECK_REPLICATION] = {
        "replication", "replicate", "sync", "synchronize", "consistency"
    };
    
    // Initialize preferred execution strategies
    m_preferredStrategies[SPECIFIC_INTENT::SEARCH_USER] = EXECUTION_STRATEGY::POWERSHELL_DIRECT;
    m_preferredStrategies[SPECIFIC_INTENT::RESET_PASSWORD] = EXECUTION_STRATEGY::POWERSHELL_DIRECT;
    m_preferredStrategies[SPECIFIC_INTENT::UNLOCK_ACCOUNT] = EXECUTION_STRATEGY::POWERSHELL_DIRECT;
    m_preferredStrategies[SPECIFIC_INTENT::CHECK_REPLICATION] = EXECUTION_STRATEGY::MCP_TOOL_CHAIN;
    
    TraceInfo(AimxIntentEngine, "Intent resolution rules initialized successfully");
    return S_OK;
}

HRESULT
IntentResolutionEngine::InitializeParameterPatterns()
/*++

Routine Description:
    Initialize regex patterns for extracting parameters from user input.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxIntentEngine, "Initializing parameter extraction patterns");

    try
    {
        // Identity patterns
        m_parameterPatterns["username"] = std::regex(R"(\b(?:user|username|account)\s+([a-zA-Z0-9._-]+)\b)", std::regex_constants::icase);
        m_parameterPatterns["email"] = std::regex(R"(\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b)");
        m_parameterPatterns["computer"] = std::regex(R"(\b(?:computer|machine|pc|host)\s+([a-zA-Z0-9-]+)\b)", std::regex_constants::icase);
        m_parameterPatterns["domain"] = std::regex(R"(\b(?:domain|dc)\s+([a-zA-Z0-9.-]+)\b)", std::regex_constants::icase);
        m_parameterPatterns["group"] = std::regex(R"(\b(?:group)\s+([a-zA-Z0-9\s_-]+)\b)", std::regex_constants::icase);
        m_parameterPatterns["ou"] = std::regex(R"(\bou[=\s]+([^,\n]+)\b)", std::regex_constants::icase);

        // Service and process patterns
        m_parameterPatterns["service"] = std::regex(R"(\b(?:service)\s+([a-zA-Z0-9_-]+)\b)", std::regex_constants::icase);
        m_parameterPatterns["process"] = std::regex(R"(\b(?:process)\s+([a-zA-Z0-9._-]+)\b)", std::regex_constants::icase);

        // Network patterns
        m_parameterPatterns["ip_address"] = std::regex(R"(\b(?:ip|address)\s+(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b)", std::regex_constants::icase);
        m_parameterPatterns["hostname"] = std::regex(R"(\b(?:host|hostname)\s+([a-zA-Z0-9.-]+)\b)", std::regex_constants::icase);
        m_parameterPatterns["port"] = std::regex(R"(\b(?:port)\s+(\d{1,5})\b)", std::regex_constants::icase);

        TraceInfo(AimxIntentEngine, "Parameter extraction patterns initialized successfully");
    }
    catch (const std::regex_error& e)
    {
        TraceErr(AimxIntentEngine, "Regex error during pattern initialization: %s", e.what());
        return E_FAIL;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxIntentEngine, "Exception during pattern initialization: %s", e.what());
        return E_FAIL;
    }

    return S_OK;
}

HRESULT
IntentResolutionEngine::InitializeClarificationTemplates()
/*++

Routine Description:
    Initialize clarification prompt templates for different intent types.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxIntentEngine, "Initializing clarification templates");

    // USER_MANAGEMENT clarification templates
    m_clarificationTemplates[SPECIFIC_INTENT::SEARCH_USER] =
        L"To search for a user, I need:\n- Username, email address, or display name\n- Optionally: department, title, or other search criteria";

    m_clarificationTemplates[SPECIFIC_INTENT::RESET_PASSWORD] =
        L"To reset a password, I need:\n- Username or email address\n- Optionally: new password (if not provided, a temporary password will be generated)";

    m_clarificationTemplates[SPECIFIC_INTENT::UNLOCK_ACCOUNT] =
        L"To unlock an account, I need:\n- Username or email address of the locked account";

    m_clarificationTemplates[SPECIFIC_INTENT::CREATE_USER] =
        L"To create a new user, I need:\n- First and last name\n- Username (if not provided, will be generated)\n- Email address\n- Department and title\n- Manager (optional)";

    // GROUP_MANAGEMENT clarification templates
    m_clarificationTemplates[SPECIFIC_INTENT::SEARCH_GROUP] =
        L"To search for a group, I need:\n- Group name or partial name\n- Optionally: group type (security or distribution)";

    m_clarificationTemplates[SPECIFIC_INTENT::MANAGE_MEMBERSHIP] =
        L"To manage group membership, I need:\n- Group name\n- Username(s) to add or remove\n- Action: add or remove";

    // AD_OPERATIONS clarification templates
    m_clarificationTemplates[SPECIFIC_INTENT::CHECK_REPLICATION] =
        L"To check replication, I need:\n- Domain controller name (or 'all' for all DCs)\n- Optionally: specific partition or naming context";

    m_clarificationTemplates[SPECIFIC_INTENT::DC_HEALTH] =
        L"To check domain controller health, I need:\n- Domain controller name (or 'all' for all DCs)\n- Optionally: specific test to run";

    // SECURITY_OPERATIONS clarification templates
    m_clarificationTemplates[SPECIFIC_INTENT::PERMISSION_AUDIT] =
        L"To audit permissions, I need:\n- Resource path (file, folder, or AD object)\n- Optionally: specific user or group to check";

    m_clarificationTemplates[SPECIFIC_INTENT::AUTHENTICATION_ISSUES] =
        L"To troubleshoot authentication issues, I need:\n- Username experiencing issues\n- Optionally: error message or symptoms";

    // MONITORING_OPERATIONS clarification templates
    m_clarificationTemplates[SPECIFIC_INTENT::EVENT_LOG_ANALYSIS] =
        L"To analyze event logs, I need:\n- Log name (System, Application, Security, etc.)\n- Optionally: time range, event ID, or source";

    m_clarificationTemplates[SPECIFIC_INTENT::SERVICE_HEALTH] =
        L"To check service health, I need:\n- Service name\n- Optionally: computer name (defaults to local machine)";

    // NETWORK_OPERATIONS clarification templates
    m_clarificationTemplates[SPECIFIC_INTENT::CONNECTIVITY_TESTING] =
        L"To test connectivity, I need:\n- Target hostname or IP address\n- Optionally: port number for specific service testing";

    m_clarificationTemplates[SPECIFIC_INTENT::DNS_OPERATIONS] =
        L"For DNS operations, I need:\n- Hostname or domain to query\n- Optionally: record type (A, AAAA, MX, etc.)";

    TraceInfo(AimxIntentEngine, "Clarification templates initialized successfully");
    return S_OK;
}

HRESULT
IntentResolutionEngine::ClassifyIntentByRules(
    _In_ const std::wstring& userInput,
    _Out_ INTENT_CATEGORY& category,
    _Out_ double& confidence
    )
/*++

Routine Description:
    Classify intent using rule-based keyword matching.

Arguments:
    userInput - The user's natural language input
    category - Output intent category
    confidence - Output confidence score

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::string inputUtf8 = WideToUtf8(userInput);
    std::transform(inputUtf8.begin(), inputUtf8.end(), inputUtf8.begin(), ::tolower);

    std::map<INTENT_CATEGORY, int> categoryScores;

    // Score each category based on keyword matches
    for (const auto& categoryPair : m_categoryKeywords)
    {
        INTENT_CATEGORY cat = categoryPair.first;
        const auto& keywords = categoryPair.second;

        int score = 0;
        for (const auto& keyword : keywords)
        {
            if (inputUtf8.find(keyword) != std::string::npos)
            {
                score++;
            }
        }

        if (score > 0)
        {
            categoryScores[cat] = score;
        }
    }

    // Find the category with the highest score
    if (categoryScores.empty())
    {
        category = INTENT_CATEGORY::UNKNOWN_INTENT;
        confidence = 0.0;
        return S_OK;
    }

    auto maxElement = std::max_element(categoryScores.begin(), categoryScores.end(),
        [](const auto& a, const auto& b) { return a.second < b.second; });

    category = maxElement->first;

    // Calculate confidence based on score and total keywords
    int totalKeywords = static_cast<int>(m_categoryKeywords[category].size());
    confidence = static_cast<double>(maxElement->second) / totalKeywords;

    // Cap confidence at 0.9 for rule-based classification
    confidence = std::min(confidence, 0.9);

    TraceInfo(AimxIntentEngine, "Rule-based classification: %s, score: %d/%d, confidence: %.2f",
             ToString(category), maxElement->second, totalKeywords, confidence);

    return S_OK;
}

HRESULT
IntentResolutionEngine::ClassifyIntentByLLM(
    _In_ const std::wstring& userInput,
    _Out_ INTENT_CATEGORY& category,
    _Out_ double& confidence
    )
/*++

Routine Description:
    Classify intent using LLM-based classification.

Arguments:
    userInput - The user's natural language input
    category - Output intent category
    confidence - Output confidence score

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = S_OK;

    try
    {
        // Get intent classification prompt from SystemPromptManager
        SystemPromptManager promptManager;
        std::wstring systemPrompt = promptManager.GetIntentClassificationPrompt();

        // Send LLM request
        std::wstring llmResponse;
        hr = LLMInfer::SendLlmRequest(systemPrompt, userInput, llmResponse);
        if (FAILED(hr))
        {
            TraceErr(AimxIntentEngine, "Failed to send LLM request for intent classification: %!HRESULT!", hr);
            return hr;
        }

        // Parse LLM response to extract category
        std::string responseUtf8 = WideToUtf8(llmResponse);
        std::transform(responseUtf8.begin(), responseUtf8.end(), responseUtf8.begin(), ::toupper);

        // Remove any extra whitespace or newlines
        responseUtf8.erase(std::remove_if(responseUtf8.begin(), responseUtf8.end(), ::isspace), responseUtf8.end());

        category = IntentCategoryFromString(responseUtf8);

        if (category == INTENT_CATEGORY::UNKNOWN_INTENT)
        {
            TraceWarn(AimxIntentEngine, "LLM returned unknown intent category: %s", responseUtf8.c_str());
            confidence = 0.0;
        }
        else
        {
            // LLM classification gets high confidence when successful
            confidence = 0.95;
            TraceInfo(AimxIntentEngine, "LLM classification successful: %s", ToString(category));
        }
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxIntentEngine, "Exception during LLM classification: %s", e.what());
        category = INTENT_CATEGORY::UNKNOWN_INTENT;
        confidence = 0.0;
        hr = E_FAIL;
    }

    return hr;
}

// Utility function implementations
const char* ToString(INTENT_CATEGORY category)
{
    switch (category)
    {
        case INTENT_CATEGORY::USER_MANAGEMENT: return "USER_MANAGEMENT";
        case INTENT_CATEGORY::GROUP_MANAGEMENT: return "GROUP_MANAGEMENT";
        case INTENT_CATEGORY::AD_OPERATIONS: return "AD_OPERATIONS";
        case INTENT_CATEGORY::SECURITY_OPERATIONS: return "SECURITY_OPERATIONS";
        case INTENT_CATEGORY::MONITORING_OPERATIONS: return "MONITORING_OPERATIONS";
        case INTENT_CATEGORY::COMPUTER_MANAGEMENT: return "COMPUTER_MANAGEMENT";
        case INTENT_CATEGORY::NETWORK_OPERATIONS: return "NETWORK_OPERATIONS";
        case INTENT_CATEGORY::GPO_OPERATIONS: return "GPO_OPERATIONS";
        case INTENT_CATEGORY::SYSTEM_OPERATIONS: return "SYSTEM_OPERATIONS";
        case INTENT_CATEGORY::POWERSHELL_OPERATIONS: return "POWERSHELL_OPERATIONS";
        case INTENT_CATEGORY::BACKUP_OPERATIONS: return "BACKUP_OPERATIONS";
        case INTENT_CATEGORY::TROUBLESHOOTING: return "TROUBLESHOOTING";
        default: return "UNKNOWN_INTENT";
    }
}

const char* ToString(SPECIFIC_INTENT intent)
{
    switch (intent)
    {
        case SPECIFIC_INTENT::SEARCH_USER: return "SEARCH_USER";
        case SPECIFIC_INTENT::CREATE_USER: return "CREATE_USER";
        case SPECIFIC_INTENT::MODIFY_USER: return "MODIFY_USER";
        case SPECIFIC_INTENT::RESET_PASSWORD: return "RESET_PASSWORD";
        case SPECIFIC_INTENT::UNLOCK_ACCOUNT: return "UNLOCK_ACCOUNT";
        case SPECIFIC_INTENT::DELETE_USER: return "DELETE_USER";
        case SPECIFIC_INTENT::SEARCH_GROUP: return "SEARCH_GROUP";
        case SPECIFIC_INTENT::CREATE_GROUP: return "CREATE_GROUP";
        case SPECIFIC_INTENT::MODIFY_GROUP: return "MODIFY_GROUP";
        case SPECIFIC_INTENT::MANAGE_MEMBERSHIP: return "MANAGE_MEMBERSHIP";
        case SPECIFIC_INTENT::DELETE_GROUP: return "DELETE_GROUP";
        case SPECIFIC_INTENT::CHECK_REPLICATION: return "CHECK_REPLICATION";
        case SPECIFIC_INTENT::DC_HEALTH: return "DC_HEALTH";
        case SPECIFIC_INTENT::SCHEMA_OPERATIONS: return "SCHEMA_OPERATIONS";
        case SPECIFIC_INTENT::TRUST_MANAGEMENT: return "TRUST_MANAGEMENT";
        case SPECIFIC_INTENT::FSMO_OPERATIONS: return "FSMO_OPERATIONS";
        case SPECIFIC_INTENT::PERMISSION_AUDIT: return "PERMISSION_AUDIT";
        case SPECIFIC_INTENT::AUTHENTICATION_ISSUES: return "AUTHENTICATION_ISSUES";
        case SPECIFIC_INTENT::CERTIFICATE_MANAGEMENT: return "CERTIFICATE_MANAGEMENT";
        case SPECIFIC_INTENT::SECURITY_AUDITING: return "SECURITY_AUDITING";
        case SPECIFIC_INTENT::EVENT_LOG_ANALYSIS: return "EVENT_LOG_ANALYSIS";
        case SPECIFIC_INTENT::PERFORMANCE_MONITORING: return "PERFORMANCE_MONITORING";
        case SPECIFIC_INTENT::SERVICE_HEALTH: return "SERVICE_HEALTH";
        case SPECIFIC_INTENT::SYSTEM_HEALTH: return "SYSTEM_HEALTH";
        case SPECIFIC_INTENT::CONNECTIVITY_TESTING: return "CONNECTIVITY_TESTING";
        case SPECIFIC_INTENT::DNS_OPERATIONS: return "DNS_OPERATIONS";
        case SPECIFIC_INTENT::DHCP_OPERATIONS: return "DHCP_OPERATIONS";
        case SPECIFIC_INTENT::NETWORK_DIAGNOSTICS: return "NETWORK_DIAGNOSTICS";
        default: return "UNKNOWN_SPECIFIC_INTENT";
    }
}

const char* ToString(EXECUTION_STRATEGY strategy)
{
    switch (strategy)
    {
        case EXECUTION_STRATEGY::POWERSHELL_DIRECT: return "POWERSHELL_DIRECT";
        case EXECUTION_STRATEGY::POWERSHELL_SCRIPT: return "POWERSHELL_SCRIPT";
        case EXECUTION_STRATEGY::MCP_TOOL_CHAIN: return "MCP_TOOL_CHAIN";
        case EXECUTION_STRATEGY::NATIVE_API_CALL: return "NATIVE_API_CALL";
        case EXECUTION_STRATEGY::WORKFLOW_ORCHESTRATION: return "WORKFLOW_ORCHESTRATION";
        default: return "UNKNOWN_STRATEGY";
    }
}

INTENT_CATEGORY IntentCategoryFromString(const std::string& categoryStr)
{
    if (categoryStr == "USER_MANAGEMENT") return INTENT_CATEGORY::USER_MANAGEMENT;
    if (categoryStr == "GROUP_MANAGEMENT") return INTENT_CATEGORY::GROUP_MANAGEMENT;
    if (categoryStr == "AD_OPERATIONS") return INTENT_CATEGORY::AD_OPERATIONS;
    if (categoryStr == "SECURITY_OPERATIONS") return INTENT_CATEGORY::SECURITY_OPERATIONS;
    if (categoryStr == "MONITORING_OPERATIONS") return INTENT_CATEGORY::MONITORING_OPERATIONS;
    if (categoryStr == "COMPUTER_MANAGEMENT") return INTENT_CATEGORY::COMPUTER_MANAGEMENT;
    if (categoryStr == "NETWORK_OPERATIONS") return INTENT_CATEGORY::NETWORK_OPERATIONS;
    if (categoryStr == "GPO_OPERATIONS") return INTENT_CATEGORY::GPO_OPERATIONS;
    if (categoryStr == "SYSTEM_OPERATIONS") return INTENT_CATEGORY::SYSTEM_OPERATIONS;
    if (categoryStr == "POWERSHELL_OPERATIONS") return INTENT_CATEGORY::POWERSHELL_OPERATIONS;
    if (categoryStr == "BACKUP_OPERATIONS") return INTENT_CATEGORY::BACKUP_OPERATIONS;
    if (categoryStr == "TROUBLESHOOTING") return INTENT_CATEGORY::TROUBLESHOOTING;
    return INTENT_CATEGORY::UNKNOWN_INTENT;
}
