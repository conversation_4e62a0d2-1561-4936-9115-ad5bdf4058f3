# Test Microsoft Docs scraper
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing Microsoft Docs Scraper" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    Write-Host "✅ Core module imported" -ForegroundColor Green

    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    if ($result) {
        Write-Host "✅ Framework initialized" -ForegroundColor Green
    } else {
        Write-Host "❌ Framework initialization failed" -ForegroundColor Red
        exit 1
    }

    # Test basic web request to Microsoft Docs
    Write-Host "`nTesting Microsoft Docs connectivity..." -ForegroundColor Yellow
    $testUrl = "https://docs.microsoft.com/en-us/powershell/module/activedirectory/"
    
    try {
        $response = Invoke-WebRequestWithRetry -Uri $testUrl -MaxRetries 2
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Microsoft Docs is accessible" -ForegroundColor Green
            Write-Host "   Content length: $($response.Content.Length) characters" -ForegroundColor Gray
        } else {
            Write-Host "❌ Microsoft Docs returned status: $($response.StatusCode)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Failed to access Microsoft Docs: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Test cmdlet list extraction
    Write-Host "`nTesting cmdlet list extraction..." -ForegroundColor Yellow
    try {
        # Simple regex to find AD cmdlet links
        $content = $response.Content
        $cmdletPattern = 'href="([^"]*?/activedirectory/[^"]*?)"[^>]*?>([A-Za-z]+-AD[A-Za-z]+)'
        $matches = [regex]::Matches($content, $cmdletPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        
        Write-Host "✅ Found $($matches.Count) cmdlet matches" -ForegroundColor Green
        
        # Show first few matches
        $count = 0
        foreach ($match in $matches) {
            if ($count -ge 5) { break }
            $cmdletName = $match.Groups[2].Value
            $relativeUrl = $match.Groups[1].Value
            Write-Host "   $cmdletName -> $relativeUrl" -ForegroundColor Gray
            $count++
        }
        
        if ($matches.Count -gt 5) {
            Write-Host "   ... and $($matches.Count - 5) more" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "❌ Failed to extract cmdlet list: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Test specific cmdlet documentation
    if ($matches.Count -gt 0) {
        Write-Host "`nTesting specific cmdlet documentation..." -ForegroundColor Yellow
        $firstMatch = $matches[0]
        $cmdletName = $firstMatch.Groups[2].Value
        $relativeUrl = $firstMatch.Groups[1].Value
        
        # Convert relative URL to absolute
        if ($relativeUrl.StartsWith("/")) {
            $fullUrl = "https://docs.microsoft.com" + $relativeUrl
        } else {
            $fullUrl = $relativeUrl
        }
        
        Write-Host "   Testing: $cmdletName" -ForegroundColor Gray
        Write-Host "   URL: $fullUrl" -ForegroundColor Gray
        
        try {
            $cmdletResponse = Invoke-WebRequestWithRetry -Uri $fullUrl -MaxRetries 2
            if ($cmdletResponse.StatusCode -eq 200) {
                Write-Host "✅ Cmdlet documentation accessible" -ForegroundColor Green
                Write-Host "   Content length: $($cmdletResponse.Content.Length) characters" -ForegroundColor Gray
                
                # Test pattern creation
                $pattern = New-KnowledgePattern -Title "Microsoft Docs: $cmdletName" -Content $cmdletResponse.Content -SourceUrl $fullUrl -SourceType "microsoft_docs" -Domain "user_management" -Operation "read" -Author "Microsoft" -CredibilityScore 0.95
                
                if ($pattern -and $pattern.Id) {
                    Write-Host "✅ Pattern creation successful" -ForegroundColor Green
                    Write-Host "   Pattern ID: $($pattern.Id)" -ForegroundColor Gray
                    Write-Host "   Credibility: $($pattern.CredibilityScore)" -ForegroundColor Gray
                } else {
                    Write-Host "❌ Pattern creation failed" -ForegroundColor Red
                }
            } else {
                Write-Host "❌ Cmdlet documentation returned status: $($cmdletResponse.StatusCode)" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "❌ Failed to access cmdlet documentation: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    Write-Host "`n✅ Microsoft Docs scraper test completed successfully!" -ForegroundColor Green
}
catch {
    Write-Host "`n❌ Microsoft Docs scraper test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    exit 1
}
