# Analyze Available Tools in IntentPlanningService
Write-Host "Analyzing Available Tools..." -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:8082/api/IntentPlanning/health/tools' -Method Get
    $toolNames = $response.PSObject.Properties.Name | Sort-Object
    
    Write-Host "Total Tools Available: $($toolNames.Count)" -ForegroundColor Yellow
    Write-Host ""
    
    # Group Management Tools
    $groupTools = $toolNames | Where-Object { $_ -like '*group*' }
    Write-Host "Group Management Tools ($($groupTools.Count)):" -ForegroundColor Green
    foreach ($tool in $groupTools) {
        Write-Host "  - $tool" -ForegroundColor White
    }
    Write-Host ""
    
    # Computer Management Tools
    $computerTools = $toolNames | Where-Object { $_ -like '*computer*' }
    Write-Host "Computer Management Tools ($($computerTools.Count)):" -ForegroundColor Green
    foreach ($tool in $computerTools) {
        Write-Host "  - $tool" -ForegroundColor White
    }
    Write-Host ""
    
    # User Management Tools
    $userTools = $toolNames | Where-Object { $_ -like '*user*' }
    Write-Host "User Management Tools ($($userTools.Count)):" -ForegroundColor Green
    foreach ($tool in $userTools | Select-Object -First 10) {
        Write-Host "  - $tool" -ForegroundColor White
    }
    if ($userTools.Count -gt 10) {
        Write-Host "  ... and $($userTools.Count - 10) more" -ForegroundColor Gray
    }
    Write-Host ""
    
    # Account Management Tools
    $accountTools = $toolNames | Where-Object { $_ -like '*account*' }
    Write-Host "Account Management Tools ($($accountTools.Count)):" -ForegroundColor Green
    foreach ($tool in $accountTools) {
        Write-Host "  - $tool" -ForegroundColor White
    }
    Write-Host ""
    
    # Service Management Tools
    $serviceTools = $toolNames | Where-Object { $_ -like '*service*' }
    Write-Host "Service Management Tools ($($serviceTools.Count)):" -ForegroundColor Green
    foreach ($tool in $serviceTools | Select-Object -First 5) {
        Write-Host "  - $tool" -ForegroundColor White
    }
    if ($serviceTools.Count -gt 5) {
        Write-Host "  ... and $($serviceTools.Count - 5) more" -ForegroundColor Gray
    }
    Write-Host ""
    
    # Tools by Operation Type
    Write-Host "Tools by Operation Type:" -ForegroundColor Magenta
    
    $newTools = $toolNames | Where-Object { $_ -like '*new*' }
    Write-Host "  Create/New ($($newTools.Count)): " -NoNewline -ForegroundColor Yellow
    Write-Host ($newTools | Select-Object -First 3) -join ", " -ForegroundColor White
    
    $setTools = $toolNames | Where-Object { $_ -like '*set*' }
    Write-Host "  Set/Modify ($($setTools.Count)): " -NoNewline -ForegroundColor Yellow
    Write-Host ($setTools | Select-Object -First 3) -join ", " -ForegroundColor White
    
    $getTools = $toolNames | Where-Object { $_ -like '*get*' }
    Write-Host "  Get/Query ($($getTools.Count)): " -NoNewline -ForegroundColor Yellow
    Write-Host ($getTools | Select-Object -First 3) -join ", " -ForegroundColor White
    
    $removeTools = $toolNames | Where-Object { $_ -like '*remove*' }
    Write-Host "  Remove/Delete ($($removeTools.Count)): " -NoNewline -ForegroundColor Yellow
    Write-Host ($removeTools | Select-Object -First 3) -join ", " -ForegroundColor White
    
    $enableTools = $toolNames | Where-Object { $_ -like '*enable*' }
    Write-Host "  Enable ($($enableTools.Count)): " -NoNewline -ForegroundColor Yellow
    Write-Host ($enableTools | Select-Object -First 3) -join ", " -ForegroundColor White
    
    $disableTools = $toolNames | Where-Object { $_ -like '*disable*' }
    Write-Host "  Disable ($($disableTools.Count)): " -NoNewline -ForegroundColor Yellow
    Write-Host ($disableTools | Select-Object -First 3) -join ", " -ForegroundColor White
    
    $unlockTools = $toolNames | Where-Object { $_ -like '*unlock*' }
    Write-Host "  Unlock ($($unlockTools.Count)): " -NoNewline -ForegroundColor Yellow
    Write-Host ($unlockTools | Select-Object -First 3) -join ", " -ForegroundColor White
    
    Write-Host ""
    Write-Host "Analysis Complete!" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
