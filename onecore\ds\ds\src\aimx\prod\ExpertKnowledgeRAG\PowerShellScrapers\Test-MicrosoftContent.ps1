# Test Microsoft Docs content structure
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing Microsoft Docs Content Structure" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force

    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath

    # Get Microsoft Docs content
    $testUrl = "https://docs.microsoft.com/en-us/powershell/module/activedirectory/"
    $response = Invoke-WebRequestWithRetry -Uri $testUrl -MaxRetries 2
    $content = $response.Content

    Write-Host "✅ Downloaded $($content.Length) characters" -ForegroundColor Green

    # Save content to file for analysis
    $outputFile = Join-Path $PSScriptRoot "microsoft_docs_sample.html"
    $content | Out-File -FilePath $outputFile -Encoding UTF8
    Write-Host "✅ Content saved to: $outputFile" -ForegroundColor Green

    # Test different patterns to find cmdlet links
    Write-Host "`nTesting different patterns..." -ForegroundColor Yellow

    $patterns = @(
        @{ Name = "Original pattern"; Pattern = 'href="([^"]*?/activedirectory/[^"]*?)"[^>]*?>([A-Za-z]+-AD[A-Za-z]+)' },
        @{ Name = "Any AD cmdlet"; Pattern = '([A-Za-z]+-AD[A-Za-z]+)' },
        @{ Name = "Href with AD"; Pattern = 'href="([^"]*activedirectory[^"]*)"' },
        @{ Name = "Link text AD"; Pattern = '>([A-Za-z]+-AD[A-Za-z]+)<' },
        @{ Name = "Module links"; Pattern = 'href="([^"]*module/activedirectory/[^"]*)"' }
    )

    foreach ($patternInfo in $patterns) {
        Write-Host "`n  Testing: $($patternInfo.Name)" -ForegroundColor Cyan
        try {
            $matches = [regex]::Matches($content, $patternInfo.Pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            Write-Host "    Found $($matches.Count) matches" -ForegroundColor Green
            
            # Show first few matches
            $count = 0
            foreach ($match in $matches) {
                if ($count -ge 3) { break }
                Write-Host "      Match: $($match.Value.Substring(0, [Math]::Min(80, $match.Value.Length)))..." -ForegroundColor Gray
                $count++
            }
        }
        catch {
            Write-Host "    Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # Look for specific known cmdlets in content
    Write-Host "`nLooking for specific cmdlets..." -ForegroundColor Yellow
    $knownCmdlets = @("Get-ADUser", "Set-ADUser", "New-ADUser", "Get-ADGroup", "Set-ADGroup")
    
    foreach ($cmdlet in $knownCmdlets) {
        if ($content -match [regex]::Escape($cmdlet)) {
            Write-Host "  ✅ Found: $cmdlet" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Not found: $cmdlet" -ForegroundColor Red
        }
    }

    # Check if this is a redirect or different page structure
    Write-Host "`nChecking page structure..." -ForegroundColor Yellow
    if ($content -match "redirect|moved|location\.href") {
        Write-Host "  WARNING: Page appears to have redirects" -ForegroundColor Yellow
    }
    
    if ($content -match "learn\.microsoft\.com") {
        Write-Host "  INFO: Content references learn.microsoft.com" -ForegroundColor Cyan
    }
    
    if ($content -match "powershell.*module.*activedirectory") {
        Write-Host "  ✅ Content mentions PowerShell AD module" -ForegroundColor Green
    }

    Write-Host "`n✅ Content analysis completed!" -ForegroundColor Green
    Write-Host "Check the saved HTML file for detailed analysis: $outputFile" -ForegroundColor Gray
}
catch {
    Write-Host "`n❌ Content analysis failed: $($_.Exception.Message)" -ForegroundColor Red
}
