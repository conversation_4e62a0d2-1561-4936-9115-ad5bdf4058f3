﻿ <!DOCTYPE html>
		<html
			class="layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus"
			lang="en-us"
			dir="ltr"
			data-authenticated="false"
			data-auth-status-determined="false"
			data-target="docs"
			x-ms-format-detection="none"
		>
			
		<head>
			<title>ActiveDirectory Module | Microsoft Learn</title>
			<meta charset="utf-8" />
			<meta name="viewport" content="width=device-width, initial-scale=1.0" />
			<meta name="color-scheme" content="light dark" />

			<meta name="description" content="Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell." />
			<link rel="canonical" href="https://learn.microsoft.com/en-us/powershell/module/activedirectory/?view=windowsserver2025-ps" /> 

			<!-- Non-customizable open graph and sharing-related metadata -->
			<meta name="twitter:card" content="summary_large_image" />
			<meta name="twitter:site" content="@MicrosoftLearn" />
			<meta property="og:type" content="website" />
			<meta property="og:image:alt" content="Microsoft Learn" />
			<meta property="og:image" content="https://learn.microsoft.com/en-us/media/open-graph-image.png" />
			<!-- Page specific open graph and sharing-related metadata -->
			<meta property="og:title" content="ActiveDirectory Module" />
			<meta property="og:url" content="https://learn.microsoft.com/en-us/powershell/module/activedirectory/?view=windowsserver2025-ps" />
			<meta property="og:description" content="Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell." />
			<meta name="platform_id" content="051ee70b-63a7-8d66-5e3c-db4944d147ef" /> 
			<meta name="locale" content="en-us" />
			 <meta name="adobe-target" content="true" />
			<meta name="uhfHeaderId" content="MSDocsHeader-M365-IT" />

			<meta name="page_type" content="powershell" />

			<!--page specific meta tags-->
			

			<!-- custom meta tags -->
			
		<meta name="uid" content="ActiveDirectory" />
	
		<meta name="schema" content="PowerShellModule1" />
	
		<meta name="ROBOTS" content="INDEX, FOLLOW" />
	
		<meta name="apiPlatform" content="powershell" />
	
		<meta name="archive_url" content="https://learn.microsoft.com/previous-versions/powershell/windows/get-started" />
	
		<meta name="author" content="robinharwood" />
	
		<meta name="breadcrumb_path" content="/powershell/windows/bread/toc.json" />
	
		<meta name="feedback_product_url" content="https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332" />
	
		<meta name="feedback_system" content="Standard" />
	
		<meta name="manager" content="tedhudek" />
	
		<meta name="ms.author" content="roharwoo" />
	
		<meta name="ms.devlang" content="powershell" />
	
		<meta name="ms.service" content="windows-11" />
	
		<meta name="ms.topic" content="reference" />
	
		<meta name="products" content="https://authoring-docs-microsoft.poolparty.biz/devrel/56936876-97d9-45cc-ad1b-9d63320447c8" />
	
		<meta name="products" content="https://authoring-docs-microsoft.poolparty.biz/devrel/56754133-c3c3-4a9f-af19-71bdbe19fccf" />
	
		<meta name="HelpInfoUri" content="https://aka.ms/winsvr-2025-pshelp" />
	
		<meta name="Locale" content="en-US" />
	
		<meta name="Module Name" content="ActiveDirectory" />
	
		<meta name="ms.date" content="2016-12-27T00:00:00Z" />
	
		<meta name="PlatyPS schema version" content="2024-05-01T00:00:00Z" />
	
		<meta name="document type" content="module" />
	
		<meta name="document_id" content="23279bf1-dc2f-963f-1852-27133fc55599" />
	
		<meta name="document_version_independent_id" content="cca653e1-fb07-ecc4-9e7d-28b88dad259a" />
	
		<meta name="updated_at" content="2025-05-14T22:44:00Z" />
	
		<meta name="original_content_git_url" content="https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/ActiveDirectory.md" />
	
		<meta name="gitcommit" content="https://github.com/MicrosoftDocs/windows-powershell-docs/blob/0ef3f225d29e26d1cf3119f37dfff70bb6165746/docset/winserver2025-ps/ActiveDirectory/ActiveDirectory.md" />
	
		<meta name="git_commit_id" content="0ef3f225d29e26d1cf3119f37dfff70bb6165746" />
	
		<meta name="monikers" content="windowsserver2025-ps" />
	
		<meta name="default_moniker" content="windowsserver2025-ps" />
	
		<meta name="site_name" content="Docs" />
	
		<meta name="depot_name" content="TechNet.windows-powershell" />
	
		<meta name="in_right_rail" content="h2h3" />
	
		<meta name="page_kind" content="module" />
	
		<meta name="toc_rel" content="../windowsserver2025-ps/toc.json" />
	
		<meta name="feedback_help_link_type" content="" />
	
		<meta name="feedback_help_link_url" content="" />
	
		<meta name="config_moniker_range" content="WindowsServer2025-ps" />
	
		<meta name="asset_id" content="module/activedirectory/index" />
	
		<meta name="moniker_range_name" content="ffb05b7b47577225af7c7b6a20151268" />
	
		<meta name="item_type" content="Content" />
	
		<meta name="source_path" content="docset/winserver2025-ps/ActiveDirectory/ActiveDirectory.md" />
	
		<meta name="github_feedback_content_git_url" content="https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/ActiveDirectory.md" />
	 
		<meta name="cmProducts" content="https://authoring-docs-microsoft.poolparty.biz/devrel/bcbcbad5-4208-4783-8035-8481272c98b8" data-source="generated" />
	
		<meta name="spProducts" content="https://authoring-docs-microsoft.poolparty.biz/devrel/43b2e5aa-8a6d-4de2-a252-692232e5edc8" data-source="generated" />
	

			<!-- assets and js globals -->
			
			<link rel="stylesheet" href="/static/assets/0.4.03126.7002-3880ccdd/styles/site-ltr.css" />
			<link rel="preconnect" href="//mscom.demdex.net" crossorigin />
						<link rel="dns-prefetch" href="//target.microsoft.com" />
						<link rel="dns-prefetch" href="//microsoftmscompoc.tt.omtrdc.net" />
						<link
							rel="preload"
							as="script"
							href="/static/third-party/adobe-target/at-js/2.9.0/at.js"
							integrity="sha384-1/viVM50hgc33O2gOgkWz3EjiD/Fy/ld1dKYXJRUyjNYVEjSUGcSN+iPiQF7e4cu"
							crossorigin="anonymous"
							id="adobe-target-script"
							type="application/javascript"
						/>
			<script src="https://wcpstatic.microsoft.com/mscc/lib/v2/wcp-consent.js"></script>
			<script src="https://js.monitor.azure.com/scripts/c/ms.jsll-4.min.js"></script>
			<script src="/_themes/docs.theme/master/en-us/_themes/global/deprecation.js"></script>

			<!-- msdocs global object -->
			<script id="msdocs-script">
		var msDocs = {
  "environment": {
    "accessLevel": "online",
    "azurePortalHostname": "portal.azure.com",
    "reviewFeatures": false,
    "supportLevel": "production",
    "systemContent": true,
    "siteName": "learn",
    "legacyHosting": false
  },
  "data": {
    "contentLocale": "en-us",
    "contentDir": "ltr",
    "userLocale": "en-us",
    "userDir": "ltr",
    "pageTemplate": "Reference",
    "brand": "",
    "context": {},
    "standardFeedback": true,
    "showFeedbackReport": false,
    "feedbackHelpLinkType": "",
    "feedbackHelpLinkUrl": "",
    "feedbackSystem": "Standard",
    "feedbackGitHubRepo": "",
    "feedbackProductUrl": "https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332",
    "extendBreadcrumb": true,
    "isEditDisplayable": true,
    "isPrivateUnauthorized": false,
    "hideViewSource": false,
    "isPermissioned": false,
    "hasRecommendations": false,
    "contributors": []
  },
  "functions": {}
};;
	</script>

			<!-- base scripts, msdocs global should be before this -->
			<script src="/static/assets/0.4.03126.7002-3880ccdd/scripts/en-us/index-docs.js"></script>
			

			<!-- json-ld -->
			
		</head>
	
			<body
				id="body"
				data-bi-name="body"
				class="layout-body "
				lang="en-us"
				dir="ltr"
			>
				<header class="layout-body-header">
		<div class="header-holder has-default-focus">
			
		<a
			href="#main"
			
			style="z-index: 1070"
			class="outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body"
			
		>
			Skip to main content
		</a>
	
		<a
			href="#side-doc-outline"
			
			style="z-index: 1070"
			class="outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body"
			
		>
			Skip to in-page navigation
		</a>
	
		<a
			href="#"
			data-skip-to-ask-learn
			style="z-index: 1070"
			class="outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body"
			hidden
		>
			Skip to Ask Learn chat experience
		</a>
	

			<div hidden id="cookie-consent-holder" data-test-id="cookie-consent-container"></div>
			<!-- Unsupported browser warning -->
			<div
				id="unsupported-browser"
				style="background-color: white; color: black; padding: 16px; border-bottom: 1px solid grey;"
				hidden
			>
				<div style="max-width: 800px; margin: 0 auto;">
					<p style="font-size: 24px">This browser is no longer supported.</p>
					<p style="font-size: 16px; margin-top: 16px;">
						Upgrade to Microsoft Edge to take advantage of the latest features, security updates, and technical support.
					</p>
					<div style="margin-top: 12px;">
						<a
							href="https://go.microsoft.com/fwlink/p/?LinkID=2092881 "
							style="background-color: #0078d4; border: 1px solid #0078d4; color: white; padding: 6px 12px; border-radius: 2px; display: inline-block;"
						>
							Download Microsoft Edge
						</a>
						<a
							href="https://learn.microsoft.com/en-us/lifecycle/faq/internet-explorer-microsoft-edge"
							style="background-color: white; padding: 6px 12px; border: 1px solid #505050; color: #171717; border-radius: 2px; display: inline-block;"
						>
							More info about Internet Explorer and Microsoft Edge
						</a>
					</div>
				</div>
			</div>
			<!-- site header -->
			<header
				id="ms--site-header"
				data-test-id="site-header-wrapper"
				role="banner"
				itemscope="itemscope"
				itemtype="http://schema.org/Organization"
			>
				<div
					id="ms--mobile-nav"
					class="site-header display-none-tablet padding-inline-none gap-none"
					data-bi-name="mobile-header"
					data-test-id="mobile-header"
				></div>
				<div
					id="ms--primary-nav"
					class="site-header display-none display-flex-tablet"
					data-bi-name="L1-header"
					data-test-id="primary-header"
				></div>
				<div
					id="ms--secondary-nav"
					class="site-header display-none display-flex-tablet"
					data-bi-name="L2-header"
					data-test-id="secondary-header"
				></div>
			</header>
			
		<!-- banner -->
		<div data-banner>
			<div id="disclaimer-holder"></div>
			
		</div>
		<!-- banner end -->
	
		</div>
	</header>
				 <section
					id="layout-body-menu"
					class="layout-body-menu display-flex"
					data-bi-name="menu"
			  >
					<div
		id="left-container"
		class="left-container display-none display-block-tablet padding-inline-sm padding-bottom-sm width-full"
	>
		<nav
			id="affixed-left-container"
			class="margin-top-sm-tablet position-sticky display-flex flex-direction-column"
			aria-label="Primary"
		></nav>
	</div>
			  </section>

				<main
					id="main"
					role="main"
					class="layout-body-main "
					data-bi-name="content"
					lang="en-us"
					dir="ltr"
				>
					
			<div
		id="ms--content-header"
		class="content-header default-focus border-bottom-none"
		data-bi-name="content-header"
	>
		<div class="content-header-controls margin-xxs margin-inline-sm-tablet">
			<button
				type="button"
				class="contents-button button button-sm margin-right-xxs"
				data-bi-name="contents-expand"
				aria-haspopup="true"
				data-contents-button
			>
				<span class="icon" aria-hidden="true"><span class="docon docon-menu"></span></span>
				<span class="contents-expand-title"> Table of contents </span>
			</button>
			<button
				type="button"
				class="ap-collapse-behavior ap-expanded button button-sm"
				data-bi-name="ap-collapse"
				aria-controls="action-panel"
			>
				<span class="icon" aria-hidden="true"><span class="docon docon-exit-mode"></span></span>
				<span>Exit editor mode</span>
			</button>
		</div>
	</div>
			<div data-main-column class="padding-sm padding-top-none padding-top-sm-tablet">
				<div>
					
		<div id="article-header" class="background-color-body margin-bottom-xs display-none-print">
			<div class="display-flex align-items-center justify-content-space-between">
				
		<details
			id="article-header-breadcrumbs-overflow-popover"
			class="popover"
			data-for="article-header-breadcrumbs"
		>
			<summary
				class="button button-clear button-primary button-sm inner-focus"
				aria-label="All breadcrumbs"
			>
				<span class="icon">
					<span class="docon docon-more"></span>
				</span>
			</summary>
			<div id="article-header-breadcrumbs-overflow" class="popover-content padding-none"></div>
		</details>

		<bread-crumbs
			id="article-header-breadcrumbs"
			data-test-id="article-header-breadcrumbs"
			class="overflow-hidden flex-grow-1 margin-right-sm margin-right-md-tablet margin-right-lg-desktop margin-left-negative-xxs padding-left-xxs"
		></bread-crumbs>
	 
		<div
			id="article-header-page-actions"
			class="opacity-none margin-left-auto display-flex flex-wrap-no-wrap align-items-stretch"
		>
			
		<button
			class="button button-sm border-none inner-focus display-none-tablet flex-shrink-0 "
			data-bi-name="ask-learn-assistant-entry"
			data-test-id="ask-learn-assistant-modal-entry-mobile"
			data-ask-learn-modal-entry
			type="button"
			style="min-width: max-content;"
			aria-expanded="false"
			aria-label="Ask Learn"
			hidden
		>
			<span class="icon font-size-lg" aria-hidden="true">
				<span class="docon docon-chat-sparkle gradient-ask-learn-logo"></span>
			</span>
		</button>
		<button
			class="button button-sm display-none display-inline-flex-tablet display-none-desktop flex-shrink-0 margin-right-xxs "
			data-bi-name="ask-learn-assistant-entry"
			data-test-id="ask-learn-assistant-modal-entry-tablet"
			data-ask-learn-modal-entry
			type="button"
			style="min-width: max-content;"
			aria-expanded="false"
			hidden
		>
			<span class="icon font-size-lg" aria-hidden="true">
				<span class="docon docon-chat-sparkle gradient-ask-learn-logo"></span>
			</span>
			<span>Ask Learn</span>
		</button>
		<button
			class="button button-sm display-none flex-shrink-0 display-inline-flex-desktop margin-right-xxs	 "
			data-bi-name="ask-learn-assistant-entry"
			data-test-id="ask-learn-assistant-flyout-entry"
			data-ask-learn-flyout-entry
			data-flyout-button="toggle"
			type="button"
			style="min-width: max-content;"
			aria-expanded="false"
			aria-controls="ask-learn-flyout"
			hidden
		>
			<span class="icon font-size-lg" aria-hidden="true">
				<span class="docon docon-chat-sparkle gradient-ask-learn-logo"></span>
			</span>
			<span>Ask Learn</span>
		</button>
	 
		<button
			type="button"
			id="ms--focus-mode-button"
			data-focus-mode
			data-bi-name="focus-mode-entry"
			class="button button-sm flex-shrink-0 margin-right-xxs display-none display-inline-flex-desktop"
		>
			<span class="icon font-size-lg" aria-hidden="true">
				<span class="docon docon-glasses"></span>
			</span>
			<span>Focus mode</span>
		</button>
	 

			<details class="popover popover-right" id="article-header-page-actions-overflow">
				<summary
					class="justify-content-flex-start button button-clear button-sm button-primary inner-focus"
					aria-label="More actions"
					title="More actions"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-more-vertical"></span>
					</span>
				</summary>
				<div class="popover-content">
					
		<button
			data-page-action-item="overflow-mobile"
			type="button"
			class="button-block button-sm has-inner-focus button button-clear display-none-tablet justify-content-flex-start text-align-left"
			data-bi-name="contents-expand"
			data-contents-button
			data-popover-close
		>
			<span class="icon">
				<span class="docon docon-editor-list-bullet" aria-hidden="true"></span>
			</span>
			<span class="contents-expand-title">Table of contents</span>
		</button>
	 
		<a
			id="lang-link-overflow"
			class="button-sm has-inner-focus button button-clear button-block justify-content-flex-start text-align-left"
			data-bi-name="language-toggle"
			data-page-action-item="overflow-all"
			data-check-hidden="true"
			data-read-in-link
			href="#"
			hidden
		>
			<span class="icon" aria-hidden="true" data-read-in-link-icon>
				<span class="docon docon-locale-globe"></span>
			</span>
			<span data-read-in-link-text>Read in English</span>
		</a>
	 
		<button
			type="button"
			class="collection button button-clear button-sm button-block justify-content-flex-start text-align-left inner-focus"
			data-list-type="collection"
			data-bi-name="collection"
			data-page-action-item="overflow-all"
			data-check-hidden="true"
			data-popover-close
		>
			<span class="icon" aria-hidden="true">
				<span class="docon docon-circle-addition"></span>
			</span>
			<span class="collection-status">Add</span>
		</button>
	
					
		<button
			type="button"
			class="collection button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus"
			data-list-type="plan"
			data-bi-name="plan"
			data-page-action-item="overflow-all"
			data-check-hidden="true"
			data-popover-close
			hidden
		>
			<span class="icon" aria-hidden="true">
				<span class="docon docon-circle-addition"></span>
			</span>
			<span class="plan-status">Add to plan</span>
		</button>
	  
		<a
			data-contenteditbtn
			class="button button-clear button-block button-sm inner-focus justify-content-flex-start text-align-left text-decoration-none"
			data-bi-name="edit"
			
			href="https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/ActiveDirectory.md"
			data-original_content_git_url="https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/ActiveDirectory.md"
			data-original_content_git_url_template="{repo}/blob/{branch}/docset/winserver2025-ps/ActiveDirectory/ActiveDirectory.md"
			data-pr_repo=""
			data-pr_branch=""
		>
			<span class="icon" aria-hidden="true">
				<span class="docon docon-edit-outline"></span>
			</span>
			<span>Edit</span>
		</a>
	
					
		<hr class="margin-block-xxs" />
		<h4 class="font-size-sm padding-left-xxs">Share via</h4>
		
					<a
						class="button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-facebook"
						data-bi-name="facebook"
						data-page-action-item="overflow-all"
						href="#"
					>
						<span class="icon color-primary" aria-hidden="true">
							<span class="docon docon-facebook-share"></span>
						</span>
						<span>Facebook</span>
					</a>

					<a
						href="#"
						class="button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-twitter"
						data-bi-name="twitter"
						data-page-action-item="overflow-all"
					>
						<span class="icon color-text" aria-hidden="true">
							<span class="docon docon-xlogo-share"></span>
						</span>
						<span>x.com</span>
					</a>

					<a
						href="#"
						class="button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-linkedin"
						data-bi-name="linkedin"
						data-page-action-item="overflow-all"
					>
						<span class="icon color-primary" aria-hidden="true">
							<span class="docon docon-linked-in-logo"></span>
						</span>
						<span>LinkedIn</span>
					</a>
					<a
						href="#"
						class="button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-email"
						data-bi-name="email"
						data-page-action-item="overflow-all"
					>
						<span class="icon color-primary" aria-hidden="true">
							<span class="docon docon-mail-message"></span>
						</span>
						<span>Email</span>
					</a>
			  
	 
		<hr class="margin-block-xxs" />
		<button
			class="button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus"
			type="button"
			data-bi-name="print"
			data-page-action-item="overflow-all"
			data-popover-close
			data-print-page
			data-check-hidden="true"
		>
			<span class="icon color-primary" aria-hidden="true">
				<span class="docon docon-print"></span>
			</span>
			<span>Print</span>
		</button>
	
				</div>
			</details>
		</div>
	
			</div>
		</div>
	
					<!-- azure disclaimer -->
					
					<!-- privateUnauthorizedTemplate is hidden by default -->
					
		<div unauthorized-private-section data-bi-name="permission-content-unauthorized-private" hidden>
			<hr class="hr margin-top-xs margin-bottom-sm" />
			<div class="notification notification-info">
				<div class="notification-content">
					<p class="margin-top-none notification-title">
						<span class="icon">
							<span class="docon docon-exclamation-circle-solid" aria-hidden="true"></span>
						</span>
						<span>Note</span>
					</p>
					<p class="margin-top-none authentication-determined not-authenticated">
						Access to this page requires authorization. You can try <a class="docs-sign-in" href="#" data-bi-name="permission-content-sign-in">signing in</a> or <a  class="docs-change-directory" data-bi-name="permisson-content-change-directory">changing directories</a>.
					</p>
					<p class="margin-top-none authentication-determined authenticated">
						Access to this page requires authorization. You can try <a class="docs-change-directory" data-bi-name="permisson-content-change-directory">changing directories</a>.
					</p>
				</div>
			</div>
		</div>
	
					<div class="content"></div>
					 
					<div class="content"><h1 class="margin-bottom-xs" data-no-chunk="">Active<wbr>Directory Module</h1>

	<div class="margin-block-xxs">
		<ul class="metadata page-metadata align-items-center" data-bi-name="page info">
			
			
			
			
		</ul>
	</div>

<nav id="center-doc-outline" class="doc-outline is-hidden-desktop display-none-print margin-bottom-sm" data-bi-name="intopic toc" aria-label="">
  <h2 class="title is-6 margin-block-xs"></h2>
</nav>


	<div class="margin-block-sm" data-no-chunk="">
		<p>The Active Directory module for Windows PowerShell is a PowerShell module that consolidates a group of cmdlets. You can use these cmdlets to manage your Active Directory domains, Active Directory Lightweight Directory Services (AD LDS) configuration sets, and Active Directory Database Mounting Tool instances in a single, self-contained package.</p>
<p>If you don't have the Active Directory module installed on your machine, you need to download the correct Remote Server Administration Tools (RSAT) package for your OS.  If you're running Windows 7, you will also need to run the <code>import-module ActiveDirectory</code> command from an elevated PowerShell prompt. For more detail, see <a href="https://support.microsoft.com/help/2693643/remote-server-administration-tools-rsat-for-windows-operating-systems" data-linktype="external">RSAT for Windows operating systems</a>. Starting with Windows 10 October 2018 Update, RSAT is included as a set of Features on Demand right from Windows 10. Now, instead of downloading an RSAT package you can just go to Manage optional features in Settings and click Add a feature to see the list of available RSAT tools. Select and install the specific RSAT tools you need. To see installation progress, click the Back button to view status on the Manage optional features page.</p>
<p>If you want to use this module in PowerShell 7, see <a href="/en-us/powershell/scripting/whats-new/module-compatibility" data-linktype="absolute-path">PowerShell 7 module compatibility</a>.</p>

	</div>

		<h2 id="activedirectory-cmdlets" data-no-chunk="">ActiveDirectory Cmdlets</h2>
		<table class="table" data-no-chunk="">
			<thead>
				<tr>
					<th>Cmdlet</th>
					<th>Description</th>
				</tr>
			</thead>
			<tbody>
					<tr>
	<td>
		<a href="add-adcentralaccesspolicymember?view=windowsserver2025-ps" data-linktype="relative-path">Add-ADCentralAccessPolicyMember</a>
	</td>
	<td>
		<p>Adds central access rules to a central access policy in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="add-adcomputerserviceaccount?view=windowsserver2025-ps" data-linktype="relative-path">Add-ADComputerServiceAccount</a>
	</td>
	<td>
		<p>Adds one or more service accounts to an Active Directory computer.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="add-addomaincontrollerpasswordreplicationpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Add-ADDomainControllerPasswordReplicationPolicy</a>
	</td>
	<td>
		<p>Adds users, computers, and groups to the allowed or denied list of a read-only domain controller
password replication policy.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="add-adfinegrainedpasswordpolicysubject?view=windowsserver2025-ps" data-linktype="relative-path">Add-ADFineGrainedPasswordPolicySubject</a>
	</td>
	<td>
		<p>Applies a fine-grained password policy to one more users and groups.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="add-adgroupmember?view=windowsserver2025-ps" data-linktype="relative-path">Add-ADGroupMember</a>
	</td>
	<td>
		<p>Adds one or more members to an Active Directory group.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="add-adprincipalgroupmembership?view=windowsserver2025-ps" data-linktype="relative-path">Add-ADPrincipalGroupMembership</a>
	</td>
	<td>
		<p>Adds a member to one or more Active Directory groups.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="add-adresourcepropertylistmember?view=windowsserver2025-ps" data-linktype="relative-path">Add-ADResourcePropertyListMember</a>
	</td>
	<td>
		<p>Adds one or more resource properties to a resource property list in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="clear-adaccountexpiration?view=windowsserver2025-ps" data-linktype="relative-path">Clear-ADAccountExpiration</a>
	</td>
	<td>
		<p>Clears the expiration date for an Active Directory account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="clear-adclaimtransformlink?view=windowsserver2025-ps" data-linktype="relative-path">Clear-ADClaimTransformLink</a>
	</td>
	<td>
		<p>Removes a claims transformation from being applied to one or more cross-forest trust relationships
in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="complete-adserviceaccountmigration?view=windowsserver2025-ps" data-linktype="relative-path">Complete-ADServiceAccountMigration</a>
	</td>
	<td>
		<p>Completes the migration process and supersedes a normal user account to a delegated managed service
account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="disable-adaccount?view=windowsserver2025-ps" data-linktype="relative-path">Disable-ADAccount</a>
	</td>
	<td>
		<p>Disables an Active Directory account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="disable-adoptionalfeature?view=windowsserver2025-ps" data-linktype="relative-path">Disable-ADOptionalFeature</a>
	</td>
	<td>
		<p>Disables an Active Directory optional feature.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="enable-adaccount?view=windowsserver2025-ps" data-linktype="relative-path">Enable-ADAccount</a>
	</td>
	<td>
		<p>Enables an Active Directory account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="enable-adoptionalfeature?view=windowsserver2025-ps" data-linktype="relative-path">Enable-ADOptionalFeature</a>
	</td>
	<td>
		<p>Enables an Active Directory optional feature.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adaccountauthorizationgroup?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADAccountAuthorizationGroup</a>
	</td>
	<td>
		<p>Gets the accounts token group information.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adaccountresultantpasswordreplicationpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADAccountResultantPasswordReplicationPolicy</a>
	</td>
	<td>
		<p>Gets the resultant password replication policy for an Active Directory account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adauthenticationpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADAuthenticationPolicy</a>
	</td>
	<td>
		<p>Gets one or more Active Directory Domain Services authentication policies.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adauthenticationpolicysilo?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADAuthenticationPolicySilo</a>
	</td>
	<td>
		<p>Gets one or more Active Directory Domain Services authentication policy silos.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adcentralaccesspolicy?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADCentralAccessPolicy</a>
	</td>
	<td>
		<p>Retrieves central access policies from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adcentralaccessrule?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADCentralAccessRule</a>
	</td>
	<td>
		<p>Retrieves central access rules from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adclaimtransformpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADClaimTransformPolicy</a>
	</td>
	<td>
		<p>Returns one or more Active Directory claim transform objects based on a specified filter.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adclaimtype?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADClaimType</a>
	</td>
	<td>
		<p>Returns a claim type from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adcomputer?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADComputer</a>
	</td>
	<td>
		<p>Gets one or more Active Directory computers.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adcomputerserviceaccount?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADComputerServiceAccount</a>
	</td>
	<td>
		<p>Gets the service accounts hosted by a computer.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-addccloningexcludedapplicationlist?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADDCCloningExcludedApplicationList</a>
	</td>
	<td>
		<p>Gets a list of installed programs and services present on this domain controller that are not in the default or user defined inclusion list.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-addefaultdomainpasswordpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADDefaultDomainPasswordPolicy</a>
	</td>
	<td>
		<p>Gets the default password policy for an Active Directory domain.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-addomain?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADDomain</a>
	</td>
	<td>
		<p>Gets an Active Directory domain.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-addomaincontroller?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADDomainController</a>
	</td>
	<td>
		<p>Gets one or more Active Directory domain controllers based on discoverable services criteria, search parameters or by providing a domain controller identifier, such as the NetBIOS name.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-addomaincontrollerpasswordreplicationpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADDomainControllerPasswordReplicationPolicy</a>
	</td>
	<td>
		<p>Gets the members of the allowed list or denied list of a read-only domain controller's password replication policy.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-addomaincontrollerpasswordreplicationpolicyusage?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADDomainControllerPasswordReplicationPolicyUsage</a>
	</td>
	<td>
		<p>Gets the Active Directory accounts that are authenticated by a read-only domain controller or that are in the revealed list of the domain controller.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adfinegrainedpasswordpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADFineGrainedPasswordPolicy</a>
	</td>
	<td>
		<p>Gets one or more Active Directory fine-grained password policies.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adfinegrainedpasswordpolicysubject?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADFineGrainedPasswordPolicySubject</a>
	</td>
	<td>
		<p>Gets the users and groups to which a fine-grained password policy is applied.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adforest?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADForest</a>
	</td>
	<td>
		<p>Gets an Active Directory forest.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adgroup?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADGroup</a>
	</td>
	<td>
		<p>Gets one or more Active Directory groups.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adgroupmember?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADGroupMember</a>
	</td>
	<td>
		<p>Gets the members of an Active Directory group.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adobject?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADObject</a>
	</td>
	<td>
		<p>Gets one or more Active Directory objects.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adoptionalfeature?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADOptionalFeature</a>
	</td>
	<td>
		<p>Gets one or more Active Directory optional features.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adorganizationalunit?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADOrganizationalUnit</a>
	</td>
	<td>
		<p>Gets one or more Active Directory organizational units.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adprincipalgroupmembership?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADPrincipalGroupMembership</a>
	</td>
	<td>
		<p>Gets the Active Directory groups that have a specified user, computer, group, or service account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adreplicationattributemetadata?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADReplicationAttributeMetadata</a>
	</td>
	<td>
		<p>Gets the replication metadata for one or more Active Directory replication partners.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adreplicationconnection?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADReplicationConnection</a>
	</td>
	<td>
		<p>Returns a specific Active Directory replication connection or a set of AD replication connection objects based on a specified filter.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adreplicationfailure?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADReplicationFailure</a>
	</td>
	<td>
		<p>Returns a collection of data describing an Active Directory replication failure.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adreplicationpartnermetadata?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADReplicationPartnerMetadata</a>
	</td>
	<td>
		<p>Returns the replication metadata for a set of one or more replication partners.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adreplicationqueueoperation?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADReplicationQueueOperation</a>
	</td>
	<td>
		<p>Returns the contents of the replication queue for a specified server.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adreplicationsite?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADReplicationSite</a>
	</td>
	<td>
		<p>Returns a specific Active Directory replication site or a set of replication site objects based on a specified filter.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adreplicationsitelink?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADReplicationSiteLink</a>
	</td>
	<td>
		<p>Returns a specific Active Directory site link or a set of site links based on a specified filter.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adreplicationsitelinkbridge?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADReplicationSiteLinkBridge</a>
	</td>
	<td>
		<p>Gets a specific Active Directory site link bridge or a set of site link bridge objects based on a specified filter.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adreplicationsubnet?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADReplicationSubnet</a>
	</td>
	<td>
		<p>Gets one or more Active Directory subnets.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adreplicationuptodatenessvectortable?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADReplicationUpToDatenessVectorTable</a>
	</td>
	<td>
		<p>Displays the highest Update Sequence Number (USN) for the specified domain controller.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adresourceproperty?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADResourceProperty</a>
	</td>
	<td>
		<p>Gets one or more resource properties.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adresourcepropertylist?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADResourcePropertyList</a>
	</td>
	<td>
		<p>Gets resource property lists from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adresourcepropertyvaluetype?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADResourcePropertyValueType</a>
	</td>
	<td>
		<p>Gets a resource property value type from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adrootdse?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADRootDSE</a>
	</td>
	<td>
		<p>Gets the root of a directory server information tree.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adserviceaccount?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADServiceAccount</a>
	</td>
	<td>
		<p>Gets one or more Active Directory managed service accounts or group managed service accounts.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-adtrust?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADTrust</a>
	</td>
	<td>
		<p>Gets all trusted domain objects in the directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-aduser?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADUser</a>
	</td>
	<td>
		<p>Gets one or more Active Directory users.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="get-aduserresultantpasswordpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Get-ADUserResultantPasswordPolicy</a>
	</td>
	<td>
		<p>Gets the resultant password policy for a user.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="grant-adauthenticationpolicysiloaccess?view=windowsserver2025-ps" data-linktype="relative-path">Grant-ADAuthenticationPolicySiloAccess</a>
	</td>
	<td>
		<p>Grants permission to join an authentication policy silo.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="install-adserviceaccount?view=windowsserver2025-ps" data-linktype="relative-path">Install-ADServiceAccount</a>
	</td>
	<td>
		<p>Installs an Active Directory managed service account on a computer or caches a group managed service account on a computer.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="move-addirectoryserver?view=windowsserver2025-ps" data-linktype="relative-path">Move-ADDirectoryServer</a>
	</td>
	<td>
		<p>Moves a directory server in Active Directory to a new site.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="move-addirectoryserveroperationmasterrole?view=windowsserver2025-ps" data-linktype="relative-path">Move-ADDirectoryServerOperationMasterRole</a>
	</td>
	<td>
		<p>Moves operation master roles to an Active Directory directory server.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="move-adobject?view=windowsserver2025-ps" data-linktype="relative-path">Move-ADObject</a>
	</td>
	<td>
		<p>Moves an Active Directory object or a container of objects to a different container or domain.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adauthenticationpolicy?view=windowsserver2025-ps" data-linktype="relative-path">New-ADAuthenticationPolicy</a>
	</td>
	<td>
		<p>Creates an Active Directory Domain Services authentication policy object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adauthenticationpolicysilo?view=windowsserver2025-ps" data-linktype="relative-path">New-ADAuthenticationPolicySilo</a>
	</td>
	<td>
		<p>Creates an Active Directory Domain Services authentication policy silo object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adcentralaccesspolicy?view=windowsserver2025-ps" data-linktype="relative-path">New-ADCentralAccessPolicy</a>
	</td>
	<td>
		<p>Creates a new central access policy in Active Directory containing a set of central access rules.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adcentralaccessrule?view=windowsserver2025-ps" data-linktype="relative-path">New-ADCentralAccessRule</a>
	</td>
	<td>
		<p>Creates a central access rule in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adclaimtransformpolicy?view=windowsserver2025-ps" data-linktype="relative-path">New-ADClaimTransformPolicy</a>
	</td>
	<td>
		<p>Creates a new claim transformation policy object in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adclaimtype?view=windowsserver2025-ps" data-linktype="relative-path">New-ADClaimType</a>
	</td>
	<td>
		<p>Creates a new claim type in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adcomputer?view=windowsserver2025-ps" data-linktype="relative-path">New-ADComputer</a>
	</td>
	<td>
		<p>Creates a new Active Directory computer object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-addccloneconfigfile?view=windowsserver2025-ps" data-linktype="relative-path">New-ADDCCloneConfigFile</a>
	</td>
	<td>
		<p>Performs prerequisite checks for cloning a domain controller and generates a clone configuration file if all checks succeed.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adfinegrainedpasswordpolicy?view=windowsserver2025-ps" data-linktype="relative-path">New-ADFineGrainedPasswordPolicy</a>
	</td>
	<td>
		<p>Creates a new Active Directory fine-grained password policy.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adgroup?view=windowsserver2025-ps" data-linktype="relative-path">New-ADGroup</a>
	</td>
	<td>
		<p>Creates an Active Directory group.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adobject?view=windowsserver2025-ps" data-linktype="relative-path">New-ADObject</a>
	</td>
	<td>
		<p>Creates an Active Directory object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adorganizationalunit?view=windowsserver2025-ps" data-linktype="relative-path">New-ADOrganizationalUnit</a>
	</td>
	<td>
		<p>Creates an Active Directory organizational unit.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adreplicationsite?view=windowsserver2025-ps" data-linktype="relative-path">New-ADReplicationSite</a>
	</td>
	<td>
		<p>Creates an Active Directory replication site in the directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adreplicationsitelink?view=windowsserver2025-ps" data-linktype="relative-path">New-ADReplicationSiteLink</a>
	</td>
	<td>
		<p>Creates a new Active Directory site link for in managing replication.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adreplicationsitelinkbridge?view=windowsserver2025-ps" data-linktype="relative-path">New-ADReplicationSiteLinkBridge</a>
	</td>
	<td>
		<p>Creates a site link bridge in Active Directory for replication.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adreplicationsubnet?view=windowsserver2025-ps" data-linktype="relative-path">New-ADReplicationSubnet</a>
	</td>
	<td>
		<p>Creates an Active Directory replication subnet object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adresourceproperty?view=windowsserver2025-ps" data-linktype="relative-path">New-ADResourceProperty</a>
	</td>
	<td>
		<p>Creates a resource property in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adresourcepropertylist?view=windowsserver2025-ps" data-linktype="relative-path">New-ADResourcePropertyList</a>
	</td>
	<td>
		<p>Creates a resource property list in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-adserviceaccount?view=windowsserver2025-ps" data-linktype="relative-path">New-ADServiceAccount</a>
	</td>
	<td>
		<p>Creates a new Active Directory managed service account or group managed service account object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="new-aduser?view=windowsserver2025-ps" data-linktype="relative-path">New-ADUser</a>
	</td>
	<td>
		<p>Creates an Active Directory user.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adauthenticationpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADAuthenticationPolicy</a>
	</td>
	<td>
		<p>Removes an Active Directory Domain Services authentication policy object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adauthenticationpolicysilo?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADAuthenticationPolicySilo</a>
	</td>
	<td>
		<p>Removes an Active Directory Domain Services authentication policy silo object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adcentralaccesspolicy?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADCentralAccessPolicy</a>
	</td>
	<td>
		<p>Removes a central access policy from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adcentralaccesspolicymember?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADCentralAccessPolicyMember</a>
	</td>
	<td>
		<p>Removes central access rules from a central access policy in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adcentralaccessrule?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADCentralAccessRule</a>
	</td>
	<td>
		<p>Removes a central access rule from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adclaimtransformpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADClaimTransformPolicy</a>
	</td>
	<td>
		<p>Removes a claim transformation policy object from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adclaimtype?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADClaimType</a>
	</td>
	<td>
		<p>Removes a claim type from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adcomputer?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADComputer</a>
	</td>
	<td>
		<p>Removes an Active Directory computer.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adcomputerserviceaccount?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADComputerServiceAccount</a>
	</td>
	<td>
		<p>Removes one or more service accounts from a computer.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-addomaincontrollerpasswordreplicationpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADDomainControllerPasswordReplicationPolicy</a>
	</td>
	<td>
		<p>Removes users, computers, and groups from the allowed or denied list of a read-only domain controller password replication policy.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adfinegrainedpasswordpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADFineGrainedPasswordPolicy</a>
	</td>
	<td>
		<p>Removes an Active Directory fine-grained password policy.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adfinegrainedpasswordpolicysubject?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADFineGrainedPasswordPolicySubject</a>
	</td>
	<td>
		<p>Removes one or more users from a fine-grained password policy.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adgroup?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADGroup</a>
	</td>
	<td>
		<p>Removes an Active Directory group.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adgroupmember?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADGroupMember</a>
	</td>
	<td>
		<p>Removes one or more members from an Active Directory group.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adobject?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADObject</a>
	</td>
	<td>
		<p>Removes an Active Directory object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adorganizationalunit?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADOrganizationalUnit</a>
	</td>
	<td>
		<p>Removes an Active Directory organizational unit.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adprincipalgroupmembership?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADPrincipalGroupMembership</a>
	</td>
	<td>
		<p>Removes a member from one or more Active Directory groups.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adreplicationsite?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADReplicationSite</a>
	</td>
	<td>
		<p>Deletes the specified replication site object from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adreplicationsitelink?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADReplicationSiteLink</a>
	</td>
	<td>
		<p>Deletes an Active Directory site link used to manage replication.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adreplicationsitelinkbridge?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADReplicationSiteLinkBridge</a>
	</td>
	<td>
		<p>Deletes a replication site link bridge from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adreplicationsubnet?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADReplicationSubnet</a>
	</td>
	<td>
		<p>Deletes the specified Active Directory replication subnet object from the directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adresourceproperty?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADResourceProperty</a>
	</td>
	<td>
		<p>Removes a resource property from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adresourcepropertylist?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADResourcePropertyList</a>
	</td>
	<td>
		<p>Removes one or more resource property lists from Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adresourcepropertylistmember?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADResourcePropertyListMember</a>
	</td>
	<td>
		<p>Removes one or more resource properties from a resource property list in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-adserviceaccount?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADServiceAccount</a>
	</td>
	<td>
		<p>Removes an Active Directory managed service account or group managed service account object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="remove-aduser?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADUser</a>
	</td>
	<td>
		<p>Removes an Active Directory user.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="rename-adobject?view=windowsserver2025-ps" data-linktype="relative-path">Rename-ADObject</a>
	</td>
	<td>
		<p>Changes the name of an Active Directory object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="reset-adserviceaccountmigration?view=windowsserver2025-ps" data-linktype="relative-path">Reset-ADServiceAccountMigration</a>
	</td>
	<td>
		<p>Resets the state of a migration to an delegated managed service account and unlinks the delegated
managed service account from the user account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="reset-adserviceaccountpassword?view=windowsserver2025-ps" data-linktype="relative-path">Reset-ADServiceAccountPassword</a>
	</td>
	<td>
		<p>Resets the password for a standalone managed service account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="restore-adobject?view=windowsserver2025-ps" data-linktype="relative-path">Restore-ADObject</a>
	</td>
	<td>
		<p>Restores an Active Directory object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="revoke-adauthenticationpolicysiloaccess?view=windowsserver2025-ps" data-linktype="relative-path">Revoke-ADAuthenticationPolicySiloAccess</a>
	</td>
	<td>
		<p>Revokes membership in an authentication policy silo for the specified account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="search-adaccount?view=windowsserver2025-ps" data-linktype="relative-path">Search-ADAccount</a>
	</td>
	<td>
		<p>Gets Active Directory user, computer, or service accounts.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adaccountauthenticationpolicysilo?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADAccountAuthenticationPolicySilo</a>
	</td>
	<td>
		<p>Modifies the authentication policy or authentication policy silo of an account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adaccountcontrol?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADAccountControl</a>
	</td>
	<td>
		<p>Modifies user account control (UAC) values for an Active Directory account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adaccountexpiration?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADAccountExpiration</a>
	</td>
	<td>
		<p>Sets the expiration date for an Active Directory account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adaccountpassword?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADAccountPassword</a>
	</td>
	<td>
		<p>Modifies the password of an Active Directory account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adauthenticationpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADAuthenticationPolicy</a>
	</td>
	<td>
		<p>Modifies an Active Directory Domain Services authentication policy object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adauthenticationpolicysilo?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADAuthenticationPolicySilo</a>
	</td>
	<td>
		<p>Modifies an Active Directory Domain Services authentication policy silo object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adcentralaccesspolicy?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADCentralAccessPolicy</a>
	</td>
	<td>
		<p>Modifies a central access policy in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adcentralaccessrule?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADCentralAccessRule</a>
	</td>
	<td>
		<p>Modifies a central access rule in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adclaimtransformlink?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADClaimTransformLink</a>
	</td>
	<td>
		<p>Applies a claims transformation to one or more cross-forest trust relationships in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adclaimtransformpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADClaimTransformPolicy</a>
	</td>
	<td>
		<p>Sets the properties of a claims transformation policy in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adclaimtype?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADClaimType</a>
	</td>
	<td>
		<p>Modify a claim type in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adcomputer?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADComputer</a>
	</td>
	<td>
		<p>Modifies an Active Directory computer object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-addefaultdomainpasswordpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADDefaultDomainPasswordPolicy</a>
	</td>
	<td>
		<p>Modifies the default password policy for an Active Directory domain.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-addomain?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADDomain</a>
	</td>
	<td>
		<p>Modifies an Active Directory domain.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-addomainmode?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADDomainMode</a>
	</td>
	<td>
		<p>Sets the domain mode for an Active Directory domain.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adfinegrainedpasswordpolicy?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADFineGrainedPasswordPolicy</a>
	</td>
	<td>
		<p>Modifies an Active Directory fine-grained password policy.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adforest?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADForest</a>
	</td>
	<td>
		<p>Modifies an Active Directory forest.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adforestmode?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADForestMode</a>
	</td>
	<td>
		<p>Sets the forest mode for an Active Directory forest.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adgroup?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADGroup</a>
	</td>
	<td>
		<p>Modifies an Active Directory group.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adobject?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADObject</a>
	</td>
	<td>
		<p>Modifies an Active Directory object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adorganizationalunit?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADOrganizationalUnit</a>
	</td>
	<td>
		<p>Modifies an Active Directory organizational unit.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adreplicationconnection?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADReplicationConnection</a>
	</td>
	<td>
		<p>Sets properties on Active Directory replication connections.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adreplicationsite?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADReplicationSite</a>
	</td>
	<td>
		<p>Sets the replication properties for an Active Directory site.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adreplicationsitelink?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADReplicationSiteLink</a>
	</td>
	<td>
		<p>Sets the properties for an Active Directory site link.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adreplicationsitelinkbridge?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADReplicationSiteLinkBridge</a>
	</td>
	<td>
		<p>Sets the properties of a replication site link bridge in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adreplicationsubnet?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADReplicationSubnet</a>
	</td>
	<td>
		<p>Sets the properties of an Active Directory replication subnet object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adresourceproperty?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADResourceProperty</a>
	</td>
	<td>
		<p>Modifies a resource property in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adresourcepropertylist?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADResourcePropertyList</a>
	</td>
	<td>
		<p>Modifies a resource property list in Active Directory.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-adserviceaccount?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADServiceAccount</a>
	</td>
	<td>
		<p>Modifies an Active Directory managed service account or group managed service account object.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="set-aduser?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADUser</a>
	</td>
	<td>
		<p>Modifies an Active Directory user.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="show-adauthenticationpolicyexpression?view=windowsserver2025-ps" data-linktype="relative-path">Show-ADAuthenticationPolicyExpression</a>
	</td>
	<td>
		<p>Displays the Edit Access Control Conditions window update or create security descriptor definition language (SDDL) security descriptors.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="start-adserviceaccountmigration?view=windowsserver2025-ps" data-linktype="relative-path">Start-ADServiceAccountMigration</a>
	</td>
	<td>
		<p>Starts the migration process by linking a normal user account to a delegated managed service
account.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="sync-adobject?view=windowsserver2025-ps" data-linktype="relative-path">Sync-ADObject</a>
	</td>
	<td>
		<p>Replicates a single object between any two domain controllers that have partitions in common.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="test-adserviceaccount?view=windowsserver2025-ps" data-linktype="relative-path">Test-ADServiceAccount</a>
	</td>
	<td>
		<p>Tests a managed service account from a computer.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="undo-adserviceaccountmigration?view=windowsserver2025-ps" data-linktype="relative-path">Undo-ADServiceAccountMigration</a>
	</td>
	<td>
		<p>Reverts the previous migration phase of a migration to an delegated managed service account. If the
migration process is currently in the start phase, the accounts will be unlinked from each other.
If the migration is in the completed phase, it'll return back to the state in the start phase.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="uninstall-adserviceaccount?view=windowsserver2025-ps" data-linktype="relative-path">Uninstall-ADServiceAccount</a>
	</td>
	<td>
		<p>Uninstalls an Active Directory managed service account from a computer or removes a cached group managed service account from a computer.</p>

	</td>
</tr>
					<tr>
	<td>
		<a href="unlock-adaccount?view=windowsserver2025-ps" data-linktype="relative-path">Unlock-ADAccount</a>
	</td>
	<td>
		<p>Unlocks an Active Directory account.</p>

	</td>
</tr>
			</tbody>
		</table>
</div>
					
		<div
			id="ms--inline-notifications"
			class="margin-block-xs"
			data-bi-name="inline-notification"
		></div>
	 
		<div
			id="assertive-live-region"
			role="alert"
			aria-live="assertive"
			class="visually-hidden"
			aria-relevant="additions"
			aria-atomic="true"
		></div>
		<div
			id="polite-live-region"
			role="status"
			aria-live="polite"
			class="visually-hidden"
			aria-relevant="additions"
			aria-atomic="true"
		></div>
	
					
		<!-- feedback section -->
		<section
			id="site-user-feedback-footer"
			class="font-size-sm margin-top-md display-none-print display-none-desktop"
			data-test-id="site-user-feedback-footer"
			data-bi-name="site-feedback-section"
		>
			<hr class="hr" />
			<h2 id="ms--feedback" class="title is-3">Feedback</h2>
			<div class="display-flex flex-wrap-wrap align-items-center">
				<p class="font-weight-semibold margin-xxs margin-left-none">
					Was this page helpful?
				</p>
				<div class="buttons">
					<button
						class="thumb-rating-button like button button-primary button-sm"
						data-test-id="footer-rating-yes"
						data-binary-rating-response="rating-yes"
						type="button"
						title="This article is helpful"
						data-bi-name="button-rating-yes"
						aria-pressed="false"
					>
						<span class="icon" aria-hidden="true">
							<span class="docon docon-like"></span>
						</span>
						<span>Yes</span>
					</button>
					<button
						class="thumb-rating-button dislike button button-primary button-sm"
						data-test-id="footer-rating-no"
						data-binary-rating-response="rating-no"
						type="button"
						title="This article is not helpful"
						data-bi-name="button-rating-no"
						aria-pressed="false"
					>
						<span class="icon" aria-hidden="true">
							<span class="docon docon-dislike"></span>
						</span>
						<span>No</span>
					</button>
				</div>
			</div>
		</section>
		<!-- end feedback section -->
	
				</div>
				
			</div>
			
		<div
			id="action-panel"
			role="region"
			aria-label="Action Panel"
			class="action-panel"
			tabindex="-1"
		></div>
	
		
				</main>
				<aside
					id="layout-body-aside"
					class="layout-body-aside "
					data-bi-name="aside"
			  >
					
		<div
			id="ms--additional-resources"
			class="right-container padding-sm display-none display-block-desktop height-full"
			data-bi-name="pageactions"
			role="complementary"
			aria-label="Additional resources"
		>
			<div id="affixed-right-container" data-bi-name="right-column">
				
		<nav
			id="side-doc-outline"
			class="doc-outline border-bottom padding-bottom-xs margin-bottom-xs"
			data-bi-name="intopic toc"
			aria-label="In this article"
		>
			<h3>In this article</h3>
		</nav>
	
				<!-- Feedback -->
				
		<section
			id="ms--site-user-feedback-right-rail"
			class="font-size-sm display-none-print"
			data-test-id="site-user-feedback-right-rail"
			data-bi-name="site-feedback-right-rail"
		>
			<p class="font-weight-semibold margin-bottom-xs">Was this page helpful?</p>
			<div class="buttons">
				<button
					class="thumb-rating-button like button button-primary button-sm"
					data-test-id="right-rail-rating-yes"
					data-binary-rating-response="rating-yes"
					type="button"
					title="This article is helpful"
					data-bi-name="button-rating-yes"
					aria-pressed="false"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-like"></span>
					</span>
					<span>Yes</span>
				</button>
				<button
					class="thumb-rating-button dislike button button-primary button-sm"
					data-test-id="right-rail-rating-no"
					data-binary-rating-response="rating-no"
					type="button"
					title="This article is not helpful"
					data-bi-name="button-rating-no"
					aria-pressed="false"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-dislike"></span>
					</span>
					<span>No</span>
				</button>
			</div>
		</section>
	
			</div>
		</div>
	
			  </aside> <section
					id="layout-body-flyout"
					class="layout-body-flyout "
					data-bi-name="flyout"
			  >
					 <div
	class="height-full border-left background-color-body-medium"
	id="ask-learn-flyout"
></div>
			  </section> <div class="layout-body-footer " data-bi-name="layout-footer">
		<footer
			id="footer"
			data-test-id="footer"
			data-bi-name="footer"
			class="footer-layout has-padding has-default-focus border-top  uhf-container"
			role="contentinfo"
		>
			<div class="display-flex gap-xs flex-wrap-wrap is-full-height padding-right-lg-desktop">
				
		<a
			data-mscc-ic="false"
			href="#"
			data-bi-name="select-locale"
			class="locale-selector-link flex-shrink-0 button button-sm button-clear external-link-indicator"
			id=""
			title=""
			><span class="icon" aria-hidden="true"
				><span class="docon docon-world"></span></span
			><span class="local-selector-link-text">en-us</span></a
		>
	
				<div class="ccpa-privacy-link" data-ccpa-privacy-link hidden>
		
		<a
			data-mscc-ic="false"
			href="https://aka.ms/yourcaliforniaprivacychoices"
			data-bi-name="your-privacy-choices"
			class="button button-sm button-clear flex-shrink-0 external-link-indicator"
			id=""
			title=""
			>
		<svg
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 0 30 14"
			xml:space="preserve"
			height="16"
			width="43"
			aria-hidden="true"
			focusable="false"
		>
			<path
				d="M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8z"
				style="fill-rule:evenodd;clip-rule:evenodd;fill:#fff"
			></path>
			<path
				d="M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7zm-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8z"
				style="fill-rule:evenodd;clip-rule:evenodd;fill:#06f"
			></path>
			<path
				d="M24.6 4c.******* 0 .8L22.5 7l2.2 2.2c.******* 0 .8-.2.2-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0-.2-.2-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0z"
				style="fill:#fff"
			></path>
			<path
				d="M12.7 4.1c.*******.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0z"
				style="fill:#06f"
			></path>
		</svg>
	
			<span>Your Privacy Choices</span></a
		>
	
	</div>
				<div class="flex-shrink-0">
		<div class="dropdown has-caret-up">
			<button
				data-test-id="theme-selector-button"
				class="dropdown-trigger button button-clear button-sm has-inner-focus theme-dropdown-trigger"
				aria-controls="{{ themeMenuId }}"
				aria-expanded="false"
				title="Theme"
				data-bi-name="theme"
			>
				<span class="icon">
					<span class="docon docon-sun" aria-hidden="true"></span>
				</span>
				<span>Theme</span>
				<span class="icon expanded-indicator" aria-hidden="true">
					<span class="docon docon-chevron-down-light"></span>
				</span>
			</button>
			<div class="dropdown-menu" id="{{ themeMenuId }}" role="menu">
				<ul class="theme-selector padding-xxs" data-test-id="theme-dropdown-menu">
					<li class="theme display-block">
						<button
							class="button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left"
							data-theme-to="light"
						>
							<span class="theme-light margin-right-xxs">
								<span
									class="theme-selector-icon border display-inline-block has-body-background"
									aria-hidden="true"
								>
									<svg class="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 14">
										<rect width="22" height="14" class="has-fill-body-background" />
										<rect x="5" y="5" width="12" height="4" class="has-fill-secondary" />
										<rect x="5" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="8" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="11" y="2" width="3" height="1" class="has-fill-secondary" />
										<rect x="1" y="1" width="2" height="2" class="has-fill-secondary" />
										<rect x="5" y="10" width="7" height="2" rx="0.3" class="has-fill-primary" />
										<rect x="19" y="1" width="2" height="2" rx="1" class="has-fill-secondary" />
									</svg>
								</span>
							</span>
							<span role="menuitem"> Light </span>
						</button>
					</li>
					<li class="theme display-block">
						<button
							class="button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left"
							data-theme-to="dark"
						>
							<span class="theme-dark margin-right-xxs">
								<span
									class="border theme-selector-icon display-inline-block has-body-background"
									aria-hidden="true"
								>
									<svg class="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 14">
										<rect width="22" height="14" class="has-fill-body-background" />
										<rect x="5" y="5" width="12" height="4" class="has-fill-secondary" />
										<rect x="5" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="8" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="11" y="2" width="3" height="1" class="has-fill-secondary" />
										<rect x="1" y="1" width="2" height="2" class="has-fill-secondary" />
										<rect x="5" y="10" width="7" height="2" rx="0.3" class="has-fill-primary" />
										<rect x="19" y="1" width="2" height="2" rx="1" class="has-fill-secondary" />
									</svg>
								</span>
							</span>
							<span role="menuitem"> Dark </span>
						</button>
					</li>
					<li class="theme display-block">
						<button
							class="button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left"
							data-theme-to="high-contrast"
						>
							<span class="theme-high-contrast margin-right-xxs">
								<span
									class="border theme-selector-icon display-inline-block has-body-background"
									aria-hidden="true"
								>
									<svg class="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 14">
										<rect width="22" height="14" class="has-fill-body-background" />
										<rect x="5" y="5" width="12" height="4" class="has-fill-secondary" />
										<rect x="5" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="8" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="11" y="2" width="3" height="1" class="has-fill-secondary" />
										<rect x="1" y="1" width="2" height="2" class="has-fill-secondary" />
										<rect x="5" y="10" width="7" height="2" rx="0.3" class="has-fill-primary" />
										<rect x="19" y="1" width="2" height="2" rx="1" class="has-fill-secondary" />
									</svg>
								</span>
							</span>
							<span role="menuitem"> High contrast </span>
						</button>
					</li>
				</ul>
			</div>
		</div>
	</div>
			</div>
			<ul class="links" data-bi-name="footerlinks">
				<li class="manage-cookies-holder" hidden=""></li>
				<li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/en-us/principles-for-ai-generated-content"
			data-bi-name="aiDisclaimer"
			class=" external-link-indicator"
			id=""
			title=""
			>AI Disclaimer</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/en-us/previous-versions/"
			data-bi-name="archivelink"
			class=" external-link-indicator"
			id=""
			title=""
			>Previous Versions</a
		>
	
	</li> <li>
		
		<a
			data-mscc-ic="false"
			href="https://techcommunity.microsoft.com/t5/microsoft-learn-blog/bg-p/MicrosoftLearnBlog"
			data-bi-name="bloglink"
			class=" external-link-indicator"
			id=""
			title=""
			>Blog</a
		>
	
	</li> <li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/en-us/contribute"
			data-bi-name="contributorGuide"
			class=" external-link-indicator"
			id=""
			title=""
			>Contribute</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://go.microsoft.com/fwlink/?LinkId=521839"
			data-bi-name="privacy"
			class=" external-link-indicator"
			id=""
			title=""
			>Privacy</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/en-us/legal/termsofuse"
			data-bi-name="termsofuse"
			class=" external-link-indicator"
			id=""
			title=""
			>Terms of Use</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://www.microsoft.com/legal/intellectualproperty/Trademarks/"
			data-bi-name="trademarks"
			class=" external-link-indicator"
			id=""
			title=""
			>Trademarks</a
		>
	
	</li>
				<li>&copy; Microsoft 2025</li>
			</ul>
		</footer>
	</footer>
			</body>
		</html>
