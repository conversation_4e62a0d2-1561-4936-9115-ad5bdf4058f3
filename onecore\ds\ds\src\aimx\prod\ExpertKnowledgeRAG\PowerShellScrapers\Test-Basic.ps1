# Basic test to debug the scraping framework
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Basic Scraping Framework Test" -ForegroundColor Cyan

# Test 1: Check if config file exists
Write-Host "`nTest 1: Configuration File" -ForegroundColor Yellow
if (Test-Path $ConfigPath) {
    Write-Host "   Config file found: $ConfigPath" -ForegroundColor Green
    try {
        $config = Get-Content $ConfigPath | ConvertFrom-Json
        Write-Host "   Config file is valid JSON" -ForegroundColor Green
    }
    catch {
        Write-Host "   Config file is invalid JSON: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "   Config file not found: $ConfigPath" -ForegroundColor Red
}

# Test 2: Check if core module exists
Write-Host "`nTest 2: Core Module" -ForegroundColor Yellow
$coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
if (Test-Path $coreModulePath) {
    Write-Host "   Core module found: $coreModulePath" -ForegroundColor Green
} else {
    Write-Host "   Core module not found: $coreModulePath" -ForegroundColor Red
}

# Test 3: Check scraper files
Write-Host "`nTest 3: Scraper Files" -ForegroundColor Yellow
$scrapers = @(
    "Scrapers/MicrosoftDocsScraper.ps1",
    "Scrapers/StackOverflowScraper.ps1", 
    "Scrapers/GitHubScraper.ps1"
)

foreach ($scraper in $scrapers) {
    $scraperPath = Join-Path $PSScriptRoot $scraper
    if (Test-Path $scraperPath) {
        Write-Host "   Found: $scraper" -ForegroundColor Green
    } else {
        Write-Host "   Missing: $scraper" -ForegroundColor Red
    }
}

# Test 4: Test basic web request
Write-Host "`nTest 4: Basic Web Request" -ForegroundColor Yellow
try {
    $testUrl = "https://httpbin.org/get"
    $response = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "   Basic web request: PASSED" -ForegroundColor Green
    } else {
        Write-Host "   Basic web request: FAILED - Status: $($response.StatusCode)" -ForegroundColor Red
    }
}
catch {
    Write-Host "   Basic web request: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nBasic test completed!" -ForegroundColor Cyan
