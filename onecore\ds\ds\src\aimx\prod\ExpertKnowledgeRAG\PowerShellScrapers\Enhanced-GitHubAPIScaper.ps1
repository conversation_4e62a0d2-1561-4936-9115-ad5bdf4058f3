# Enhanced GitHub API Scraper for PowerShell Content
# Focuses on highly-starred repositories from reputable sources
param(
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "./ScrapedData/enhanced_github_api_$(Get-Date -Format 'yyyyMMdd_HHmmss').json",
    [Parameter(Mandatory = $false)]
    [string]$GitHubToken = $env:GITHUB_TOKEN,
    [Parameter(Mandatory = $false)]
    [int]$MaxReposPerOrg = 10,
    [Parameter(Mandatory = $false)]
    [int]$MaxFilesPerRepo = 15,
    [Parameter(Mandatory = $false)]
    [int]$MinStars = 5
)

Write-Host "Enhanced GitHub API Scraper for PowerShell Content" -ForegroundColor Cyan
Write-Host "Targeting highly-starred repositories from reputable sources" -ForegroundColor Gray
Write-Host "=" * 70 -ForegroundColor Gray

# GitHub API configuration
$githubApiBase = "https://api.github.com"
$headers = @{
    "Accept" = "application/vnd.github.v3+json"
    "User-Agent" = "PowerShell-Knowledge-Scraper"
}

if ($GitHubToken) {
    $headers["Authorization"] = "token $GitHubToken"
    Write-Host "Using GitHub token for enhanced API limits" -ForegroundColor Green
} else {
    Write-Host "No GitHub token provided - using anonymous access (limited rate)" -ForegroundColor Yellow
}

# Priority organizations and users (reputable sources)
$priorityOrgs = @(
    # Microsoft organizations
    @{ name = "microsoft"; credibility = 0.95; type = "official" },
    @{ name = "microsoftdocs"; credibility = 0.95; type = "official" },
    @{ name = "azure"; credibility = 0.95; type = "official" },
    @{ name = "powershell"; credibility = 0.95; type = "official" },
    @{ name = "microsoftgraph"; credibility = 0.95; type = "official" },
    @{ name = "azure-samples"; credibility = 0.9; type = "official" },
    @{ name = "microsoft-365"; credibility = 0.9; type = "official" },
    @{ name = "officedev"; credibility = 0.9; type = "official" },
    @{ name = "microsoftlearning"; credibility = 0.9; type = "official" },
    
    # Well-known MVPs and community experts
    @{ name = "dfinke"; credibility = 0.9; type = "mvp" },  # Doug Finke - PowerShell MVP
    @{ name = "lazywinadmin"; credibility = 0.85; type = "mvp" },  # Francois-Xavier Cat
    @{ name = "adamtheautomator"; credibility = 0.85; type = "expert" },  # Adam Bertram
    @{ name = "kevinmarquette"; credibility = 0.9; type = "mvp" },  # Kevin Marquette
    @{ name = "rjmholt"; credibility = 0.85; type = "expert" },  # PowerShell team member
    @{ name = "SteveL-MSFT"; credibility = 0.85; type = "expert" },  # PowerShell team
    @{ name = "PaulHigin"; credibility = 0.85; type = "expert" },  # PowerShell team
    
    # Popular open-source projects
    @{ name = "dahlbyk"; credibility = 0.8; type = "community" },  # posh-git
    @{ name = "nightroman"; credibility = 0.8; type = "community" },  # Invoke-Build
    @{ name = "pester"; credibility = 0.85; type = "community" }  # Pester testing framework
)

# Enhanced topic and keyword filtering
$targetTopics = @(
    "powershell", "powershell-script", "powershell-module", "automation", 
    "active-directory", "azure-ad", "microsoft-graph", "exchange", "office365",
    "windows-powershell", "powershell-core", "devops", "system-administration",
    "azure", "sharepoint", "teams", "intune", "security", "compliance"
)

$powerShellKeywords = @(
    "Get-", "Set-", "New-", "Remove-", "Add-", "Update-", "Start-", "Stop-",
    "Enable-", "Disable-", "Test-", "Import-", "Export-", "Connect-", "Disconnect-",
    "cmdlet", "function", "module", "script", "pipeline", "parameter"
)

function Invoke-GitHubAPIWithRetry {
    param(
        [string]$Uri,
        [int]$MaxRetries = 3,
        [int]$DelaySeconds = 2
    )
    
    $attempt = 0
    while ($attempt -lt $MaxRetries) {
        try {
            $attempt++
            $response = Invoke-RestMethod -Uri $Uri -Headers $headers -ErrorAction Stop
            
            # Check rate limit
            if ($response.PSObject.Properties.Name -contains "message" -and $response.message -match "rate limit") {
                Write-Host "    Rate limit hit, waiting..." -ForegroundColor Yellow
                Start-Sleep -Seconds 60
                continue
            }
            
            return $response
        }
        catch {
            Write-Host "    Attempt $attempt failed: $($_.Exception.Message)" -ForegroundColor Yellow
            if ($attempt -eq $MaxRetries) {
                throw
            }
            Start-Sleep -Seconds ($DelaySeconds * $attempt)
        }
    }
}

function Get-RepositoriesByOrganization {
    param(
        [hashtable]$OrgInfo,
        [int]$MaxRepos = 10
    )
    
    Write-Host "  Searching organization: $($OrgInfo.name)" -ForegroundColor Gray
    
    try {
        # Search for PowerShell repositories in the organization
        $searchUrl = "$githubApiBase/search/repositories?q=org:$($OrgInfo.name)+language:powershell&sort=stars&order=desc&per_page=$MaxRepos"
        $response = Invoke-GitHubAPIWithRetry -Uri $searchUrl
        
        $repos = @()
        foreach ($repo in $response.items) {
            if ($repo.stargazers_count -ge $MinStars -and $repo.size -gt 0) {
                $repos += @{
                    name = $repo.name
                    full_name = $repo.full_name
                    description = $repo.description
                    stars = $repo.stargazers_count
                    url = $repo.html_url
                    api_url = $repo.url
                    topics = $repo.topics
                    language = $repo.language
                    size = $repo.size
                    updated_at = $repo.updated_at
                    organization = $OrgInfo.name
                    org_credibility = $OrgInfo.credibility
                    org_type = $OrgInfo.type
                }
            }
        }
        
        Write-Host "    Found $($repos.Count) qualifying repositories" -ForegroundColor Green
        return $repos
    }
    catch {
        Write-Host "    Failed to search organization $($OrgInfo.name): $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

function Get-RepositoriesByTopic {
    param(
        [string]$Topic,
        [int]$MaxRepos = 20
    )
    
    Write-Host "  Searching topic: $Topic" -ForegroundColor Gray
    
    try {
        $searchUrl = "$githubApiBase/search/repositories?q=language:powershell+topic:$Topic&sort=stars&order=desc&per_page=$MaxRepos"
        $response = Invoke-GitHubAPIWithRetry -Uri $searchUrl
        
        $repos = @()
        foreach ($repo in $response.items) {
            if ($repo.stargazers_count -ge $MinStars -and $repo.size -gt 0) {
                # Calculate credibility based on stars and organization
                $credibility = [math]::Min(0.9, [math]::Max(0.5, [math]::Log10($repo.stargazers_count + 1) / 4))
                
                # Boost credibility for known good organizations
                $orgInfo = $priorityOrgs | Where-Object { $_.name -eq $repo.owner.login }
                if ($orgInfo) {
                    $credibility = [math]::Max($credibility, $orgInfo.credibility)
                }
                
                $repos += @{
                    name = $repo.name
                    full_name = $repo.full_name
                    description = $repo.description
                    stars = $repo.stargazers_count
                    url = $repo.html_url
                    api_url = $repo.url
                    topics = $repo.topics
                    language = $repo.language
                    size = $repo.size
                    updated_at = $repo.updated_at
                    owner = $repo.owner.login
                    credibility = $credibility
                    discovery_method = "topic_search"
                    search_topic = $Topic
                }
            }
        }
        
        Write-Host "    Found $($repos.Count) repositories for topic '$Topic'" -ForegroundColor Green
        return $repos
    }
    catch {
        Write-Host "    Failed to search topic $Topic : $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

function Get-PowerShellFilesFromRepository {
    param(
        [hashtable]$Repository,
        [int]$MaxFiles = 15
    )
    
    try {
        Write-Host "    Processing repository: $($Repository.full_name)" -ForegroundColor Gray
        
        # Search for PowerShell files and README files
        $searchUrl = "$githubApiBase/search/code?q=repo:$($Repository.full_name)+extension:ps1+OR+extension:psm1+OR+filename:README.md&per_page=$MaxFiles"
        $response = Invoke-GitHubAPIWithRetry -Uri $searchUrl
        
        $files = @()
        foreach ($file in $response.items) {
            if ($file.name -match '\.(ps1|psm1)$' -or $file.name -eq "README.md") {
                # Get file content
                try {
                    $content = ""
                    if ($file.download_url) {
                        $contentResponse = Invoke-WebRequest -Uri $file.download_url -ErrorAction Stop
                        $content = $contentResponse.Content
                    }
                    
                    # Quality filtering for PowerShell files
                    if ($file.name -match '\.(ps1|psm1)$') {
                        $hasQualityIndicators = $false
                        foreach ($keyword in $powerShellKeywords) {
                            if ($content -match [regex]::Escape($keyword)) {
                                $hasQualityIndicators = $true
                                break
                            }
                        }
                        
                        if (-not $hasQualityIndicators -or $content.Length -lt 100) {
                            continue  # Skip low-quality files
                        }
                    }
                    
                    $files += @{
                        name = $file.name
                        path = $file.path
                        content = $content
                        download_url = $file.download_url
                        html_url = $file.html_url
                        type = if ($file.name -match '\.ps1$') { "script" } 
                               elseif ($file.name -match '\.psm1$') { "module" } 
                               else { "documentation" }
                        size = $content.Length
                    }
                }
                catch {
                    Write-Host "      Failed to get content for $($file.name): $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
        
        Write-Host "      Found $($files.Count) PowerShell files" -ForegroundColor Green
        return $files
    }
    catch {
        Write-Host "      Failed to get files from $($Repository.full_name): $($_.Exception.Message)" -ForegroundColor Red
        return @()
    }
}

function Extract-PowerShellCmdlets {
    param([string]$Content)
    
    $cmdletPattern = '\b(Get-|Set-|New-|Remove-|Add-|Update-|Start-|Stop-|Enable-|Disable-|Test-|Import-|Export-|Connect-|Disconnect-)[A-Za-z][A-Za-z0-9]*\b'
    $matches = [regex]::Matches($Content, $cmdletPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    $cmdlets = $matches | ForEach-Object { $_.Value } | Sort-Object -Unique
    return $cmdlets
}

function Extract-CodeBlocks {
    param([string]$Content)
    
    $codeBlocks = @()
    
    # PowerShell code blocks in markdown
    $markdownPattern = '```(?:powershell|ps1)?\s*\n(.*?)\n```'
    $matches = [regex]::Matches($Content, $markdownPattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    foreach ($match in $matches) {
        $code = $match.Groups[1].Value.Trim()
        if ($code.Length -gt 20) {
            $codeBlocks += $code
        }
    }
    
    # For .ps1 files, treat the entire content as a code block if it's reasonable size
    if ($codeBlocks.Count -eq 0 -and $Content.Length -gt 50 -and $Content.Length -lt 10000) {
        $codeBlocks += $Content
    }
    
    return $codeBlocks
}

# Main execution logic
try {
    $scrapedData = @{
        source = "enhanced_github_api"
        description = "PowerShell scripts and modules from highly-starred GitHub repositories"
        scraped_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        search_criteria = @{
            min_stars = $MinStars
            max_repos_per_org = $MaxReposPerOrg
            max_files_per_repo = $MaxFilesPerRepo
            target_topics = $targetTopics
            priority_organizations = ($priorityOrgs | ForEach-Object { $_.name })
        }
        entries = @()
        statistics = @{
            organizations_processed = 0
            topics_processed = 0
            repositories_processed = 0
            files_processed = 0
            total_cmdlets = 0
            avg_credibility = 0
            avg_stars = 0
        }
    }

    $allEntries = @()
    $allRepositories = @()

    Write-Host "`nPhase 1: Searching priority organizations..." -ForegroundColor Yellow

    # Search priority organizations first
    foreach ($orgInfo in $priorityOrgs) {
        $repos = Get-RepositoriesByOrganization -OrgInfo $orgInfo -MaxRepos $MaxReposPerOrg
        $allRepositories += $repos
        $scrapedData.statistics.organizations_processed++

        Start-Sleep -Milliseconds 500  # Rate limiting
    }

    Write-Host "`nPhase 2: Searching by topics..." -ForegroundColor Yellow

    # Search by topics to find additional repositories
    foreach ($topic in $targetTopics) {
        $repos = Get-RepositoriesByTopic -Topic $topic -MaxRepos 15

        # Add repos that aren't already in our collection
        foreach ($repo in $repos) {
            $existing = $allRepositories | Where-Object { $_.full_name -eq $repo.full_name }
            if (-not $existing) {
                $allRepositories += $repo
            }
        }

        $scrapedData.statistics.topics_processed++
        Start-Sleep -Milliseconds 500  # Rate limiting
    }

    # Remove duplicates and sort by stars
    $uniqueRepositories = $allRepositories | Sort-Object full_name -Unique | Sort-Object stars -Descending

    Write-Host "`nPhase 3: Processing repository files..." -ForegroundColor Yellow
    Write-Host "Found $($uniqueRepositories.Count) unique repositories to process" -ForegroundColor Green

    # Process files from each repository
    foreach ($repo in $uniqueRepositories) {
        Write-Host "Processing: $($repo.full_name) ($($repo.stars) stars)" -ForegroundColor Cyan

        $files = Get-PowerShellFilesFromRepository -Repository $repo -MaxFiles $MaxFilesPerRepo

        foreach ($file in $files) {
            $cmdlets = Extract-PowerShellCmdlets -Content $file.content
            $codeBlocks = Extract-CodeBlocks -Content $file.content

            $entry = @{
                id = "github_$($repo.full_name.Replace('/', '_'))_$($file.name.Replace('.', '_'))"
                title = "$($file.name) - $($repo.name)"
                content = $file.content
                source = @{
                    url = $file.html_url
                    type = "github"
                    repository = $repo.full_name
                    file_type = $file.type
                    credibility = if ($repo.credibility) { $repo.credibility } else {
                        [math]::Min(0.9, [math]::Max(0.5, [math]::Log10($repo.stars + 1) / 4))
                    }
                    stars = $repo.stars
                    organization = if ($repo.organization) { $repo.organization } else { $repo.owner }
                    org_type = if ($repo.org_type) { $repo.org_type } else { "community" }
                }
                cmdlets = $cmdlets
                code_examples = $codeBlocks
                tags = @("github", "powershell", $file.type) + $repo.topics
                metadata = @{
                    repository_description = $repo.description
                    file_path = $file.path
                    file_size = $file.size
                    repository_updated = $repo.updated_at
                    discovery_method = if ($repo.discovery_method) { $repo.discovery_method } else { "organization_search" }
                }
                last_updated = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
            }

            $allEntries += $entry
            $scrapedData.statistics.files_processed++
        }

        $scrapedData.statistics.repositories_processed++
        Start-Sleep -Milliseconds 1000  # Be respectful with rate limiting
    }

    # Calculate final statistics
    $scrapedData.entries = $allEntries
    $scrapedData.statistics.total_entries = $allEntries.Count

    if ($allEntries.Count -gt 0) {
        $allCmdlets = $allEntries | ForEach-Object { $_.cmdlets } | Sort-Object -Unique
        $scrapedData.statistics.total_cmdlets = $allCmdlets.Count

        $avgCredibility = ($allEntries | ForEach-Object { $_.source.credibility } | Measure-Object -Average).Average
        $scrapedData.statistics.avg_credibility = [math]::Round($avgCredibility, 3)

        $avgStars = ($allEntries | ForEach-Object { $_.source.stars } | Measure-Object -Average).Average
        $scrapedData.statistics.avg_stars = [math]::Round($avgStars, 1)
    }

    # Save the scraped data
    $scrapedData | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8

    Write-Host "`nEnhanced GitHub API scraping completed!" -ForegroundColor Green
    Write-Host "=" * 70 -ForegroundColor Gray
    Write-Host "Statistics:" -ForegroundColor Cyan
    Write-Host "  Organizations processed: $($scrapedData.statistics.organizations_processed)" -ForegroundColor White
    Write-Host "  Topics searched: $($scrapedData.statistics.topics_processed)" -ForegroundColor White
    Write-Host "  Repositories processed: $($scrapedData.statistics.repositories_processed)" -ForegroundColor White
    Write-Host "  Files processed: $($scrapedData.statistics.files_processed)" -ForegroundColor White
    Write-Host "  Total entries: $($scrapedData.statistics.total_entries)" -ForegroundColor White
    Write-Host "  Unique cmdlets: $($scrapedData.statistics.total_cmdlets)" -ForegroundColor White
    Write-Host "  Average credibility: $($scrapedData.statistics.avg_credibility)" -ForegroundColor White
    Write-Host "  Average repository stars: $($scrapedData.statistics.avg_stars)" -ForegroundColor White
    Write-Host "  Output saved to: $OutputPath" -ForegroundColor Green

    Write-Host "`nQuality Insights:" -ForegroundColor Cyan
    $highCredibilityCount = ($allEntries | Where-Object { $_.source.credibility -ge 0.9 }).Count
    $officialCount = ($allEntries | Where-Object { $_.source.org_type -eq "official" }).Count
    $mvpCount = ($allEntries | Where-Object { $_.source.org_type -eq "mvp" }).Count

    Write-Host "  High credibility entries (≥0.9): $highCredibilityCount" -ForegroundColor White
    Write-Host "  From official Microsoft sources: $officialCount" -ForegroundColor White
    Write-Host "  From MVP/expert sources: $mvpCount" -ForegroundColor White
}
catch {
    Write-Host "Enhanced GitHub API scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Full error: $($_.Exception)" -ForegroundColor Red
    exit 1
}
