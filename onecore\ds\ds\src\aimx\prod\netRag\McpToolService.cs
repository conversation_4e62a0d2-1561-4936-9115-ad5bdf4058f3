using Microsoft.Extensions.AI;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace netRag;

/// <summary>
/// Service for managing MCP tools in the vector database
/// </summary>
public class McpToolService
{
    private readonly VectorStoreService _vectorStoreService;
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly McpServiceConfig _config;
    private readonly ILogger<McpToolService> _logger;
    private readonly Dictionary<string, McpTool> _toolsCache;
    private readonly object _cacheLock = new();

    public McpToolService(
        VectorStoreService vectorStoreService,
        IEmbeddingGenerator<string, Embedding<float>> embeddingService,
        McpServiceConfig config,
        ILogger<McpToolService> logger)
    {
        _vectorStoreService = vectorStoreService;
        _embeddingService = embeddingService;
        _config = config;
        _logger = logger;
        _toolsCache = new Dictionary<string, McpTool>();
    }

    /// <summary>
    /// Register a new MCP tool in the vector database
    /// </summary>
    public async Task<bool> RegisterToolAsync(RegisterToolRequest request)
    {
        try
        {
            // Validate the tool ID format (single GUID)
            if (!IsValidToolId(request.Id))
            {
                _logger.LogWarning("Invalid tool ID format: {ToolId}. Expected format: GUID", request.Id);
                return false;
            }

            // Create the MCP tool object
            var tool = new McpTool
            {
                Id = request.Id,
                ToolGuid = request.Id, // Tool ID is now just the tool GUID
                Name = request.Name,
                Description = request.Description,
                Category = request.Category,
                InputSchema = request.InputSchema,
                Examples = request.Examples,
                Tags = request.Tags,
                Version = request.Version,
                Metadata = request.Metadata,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Parse parameters from input schema if provided
            if (!string.IsNullOrEmpty(request.InputSchema))
            {
                tool.Parameters = ParseParametersFromSchema(request.InputSchema);
            }

            // Create searchable text for embedding
            var searchableText = CreateSearchableText(tool);

            // Generate embedding for the tool
            var embeddings = await _embeddingService.GenerateAsync([searchableText]);
            var embedding = embeddings[0];

            // Create metadata for vector store
            var metadata = new Dictionary<string, object>
            {
                ["toolId"] = tool.Id,
                ["toolGuid"] = tool.ToolGuid,
                ["name"] = tool.Name,
                ["category"] = tool.Category,
                ["version"] = tool.Version,
                ["createdAt"] = tool.CreatedAt.ToString("O"),
                ["toolData"] = JsonSerializer.Serialize(tool)
            };

            // Store in vector database
            await _vectorStoreService.UpsertAsync(tool.Id, embedding.Vector, metadata);

            // Update cache
            lock (_cacheLock)
            {
                _toolsCache[tool.Id] = tool;
            }

            _logger.LogInformation("Successfully registered MCP tool: {ToolId} ({ToolName})", tool.Id, tool.Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register MCP tool: {ToolId}", request.Id);
            return false;
        }
    }

    /// <summary>
    /// Search for MCP tools based on a query
    /// </summary>
    public async Task<SearchToolsResponse> SearchToolsAsync(SearchToolsRequest request)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            // Generate embedding for the search query
            var queryEmbeddings = await _embeddingService.GenerateAsync([request.Query]);
            var queryEmbedding = queryEmbeddings[0];

            // Search in vector store
            var searchResults = await _vectorStoreService.SearchAsync(queryEmbedding.Vector, request.TopK);

            // Convert to tool search results
            var toolResults = new List<ToolSearchResult>();

            foreach (var result in searchResults)
            {
                if (result.Score < request.MinScore)
                    continue;

                // Try to get tool from cache first
                McpTool? tool = null;
                lock (_cacheLock)
                {
                    _toolsCache.TryGetValue(result.Id, out tool);
                }

                // If not in cache, deserialize from metadata
                if (tool == null && result.Metadata.TryGetValue("toolData", out var toolDataObj))
                {
                    var toolDataJson = toolDataObj.ToString();
                    if (!string.IsNullOrEmpty(toolDataJson))
                    {
                        tool = JsonSerializer.Deserialize<McpTool>(toolDataJson);
                        if (tool != null)
                        {
                            lock (_cacheLock)
                            {
                                _toolsCache[tool.Id] = tool;
                            }
                        }
                    }
                }

                if (tool != null)
                {
                    // Apply additional filters
                    if (!string.IsNullOrEmpty(request.Category) && 
                        !tool.Category.Equals(request.Category, StringComparison.OrdinalIgnoreCase))
                        continue;

                    if (request.Tags.Any() && !request.Tags.Any(tag => 
                        tool.Tags.Contains(tag, StringComparer.OrdinalIgnoreCase)))
                        continue;

                    toolResults.Add(new ToolSearchResult
                    {
                        Tool = tool,
                        Score = result.Score,
                        MatchReason = GenerateMatchReason(request.Query, tool, result.Score)
                    });
                }
            }

            stopwatch.Stop();

            var response = new SearchToolsResponse
            {
                Query = request.Query,
                Tools = toolResults,
                TotalSearched = await _vectorStoreService.GetVectorCountAsync(),
                SearchTimeMs = stopwatch.ElapsedMilliseconds
            };

            _logger.LogInformation("Search completed: {Query} -> {ResultCount} tools in {ElapsedMs}ms", 
                request.Query, toolResults.Count, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search MCP tools for query: {Query}", request.Query);
            stopwatch.Stop();
            
            return new SearchToolsResponse
            {
                Query = request.Query,
                Tools = new List<ToolSearchResult>(),
                TotalSearched = 0,
                SearchTimeMs = stopwatch.ElapsedMilliseconds
            };
        }
    }

    /// <summary>
    /// Get a specific tool by ID
    /// </summary>
    public async Task<McpTool?> GetToolAsync(string toolId)
    {
        // Try cache first
        lock (_cacheLock)
        {
            if (_toolsCache.TryGetValue(toolId, out var cachedTool))
                return cachedTool;
        }

        // Search in vector store
        var searchResults = await _vectorStoreService.SearchAsync(
            new ReadOnlyMemory<float>(new float[768]), // Dummy embedding for exact ID match
            1000); // Large limit to find the exact match

        var result = searchResults.FirstOrDefault(r => r.Id == toolId);
        if (result?.Metadata.TryGetValue("toolData", out var toolDataObj) == true)
        {
            var toolDataJson = toolDataObj.ToString();
            if (!string.IsNullOrEmpty(toolDataJson))
            {
                var tool = JsonSerializer.Deserialize<McpTool>(toolDataJson);
                if (tool != null)
                {
                    lock (_cacheLock)
                    {
                        _toolsCache[tool.Id] = tool;
                    }
                    return tool;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Get statistics about registered tools
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatisticsAsync()
    {
        var totalTools = await _vectorStoreService.GetVectorCountAsync();

        lock (_cacheLock)
        {
            var categories = _toolsCache.Values
                .GroupBy(t => t.Category)
                .ToDictionary(g => g.Key, g => g.Count());

            return new Dictionary<string, object>
            {
                ["totalTools"] = totalTools,
                ["cachedTools"] = _toolsCache.Count,
                ["categoriesCount"] = categories.Count,
                ["categories"] = categories
            };
        }
    }

    /// <summary>
    /// Remove a specific MCP tool by ID
    /// </summary>
    public async Task<bool> RemoveToolAsync(string toolId)
    {
        try
        {
            if (string.IsNullOrEmpty(toolId))
            {
                _logger.LogWarning("Tool ID is required for removal");
                return false;
            }

            // Validate the tool ID format
            if (!IsValidToolId(toolId))
            {
                _logger.LogWarning("Invalid tool ID format: {ToolId}. Expected format: GUID", toolId);
                return false;
            }

            _logger.LogInformation("Removing tool: {ToolId}", toolId);

            // Remove from vector store
            var vectorRemoved = await _vectorStoreService.RemoveAsync(toolId);

            // Remove from cache
            lock (_cacheLock)
            {
                _toolsCache.Remove(toolId);
            }

            if (vectorRemoved)
            {
                _logger.LogInformation("Successfully removed tool: {ToolId}", toolId);
                return true;
            }
            else
            {
                _logger.LogWarning("Tool not found in vector store: {ToolId}", toolId);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing tool: {ToolId}", toolId);
            return false;
        }
    }

    /// <summary>
    /// Remove all tools (batch operation)
    /// </summary>
    public async Task<int> RemoveAllToolsAsync()
    {
        try
        {
            _logger.LogInformation("Removing all tools from the system");

            // Get all tool IDs from cache
            var toolsToRemove = new List<string>();
            lock (_cacheLock)
            {
                toolsToRemove.AddRange(_toolsCache.Keys);
            }

            // Remove each tool
            int removedCount = 0;
            foreach (var toolId in toolsToRemove)
            {
                try
                {
                    var removed = await RemoveToolAsync(toolId);
                    if (removed)
                    {
                        removedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error removing tool {ToolId}", toolId);
                }
            }

            _logger.LogInformation("Removed {Count} tools from the system", removedCount);
            return removedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing all tools");
            return 0;
        }
    }

    private bool IsValidToolId(string toolId)
    {
        if (string.IsNullOrEmpty(toolId))
            return false;

        return Guid.TryParse(toolId, out _);
    }

    private string CreateSearchableText(McpTool tool)
    {
        var parts = new List<string>
        {
            tool.Name,
            tool.Description,
            tool.Category
        };

        parts.AddRange(tool.Tags);
        parts.AddRange(tool.Examples);
        parts.AddRange(tool.Parameters.Select(p => $"{p.Name}: {p.Description}"));

        return string.Join(" ", parts.Where(p => !string.IsNullOrEmpty(p)));
    }

    private List<McpToolParameter> ParseParametersFromSchema(string inputSchema)
    {
        try
        {
            var parameters = new List<McpToolParameter>();
            var schema = JsonSerializer.Deserialize<JsonElement>(inputSchema);

            if (schema.TryGetProperty("properties", out var properties))
            {
                foreach (var prop in properties.EnumerateObject())
                {
                    var parameter = new McpToolParameter
                    {
                        Name = prop.Name
                    };

                    if (prop.Value.TryGetProperty("type", out var typeElement))
                        parameter.Type = typeElement.GetString() ?? "string";

                    if (prop.Value.TryGetProperty("description", out var descElement))
                        parameter.Description = descElement.GetString() ?? "";

                    if (prop.Value.TryGetProperty("enum", out var enumElement))
                    {
                        parameter.EnumValues = enumElement.EnumerateArray()
                            .Select(e => e.GetString() ?? "")
                            .Where(s => !string.IsNullOrEmpty(s))
                            .ToList();
                    }

                    parameters.Add(parameter);
                }
            }

            return parameters;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse parameters from schema: {Schema}", inputSchema);
            return new List<McpToolParameter>();
        }
    }

    private string GenerateMatchReason(string query, McpTool tool, float score)
    {
        var reasons = new List<string>();

        if (tool.Name.Contains(query, StringComparison.OrdinalIgnoreCase))
            reasons.Add("name match");

        if (tool.Description.Contains(query, StringComparison.OrdinalIgnoreCase))
            reasons.Add("description match");

        if (tool.Tags.Any(tag => tag.Contains(query, StringComparison.OrdinalIgnoreCase)))
            reasons.Add("tag match");

        if (score > 0.8f)
            reasons.Add("high semantic similarity");
        else if (score > 0.6f)
            reasons.Add("good semantic similarity");

        return reasons.Any() ? string.Join(", ", reasons) : "semantic similarity";
    }
}
