# Test the improved group management scenario
$baseUrl = "http://localhost:8082"

Write-Host "=== TESTING IMPROVED GROUP MANAGEMENT ===" -ForegroundColor Cyan
Write-Host ""

$testInput = "Add user <PERSON> to the Finance group"
Write-Host "Input: $testInput" -ForegroundColor White
Write-Host "Expected: Should use variables and correct cmdlets" -ForegroundColor Gray
Write-Host "Expected Pattern:" -ForegroundColor Gray
Write-Host '  $user = Get-ADUser -Filter "Name -like ''*<PERSON>*''"' -ForegroundColor Yellow
Write-Host '  $group = Get-ADGroup -Filter "Name -like ''*Finance*''"' -ForegroundColor Yellow
Write-Host '  Add-ADGroupMember -Identity $group -Members $user' -ForegroundColor Yellow
Write-Host ""

try {
    $requestBody = @{
        requestId = [System.Guid]::NewGuid().ToString()
        userInput = $testInput
        priority = "normal"
        environment = "production"
        userId = "test.user"
    } | ConvertTo-Json
    
    $startTime = Get-Date
    $response = Invoke-RestMethod -Uri "$baseUrl/api/IntentPlanning/analyze" -Method Post -Body $requestBody -ContentType "application/json"
    $endTime = Get-Date
    $responseTime = ($endTime - $startTime).TotalMilliseconds
    
    Write-Host "✅ SUCCESS" -ForegroundColor Green
    Write-Host "Response Time: $([math]::Round($responseTime))ms" -ForegroundColor Gray
    Write-Host "Steps Generated: $($response.primaryWorkflow.steps.Count)" -ForegroundColor Gray
    
    if ($response.primaryWorkflow.steps.Count -gt 0) {
        Write-Host "`nGenerated Steps:" -ForegroundColor White
        for ($i = 0; $i -lt $response.primaryWorkflow.steps.Count; $i++) {
            $step = $response.primaryWorkflow.steps[$i]
            Write-Host "  $($i + 1). $($step.description)" -ForegroundColor White
            Write-Host "     Command: $($step.operation)" -ForegroundColor Cyan
        }
        
        # Analysis
        Write-Host "`n=== ANALYSIS ===" -ForegroundColor Yellow
        $allCommands = ($response.primaryWorkflow.steps | ForEach-Object { $_.operation }) -join " "
        
        if ($allCommands -match 'Get-ADGroup.*Finance') {
            Write-Host "✅ GOOD: Uses Get-ADGroup for group lookup" -ForegroundColor Green
        } else {
            Write-Host "❌ BAD: Does not use Get-ADGroup for group lookup" -ForegroundColor Red
        }
        
        if ($allCommands -match 'Get-ADUser.*Bob Wilson') {
            Write-Host "✅ GOOD: Uses Get-ADUser for user lookup" -ForegroundColor Green
        } else {
            Write-Host "❌ BAD: Does not use Get-ADUser for user lookup" -ForegroundColor Red
        }
        
        if ($allCommands -match 'Add-ADGroupMember') {
            Write-Host "✅ GOOD: Uses Add-ADGroupMember for group membership" -ForegroundColor Green
        } else {
            Write-Host "❌ BAD: Does not use Add-ADGroupMember" -ForegroundColor Red
        }
        
        if ($allCommands -match '\$user.*\$group' -or $allCommands -match '\$group.*\$user') {
            Write-Host "✅ EXCELLENT: Uses variables for object references" -ForegroundColor Green
        } else {
            Write-Host "⚠️  IMPROVEMENT: Could use variables for better clarity" -ForegroundColor Yellow
        }
    }
}
catch {
    Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== TEST COMPLETE ===" -ForegroundColor Cyan
