# Component 3: Knowledge Graph & Context Engine

## 🎯 Purpose
Maintain dynamic understanding of IT environment relationships, dependencies, and state to enable context-aware workflow planning and intelligent decision making.

## 🏗️ Architecture Overview

```
[Real-time Data Ingestion] → [Relationship Mapping] → [State Tracking]
        ↓                           ↓                      ↓
[Graph Database] → [Context Enrichment] → [Dependency Analysis]
        ↓                           ↓                      ↓
[Query Engine] → [Pattern Recognition] → [Predictive Analytics]
```

## 🧠 Core Knowledge Graph Schema

### 1. Entity Types & Relationships
```cpp
enum class EntityType {
    USER,
    GROUP, 
    COMPUTER,
    DOMAIN_CONTROLLER,
    ORGANIZATIONAL_UNIT,
    SERVICE,
    PROCESS,
    NETWORK_RESOURCE,
    SECURITY_PRINCIPAL,
    GPO,
    CERTIFICATE,
    EVENT,
    OPERATION
};

enum class RelationshipType {
    // Identity relationships
    MEMBER_OF,          // User -> Group
    MANAGES,            // User -> User (manager relationship)
    OWNS,               // User -> Resource
    
    // Organizational relationships  
    LOCATED_IN,         // Computer -> OU
    CHILD_OF,           // OU -> OU
    APPLIES_TO,         // GPO -> OU
    
    // Technical relationships
    DEPENDS_ON,         // Service -> Service
    RUNS_ON,            // Process -> Computer
    AUTHENTICATES_TO,   // Computer -> DC
    REPLICATES_WITH,    // DC -> DC
    
    // Temporal relationships
    CAUSED_BY,          // Event -> Operation
    FOLLOWED_BY,        // Operation -> Operation
    TRIGGERED,          // Event -> Event
    
    // Security relationships
    HAS_PERMISSION,     // Principal -> Resource
    TRUSTS,             // Domain -> Domain
    ISSUED_BY           // Certificate -> CA
};

struct Entity {
    std::string id;
    EntityType type;
    std::string name;
    std::map<std::string, std::string> attributes;
    std::chrono::system_clock::time_point lastUpdated;
    std::string source; // "AD", "EventLog", "WMI", etc.
    bool isActive;
};

struct Relationship {
    std::string id;
    std::string sourceEntityId;
    std::string targetEntityId;
    RelationshipType type;
    std::map<std::string, std::string> properties;
    std::chrono::system_clock::time_point created;
    std::chrono::system_clock::time_point lastVerified;
    double confidence; // 0.0 - 1.0
    std::string source;
};
```

### 2. Dynamic State Tracking
```cpp
struct EntityState {
    std::string entityId;
    std::string currentState; // "active", "disabled", "locked", "healthy", etc.
    std::map<std::string, std::string> stateProperties;
    std::chrono::system_clock::time_point stateTimestamp;
    std::string stateSource;
    
    // Historical states for trend analysis
    std::vector<StateTransition> stateHistory;
};

struct StateTransition {
    std::string fromState;
    std::string toState;
    std::chrono::system_clock::time_point timestamp;
    std::string trigger; // What caused the state change
    std::string operationId; // If caused by an operation
};

class StateTracker {
public:
    void UpdateEntityState(const std::string& entityId, const EntityState& newState);
    EntityState GetCurrentState(const std::string& entityId) const;
    std::vector<StateTransition> GetStateHistory(const std::string& entityId) const;
    
    // Predictive state analysis
    std::string PredictNextState(const std::string& entityId) const;
    std::vector<std::string> GetEntitiesInState(const std::string& state) const;
    std::vector<std::string> GetEntitiesWithStatePattern(const std::string& pattern) const;
};
```

## 🔍 Context Enrichment Engine

### 1. Multi-Dimensional Context Builder
```cpp
struct ContextDimensions {
    // Identity context
    IdentityContext identity;
    
    // Environmental context
    EnvironmentalContext environment;
    
    // Temporal context
    TemporalContext temporal;
    
    // Operational context
    OperationalContext operational;
    
    // Security context
    SecurityContext security;
};

struct IdentityContext {
    std::string primaryIdentity;
    std::vector<std::string> aliases;
    std::vector<std::string> groupMemberships;
    std::string manager;
    std::string department;
    std::map<std::string, std::string> attributes;
    std::vector<std::string> relatedIdentities;
};

struct EnvironmentalContext {
    std::string domain;
    std::vector<std::string> availableDomainControllers;
    std::map<std::string, std::string> systemHealth;
    std::vector<std::string> networkSegments;
    std::string timeZone;
    std::map<std::string, std::string> environmentVariables;
};

struct TemporalContext {
    std::chrono::system_clock::time_point currentTime;
    std::string businessHours; // "business", "after-hours", "weekend"
    std::vector<std::string> recentOperations;
    std::vector<std::string> scheduledMaintenance;
    std::map<std::string, std::chrono::system_clock::time_point> lastOperationTimes;
};

class ContextEnricher {
public:
    ContextDimensions BuildContext(
        const std::string& primaryEntity,
        const std::vector<std::string>& additionalEntities = {}
    );
    
    // Specific context builders
    IdentityContext BuildIdentityContext(const std::string& identity);
    EnvironmentalContext BuildEnvironmentalContext();
    TemporalContext BuildTemporalContext(const std::string& operationType);
    OperationalContext BuildOperationalContext(const std::string& operation);
    SecurityContext BuildSecurityContext(const std::string& user, const std::string& operation);
    
    // Context validation and enrichment
    void ValidateContext(const ContextDimensions& context);
    ContextDimensions EnrichContext(const ContextDimensions& baseContext);
};
```

### 2. Dependency Analysis Engine
```cpp
class DependencyAnalyzer {
public:
    struct DependencyChain {
        std::string rootEntity;
        std::vector<std::string> dependentEntities;
        std::vector<Relationship> relationships;
        int depth;
        double impactScore; // 0.0 - 1.0
    };
    
    struct ImpactAnalysis {
        std::string targetEntity;
        std::vector<std::string> directlyAffected;
        std::vector<std::string> indirectlyAffected;
        std::map<std::string, double> impactScores;
        std::vector<std::string> criticalDependencies;
    };
    
    // Dependency discovery
    std::vector<DependencyChain> FindDependencies(
        const std::string& entityId,
        int maxDepth = 5
    );
    
    std::vector<std::string> FindDependents(
        const std::string& entityId,
        int maxDepth = 5
    );
    
    // Impact analysis
    ImpactAnalysis AnalyzeImpact(
        const std::string& entityId,
        const std::string& proposedOperation
    );
    
    // Critical path analysis
    std::vector<std::string> FindCriticalPath(
        const std::string& sourceEntity,
        const std::string& targetEntity
    );
    
    // Circular dependency detection
    std::vector<std::vector<std::string>> DetectCircularDependencies();

private:
    // Graph traversal algorithms
    void DepthFirstSearch(
        const std::string& startEntity,
        std::function<bool(const std::string&, int)> visitor,
        int maxDepth
    );
    
    void BreadthFirstSearch(
        const std::string& startEntity,
        std::function<bool(const std::string&, int)> visitor,
        int maxDepth
    );
};
```

## 🔄 Real-Time Data Ingestion

### 1. Multi-Source Data Collectors
```cpp
class DataCollector {
public:
    virtual ~DataCollector() = default;
    virtual std::string GetCollectorId() const = 0;
    virtual std::vector<EntityType> GetSupportedEntityTypes() const = 0;
    virtual void StartCollection() = 0;
    virtual void StopCollection() = 0;
    virtual bool IsHealthy() const = 0;
};

class ActiveDirectoryCollector : public DataCollector {
public:
    std::string GetCollectorId() const override { return "AD_COLLECTOR"; }
    std::vector<EntityType> GetSupportedEntityTypes() const override {
        return {EntityType::USER, EntityType::GROUP, EntityType::COMPUTER, 
                EntityType::ORGANIZATIONAL_UNIT, EntityType::DOMAIN_CONTROLLER};
    }
    
    void StartCollection() override;
    void StopCollection() override;
    
private:
    void CollectUsers();
    void CollectGroups();
    void CollectComputers();
    void CollectOrganizationalUnits();
    void MonitorChanges(); // Real-time change notifications
};

class EventLogCollector : public DataCollector {
public:
    std::string GetCollectorId() const override { return "EVENTLOG_COLLECTOR"; }
    std::vector<EntityType> GetSupportedEntityTypes() const override {
        return {EntityType::EVENT, EntityType::OPERATION};
    }
    
    void StartCollection() override;
    void StopCollection() override;
    
private:
    void CollectSecurityEvents();
    void CollectSystemEvents();
    void CollectApplicationEvents();
    void CorrelateEvents(); // Find related events
};

class WMICollector : public DataCollector {
public:
    std::string GetCollectorId() const override { return "WMI_COLLECTOR"; }
    std::vector<EntityType> GetSupportedEntityTypes() const override {
        return {EntityType::SERVICE, EntityType::PROCESS, EntityType::COMPUTER};
    }
    
    void StartCollection() override;
    void StopCollection() override;
    
private:
    void CollectServices();
    void CollectProcesses();
    void CollectSystemInfo();
    void MonitorPerformance();
};
```

### 2. Data Fusion & Correlation Engine
```cpp
class DataFusionEngine {
public:
    struct CorrelationRule {
        std::string ruleId;
        std::string description;
        std::vector<std::string> sourceCollectors;
        std::function<bool(const Entity&, const Entity&)> correlationFunction;
        RelationshipType resultRelationship;
        double confidence;
    };
    
    void RegisterCorrelationRule(const CorrelationRule& rule);
    void ProcessIncomingData(const std::string& collectorId, const std::vector<Entity>& entities);
    
    // Entity resolution and deduplication
    std::string ResolveEntity(const Entity& entity);
    void MergeEntities(const std::string& primaryId, const std::string& duplicateId);
    
    // Relationship inference
    std::vector<Relationship> InferRelationships(const Entity& entity);
    void ValidateRelationships();
    
private:
    std::vector<CorrelationRule> m_correlationRules;
    std::map<std::string, Entity> m_entityIndex;
    std::map<std::string, std::vector<std::string>> m_entityAliases;
};
```

## 🧮 Graph Query Engine

### 1. High-Performance Graph Queries
```cpp
class GraphQueryEngine {
public:
    // Basic queries
    std::vector<Entity> FindEntities(const EntityQuery& query);
    std::vector<Relationship> FindRelationships(const RelationshipQuery& query);
    
    // Path queries
    std::vector<std::vector<std::string>> FindPaths(
        const std::string& sourceId,
        const std::string& targetId,
        int maxHops = 6
    );
    
    std::vector<std::string> FindShortestPath(
        const std::string& sourceId,
        const std::string& targetId
    );
    
    // Neighborhood queries
    std::vector<Entity> GetNeighbors(
        const std::string& entityId,
        int hops = 1,
        const std::vector<RelationshipType>& relationshipTypes = {}
    );
    
    // Pattern matching
    std::vector<std::map<std::string, std::string>> FindPattern(
        const GraphPattern& pattern
    );
    
    // Aggregation queries
    std::map<std::string, int> CountEntitiesByType();
    std::map<std::string, int> CountRelationshipsByType();
    std::vector<std::string> FindMostConnectedEntities(int limit = 10);
    
    // Temporal queries
    std::vector<Entity> FindEntitiesModifiedSince(
        std::chrono::system_clock::time_point timestamp
    );
    
    std::vector<StateTransition> FindStateChanges(
        const std::string& entityId,
        std::chrono::system_clock::time_point since
    );

private:
    // Query optimization
    std::string OptimizeQuery(const std::string& query);
    void BuildQueryPlan(const std::string& query);
    
    // Indexing for performance
    void BuildEntityIndex();
    void BuildRelationshipIndex();
    void BuildTemporalIndex();
};
```

### 2. Natural Language Query Interface
```cpp
class NaturalLanguageQueryProcessor {
public:
    struct QueryResult {
        std::vector<Entity> entities;
        std::vector<Relationship> relationships;
        std::string explanation;
        double confidence;
    };
    
    QueryResult ProcessNaturalLanguageQuery(const std::wstring& query);
    
    // Query understanding
    std::string ExtractQueryIntent(const std::wstring& query);
    std::vector<std::string> ExtractEntityReferences(const std::wstring& query);
    std::vector<RelationshipType> ExtractRelationshipTypes(const std::wstring& query);
    
    // Query translation
    std::string TranslateToGraphQuery(const std::wstring& naturalLanguageQuery);
    
private:
    // SLM for query understanding
    std::string ClassifyQueryType(const std::wstring& query);
    std::vector<std::string> ExtractParameters(const std::wstring& query);
    
    // Query templates
    std::map<std::string, std::string> m_queryTemplates;
};
```

## 🔮 Pattern Recognition & Predictive Analytics

### 1. Pattern Recognition Engine
```cpp
class PatternRecognitionEngine {
public:
    struct Pattern {
        std::string patternId;
        std::string description;
        std::vector<std::string> entitySequence;
        std::vector<RelationshipType> relationshipSequence;
        double frequency;
        double confidence;
        std::string category; // "normal", "anomaly", "security_risk"
    };
    
    // Pattern discovery
    std::vector<Pattern> DiscoverPatterns(
        const std::vector<std::string>& entityIds,
        int minSupport = 3
    );
    
    std::vector<Pattern> FindAnomalousPatterns(
        std::chrono::system_clock::time_point since
    );
    
    // Pattern matching
    std::vector<std::string> FindEntitiesMatchingPattern(const Pattern& pattern);
    bool DoesEntityMatchPattern(const std::string& entityId, const Pattern& pattern);
    
    // Trend analysis
    std::map<std::string, double> AnalyzeTrends(
        const std::vector<std::string>& entityIds,
        std::chrono::hours timeWindow
    );

private:
    // Machine learning for pattern discovery
    void TrainPatternModels();
    void UpdatePatternModels(const std::vector<Entity>& newData);
    
    // Statistical analysis
    double CalculatePatternSignificance(const Pattern& pattern);
    std::vector<Pattern> FilterSignificantPatterns(const std::vector<Pattern>& patterns);
};
```

### 2. Predictive Analytics Engine
```cpp
class PredictiveAnalyticsEngine {
public:
    struct Prediction {
        std::string entityId;
        std::string predictedEvent;
        std::chrono::system_clock::time_point predictedTime;
        double probability;
        std::string reasoning;
        std::vector<std::string> contributingFactors;
    };
    
    // Failure prediction
    std::vector<Prediction> PredictFailures(
        const std::vector<std::string>& entityIds,
        std::chrono::hours timeHorizon
    );
    
    // Capacity prediction
    std::vector<Prediction> PredictCapacityIssues(
        std::chrono::hours timeHorizon
    );
    
    // Security risk prediction
    std::vector<Prediction> PredictSecurityRisks(
        std::chrono::hours timeHorizon
    );
    
    // Operation outcome prediction
    double PredictOperationSuccess(
        const std::string& operation,
        const std::vector<std::string>& targetEntities,
        const ContextDimensions& context
    );

private:
    // Time series analysis
    std::vector<double> AnalyzeTimeSeries(
        const std::string& entityId,
        const std::string& metric,
        std::chrono::hours window
    );
    
    // Machine learning models
    void TrainPredictionModels();
    void UpdatePredictionModels();
    
    // Feature engineering
    std::vector<double> ExtractFeatures(
        const std::string& entityId,
        const ContextDimensions& context
    );
};
```

## 🎯 Success Criteria

### Performance Targets
- **Entity Ingestion**: 10,000+ entities/second
- **Relationship Discovery**: <100ms for 3-hop queries
- **Context Building**: <500ms for complex contexts
- **Pattern Recognition**: Real-time anomaly detection

### Quality Targets
- **Data Accuracy**: 99%+ entity resolution accuracy
- **Relationship Confidence**: 95%+ for inferred relationships
- **Prediction Accuracy**: 85%+ for failure predictions
- **Query Performance**: <1s for complex graph queries
