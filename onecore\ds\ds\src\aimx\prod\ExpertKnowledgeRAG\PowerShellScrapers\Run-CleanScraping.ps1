# Master Clean Scraping Script - Full Offline Knowledge Base
# Runs all clean scrapers and consolidates data for RAG usage
param(
    [Parameter(Mandatory = $false)]
    [int]$MaxStackOverflowQuestions = 100,
    
    [Parameter(Mandatory = $false)]
    [int]$MaxMicrosoftCmdlets = 50,
    
    [Parameter(Mandatory = $false)]
    [switch]$SkipStackOverflow = $false,
    
    [Parameter(Mandatory = $false)]
    [switch]$SkipMicrosoftDocs = $false,
    
    [Parameter(Mandatory = $false)]
    [switch]$SkipConsolidation = $false
)

Write-Host "Master Clean Scraping Script - Full Offline Knowledge Base" -ForegroundColor Cyan
Write-Host "Building clean, RAG-optimized PowerShell knowledge base" -ForegroundColor Gray
Write-Host "=" * 70 -ForegroundColor Gray

$startTime = Get-Date
$results = @{
    StackOverflow = @{ Success = $false; Error = $null; Entries = 0; File = $null }
    MicrosoftDocs = @{ Success = $false; Error = $null; Entries = 0; File = $null }
    Consolidation = @{ Success = $false; Error = $null; TotalEntries = 0; File = $null }
}

try {
    # Ensure output directory exists
    if (-not (Test-Path "./ScrapedData")) {
        New-Item -Path "./ScrapedData" -ItemType Directory -Force | Out-Null
    }
    
    # 1. Clean StackOverflow Scraping
    if (-not $SkipStackOverflow) {
        Write-Host "`nPHASE 1: Clean StackOverflow Scraping" -ForegroundColor Yellow
        Write-Host "Getting complete Q&A content for offline RAG usage" -ForegroundColor Gray
        Write-Host "-" * 60 -ForegroundColor Gray
        
        try {
            $soOutputFile = "./ScrapedData/clean_stackoverflow_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
            
            Write-Host "Executing Clean StackOverflow Scraper..." -ForegroundColor White
            & ".\Clean-StackOverflowScraper.ps1" -MaxQuestions $MaxStackOverflowQuestions -OutputPath $soOutputFile
            
            if (Test-Path $soOutputFile) {
                $soData = Get-Content $soOutputFile | ConvertFrom-Json
                $results.StackOverflow.Success = $true
                $results.StackOverflow.Entries = $soData.total_entries
                $results.StackOverflow.File = $soOutputFile
                Write-Host "StackOverflow scraping completed: $($soData.total_entries) entries" -ForegroundColor Green
            } else {
                throw "StackOverflow output file not created"
            }
        }
        catch {
            $results.StackOverflow.Error = $_.Exception.Message
            Write-Host "StackOverflow scraping failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "`nPHASE 1: Clean StackOverflow Scraping - SKIPPED" -ForegroundColor Yellow
    }
    
    # 2. Clean Microsoft Docs Scraping
    if (-not $SkipMicrosoftDocs) {
        Write-Host "`nPHASE 2: Clean Microsoft Docs Scraping" -ForegroundColor Yellow
        Write-Host "Getting complete cmdlet documentation for offline RAG usage" -ForegroundColor Gray
        Write-Host "-" * 60 -ForegroundColor Gray
        
        try {
            $msOutputFile = "./ScrapedData/clean_microsoft_docs_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
            
            Write-Host "Executing Clean Microsoft Docs Scraper..." -ForegroundColor White
            & ".\Clean-MicrosoftDocsScraper.ps1" -MaxCmdlets $MaxMicrosoftCmdlets -OutputPath $msOutputFile
            
            if (Test-Path $msOutputFile) {
                $msData = Get-Content $msOutputFile | ConvertFrom-Json
                $results.MicrosoftDocs.Success = $true
                $results.MicrosoftDocs.Entries = $msData.total_entries
                $results.MicrosoftDocs.File = $msOutputFile
                Write-Host "Microsoft Docs scraping completed: $($msData.total_entries) entries" -ForegroundColor Green
            } else {
                throw "Microsoft Docs output file not created"
            }
        }
        catch {
            $results.MicrosoftDocs.Error = $_.Exception.Message
            Write-Host "Microsoft Docs scraping failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "`nPHASE 2: Clean Microsoft Docs Scraping - SKIPPED" -ForegroundColor Yellow
    }
    
    # 3. Data Consolidation
    if (-not $SkipConsolidation) {
        Write-Host "`nPHASE 3: Data Consolidation" -ForegroundColor Yellow
        Write-Host "Merging all clean data into single knowledge base" -ForegroundColor Gray
        Write-Host "-" * 60 -ForegroundColor Gray
        
        try {
            $consolidatedFile = "./ScrapedData/consolidated_knowledge_base_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
            
            Write-Host "Executing Data Consolidation..." -ForegroundColor White
            & ".\Consolidate-CleanData.ps1" -OutputPath $consolidatedFile
            
            if (Test-Path $consolidatedFile) {
                $consolidatedData = Get-Content $consolidatedFile | ConvertFrom-Json
                $results.Consolidation.Success = $true
                $results.Consolidation.TotalEntries = $consolidatedData.total_entries
                $results.Consolidation.File = $consolidatedFile
                Write-Host "Data consolidation completed: $($consolidatedData.total_entries) total entries" -ForegroundColor Green
            } else {
                throw "Consolidated output file not created"
            }
        }
        catch {
            $results.Consolidation.Error = $_.Exception.Message
            Write-Host "Data consolidation failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "`nPHASE 3: Data Consolidation - SKIPPED" -ForegroundColor Yellow
    }
    
    # Final Summary
    $endTime = Get-Date
    $totalTime = $endTime - $startTime
    
    Write-Host "`n" + "=" * 70 -ForegroundColor Gray
    Write-Host "CLEAN SCRAPING SUMMARY" -ForegroundColor Cyan
    Write-Host "=" * 70 -ForegroundColor Gray
    
    Write-Host "Total execution time: $($totalTime.ToString('hh\:mm\:ss'))" -ForegroundColor White
    
    Write-Host "`nResults by phase:" -ForegroundColor Yellow
    
    # StackOverflow Results
    if (-not $SkipStackOverflow) {
        if ($results.StackOverflow.Success) {
            Write-Host "  ✅ StackOverflow: $($results.StackOverflow.Entries) entries" -ForegroundColor Green
            Write-Host "     File: $($results.StackOverflow.File)" -ForegroundColor Gray
        } else {
            Write-Host "  ❌ StackOverflow: FAILED" -ForegroundColor Red
            Write-Host "     Error: $($results.StackOverflow.Error)" -ForegroundColor Red
        }
    }
    
    # Microsoft Docs Results
    if (-not $SkipMicrosoftDocs) {
        if ($results.MicrosoftDocs.Success) {
            Write-Host "  ✅ Microsoft Docs: $($results.MicrosoftDocs.Entries) entries" -ForegroundColor Green
            Write-Host "     File: $($results.MicrosoftDocs.File)" -ForegroundColor Gray
        } else {
            Write-Host "  ❌ Microsoft Docs: FAILED" -ForegroundColor Red
            Write-Host "     Error: $($results.MicrosoftDocs.Error)" -ForegroundColor Red
        }
    }
    
    # Consolidation Results
    if (-not $SkipConsolidation) {
        if ($results.Consolidation.Success) {
            Write-Host "  ✅ Consolidation: $($results.Consolidation.TotalEntries) total entries" -ForegroundColor Green
            Write-Host "     File: $($results.Consolidation.File)" -ForegroundColor Gray
        } else {
            Write-Host "  ❌ Consolidation: FAILED" -ForegroundColor Red
            Write-Host "     Error: $($results.Consolidation.Error)" -ForegroundColor Red
        }
    }
    
    # Success/Failure Summary
    $successCount = 0
    $totalPhases = 0
    
    if (-not $SkipStackOverflow) {
        $totalPhases++
        if ($results.StackOverflow.Success) { $successCount++ }
    }
    if (-not $SkipMicrosoftDocs) {
        $totalPhases++
        if ($results.MicrosoftDocs.Success) { $successCount++ }
    }
    if (-not $SkipConsolidation) {
        $totalPhases++
        if ($results.Consolidation.Success) { $successCount++ }
    }
    
    Write-Host "`nOverall Status: $successCount/$totalPhases phases completed successfully" -ForegroundColor $(if ($successCount -eq $totalPhases) { "Green" } else { "Yellow" })
    
    if ($results.Consolidation.Success) {
        Write-Host "`n🎉 CLEAN KNOWLEDGE BASE READY FOR RAG!" -ForegroundColor Green
        Write-Host "   Final file: $($results.Consolidation.File)" -ForegroundColor White
        Write-Host "   Total entries: $($results.Consolidation.TotalEntries)" -ForegroundColor White
        Write-Host "   This file contains clean, offline content optimized for RAG search" -ForegroundColor Gray
    } else {
        Write-Host "`n⚠️  Some phases failed. Check individual scraper outputs." -ForegroundColor Yellow
    }
    
    Write-Host "`nNext steps:" -ForegroundColor Cyan
    Write-Host "1. Use the consolidated knowledge base file for your RAG system" -ForegroundColor White
    Write-Host "2. The data is clean and contains full offline content" -ForegroundColor White
    Write-Host "3. No more web requests needed during RAG search" -ForegroundColor White
    
}
catch {
    Write-Host "`nCritical error in master script: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
