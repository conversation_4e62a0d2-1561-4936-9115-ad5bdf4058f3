using Microsoft.Extensions.Logging;

namespace netRag;

public class RagApplication
{
    private readonly VectorStoreService _vectorStoreService;
    private readonly DocumentIngestionService _documentIngestionService;
    private readonly RagQueryService _ragQueryService;
    private readonly RagConfiguration _config;
    private readonly ILogger<RagApplication> _logger;

    public RagApplication(
        VectorStoreService vectorStoreService,
        DocumentIngestionService documentIngestionService,
        RagQueryService ragQueryService,
        RagConfiguration config,
        ILogger<RagApplication> logger)
    {
        _vectorStoreService = vectorStoreService;
        _documentIngestionService = documentIngestionService;
        _ragQueryService = ragQueryService;
        _config = config;
        _logger = logger;
    }

    public async Task RunAsync()
    {
        try
        {
            _logger.LogInformation("Starting Foundry Local RAG Application");
            
            // Initialize vector store
            await InitializeVectorStoreAsync();
            
            // Show menu and handle user input
            await RunInteractiveMenuAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Application failed with error");
            throw;
        }
    }

    private async Task InitializeVectorStoreAsync()
    {
        _logger.LogInformation("Initializing vector store...");
        await _vectorStoreService.InitializeAsync(_config.Embedding.VectorSize);
        _logger.LogInformation("Vector store initialized successfully");
    }

    private async Task RunInteractiveMenuAsync()
    {
        while (true)
        {
            Console.WriteLine("\n=== Foundry Local RAG Application ===");
            Console.WriteLine("1. Ingest Document");
            Console.WriteLine("2. Ingest Multiple Documents");
            Console.WriteLine("3. Query RAG System");
            Console.WriteLine("4. Query with Details");
            Console.WriteLine("5. Show Vector Store Statistics");
            Console.WriteLine("6. Reset Vector Database");
            Console.WriteLine("7. Exit");
            Console.Write("Select an option (1-7): ");

            var choice = Console.ReadLine();

            try
            {
                switch (choice)
                {
                    case "1":
                        await HandleSingleDocumentIngestionAsync();
                        break;
                    case "2":
                        await HandleMultipleDocumentIngestionAsync();
                        break;
                    case "3":
                        await HandleQueryAsync();
                        break;
                    case "4":
                        await HandleDetailedQueryAsync();
                        break;
                    case "5":
                        await HandleShowStatisticsAsync();
                        break;
                    case "6":
                        await HandleResetDatabaseAsync();
                        break;
                    case "7":
                        Console.WriteLine("Goodbye!");
                        return;
                    default:
                        Console.WriteLine("Invalid option. Please try again.");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing menu option {Option}", choice);
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    }

    private async Task HandleSingleDocumentIngestionAsync()
    {
        Console.Write("Enter document path: ");
        var documentPath = Console.ReadLine();
        
        if (string.IsNullOrWhiteSpace(documentPath))
        {
            Console.WriteLine("Document path cannot be empty.");
            return;
        }

        Console.Write("Enter document ID: ");
        var documentId = Console.ReadLine();
        
        if (string.IsNullOrWhiteSpace(documentId))
        {
            documentId = Path.GetFileNameWithoutExtension(documentPath);
        }

        Console.WriteLine($"Ingesting document: {documentPath}");
        var startTime = DateTime.Now;
        
        await _documentIngestionService.IngestDocumentAsync(documentPath, documentId);
        
        var duration = DateTime.Now - startTime;
        Console.WriteLine($"Document ingested successfully in {duration.TotalSeconds:F2} seconds!");
    }

    private async Task HandleMultipleDocumentIngestionAsync()
    {
        Console.Write("Enter documents directory path: ");
        var directoryPath = Console.ReadLine();
        
        if (string.IsNullOrWhiteSpace(directoryPath) || !Directory.Exists(directoryPath))
        {
            Console.WriteLine("Invalid directory path.");
            return;
        }

        var files = Directory.GetFiles(directoryPath, "*.txt")
            .Concat(Directory.GetFiles(directoryPath, "*.md"))
            .ToArray();

        if (files.Length == 0)
        {
            Console.WriteLine("No .txt or .md files found in the directory.");
            return;
        }

        Console.WriteLine($"Found {files.Length} documents to ingest:");
        foreach (var file in files)
        {
            Console.WriteLine($"  - {Path.GetFileName(file)}");
        }

        Console.Write("Continue? (y/n): ");
        if (Console.ReadLine()?.ToLower() != "y")
        {
            return;
        }

        var documentPaths = files.ToDictionary(
            file => Path.GetFileNameWithoutExtension(file),
            file => file);

        Console.WriteLine("Starting batch ingestion...");
        var startTime = DateTime.Now;
        
        await _documentIngestionService.IngestMultipleDocumentsAsync(documentPaths);
        
        var duration = DateTime.Now - startTime;
        Console.WriteLine($"All documents ingested successfully in {duration.TotalSeconds:F2} seconds!");
    }

    private async Task HandleQueryAsync()
    {
        Console.Write("Enter your question: ");
        var question = Console.ReadLine();
        
        if (string.IsNullOrWhiteSpace(question))
        {
            Console.WriteLine("Question cannot be empty.");
            return;
        }

        Console.WriteLine("\nProcessing your question...");
        var startTime = DateTime.Now;
        
        var answer = await _ragQueryService.QueryAsync(question);
        
        var duration = DateTime.Now - startTime;
        
        Console.WriteLine("\n=== Answer ===");
        Console.WriteLine(answer);
        Console.WriteLine($"\n(Response generated in {duration.TotalSeconds:F2} seconds)");
    }

    private async Task HandleDetailedQueryAsync()
    {
        Console.Write("Enter your question: ");
        var question = Console.ReadLine();
        
        if (string.IsNullOrWhiteSpace(question))
        {
            Console.WriteLine("Question cannot be empty.");
            return;
        }

        Console.WriteLine("\nProcessing your question...");
        var startTime = DateTime.Now;
        
        var result = await _ragQueryService.QueryWithDetailsAsync(question);
        
        var duration = DateTime.Now - startTime;
        
        Console.WriteLine("\n=== Detailed Query Result ===");
        Console.WriteLine($"Question: {result.Question}");
        Console.WriteLine($"\nAnswer: {result.Answer}");
        
        Console.WriteLine($"\nSources ({result.Sources.Count}):");
        for (int i = 0; i < result.Sources.Count; i++)
        {
            var source = result.Sources[i];
            Console.WriteLine($"  {i + 1}. Document: {source.DocumentId} (Score: {source.Score:F3})");
            Console.WriteLine($"     Path: {source.DocumentPath}");
            Console.WriteLine($"     Chunk: {source.ChunkIndex}");
            Console.WriteLine($"     Text: {source.Text[..Math.Min(100, source.Text.Length)]}...");
            Console.WriteLine();
        }
        
        Console.WriteLine($"(Response generated in {duration.TotalSeconds:F2} seconds)");
    }

    private async Task HandleShowStatisticsAsync()
    {
        var vectorCount = await _vectorStoreService.GetVectorCountAsync();

        Console.WriteLine("\n=== Vector Store Statistics ===");
        Console.WriteLine($"Total vectors stored: {vectorCount}");
        Console.WriteLine($"Vector dimensions: {_config.Embedding.VectorSize}");
        Console.WriteLine($"Chunk size: {_config.DocumentIngestion.ChunkSize} words");
        Console.WriteLine($"Chunk overlap: {_config.DocumentIngestion.ChunkOverlap} words");
        Console.WriteLine($"Search limit: {_config.DocumentIngestion.SearchLimit} results");

        if (vectorCount > 0)
        {
            var estimatedMemoryMB = (vectorCount * _config.Embedding.VectorSize * sizeof(float)) / (1024 * 1024);
            Console.WriteLine($"Estimated memory usage: ~{estimatedMemoryMB:F1} MB");
        }
    }

    private async Task HandleResetDatabaseAsync()
    {
        Console.Write("Are you sure you want to reset the vector database? This will delete all ingested documents. (y/n): ");
        if (Console.ReadLine()?.ToLower() != "y")
        {
            return;
        }

        Console.WriteLine("Resetting vector database...");
        await _vectorStoreService.ClearAsync();
        Console.WriteLine("Vector database reset successfully!");
    }
}
