# Test code extraction specifically
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Code Extraction Test" -ForegroundColor Cyan

# Import core module
$coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
Import-Module $coreModulePath -Force

# Initialize framework
$result = Initialize-ScrapingFramework -ConfigPath $ConfigPath

# Test content
$testContent = @"
Here is some PowerShell code:
```powershell
Get-ADUser -Filter "Name -eq '<PERSON>'"
Set-ADUser -Identity jsmith -Title "Manager"
```
And some more text with Get-ADGroup and New-ADUser commands.

Another code block:
<code>
New-ADGroup -Name "TestGroup" -GroupScope Global
</code>
"@

Write-Host "`nTest content:" -ForegroundColor Yellow
Write-Host $testContent -ForegroundColor Gray

# Test with explicit patterns
Write-Host "`nTesting with explicit patterns..." -ForegroundColor Yellow
$patterns = @(
    '```powershell([\\s\\S]*?)```',
    '```ps([\\s\\S]*?)```',
    '<code>([\\s\\S]*?)</code>'
)

foreach ($pattern in $patterns) {
    Write-Host "  Testing pattern: $pattern" -ForegroundColor Gray
    try {
        $matches = [regex]::Matches($testContent, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase -bor [System.Text.RegularExpressions.RegexOptions]::Multiline)
        Write-Host "    Found $($matches.Count) matches" -ForegroundColor Green
        
        foreach ($match in $matches) {
            if ($match.Groups.Count -gt 1) {
                $code = $match.Groups[1].Value.Trim()
                Write-Host "    Code block: $($code.Length) characters" -ForegroundColor Green
                Write-Host "    Content: $($code.Substring(0, [Math]::Min(50, $code.Length)))..." -ForegroundColor Gray
            }
        }
    }
    catch {
        Write-Host "    Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test the function with explicit patterns
Write-Host "`nTesting Extract-CodeBlocks function with explicit patterns..." -ForegroundColor Yellow
try {
    $codeBlocks = Extract-CodeBlocks -Content $testContent -Patterns $patterns
    Write-Host "   Found $($codeBlocks.Count) code blocks" -ForegroundColor Green
    foreach ($block in $codeBlocks) {
        Write-Host "   Block: $($block.Substring(0, [Math]::Min(50, $block.Length)))..." -ForegroundColor Gray
    }
}
catch {
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test the function without explicit patterns (using config)
Write-Host "`nTesting Extract-CodeBlocks function with config patterns..." -ForegroundColor Yellow
try {
    $codeBlocks = Extract-CodeBlocks -Content $testContent
    Write-Host "   Found $($codeBlocks.Count) code blocks" -ForegroundColor Green
}
catch {
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nCode extraction test completed!" -ForegroundColor Cyan
