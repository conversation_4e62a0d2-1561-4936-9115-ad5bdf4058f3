# Debug Group Management Operation
Write-Host "Testing Group Management Operation..." -ForegroundColor Cyan

$body = @{
    userInput = "Add user bob.wilson to Finance group"
    userId = "test-debug"
    environment = "production"
    priority = "normal"
    requestId = [guid]::NewGuid().ToString()
} | ConvertTo-Json

Write-Host "Request Body:"
Write-Host $body

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8082/api/IntentPlanning/analyze" -Method Post -Body $body -ContentType "application/json" -TimeoutSec 30
    
    Write-Host "`nResponse Analysis:" -ForegroundColor Yellow
    Write-Host "Success: $($response.success)"
    
    if ($response.userGoal) {
        Write-Host "Goal: $($response.userGoal.primaryObjective)"
        Write-Host "Category: $($response.userGoal.context.intentCategory)"
        Write-Host "Confidence: $($response.userGoal.extractionConfidence)"
    }
    
    if ($response.primaryWorkflow -and $response.primaryWorkflow.steps) {
        Write-Host "Primary Workflow Steps: $($response.primaryWorkflow.steps.Count)"
        foreach ($step in $response.primaryWorkflow.steps) {
            Write-Host "  - Step: $($step.stepName)"
            Write-Host "    Tool: $($step.toolId)"
            Write-Host "    Operation: $($step.operation)"
        }
    } else {
        Write-Host "No primary workflow steps generated"
    }
    
    if (-not $response.success) {
        Write-Host "Error Message: $($response.errorMessage)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Request failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nDebug Complete!" -ForegroundColor Green
