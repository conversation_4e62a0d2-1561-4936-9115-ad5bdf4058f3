/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    IntentResolutionEngine.h

Abstract:

    Header file for the Intent Resolution Engine component that handles
    hierarchical intent classification and resolution for IT administration tasks.
    This engine maps user intents to specific execution strategies and parameters.

Author:

    AI Assistant 07/27/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <regex>
#include "AimxCommon.h"
#include "nlohmann/json.hpp"

// Intent categories covering 80%+ of daily IT tasks
enum class INTENT_CATEGORY
{
    USER_MANAGEMENT = 0,        // 15% of daily tasks
    GROUP_MANAGEMENT,           // 8% of daily tasks
    AD_OPERATIONS,              // 12% of daily tasks
    SECURITY_OPERATIONS,        // 10% of daily tasks
    MONITORING_OPERATIONS,      // 15% of daily tasks
    COMPUTER_MANAGEMENT,        // 8% of daily tasks
    NETWORK_OPERATIONS,         // 7% of daily tasks
    GPO_OPERATIONS,             // 5% of daily tasks
    SYSTEM_OPERATIONS,          // 3% of daily tasks
    POWERSHELL_OPERATIONS,      // 2% of daily tasks
    BACKUP_OPERATIONS,          // 2% of daily tasks
    TROUBLESHOOTING,            // 13% of daily tasks
    UNKNOWN_INTENT
};

// Specific intent actions within categories
enum class SPECIFIC_INTENT
{
    // USER_MANAGEMENT intents
    SEARCH_USER,
    CREATE_USER,
    MODIFY_USER,
    RESET_PASSWORD,
    UNLOCK_ACCOUNT,
    DELETE_USER,
    
    // GROUP_MANAGEMENT intents
    SEARCH_GROUP,
    CREATE_GROUP,
    MODIFY_GROUP,
    MANAGE_MEMBERSHIP,
    DELETE_GROUP,
    
    // AD_OPERATIONS intents
    CHECK_REPLICATION,
    DC_HEALTH,
    SCHEMA_OPERATIONS,
    TRUST_MANAGEMENT,
    FSMO_OPERATIONS,
    
    // SECURITY_OPERATIONS intents
    PERMISSION_AUDIT,
    AUTHENTICATION_ISSUES,
    CERTIFICATE_MANAGEMENT,
    SECURITY_AUDITING,
    
    // MONITORING_OPERATIONS intents
    EVENT_LOG_ANALYSIS,
    PERFORMANCE_MONITORING,
    SERVICE_HEALTH,
    SYSTEM_HEALTH,
    
    // NETWORK_OPERATIONS intents
    CONNECTIVITY_TESTING,
    DNS_OPERATIONS,
    DHCP_OPERATIONS,
    NETWORK_DIAGNOSTICS,
    
    // And more...
    UNKNOWN_SPECIFIC_INTENT
};

// Execution strategy types
enum class EXECUTION_STRATEGY
{
    POWERSHELL_DIRECT,      // Single PowerShell command
    POWERSHELL_SCRIPT,      // PowerShell script execution
    MCP_TOOL_CHAIN,         // MCP tool sequence
    NATIVE_API_CALL,        // Direct Windows API
    WORKFLOW_ORCHESTRATION  // Multi-step workflow
};

// Parameter extraction result
struct PARAMETER_EXTRACTION_RESULT
{
    nlohmann::json extractedParameters;
    std::vector<std::string> missingRequiredParameters;
    std::vector<std::string> inferredParameters;
    double confidenceScore;
    bool requiresClarification;
};

// Intent resolution result
struct INTENT_RESOLUTION_RESULT
{
    INTENT_CATEGORY category;
    SPECIFIC_INTENT specificIntent;
    EXECUTION_STRATEGY recommendedStrategy;
    PARAMETER_EXTRACTION_RESULT parameters;
    std::vector<std::string> requiredCapabilities;
    std::vector<std::string> suggestedCommands;
    double confidenceScore;
    std::wstring clarificationPrompt;
};

// Intent resolution rule
struct INTENT_RESOLUTION_RULE
{
    INTENT_CATEGORY category;
    SPECIFIC_INTENT specificIntent;
    std::vector<std::regex> patterns;
    std::vector<std::string> keywords;
    std::vector<std::string> requiredParameters;
    std::vector<std::string> optionalParameters;
    EXECUTION_STRATEGY preferredStrategy;
    std::function<bool(const std::wstring&, const nlohmann::json&)> validator;
};

// Context enrichment data
struct CONTEXT_ENRICHMENT_DATA
{
    std::map<std::string, std::string> identityMappings;
    std::map<std::string, std::string> domainContext;
    std::map<std::string, std::string> systemState;
    std::vector<std::string> availableResources;
};

// Main Intent Resolution Engine class
class IntentResolutionEngine
{
public:
    // Initialize the engine with resolution rules
    HRESULT Initialize();
    
    // Uninitialize and cleanup
    void Uninitialize();
    
    // Resolve user intent from natural language input
    HRESULT ResolveIntent(
        _In_ const std::wstring& userInput,
        _In_ const CONTEXT_ENRICHMENT_DATA& context,
        _Out_ INTENT_RESOLUTION_RESULT& result
    );
    
    // Classify intent category using LLM or rule-based approach
    HRESULT ClassifyIntentCategory(
        _In_ const std::wstring& userInput,
        _Out_ INTENT_CATEGORY& category,
        _Out_ double& confidence
    );
    
    // Resolve specific intent within a category
    HRESULT ResolveSpecificIntent(
        _In_ const std::wstring& userInput,
        _In_ INTENT_CATEGORY category,
        _Out_ SPECIFIC_INTENT& specificIntent,
        _Out_ double& confidence
    );
    
    // Extract and validate parameters from user input
    HRESULT ExtractParameters(
        _In_ const std::wstring& userInput,
        _In_ SPECIFIC_INTENT specificIntent,
        _In_ const CONTEXT_ENRICHMENT_DATA& context,
        _Out_ PARAMETER_EXTRACTION_RESULT& result
    );
    
    // Generate clarification prompt for missing parameters
    HRESULT GenerateClarificationPrompt(
        _In_ SPECIFIC_INTENT specificIntent,
        _In_ const std::vector<std::string>& missingParameters,
        _Out_ std::wstring& clarificationPrompt
    );
    
    // Recommend execution strategy based on intent and context
    HRESULT RecommendExecutionStrategy(
        _In_ SPECIFIC_INTENT specificIntent,
        _In_ const nlohmann::json& parameters,
        _In_ const CONTEXT_ENRICHMENT_DATA& context,
        _Out_ EXECUTION_STRATEGY& strategy
    );
    
    // Get suggested commands for a specific intent
    HRESULT GetSuggestedCommands(
        _In_ SPECIFIC_INTENT specificIntent,
        _In_ const nlohmann::json& parameters,
        _Out_ std::vector<std::string>& commands
    );
    
    // Update resolution rules based on feedback
    HRESULT UpdateResolutionRules(
        _In_ const std::wstring& userInput,
        _In_ INTENT_CATEGORY actualCategory,
        _In_ SPECIFIC_INTENT actualIntent,
        _In_ bool wasSuccessful
    );

private:
    // Initialize intent resolution rules
    HRESULT InitializeResolutionRules();
    
    // Initialize parameter extraction patterns
    HRESULT InitializeParameterPatterns();
    
    // Initialize clarification templates
    HRESULT InitializeClarificationTemplates();
    
    // Rule-based intent classification
    HRESULT ClassifyIntentByRules(
        _In_ const std::wstring& userInput,
        _Out_ INTENT_CATEGORY& category,
        _Out_ double& confidence
    );
    
    // LLM-based intent classification
    HRESULT ClassifyIntentByLLM(
        _In_ const std::wstring& userInput,
        _Out_ INTENT_CATEGORY& category,
        _Out_ double& confidence
    );
    
    // Extract identity parameters (usernames, computer names, etc.)
    HRESULT ExtractIdentityParameters(
        _In_ const std::wstring& userInput,
        _Out_ nlohmann::json& parameters
    );
    
    // Validate extracted parameters
    HRESULT ValidateParameters(
        _In_ SPECIFIC_INTENT specificIntent,
        _In_ const nlohmann::json& parameters,
        _Out_ bool& isValid
    );
    
    // Infer missing parameters from context
    HRESULT InferMissingParameters(
        _In_ SPECIFIC_INTENT specificIntent,
        _In_ const nlohmann::json& extractedParameters,
        _In_ const CONTEXT_ENRICHMENT_DATA& context,
        _Out_ nlohmann::json& inferredParameters
    );

    // Member variables
    std::vector<INTENT_RESOLUTION_RULE> m_resolutionRules;
    std::map<std::string, std::regex> m_parameterPatterns;
    std::map<SPECIFIC_INTENT, std::wstring> m_clarificationTemplates;
    std::map<INTENT_CATEGORY, std::vector<std::string>> m_categoryKeywords;
    std::map<SPECIFIC_INTENT, std::vector<std::string>> m_intentKeywords;
    std::map<SPECIFIC_INTENT, EXECUTION_STRATEGY> m_preferredStrategies;
    
    // Statistics for continuous improvement
    std::map<INTENT_CATEGORY, int> m_categorySuccessCount;
    std::map<INTENT_CATEGORY, int> m_categoryTotalCount;
    std::map<SPECIFIC_INTENT, int> m_intentSuccessCount;
    std::map<SPECIFIC_INTENT, int> m_intentTotalCount;
};

// Utility functions
const char* ToString(INTENT_CATEGORY category);
const char* ToString(SPECIFIC_INTENT intent);
const char* ToString(EXECUTION_STRATEGY strategy);

INTENT_CATEGORY IntentCategoryFromString(const std::string& categoryStr);
SPECIFIC_INTENT SpecificIntentFromString(const std::string& intentStr);
EXECUTION_STRATEGY ExecutionStrategyFromString(const std::string& strategyStr);
