# Simplified GitHub scraper test
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxRepos = 3
)

Write-Host "Testing Simplified GitHub Scraper" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    
    Write-Host "✅ Framework initialized" -ForegroundColor Green
    
    # Get GitHub repositories
    Write-Host "`nFetching GitHub repositories..." -ForegroundColor Yellow
    
    $baseUrl = "https://api.github.com/search/repositories"
    $query = "PowerShell ActiveDirectory language:PowerShell"
    $apiUrl = "${baseUrl}?q=${query}&sort=stars&order=desc&per_page=${MaxRepos}"
    
    # GitHub API requires User-Agent header
    $headers = @{
        'User-Agent' = 'PowerShell-Scraper/1.0'
        'Accept' = 'application/vnd.github.v3+json'
    }
    
    $response = Invoke-WebRequestWithRetry -Uri $apiUrl -Headers $headers -MaxRetries 2
    $data = $response.Content | ConvertFrom-Json
    
    Write-Host "✅ Found $($data.items.Count) repositories" -ForegroundColor Green
    
    # Check rate limiting
    if ($response.Headers['X-RateLimit-Remaining']) {
        $remaining = $response.Headers['X-RateLimit-Remaining']
        Write-Host "  Rate limit remaining: $remaining" -ForegroundColor Gray
        
        if ([int]$remaining -lt 5) {
            Write-Host "  ⚠️  Low rate limit remaining - limiting scraping" -ForegroundColor Yellow
        }
    }
    
    # Process each repository into knowledge patterns
    $patterns = @()
    $count = 0
    
    foreach ($repo in $data.items) {
        $count++
        Write-Host "`nProcessing repository $count..." -ForegroundColor Yellow
        Write-Host "  Name: $($repo.full_name)" -ForegroundColor Cyan
        Write-Host "  Stars: $($repo.stargazers_count), Language: $($repo.language)" -ForegroundColor Gray
        Write-Host "  Description: $($repo.description)" -ForegroundColor Gray
        
        # Create knowledge pattern for the repository
        $repoContent = "# $($repo.full_name)`n`n"
        $repoContent += "**Description:** $($repo.description)`n`n"
        $repoContent += "**Stars:** $($repo.stargazers_count)`n"
        $repoContent += "**Language:** $($repo.language)`n"
        $repoContent += "**URL:** $($repo.html_url)`n`n"
        
        if ($repo.topics) {
            $repoContent += "**Topics:** $($repo.topics -join ', ')`n`n"
        }
        
        # Determine domain based on repository name and description
        $domain = "general_ad"
        if ($repo.full_name -like "*user*" -or $repo.description -like "*user*") {
            $domain = "user_management"
        } elseif ($repo.full_name -like "*group*" -or $repo.description -like "*group*") {
            $domain = "group_management"
        } elseif ($repo.full_name -like "*computer*" -or $repo.description -like "*computer*") {
            $domain = "computer_management"
        }
        
        # Create knowledge pattern
        $pattern = New-KnowledgePattern -Title "GitHub: $($repo.full_name)" -Content $repoContent -SourceUrl $repo.html_url -SourceType "github" -Domain $domain -Operation "read" -Author $repo.owner.login -CredibilityScore 0.75
        
        if ($pattern) {
            # Set relevance score based on stars (logarithmic scale)
            $pattern.RelevanceScore = [Math]::Min(1.0, [Math]::Log10($repo.stargazers_count + 1) / 4.0)
            
            # Add topics as tags
            if ($repo.topics) {
                $pattern.Tags += $repo.topics
            }
            
            # Add language and repository info
            $pattern.Tags += @("github", "repository", $repo.language.ToLower())
            
            $patterns += $pattern
            Write-Host "  ✅ Created pattern: $($pattern.Id)" -ForegroundColor Green
            Write-Host "    Domain: $($pattern.Domain), Relevance: $($pattern.RelevanceScore.ToString('F2'))" -ForegroundColor Gray
        }
    }
    
    # Save results
    if ($patterns.Count -gt 0) {
        Write-Host "`nSaving results..." -ForegroundColor Yellow
        $outputFile = Save-ScrapingResults -Patterns $patterns -SourceType "github"
        Write-Host "✅ Saved $($patterns.Count) patterns to: $outputFile" -ForegroundColor Green
        
        # Display summary
        Write-Host "`nSummary:" -ForegroundColor Yellow
        foreach ($pattern in $patterns) {
            Write-Host "  Repository: $($pattern.Title)" -ForegroundColor Cyan
            Write-Host "    ID: $($pattern.Id)" -ForegroundColor Gray
            Write-Host "    Domain: $($pattern.Domain)" -ForegroundColor Gray
            Write-Host "    Relevance: $($pattern.RelevanceScore.ToString('F2'))" -ForegroundColor Gray
            Write-Host "    Author: $($pattern.CreatedBy)" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ No patterns created" -ForegroundColor Red
    }
    
    Write-Host "`n✅ GitHub scraper test completed!" -ForegroundColor Green
}
catch {
    Write-Host "`n❌ GitHub scraper test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    
    # Check if it's a rate limiting issue
    if ($_.Exception.Message -like "*rate limit*" -or $_.Exception.Message -like "*403*") {
        Write-Host "`nNote: This might be a rate limiting issue. GitHub API has limits for unauthenticated requests." -ForegroundColor Yellow
        Write-Host "Consider using a GitHub token for higher rate limits." -ForegroundColor Yellow
    }
}
