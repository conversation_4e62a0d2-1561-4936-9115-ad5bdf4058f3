# IntentPlanningService API Documentation

## Overview

The IntentPlanningService is a core component of the LLM-orchestrated expert system that analyzes user intent and generates executable workflows for IT administration tasks. It provides AI-driven intent understanding, workflow planning, tool discovery, and risk assessment capabilities.

**Base URL:** `http://localhost:8082`  
**Content-Type:** `application/json`  
**Service Port:** 8082

## Authentication

Currently, the service operates without authentication for development purposes. In production, implement appropriate authentication mechanisms.

## API Endpoints

### 1. Intent Analysis

#### POST `/api/IntentPlanning/analyze`

Analyzes user intent and generates an executable workflow with risk assessment.

**Request Body:**
```json
{
  "requestId": "string (optional, auto-generated if not provided)",
  "userId": "string (required)",
  "userInput": "string (required, max 5000 characters)",
  "context": {
    "key": "value"
  },
  "timestamp": "2024-01-01T00:00:00Z (optional, auto-generated)",
  "priority": "normal|low|high|critical (optional, default: normal)",
  "environment": "production|staging|test|development (optional, default: production)"
}
```

**Response (200 OK):**
```json
{
  "requestId": "string",
  "userGoal": {
    "goalId": "string",
    "primaryObjective": "string",
    "successCriteria": "string",
    "constraints": ["string"],
    "urgencyLevel": "string",
    "context": {"key": "value"},
    "extractionConfidence": 0.85,
    "extractionMethod": "ai_rag_llm"
  },
  "primaryWorkflow": {
    "workflowId": "string",
    "workflowName": "string",
    "description": "string",
    "steps": [
      {
        "stepId": "string",
        "stepName": "string",
        "description": "string",
        "toolId": "string",
        "operation": "string",
        "parameters": {"key": "value"},
        "dependencies": ["stepId"],
        "timeoutSeconds": 300,
        "retryPolicy": {
          "maxRetries": 3,
          "retryDelaySeconds": 5,
          "exponentialBackoff": true,
          "retryableErrors": ["timeout", "network_error"]
        },
        "rollbackOperation": "string",
        "rollbackParameters": {"key": "value"},
        "isOptional": false,
        "executionOrder": 1
      }
    ],
    "errorHandling": {"key": "value"},
    "requiredPermissions": ["string"],
    "riskLevel": "low|medium|high|critical",
    "estimatedTotalTimeSeconds": 600,
    "createdAt": "2024-01-01T00:00:00Z",
    "createdBy": "string",
    "approvalRequired": false,
    "approvalReason": "string",
    "planningConfidence": 0.92,
    "planningMethod": "ai_rag_llm"
  },
  "alternativeWorkflows": [],
  "availableTools": [],
  "success": true,
  "processingTimeMs": 1250
}
```

**Error Responses:**
- `400 Bad Request`: Invalid request (missing required fields, input too long)
- `500 Internal Server Error`: Processing error

### 2. Tool Discovery

#### GET `/api/IntentPlanning/tools`

Retrieves all available tools from the Universal Tool Manager.

**Query Parameters:**
- `category` (optional): Filter by tool category (e.g., "ad", "exchange", "azure")
- `capabilities` (optional): Comma-separated list of required capabilities

**Response (200 OK):**
```json
[
  {
    "toolId": "string",
    "toolName": "string",
    "description": "string",
    "category": "string",
    "operations": [
      {
        "operationId": "string",
        "operationName": "string",
        "description": "string",
        "parameters": [
          {
            "parameterName": "string",
            "parameterType": "string|int|bool|array|object",
            "description": "string",
            "isRequired": true,
            "defaultValue": null,
            "validationRules": ["string"],
            "examples": []
          }
        ],
        "returnType": "string",
        "estimatedDurationSeconds": 30,
        "riskLevel": "low|medium|high|critical",
        "requiredPermissions": ["string"],
        "supportsRollback": false,
        "rollbackOperation": "string"
      }
    ],
    "capabilities": {
      "supportsParallelExecution": false,
      "supportsBatching": false,
      "supportsStreaming": false,
      "supportsRollback": false,
      "maxConcurrentOperations": 1,
      "maxBatchSize": 1,
      "supportedEnvironments": ["production", "staging", "test"],
      "resourceRequirements": {"key": "value"}
    },
    "isAvailable": true,
    "healthStatus": "healthy|degraded|unhealthy",
    "lastHealthCheck": "2024-01-01T00:00:00Z"
  }
]
```

#### GET `/api/IntentPlanning/tools/{toolId}`

Retrieves a specific tool by its ID.

**Path Parameters:**
- `toolId` (required): The unique identifier of the tool

**Response (200 OK):** Same as single tool object above  
**Response (404 Not Found):** Tool not found

### 3. Risk Assessment

#### POST `/api/IntentPlanning/assess-risk`

Assesses the risk level of a workflow and determines if approval is required.

**Request Body:**
```json
{
  "workflowId": "string",
  "workflowName": "string",
  "description": "string",
  "steps": [
    {
      "stepId": "string",
      "stepName": "string",
      "description": "string",
      "toolId": "string",
      "operation": "string",
      "parameters": {"key": "value"},
      "dependencies": ["stepId"],
      "timeoutSeconds": 300,
      "retryPolicy": {
        "maxRetries": 3,
        "retryDelaySeconds": 5,
        "exponentialBackoff": true
      },
      "rollbackOperation": "string",
      "isOptional": false,
      "executionOrder": 1
    }
  ],
  "requiredPermissions": ["string"],
  "riskLevel": "string",
  "estimatedTotalTimeSeconds": 600,
  "createdBy": "string"
}
```

**Response (200 OK):**
```json
{
  "workflowId": "string",
  "riskLevel": "low|medium|high|critical",
  "riskScore": 0.75,
  "requiresApproval": true,
  "approvalReason": "string",
  "riskFactors": ["string"],
  "mitigationRecommendations": ["string"],
  "stepRisks": [
    {
      "stepId": "string",
      "stepName": "string",
      "riskLevel": "string",
      "riskScore": 0.6,
      "riskFactors": ["string"]
    }
  ],
  "assessmentTimestamp": "2024-01-01T00:00:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid workflow (empty steps, missing required fields)

### 4. Health & Maintenance

#### GET `/api/IntentPlanning/health/tools`

Checks the health status of all available tools.

**Response (200 OK):**
```json
{
  "tool-id-1": "healthy",
  "tool-id-2": "degraded",
  "tool-id-3": "unhealthy"
}
```

#### POST `/api/IntentPlanning/tools/refresh`

Refreshes the tool cache by fetching the latest tool information.

**Response (200 OK):**
```json
{
  "message": "Tool cache refreshed successfully",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Data Models

### UserRequest
- `requestId`: Unique identifier for the request
- `userId`: Identifier of the user making the request
- `userInput`: Natural language description of the desired operation
- `context`: Additional context information
- `priority`: Request priority level
- `environment`: Target environment for execution

### UserGoal
- `goalId`: Unique identifier for the extracted goal
- `primaryObjective`: Main objective extracted from user input
- `successCriteria`: Criteria for successful completion
- `constraints`: List of constraints or limitations
- `extractionConfidence`: AI confidence in goal extraction (0.0-1.0)

### ExecutableWorkflow
- `workflowId`: Unique identifier for the workflow
- `steps`: Ordered list of workflow steps
- `requiredPermissions`: Permissions needed for execution
- `riskLevel`: Overall risk assessment
- `approvalRequired`: Whether manual approval is needed

### WorkflowStep
- `stepId`: Unique identifier for the step
- `toolId`: ID of the tool to execute this step
- `operation`: Specific operation to perform
- `parameters`: Operation parameters
- `dependencies`: List of step IDs this step depends on
- `retryPolicy`: Retry configuration for failed executions

## Error Handling

All endpoints return standard HTTP status codes:

- **200 OK**: Request successful
- **400 Bad Request**: Invalid request data
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server processing error

Error responses include a `ProblemDetails` object:
```json
{
  "title": "Error Title",
  "detail": "Detailed error description",
  "status": 400
}
```

## Rate Limiting

Current configuration allows up to 100 concurrent requests with a 30-second timeout per request.

## Examples

See the `test-intent-planning-service.ps1` script for comprehensive examples of all API endpoints and various use cases including user management, group management, computer management, and error scenarios.
