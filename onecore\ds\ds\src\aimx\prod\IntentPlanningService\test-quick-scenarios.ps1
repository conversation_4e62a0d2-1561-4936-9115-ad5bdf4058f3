# Quick Robustness Testing for IntentPlanningService
param(
    [string]$BaseUrl = "http://localhost:8082"
)

Write-Host "Quick Robustness Testing - IntentPlanningService" -ForegroundColor Cyan
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow

function Test-Scenario {
    param(
        [string]$Name,
        [string]$UserInput,
        [string]$Expected = ""
    )

    Write-Host "`nTesting: $Name" -ForegroundColor Green
    Write-Host "Input: $UserInput" -ForegroundColor White

    $body = @{
        userInput = $UserInput
        userId = "test-$(Get-Random)"
        environment = "production"
        priority = "normal"
        requestId = [guid]::NewGuid().ToString()
    } | ConvertTo-Json -Depth 10
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        $response = Invoke-RestMethod -Uri "$BaseUrl/api/IntentPlanning/analyze" -Method Post -Body $body -ContentType "application/json" -TimeoutSec 30
        $stopwatch.Stop()
        
        $primarySteps = if ($response.primaryWorkflow -and $response.primaryWorkflow.steps) { $response.primaryWorkflow.steps.Count } else { 0 }
        $workflowSteps = if ($response.workflow -and $response.workflow.steps) { $response.workflow.steps.Count } else { 0 }
        $category = if ($response.userGoal) { $response.userGoal.context.intentCategory } else { "unknown" }
        $confidence = if ($response.userGoal) { $response.userGoal.extractionConfidence } else { 0 }
        $goal = if ($response.userGoal) { $response.userGoal.primaryObjective } else { "none" }

        if ($response.success) {
            Write-Host "PASS - Category: $category | Primary Steps: $primarySteps | Workflow Steps: $workflowSteps | Time: $($stopwatch.ElapsedMilliseconds)ms | Confidence: $confidence" -ForegroundColor Green
            Write-Host "  Goal: $goal" -ForegroundColor Gray
        } else {
            Write-Host "FAIL - $($response.errorMessage)" -ForegroundColor Red
        }

        return @{
            Success = $response.success
            Category = $category
            PrimarySteps = $primarySteps
            WorkflowSteps = $workflowSteps
            Time = $stopwatch.ElapsedMilliseconds
            Confidence = $confidence
            Goal = $goal
        }
    }
    catch {
        $stopwatch.Stop()
        Write-Host "ERROR - $($_.Exception.Message)" -ForegroundColor Red
        return @{ Success = $false; Error = $_.Exception.Message; Time = $stopwatch.ElapsedMilliseconds }
    }
}

Write-Host "`n=== CORE AD OPERATIONS ===" -ForegroundColor Magenta

$results = @()
$results += Test-Scenario -Name "User Creation Basic" -UserInput "Create user account for Sarah Johnson" -Expected "user_management"
$results += Test-Scenario -Name "Password Reset" -UserInput "Reset password for user alice.smith" -Expected "user_management"
$results += Test-Scenario -Name "Group Management" -UserInput "Add user bob.wilson to Finance group" -Expected "group_management"
$results += Test-Scenario -Name "Computer Management" -UserInput "Join computer DESKTOP-001 to domain" -Expected "computer_management"

Write-Host "`n=== COMPLEX SCENARIOS ===" -ForegroundColor Magenta

$results += Test-Scenario -Name "Multi-Step Setup" -UserInput "Create new employee Lisa Chen in Sales, add to Sales group, set password"
$results += Test-Scenario -Name "User Offboarding" -UserInput "Disable user tom.anderson, remove from groups"
$results += Test-Scenario -Name "Security Audit" -UserInput "Generate report of admin users and login dates"

Write-Host "`n=== EDGE CASES ===" -ForegroundColor Magenta

$results += Test-Scenario -Name "Ambiguous Request" -UserInput "Fix the user issue"
$results += Test-Scenario -Name "Non-IT Request" -UserInput "Order pizza for meeting"
$results += Test-Scenario -Name "Incomplete Info" -UserInput "Create user"

Write-Host "`n=== RESULTS SUMMARY ===" -ForegroundColor Cyan

$total = $results.Count
$passed = ($results | Where-Object { $_.Success -eq $true }).Count
$failed = $total - $passed

Write-Host "Total Tests: $total" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passed / $total) * 100, 1))%" -ForegroundColor Yellow

$times = $results | Where-Object { $_.Time } | ForEach-Object { $_.Time }
if ($times.Count -gt 0) {
    $avgTime = ($times | Measure-Object -Average).Average
    Write-Host "Average Response Time: $([math]::Round($avgTime, 0))ms" -ForegroundColor White
}

Write-Host "`nTesting Complete!" -ForegroundColor Green
