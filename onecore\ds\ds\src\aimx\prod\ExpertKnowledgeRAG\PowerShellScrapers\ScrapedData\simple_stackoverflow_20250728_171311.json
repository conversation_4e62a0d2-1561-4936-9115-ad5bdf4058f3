﻿{
    "source":  "stackoverflow_simple",
    "schema_version":  "1.0",
    "total_entries":  3,
    "entries":  [
                    {
                        "source":  {
                                       "url":  "https://stackoverflow.com/questions/4037939/powershell-says-execution-of-scripts-is-disabled-on-this-system",
                                       "type":  "stackoverflow",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Set-ExecutionPolicy",
                                        "Get-ExecutionPolicy",
                                        "get-help",
                                        "set-ExecutionPolicy",
                                        "Get-ChildItem",
                                        "Get-Content"
                                    ],
                        "id":  "stackoverflow_4037939",
                        "metadata":  {
                                         "view_count":  5174867,
                                         "answer_count":  30,
                                         "score":  2987,
                                         "has_accepted_answer":  false
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Question: PowerShell says \"execution of scripts is disabled on this system.\"\n\n**Score:** 2987 | **Views:** 5174867 | **Answers:** 30\n\n## Problem Description\n\nI am trying to run a `cmd` file that calls a PowerShell script from `cmd.exe`, but I am getting this error: `Management_Install.ps1` cannot be loaded because the execution of scripts is disabled on this system. I ran this command: `Set-ExecutionPolicy -ExecutionPolicy Unrestricted ``` When I run `Get-ExecutionPolicy` from PowerShell, it returns `Unrestricted`. `Get-ExecutionPolicy ``` Output: `Unrestricted ``` cd \"C:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\" powershell .\\Management_Install.ps1 1 WARNING: Running x86 PowerShell... File `C:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\\Management_Install.ps1` cannot be loaded because the execution of scripts is disabled on this system. Please see \"`get-help about_signing`\" for more details. At line:1 char:25 `.\\Management_Install.ps1` \u003c\u003c\u003c\u003c 1 CategoryInfo : NotSpecified: (:) [], PSSecurityException FullyQualifiedErrorId : RuntimeException C:\\Projects\\Microsoft.Practices.ESB\\Source\\Samples\\Management Portal\\Install\\Scripts\u003e PAUSE Press any key to continue . . . The system is Windows Server 2008 R2. What am I doing wrong?\n\n## Solutions\n\n### Solution 1 âœ… (Accepted Answer) - Score: 3631\n\nIf you\u0027re using Windows Server 2008 R2 then there is an x64 and x86 version of PowerShell both of which have to have their execution policies set. Did you set the execution policy on both hosts? As an Administrator, you can set the execution policy by typing this into your PowerShell window: `Set-ExecutionPolicy RemoteSigned ``` For more information, see Using the Set-ExecutionPolicy Cmdlet. When you are done, you can set the policy back to its default value with: `Set-ExecutionPolicy Restricted ``` You may see an error: `Access to the registry key \u0027HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\ShellIds\\Microsoft.PowerShell\u0027 is denied. To change the execution policy for the default (LocalMachine) scope, start Windows PowerShell with the \"Run as administrator\" option. To change the execution policy for the current user, run \"Set-ExecutionPolicy -Scope CurrentUser\". ``` So you may need to run the command like this (as seen in comments): `Set-ExecutionPolicy RemoteSigned -Scope CurrentUser ```\n\n### Solution 2 - Score: 1186\n\nYou can bypass this policy for a single file by adding `-ExecutionPolicy Bypass` when running PowerShell `powershell -ExecutionPolicy Bypass -File script.ps1 ```\n\n### Solution 3 - Score: 412\n\nI had a similar issue and noted that the default `cmd` on Windows Server 2012, was running the x64 one. For Windows 11, Windows 10, Windows 7, Windows 8, Windows Server 2008 R2 or Windows Server 2012, run the following commands as Administrator: x86 (32 bit) Open `C:\\Windows\\SysWOW64\\cmd.exe` Run the command `powershell Set-ExecutionPolicy RemoteSigned` x64 (64 bit) Open `C:\\Windows\\system32\\cmd.exe` Run the command `powershell Set-ExecutionPolicy RemoteSigned` You can check mode using In CMD: `echo %PROCESSOR_ARCHITECTURE%` In Powershell: `[Environment]::Is64BitProcess` References: MSDN - Windows PowerShell execution policies Windows - 32bit vs 64bit directory explanation\n\n### Solution 4 - Score: 280\n\nMost of the existing answers explain the How, but very few explain the Why. And before you go around executing code from strangers on the Internet, especially code that disables security measures, you should understand exactly what you\u0027re doing. So here\u0027s a little more detail on this problem. From the TechNet About Execution Policies Page: Windows PowerShell execution policies let you determine the conditions under which Windows PowerShell loads configuration files and runs scripts. The benefits of which, as enumerated by PowerShell Basics - Execution Policy and Code Signing, are: Control of Execution - Control the level of trust for executing scripts. Command Highjack - Prevent injection of commands in my path. Identity - Is the script created and signed by a developer I trust and/or a signed with a certificate from a Certificate Authority I trust. Integrity - Scripts cannot be modified by malware or malicious user. To check your current execution policy, you can run `Get-ExecutionPolicy`. But you\u0027re probably here because you want to change it. To do so you\u0027ll run the `Set-ExecutionPolicy` cmdlet. You\u0027ll have two major decisions to make when updating the execution policy. Execution Policy Type: `Restricted`† - No Script either local, remote or downloaded can be executed on the system. `AllSigned` - All script that are ran require to be digitally signed. `RemoteSigned` - All remote scripts (UNC) or downloaded need to be signed. `Unrestricted` - No signature for any type of script is required. Scope of new Change `LocalMachine`† - The execution policy affects all users of the computer. `CurrentUser` - The execution policy affects only the current user. `Process` - The execution policy affects only the current Windows PowerShell process. † = Default For example: if you wanted to change the policy to RemoteSigned for just the CurrentUser, you\u0027d run the following command: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser ``` Note: In order to change the Execution policy, you must be running PowerShell As Administrator. If you are in regular mode and try to change the execution policy, you\u0027ll get the following error: Access to the registry key \u0027HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\ShellIds\\Microsoft.PowerShell\u0027 is denied. To change the execution policy for the default (LocalMachine) scope, start Windows PowerShell with the \"Run as administrator\" option. If you want to tighten up the internal restrictions on your own scripts that have not been downloaded from the Internet (or at least don\u0027t contain the UNC metadata), you can force the policy to only run signed scripts. To sign your own scripts, you can follow the instructions on Scott Hanselman\u0027s article on Signing PowerShell Scripts. Note: Most people are likely to get this error whenever they open PowerShell because the first thing PowerShell tries to do when it launches is execute your user profile script that sets up your environment however you like it. The file is typically located in: `%UserProfile%\\My Documents\\WindowsPowerShell\\Microsoft.PowerShellISE_profile.ps1 ``` You can find the exact location by running the PowerShell variable `$profile ``` If there\u0027s nothing that you care about in the profile, and don\u0027t want to fuss with your security settings, you can just delete it and PowerShell won\u0027t find anything that it cannot execute.\n\n### Solution 5 - Score: 80\n\nWe can get the status of current `ExecutionPolicy` by the command below: `Get-ExecutionPolicy ``` By default it is Restricted. To allow the execution of PowerShell scripts we need to set this ExecutionPolicy either as Unrestricted or Bypass. We can set the policy for Current User as `Bypass` by using any of the below PowerShell commands: `Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted -Force Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Bypass -Force ``` Unrestricted policy loads all configuration files and runs all scripts. If you run an unsigned script that was downloaded from the Internet, you are prompted for permission before it runs. Whereas in Bypass policy, nothing is blocked and there are no warnings or prompts during script execution. Bypass `ExecutionPolicy` is more relaxed than `Unrestricted`.\n\n### Solution 6 - Score: 68\n\nAlso running this command before the script also solves the issue: `Set-ExecutionPolicy Unrestricted ```\n\n### Solution 7 - Score: 59\n\nIf you are in an environment where you are not an administrator, you can set the Execution Policy just for you (`CurrentUser`), and it will not require administrator. You can set it to `RemoteSigned`: `Set-ExecutionPolicy -Scope \"CurrentUser\" -ExecutionPolicy \"RemoteSigned\" ``` or `Unrestricted`: `Set-ExecutionPolicy -Scope \"CurrentUser\" -ExecutionPolicy \"Unrestricted\" ``` You can read all about Getting and Setting Execution policy in the help entries: `Help Get-ExecutionPolicy -Full Help Set-ExecutionPolicy -Full ```\n\n### Solution 8 - Score: 58\n\nIn Windows 7: Go to Start Menu and search for \"Windows PowerShell ISE\". Right click the x86 version and choose \"Run as administrator\". In the top part, paste `Set-ExecutionPolicy RemoteSigned`; run the script. Choose \"Yes\". Repeat these steps for the 64-bit version of Powershell ISE too (the non x86 version).\n\n### Solution 9 - Score: 43\n\nRemoteSigned: all scripts you created yourself will be run, and all scripts downloaded from the Internet will need to be signed by a trusted publisher. OK, change the policy by simply typing: `Set-ExecutionPolicy RemoteSigned ```\n\n### Solution 10 - Score: 36\n\nFirst, you need to open the PowerShell window and run this command. `set-ExecutionPolicy RemoteSigned -Scope CurrentUser` Then it will ask you to confirm. Type Y and press Enter. When you run this command, you can see that your system has set all policies for the current user as remotely. It will take a few seconds to complete this process. The image will be shown like below: To check if the execution policy has set. Type: `Get-ExecutionPolicy` If it was set, the output would be like this:\n\n### Solution 11 - Score: 33\n\nI\u0027m using Windows 10 and was unable to run any command. The only command that gave me some clues was this: [x64] Open C:\\Windows\\SysWOW64\\cmd.exe [as administrator] Run the command\u003e powershell Set-ExecutionPolicy Unrestricted But this didn\u0027t work. It was limited. Probably new security policies for Windows10. I had this error: Set-ExecutionPolicy: Windows PowerShell updated your execution policy successfully, but the setting is overridden by a policy defined at a more specific scope. Due to the override, your shell will retain its current effective execution policy of... So I found another way (solution): Open Run Command/Console (Win + R) Type: gpedit.msc (Group Policy Editor) Browse to Local Computer Policy -\u003e Computer Configuration -\u003e Administrative Templates -\u003e Windows Components -\u003e Windows Powershell. Enable \"Turn on Script Execution\" Set the policy as needed. I set mine to \"Allow all scripts\". Now open PowerShell and enjoy ;)\n\n### Solution 12 - Score: 30\n\nOpen a Windows PowerShell command window and run the below query to change `ExecutionPolicy`: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser` If it asks for confirming changes, press Y and hit Enter.\n\n### Solution 13 - Score: 25\n\n```powershellSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted ``` This worked for me\n\n### Solution 14 - Score: 22\n\nYou should run this command: `Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy Unrestricted ```\n\n### Solution 15 - Score: 19\n\nWin + R and type copy paste command and press OK: `powershell Set-ExecutionPolicy -Scope \"CurrentUser\" -ExecutionPolicy \"RemoteSigned\" ``` And execute your script. Then revert changes like: `powershell Set-ExecutionPolicy -Scope \"CurrentUser\" -ExecutionPolicy \"AllSigned\" ```\n\n### Solution 16 - Score: 14\n\nOpen the command prompt in Windows. If the problem is only with PowerShell, use the following command: `powershell Set-ExecutionPolicy -Scope \"CurrentUser\" -ExecutionPolicy \"RemoteSigned\" ```\n\n### Solution 17 - Score: 14\n\nThere\u0027s great information in the existing answers, but let me attempt a systematic overview: Context PowerShell\u0027s effective execution policy applies: to PowerShell code stored in files, which means: regular script files (`*.ps1`) script module files (`*.psm1`) (modules implemented in PowerShell) modules that are bundled with formatting and type-extension files (`*.Format.ps1xml` and `*.Types.ps1xml`) - even if those files happen not to contain embedded PowerShell script blocks. It does not apply to: calls to (binary) cmdlets (e.g., `Get-ChildItem`), except for third-party binary cmdlets that come with modules that encompass formatting and type-extension files, as discussed above. commands submitted interactively or passed to the PowerShell CLI via the `-Command` parameter (unless these commands directly or indirectly call script files as defined above). on Windows only (that is, on Unix-like platforms (Linux, macOS) execution policies do not apply and no restrictions are placed on executing PowerShell code) In workstation editions of Windows, script-file execution is disabled by default (policy `Restricted`), requiring either a persistent modification of the policy to enable it, or a current-process-only modification such as via the `-ExecutionPolicy` parameter when calling the PowerShell CLI, `powershell.exe` (Windows PowerShell edition) / `pwsh.exe` (PowerShell (Core) 7 edition). In recent server editions of Windows, the default policy is `RemoteSigned`, meaning that while locally stored scripts (including on file shares) may be executed, downloaded-from-the-web scripts only execute if they\u0027re signed. Execution policies are maintained separately: for the two PowerShell editions: the legacy, Windows-only, ships-with-Windows Windows PowerShell edition (whose latest and last version is v5.1.x) the modern, cross-platform, install-on-demand PowerShell (Core) 7 edition. for the 32-bit and 64-bit versions of Windows PowerShell (both of which are preinstalled) Note: If you were to install 32-bit and 64-bit versions of PowerShell (Core) side by side (which would be unusual), only the `LocalMachine` scope would be bitness-specific. For a given edition / bitness combination of PowerShell, the execution policies can be set in multiple scopes, but there\u0027s only ever one effective policy, based on precedence rules - see below. Details In PowerShell on Windows, script-file execution is disabled by default in workstation editions of Windows (on Unix, execution policies do not apply); that is, the default execution policy in workstation editions of Windows is `Restricted`, whereas in server editions, it is `RemoteSigned`; see the conceptual about_Execution_Policies help topic for a description of all available policies. To set a (local) policy that permits script execution, use `Set-ExecutionPolicy` with a policy of `AllSigned`, `RemoteSigned`, `Unrestricted`, or `Bypass`, in descending order of security. There are three scopes that `Set-ExecutionPolicy` can target, using the `-Scope` parameter (see below); changing the `LocalMachine` scope\u0027s policy requires elevation (running as admin). A frequently used policy that provides a balance between security and convenience is `RemoteSigned`, which allows local scripts - including from network shares - to execute without containing a signature, while requiring scripts downloaded from the internet to be signed (assuming that the downloading mechanism marks such as scripts as internet-originated, which web browsers do by default). For instance, to set the current user\u0027s execution policy to `RemoteSigned`, run the following (`-Force` bypasses the confirmation prompt that is shown by default): `Set-ExecutionPolicy -Scope CurrentUser RemoteSigned -Force ``` To unset a previously set policy in a given scope, use the `Undefined` policy. The PowerShell CLI (`powershell.exe` for Windows PowerShell, `pwsh.exe` for PowerShell (Core) 7) accepts a process-specific `-ExecutionPolicy \u003cpolicy\u003e` argument too, which is often used for ad-hoc policy overrides (only for the process being created, the equivalent of `Set-ExecutionPolicy -Scope Process ...`); e.g.: `pwsh.exe -ExecutionPolicy RemoteSigned -File someScript.ps1 ``` Important: Execution policies can also be set via Group Policy Objects (GPOs), in which case they can not be changed or overridden with `Set-ExecutionPolicy` or via the CLI\u0027s `-ExecutionPolicy` parameter: see about_Group_Policy_Settings Execution policies can be set in various scopes, and which one is in effect is determined by their precedence (run `Get-ExecutionPolicy`` -List` to see all scopes and their respective policies), in descending order: `MachinePolicy` (via GPO; cannot be overridden locally)[1] `UserPolicy` (via GPO; cannot be overridden locally)[1] `Process` (current process only; typically set ad-hoc via the CLI) `CurrentUser` (as set by `Set-ExecutionPolicy`) `LocalMachine` (as set by `Set-ExecutionPolicy`, with admin rights) [1] This applies to domain-wide GPOs. Local GPOs can be modified locally, namely via `gpedit.msc` or directly via the registry, which in the case of the machine policy requires administrative privileges.\n\n### Solution 18 - Score: 11\n\nSetting the execution policy is environment-specific. If you are trying to execute a script from the running x86 ISE you have to use the x86 PowerShell to set the execution policy. Likewise, if you are running the 64-bit ISE you have to set the policy with the 64-bit PowerShell.\n\n### Solution 19 - Score: 11\n\nOpen Run Command/Console ( Win + R ) Type: gpedit. msc (Group Policy Editor) Browse to Local Computer Policy -\u003e Computer Configuration -\u003e Administrative Templates -\u003e Windows Components -\u003e Windows Powershell. Enable \"Turn on Script Execution\" Set the policy as needed. I set mine to \"Allow all scripts\". Now run the run command what ever you are using.. Trust this the app will runs.. Enjoy :)\n\n### Solution 20 - Score: 10\n\nYou can also bypass this by using the following command: `powershell Get-Content .\\test.ps1 | Invoke-Expression ``` You can also read this article by Scott Sutherland that explains 15 different ways to bypass the PowerShell `Set-ExecutionPolicy` if you don\u0027t have administrator privileges: 15 Ways to Bypass the PowerShell Execution Policy\n\n### Solution 21 - Score: 10\n\nyou may try this and select \"All\" Option ```powershellSet-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned ```\n\n### Solution 22 - Score: 10\n\nI have also faced a similar issue. Try this. As I\u0027m using Windows, I followed the steps as given below. Open a command prompt as an administrator and then go to this path: `C:\\Users\\<USER>\\AppData\\Roaming\\npm\\ ``` Look for the file ng.ps1 in this folder (directory) and then delete it (del ng.ps1). You can also clear npm cache after this though it should work without this step as well.\n\n### Solution 23 - Score: 9\n\nIf you have Git installed, just use Git Bash.\n\n### Solution 24 - Score: 8\n\nFor Windows 11... It is indeed very easy. Just open the settings application. Navigate to Privacy and Security: Click on For Developers and scroll to the bottom and find the PowerShell option under which check the checkbox stating \"Change the execution policy ... remote scripts\".\n\n### Solution 25 - Score: 8\n\nIn VS code just run this command: ```powershellSet-ExecutionPolicy -Scope CurrentUser Unrestricted ```\n\n### Solution 26 - Score: 7\n\nIn the PowerShell ISE editor I found running the following line first allowed scripts. `Set-ExecutionPolicy RemoteSigned -Scope Process ```\n\n### Solution 27 - Score: 7\n\n`Set-ExecutionPolicy RemoteSigned ``` Executing this command in administrator mode in PowerShell will solve the problem.\n\n### Solution 28 - Score: 7\n\nIn Window 10: If you are not administrator, you can use this: `powershell Set-ExecutionPolicy -Scope CurrentUser cmdlet Set-ExecutionPolicy at command pipeline position 1 Supply values for the following parameters: ExecutionPolicy: `RemoteSigned` ``` It solved my problem like a charm!\n\n### Solution 29 - Score: 6\n\nIn powershell To check the current execution policy, use the following command: `Get-ExecutionPolicy` To change the execution policy to Unrestricted, which allows running any script without digital signatures, use the following command: `Set-ExecutionPolicy Unrestricted` This solution worked for me, but be careful of the security risks involved.\n\n### Solution 30 - Score: 5\n\nOpen PowerShell as Administrator and run Set-ExecutionPolicy -Scope CurrentUser Provide RemoteSigned and press Enter Run Set-ExecutionPolicy -Scope CurrentUser Provide Unrestricted and press Enter\n\n",
                        "tags":  [
                                     "powershell",
                                     "windows-server-2008-r2",
                                     "powershell",
                                     "stackoverflow"
                                 ],
                        "last_updated":  "2025-07-28T17:13:12Z",
                        "title":  "PowerShell says \"execution of scripts is disabled on this system.\""
                    },
                    {
                        "source":  {
                                       "url":  "https://stackoverflow.com/questions/1825585/determine-installed-powershell-version",
                                       "type":  "stackoverflow",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "Get-Host",
                                        "get-host",
                                        "test-path",
                                        "Get-PSVersion",
                                        "Get-Variable",
                                        "Get-ItemPropertyValue",
                                        "New-Object",
                                        "Get-ChildItem",
                                        "Test-Path"
                                    ],
                        "id":  "stackoverflow_1825585",
                        "metadata":  {
                                         "view_count":  3218402,
                                         "answer_count":  24,
                                         "score":  2908,
                                         "has_accepted_answer":  false
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Question: Determine installed PowerShell version\n\n**Score:** 2908 | **Views:** 3218402 | **Answers:** 24\n\n## Problem Description\n\nHow can I determine what version of PowerShell is installed on a computer, and indeed if it is installed at all?\n\n## Solutions\n\n### Solution 1 âœ… (Accepted Answer) - Score: 3864\n\nUse `$PSVersionTable.PSVersion` to determine the engine version. If the variable does not exist, it is safe to assume the engine is version `1.0`. Note that `$Host.Version` and `(Get-Host).Version` are not reliable - they reflect the version of the host only, not the engine. PowerGUI, PowerShellPLUS, etc. are all hosting applications, and they will set the host\u0027s version to reflect their product version\u0026nbsp;\u0026mdash; which is entirely correct, but not what you\u0027re looking for. ```powershellPS C:\\\u003e $PSVersionTable.PSVersion Major Minor Build Revision ----- ----- ----- -------- 4 0 -1 -1 ```\n\n### Solution 2 - Score: 459\n\nI would use either Get-Host or $PSVersionTable. As Andy Schneider points out, `$PSVersionTable` doesn\u0027t work in version 1; it was introduced in version 2. ```powershellget-host Name : ConsoleHost Version : 2.0 InstanceId : d730016e-2875-4b57-9cd6-d32c8b71e18a UI : System.Management.Automation.Internal.Host.InternalHostUserInterface CurrentCulture : en-GB CurrentUICulture : en-US PrivateData : Microsoft.PowerShell.ConsoleHost+ConsoleColorProxy IsRunspacePushed : False Runspace : System.Management.Automation.Runspaces.LocalRunspace $PSVersionTable Name Value ---- ----- CLRVersion 2.0.50727.4200 BuildVersion 6.0.6002.18111 PSVersion 2.0 WSManStackVersion 2.0 PSCompatibleVersions {1.0, 2.0} SerializationVersion ******* PSRemotingProtocolVersion 2.1 ```\n\n### Solution 3 - Score: 106\n\nYou can look at the built in variable, `$psversiontable`. If it doesn\u0027t exist, you have V1. If it does exist, it will give you all the info you need. ```powershell1 \u003e $psversiontable Name Value ---- ----- CLRVersion 2.0.50727.4927 BuildVersion 6.1.7600.16385 PSVersion 2.0 WSManStackVersion 2.0 PSCompatibleVersions {1.0, 2.0} SerializationVersion ******* PSRemotingProtocolVersion 2.1 ```\n\n### Solution 4 - Score: 100\n\nTo determine if PowerShell is installed, you can check the registry for the existence of ```powershellHKEY_LOCAL_MACHINE\\Software\\Microsoft\\PowerShell\\1\\Install ``` and ```powershellHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\3 ``` and, if it exists, whether the value is 1 (for installed), as detailed in the blog post Check if PowerShell installed and version. To determine the version of PowerShell that is installed, you can check the registry keys ```powershellHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\PowerShellEngine\\PowerShellVersion ``` and ```powershellHKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\3\\PowerShellEngine\\PowerShellVersion ``` To determine the version of PowerShell that is installed from a .ps1 script, you can use the following one-liner, as detailed on PowerShell.com in Which PowerShell Version Am I Running. ```powershell$isV2 = test-path variable:\\psversiontable ``` The same site also gives a function to return the version: ```powershellfunction Get-PSVersion { if (test-path variable:psversiontable) {$psversiontable.psversion} else {[version]\"*******\"} } ```\n\n### Solution 5 - Score: 61\n\nYou can directly check the version with one line only by invoking PowerShell externally, such as from Command Prompt ```powershellpowershell -Command \"$PSVersionTable.PSVersion\" ``` According to @psaul you can actually have one command that is agnostic from where it came (CMD, PowerShell or Pwsh). Thank you for that. ```powershellpowershell -command \"(Get-Variable PSVersionTable -ValueOnly).PSVersion\" ``` I\u0027ve tested and it worked flawlessly on both CMD and PowerShell.\n\n### Solution 6 - Score: 49\n\nYou can verify that Windows PowerShell version installed by completing the following check: Click Start, click All Programs, click Accessories, click Windows PowerShell, and then click Windows PowerShell. In the Windows PowerShell console, type the following command at the command prompt and then press ENTER: ```powershellGet-Host | Select-Object Version ``` You will see output that looks like this: ```powershellVersion ------- 3.0 ``` http://www.myerrorsandmysolutions.com/how-to-verify-the-windows-powershell-version-installed/\n\n### Solution 7 - Score: 24\n\nMicrosoft\u0027s recommended forward compatible method for checking if PowerShell is installed and determining the installed version is to look at two specific registry keys. I\u0027ve reproduced the details here in case the link breaks. According to the linked page: Depending on any other registry key(s), or version of PowerShell.exe or the location of PowerShell.exe is not guaranteed to work in the long term. To check if any version of PowerShell is installed, check for the following value in the registry: Key Location: `HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1` Value Name: Install Value Type: REG_DWORD Value Data: 0x00000001 (1 To check whether version 1.0 or 2.0 of PowerShell is installed, check for the following value in the registry: Key Location: `HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\PowerShell\\1\\PowerShellEngine` Value Name: PowerShellVersion Value Type: REG_SZ Value Data: \u003c1.0 | 2.0\u003e\n\n### Solution 8 - Score: 17\n\nPowerShell 7 The accepted answer is only appropriate if one version of PowerShell is installed on a computer. With the advent of PowerShell 7, this scenario becomes increasingly unlikely. Microsoft\u0027s documentation states that additional registry keys are created when PowerShell 7 is installed: Beginning in PowerShell 7.1, the [installer] package creates registry keys that store the installation location and version of PowerShell. These values are located in `HKLM\\Software\\Microsoft\\PowerShellCore\\InstalledVersions\\\u003cGUID\u003e`. The value of `\u003cGUID\u003e` is unique for each build type (release or preview), major version, and architecture. Exploring the registry in the aforementioned location reveals the following registry value: `SemanticVersion`. This value contains the information we seek. On my computer it appears like the following: `Path Name Type Data ---- ---- ---- ---- HKLM:\\SOFTWARE\\Microsoft\\PowerShellCore\\InstalledVersions\\31ab5147-9a97-4452-8443-d9709f0516e1 SemanticVersion String 7.1.3 ``` As you can see, the version of PowerShell 7 installed on my computer is 7.1.3. If PowerShell 7 is not installed on the target computer, the key in its entirety should not exist. As mentioned in the Microsoft documentation, the registry path will be slightly different dependent on installed PowerShell version. Part of the key path changing could pose a challenge in some scenarios, but for those interested in a command line-based solution, PowerShell itself can handle this problem easily. The PowerShell cmdlet used to query the data in this registry value is the `Get-ItemPropertyValue` cmdlet. Observe its use and output as follows (note the asterisk wildcard character used in place of the part of the key path that is likely to change): `PS\u003e Get-ItemPropertyValue -Path \"HKLM:\\SOFTWARE\\Microsoft\\PowerShellCore\\InstalledVersions\\*\" -Name \"SemanticVersion\" 7.1.3 ``` Just a simple one-liner.\n\n### Solution 9 - Score: 11\n\nI found the easiest way to check if installed was to: run a command prompt (Start, Run, `cmd`, then OK) type `powershell` then hit return. You should then get the PowerShell `PS` prompt: ```powershellC:\\Users\\<USER>\u003epowershell Windows PowerShell Copyright (C) 2009 Microsoft Corporation. All rights reserved. PS C:\\Users\\<USER>\u003e ``` You can then check the version from the PowerShell prompt by typing `$PSVersionTable.PSVersion`: ```powershellPS C:\\Users\\<USER>\u003e $PSVersionTable.PSVersion Major Minor Build Revision ----- ----- ----- -------- 2 0 -1 -1 PS C:\\Users\\<USER>\u003e ``` Type `exit` if you want to go back to the command prompt (`exit` again if you want to also close the command prompt). To run scripts, see http://ss64.com/ps/syntax-run.html.\n\n### Solution 10 - Score: 9\n\n`$host.version` is just plain wrong/unreliable. This gives you the version of the hosting executable (powershell.exe, powergui.exe, powershell_ise.exe, powershellplus.exe etc) and not the version of the engine itself. The engine version is contained in `$psversiontable.psversion`. For PowerShell 1.0, this variable does not exist, so obviously if this variable is not available it is entirely safe to assume the engine is 1.0, obviously.\n\n### Solution 11 - Score: 9\n\nThe easiest way to forget this page and never return to it is to learn the `Get-Variable`: ```powershellGet-Variable | where {$_.Name -Like \u0027*version*\u0027} | %{$_[0].Value} ``` There is no need to remember every variable. Just `Get-Variable` is enough (and \"There should be something about version\").\n\n### Solution 12 - Score: 8\n\nUse: ```powershell$psVersion = $PSVersionTable.PSVersion If ($psVersion) { #PowerShell Version Mapping $psVersionMappings = @() $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.1.14393.0\u0027;FriendlyName=\u0027Windows PowerShell 5.1 Preview\u0027;ApplicableOS=\u0027Windows 10 Anniversary Update\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.1.14300.1000\u0027;FriendlyName=\u0027Windows PowerShell 5.1 Preview\u0027;ApplicableOS=\u0027Windows Server 2016 Technical Preview 5\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.494\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1607\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.122\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1603\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.117\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM 1602\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.63\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM\u0027;ApplicableOS=\u0027Windows 10 1511 + ********* 1602\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10586.51\u0027;FriendlyName=\u0027Windows PowerShell 5 RTM 1512\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10514.6\u0027;FriendlyName=\u0027Windows PowerShell 5 Production Preview 1508\u0027;ApplicableOS=\u0027Windows Server 2012 R2\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.10018.0\u0027;FriendlyName=\u0027Windows PowerShell 5 Preview 1502\u0027;ApplicableOS=\u0027Windows Server 2012 R2\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00275.0.9883.0\u0027;FriendlyName=\u0027Windows PowerShell 5 Preview November 2014\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows 8.1\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00274.0\u0027;FriendlyName=\u0027Windows PowerShell 4 RTM\u0027;ApplicableOS=\u0027Windows Server 2012 R2, Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8.1, and Windows 7 SP1\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00273.0\u0027;FriendlyName=\u0027Windows PowerShell 3 RTM\u0027;ApplicableOS=\u0027Windows Server 2012, Windows Server 2008 R2 SP1, Windows 8, and Windows 7 SP1\u0027} $psVersionMappings += New-Object PSObject -Property @{Name=\u00272.0\u0027;FriendlyName=\u0027Windows PowerShell 2 RTM\u0027;ApplicableOS=\u0027Windows Server 2008 R2 SP1 and Windows 7\u0027} foreach ($psVersionMapping in $psVersionMappings) { If ($psVersion -ge $psVersionMapping.Name) { @{CurrentVersion=$psVersion;FriendlyName=$psVersionMapping.FriendlyName;ApplicableOS=$psVersionMapping.ApplicableOS} Break } } } Else{ @{CurrentVersion=\u00271.0\u0027;FriendlyName=\u0027Windows PowerShell 1 RTM\u0027;ApplicableOS=\u0027Windows Server 2008, Windows Server 2003, Windows Vista, Windows XP\u0027} } ``` You can download the detailed script from How to determine installed PowerShell version.\n\n### Solution 13 - Score: 7\n\nTo check if PowerShell is installed use: ```powershellHKLM\\Software\\Microsoft\\PowerShell\\1 Install ( = 1 ) ``` To check if RC2 or RTM is installed use: ```powershellHKLM\\Software\\Microsoft\\PowerShell\\1 PID (=89393-100-0001260-00301) -- For RC2 HKLM\\Software\\Microsoft\\PowerShell\\1 PID (=89393-100-0001260-04309) -- For RTM ``` Source: this website.\n\n### Solution 14 - Score: 7\n\nSince the most helpful answer didn\u0027t address the if exists portion, I thought I\u0027d give one take on it via a quick-and-dirty solution. It relies on PowerShell being in the path environment variable which is likely what you want. (Hat tip to the top answer as I didn\u0027t know that.) Paste this into a text file and name it Test Powershell Version.cmd or similar. ```powershell@echo off echo Checking powershell version... del \"%temp%\\PSVers.txt\" 2\u003enul powershell -command \"[string]$PSVersionTable.PSVersion.Major +\u0027.\u0027+ [string]$PSVersionTable.PSVersion.Minor | Out-File ([string](cat env:\\temp) + \u0027\\PSVers.txt\u0027)\" 2\u003enul if errorlevel 1 ( echo Powershell is not installed. Please install it from download.Microsoft.com; thanks. ) else ( echo You have installed Powershell version: type \"%temp%\\PSVers.txt\" del \"%temp%\\PSVers.txt\" 2\u003enul ) timeout 15 ```\n\n### Solution 15 - Score: 6\n\nI needed to check the version of PowerShell and then run the appropriate code. Some of our servers run v5, and others v4. This means that some functions, like compress, may or may not be available. This is my solution: ```powershellif ($PSVersionTable.PSVersion.Major -eq 5) { #Execute code available in PowerShell 5, like Compress Write-Host \"You are running PowerShell version 5\" } else { #Use a different process Write-Host \"This is version $PSVersionTable.PSVersion.Major\" } ```\n\n### Solution 16 - Score: 5\n\nThe below cmdlet will return the PowerShell version. ```powershell$PSVersionTable.PSVersion.Major ```\n\n### Solution 17 - Score: 4\n\nThis is the top search result for \"Batch file get powershell version\", so I\u0027d like to provide a basic example of how to do conditional flow in a batch file depending on the powershell version Generic example `powershell \"exit $PSVersionTable.PSVersion.Major\" if %errorlevel% GEQ 5 ( echo Do some fancy stuff that only powershell v5 or higher supports ) else ( echo Functionality not support by current powershell version. ) ``` Real world example `powershell \"exit $PSVersionTable.PSVersion.Major\" if %errorlevel% GEQ 5 ( rem Unzip archive automatically powershell Expand-Archive Compressed.zip ) else ( rem Make the user unzip, because lazy echo Please unzip Compressed.zip prior to continuing... pause ) ```\n\n### Solution 18 - Score: 4\n\nI tried this on `version 7.1.0` and it worked: ```powershell$PSVersionTable | Select-Object PSVersion ``` Output ```powershellPSVersion --------- 7.1.0 ``` It doesn\u0027t work on `version 5.1` though, so rather go for this on versions below 7: ```powershell$PSVersionTable.PSVersion ``` Output ```powershellMajor Minor Build Revision ----- ----- ----- -------- 5 1 18362 1171 ``` EDIT As of PowerShell 7.2.5, you can now do: ```powershellpwsh -v ``` Or ```powershellpwsh --version ``` Output ```powershellPowerShell 7.2.5 ```\n\n### Solution 19 - Score: 3\n\nI used the following, which works on PS 7 and PS 5: ```powershell$psver = $PSVersionTable.PSVersion.Major ``` or: ```powershell$psver = (Get-Host).Version.Major ``` Then I can use logic depending on which version is running. ```powershell$PSVer = (Get-Host).Version.Major $sortSwitch = \"-Descending\" if ($PSVer -gt 5) {$sortSwitch = \"-r\"} $pathSep = [IO.Path]::DirectorySeparatorChar $pattern = \"???? something.zip\" $files = (Get-ChildItem $pattern -Name | sort $sortSwitch) foreach ($file in $files) { # Do stuff } ```\n\n### Solution 20 - Score: 1\n\nVery old question but still relevant, it\u0027s just that the nature of the problem is different now in 2023. Finding the version is easy, but first we have to launch the right executable. For that, we\u0027re basically back to looking in the registry. ```powershellreg query \"HKLM\\SOFTWARE\\Microsoft\\PowerShell\\1\" /v Install \u003enul 2\u003e\u00261 if %ERRORLEVEL% EQU 0 ( :: Default to PowerShell 5 if both are installed set PSEXE=powershell ) else ( set PSEXE=pwsh ) echo Using %PSEXE% %PSEXE% -ExecutionPolicy bypass -command \"\u0026 { ... ; exit $LASTEXITCODE }\" ``` There are other hints you can get by inspecting environment variables, but I think testing the registry for \u0027Windows\u0027 PowerShell is the safest.\n\n### Solution 21 - Score: 1\n\nPowerShell versions 2-7 contain a variable named `$PSVersionTable`. It seems horrible, but you have to test for version 1: ```powershell$version = if (Test-Path Variable:PSVersionTable) { [version]$PSVersionTable.PSVersion } else { [version]\"1.0\" } ```\n\n### Solution 22 - Score: 0\n\nYou can also call the \"host\" command from the PowerShell commandline. It should give you the value of the `$host` variable.\n\n### Solution 23 - Score: -2\n\nExtending the answer with a select operator: ```powershellGet-Host | select {$_.Version} ```\n\n### Solution 24 - Score: -2\n\nI have made a small batch script that can determine PowerShell version: ```powershell@echo off for /f \"tokens=2 delims=:\" %%a in (\u0027powershell -Command Get-Host ^| findstr /c:Version\u0027) do (echo %%a) ``` This simply extracts the version of PowerShell using `Get-Host` and searches the string `Version` When the line with the version is found, it uses the `for` command to extract the version. In this case we are saying that the delimiter is a colon and search next the first colon, resulting in my case `5.1.18362.752`.\n\n",
                        "tags":  [
                                     "powershell",
                                     "version",
                                     "powershell",
                                     "stackoverflow"
                                 ],
                        "last_updated":  "2025-07-28T17:13:12Z",
                        "title":  "Determine installed PowerShell version"
                    },
                    {
                        "source":  {
                                       "url":  "https://stackoverflow.com/questions/39632667/how-can-i-close-some-specific-port-on-linux",
                                       "type":  "stackoverflow",
                                       "credibility":  0.95
                                   },
                        "cmdlets":  [
                                        "stop-process",
                                        "Stop-Process",
                                        "Get-NetTCPConnection",
                                        "Get-Process"
                                    ],
                        "id":  "stackoverflow_39632667",
                        "metadata":  {
                                         "view_count":  3942250,
                                         "answer_count":  28,
                                         "score":  1383,
                                         "has_accepted_answer":  false
                                     },
                        "code_examples":  {

                                          },
                        "content":  "# Question: How can I close some specific port on Linux?\n\n**Score:** 1383 | **Views:** 3942250 | **Answers:** 28\n\n## Problem Description\n\nHow can I remove the port that some application/process has used it but didn\u0027t close the port? For example: when I run ssh on my ubuntu and tunnel the ssh connection on some port `\u003cPORT\u003e` on my system if my network interrupts and disconnect the port is still open but the ssh connection is lost, How to close the `\u003cPORT\u003e`?\n\n## Solutions\n\n### Solution 1 âœ… (Accepted Answer) - Score: 2862\n\nStep 1: Open up cmd.exe (note: you may need to run it as an administrator, but this isn\u0027t always necessary), then run the below command: `netstat -ano | findstr :\u003cPORT\u003e` (Replace `\u003cPORT\u003e` with the port number you want, but keep the colon) The area circled in red shows the PID (process identifier). Locate the PID of the process that\u0027s using the port you want. Step 2: Next, run the following command: `taskkill /PID \u003cPID\u003e /F` (No colon this time) Lastly, you can check whether the operation succeeded or not by re-running the command in \"Step 1\". If it was successful you shouldn\u0027t see any more search results for that port number.\n\n### Solution 2 - Score: 607\n\nI know that is really old question, but found pretty easy to remember, fast command to kill apps that are using port. Requirements: npm@5.2.0^ version ```powershellnpx kill-port 8080 ``` You can also read more about kill-port here: https://www.npmjs.com/package/kill-port\n\n### Solution 3 - Score: 229\n\nThere are two ways to kill the processes Option 01 - Simplest and easiest Requirement : npm@5.2.0^ version Open the Command prompt as Administrator and give the following command with the port (Here the port is 8080) ```powershellnpx kill-port 8080 ``` Option 02 - Most commonly used Step 01 Open Windows command prompt as Administrator Step 02 Find the PID of the port you want to kill with the below command: Here port is 8080 ```powershellnetstat -ano|findstr \"PID :8080\" ``` TCP 0.0.0.0:8080 0.0.0.0:0 LISTENING 18264 Step 03 Kill the PID you received above with the below command (In my case PID is 18264) ```powershelltaskkill /PID 18264 /f ```\n\n### Solution 4 - Score: 219\n\nWith Windows 10/11 default tools: ✔ Step one: Open Windows PowerShell as Administrator ✔ Step two: Find the ProcessID for the port you need to kill (e.g. 3000) ```powershellnetstat -aon | findstr 3000 ``` TCP 0.0.0.0:3000 LISTEN 1234 ✔ Step three: Kill the zombie process: ```powershelltaskkill /f /pid 1234 ``` where \"1234\" is your ProcessID (aka PID) *Extra tip if you use Windows Subsystem for Linux (Ubuntu WSL): ✔ Step one: ```powershellsudo lsof -t -i:3000 ``` ✔ Step two: ```powershellsudo kill -9 1234 ```\n\n### Solution 5 - Score: 191\n\nStep 1 (same is in accepted answer written by KavinduWije): ```powershellnetstat -ano | findstr :yourPortNumber ``` Change in Step 2 to: ```powershelltskill typeyourPIDhere ``` Note: `taskkill` is not working in some git bash terminal\n\n### Solution 6 - Score: 116\n\nIf you are using GitBash Step one: ```powershellnetstat -ano | findstr :8080 ``` Step two: ```powershelltaskkill /PID typeyourPIDhere /F ``` (`/F` forcefully terminates the process)\n\n### Solution 7 - Score: 91\n\nThe simplest solution — the only one I can ever remember: In Windows Powershell Say we want to stop a process on port 8080 Get the process: ```powershellnetstat -ano | findstr :8080 ``` Stop the process ```powershellstop-process 82932 ```\n\n### Solution 8 - Score: 63\n\nIf you already know the port number, it will probably suffice to send a software termination signal to the process (SIGTERM): ```powershellkill $(lsof -t -i :PORT_NUMBER) ```\n\n### Solution 9 - Score: 46\n\nFor use in command line: ```powershellfor /f \"tokens=5\" %a in (\u0027netstat -aon ^| find \":8080\" ^| find \"LISTENING\"\u0027) do taskkill /f /pid %a ``` For use in bat-file: ```powershellfor /f \"tokens=5\" %%a in (\u0027netstat -aon ^| find \":8080\" ^| find \"LISTENING\"\u0027) do taskkill /f /pid %%a ```\n\n### Solution 10 - Score: 43\n\nSimple CMD is working me. Easy to remember find the port number which you want kill and run the below cmd ```powershellnpx kill-port 8080 ``` After complete the Port get stopped and getting this message ```powershellnpx: installed 3 in 13.796s Process on port 8080 killed ```\n\n### Solution 11 - Score: 34\n\nIn Windows PowerShell version 1 or later to stop a process on port 3000 type: Stop-Process (,(netstat -ano | findstr :3000).split() | foreach {$[$.length-1]}) -Force As suggested by @morganpdx here`s a more PowerShell-ish, better version: Stop-Process -Id (Get-NetTCPConnection -LocalPort 3000).OwningProcess -Force\n\n### Solution 12 - Score: 23\n\nOpen command prompt and issue below command ```powershellnetstat -ano|findstr \"PID :8888\" ``` Output will show the process id occupying the port Issue below command to kill the PID ```powershelltaskkill /pid 8912 /f ``` You will receive the output as below ```powershellSUCCESS: The process with PID 8860 has been terminated. ```\n\n### Solution 13 - Score: 20\n\nIf you can use PowerShell on Windows you just need : ```powershellGet-Process -Id (Get-NetTCPConnection -LocalPort \"8080\").OwningProcess | Stop-Process ```\n\n### Solution 14 - Score: 17\n\nFor Windows users, you can use the CurrPorts tool to kill ports under usage easily:\n\n### Solution 15 - Score: 11\n\nI was running zookeeper on Windows and wasn\u0027t able to stop ZooKeeper running at 2181 port using zookeeper-stop.sh, so tried this double slash \"//\" method to taskkill. It worked ```powershell 1. netstat -ano | findstr :2181 TCP 0.0.0.0:2181 0.0.0.0:0 LISTENING 8876 TCP [::]:2181 [::]:0 LISTENING 8876 2.taskkill //PID 8876 //F SUCCESS: The process with PID 8876 has been terminated. ```\n\n### Solution 16 - Score: 11\n\nLet\u0027s Automate! If you fall into this issue much often like me, make an .bat file and run it to end process. create a bat file \"killport.bat\" ```powershellset /P port=\"Enter port : \" echo showing process running with port %port% netstat -ano|findstr \"PID :%port%\" set /P pid=\"Enter PID to kill : \" taskkill /pid %pid% /f set /P exit=\"Press any key to exit...\" ``` Run this file by double clicking and Enter port number (then it will list process with PID) Enter PID to kill Done (Optional) Set to path environment so that you can access this file from anywhere. Most probably you will know how to add an new path to env. But here\u0027s how if you don\u0027t Step 1 of 4 Search ENV on start menu Step 2 of 4 Select Environment Variables Step 3 of 4 Select \u0027path\u0027 and click Edit button Step 4 of 4 Click \u0027New\u0027 add the path where .bat file is stored. Since I saved it on \u0027../Documents/bats\u0027 folder I am adding this path. Your path will depend on where you save this file. Open CMD and test. Remember you filename will the word to run this file. Since I saved the .bat file as \u0027killport.bat\u0027 =\u003e \u0027killport\u0027 is the word to run it. if do enjoy! else share how you done it here\n\n### Solution 17 - Score: 11\n\nRun cmd as administrator. Then type this code in there. ```powershellnetstat -ano | findstr :\u003c8080\u003e ``` Then you can see the PID run on your port. Then copy that PID number. ( PID is a unique number that helps identify a hardware product or a registered software product.) And type below code line and press enter. ```powershelltaskkill /PID \u003cEnter the copied PID Number\u003e /F ```\n\n### Solution 18 - Score: 9\n\nIf you\u0027re using Windows Terminal then the killing process might be little less tedious. I\u0027ve been using windows terminal and `kill PID` works fine for me to kill processes on the port as the new Windows Terminal supports certain bash commands. For example: `kill 13300` So, the complete process will look like this- Open Windows Terminal Type the following command to show processes running on the port you\u0027re looking to kill processes. `netstat -ano | findstr :PORT` Type following to kill the process. `kill PID` For Example: ```powershellPS C:\\Users\\<USER>\u003e netstat -ano | findstr :4445 TCP 0.0.0.0:4445 0.0.0.0:0 LISTENING 7368 TCP [::]:4445 [::]:0 LISTENING 7368 PS C:\\Users\\<USER>\u003e kill 7368 PS C:\\Users\\<USER>\u003e netstat -ano | findstr :4445 PS C:\\Users\\<USER>\u003e ``` See when I typed the first command to list processes on the port it returned empty. That means all processes are killed now. Update: `kill` is an alias for Stop-Process. Thanks, @FSCKur for letting us know.\n\n### Solution 19 - Score: 8\n\nIf you use powershell 7+ this worked for me. Just add this function in your $PROFILE file. ```powershellfunction killport([parameter(mandatory)] [string] $uport){ if($upid = (Get-NetTCPConnection -LocalPort $uport -ErrorAction Ignore).OwningProcess){kill $upid} } ``` then simply use `killport 8080` or if you prefer just the command you can try this: ```powershellkill $(Get-NetTCPConnection -LocalPort 8761 -ErrorAction Ignore).OwningProcess ```\n\n### Solution 20 - Score: 6\n\nYou can do by run a bat file: ```powershell@ECHO OFF FOR /F \"tokens=5\" %%T IN (\u0027netstat -a -n -o ^| findstr \"9797\" \u0027) DO ( SET /A ProcessId=%%T) \u0026GOTO SkipLine :SkipLine echo ProcessId to kill = %ProcessId% taskkill /f /pid %ProcessId% PAUSE ```\n\n### Solution 21 - Score: 4\n\nIn case you want to do it using Python: check Is it possible in python to kill process that is listening on specific port, for example 8080? The answer from Smunk works nicely. I repeat his code here: ```powershellfrom psutil import process_iter from signal import SIGTERM # or SIGKILL for proc in process_iter(): for conns in proc.connections(kind=\u0027inet\u0027): if conns.laddr.port == 8080: proc.send_signal(SIGTERM) # or SIGKILL continue ```\n\n### Solution 22 - Score: 3\n\nthe first step ```powershellnetstat -vanp tcp | grep 8888 ``` example ```powershelltcp4 0 0 127.0.0.1.8888 *.* LISTEN 131072 131072 76061 0 tcp46 0 0 *.8888 *.* LISTEN 131072 131072 50523 0 ``` the second step: find your PIDs and kill them in my case ```powershellsudo kill -9 76061 50523 ```\n\n### Solution 23 - Score: 3\n\n```powershellnetstat -ano | findstr :PORT kill PI ```\n\n### Solution 24 - Score: 2\n\nOne line solution using GitBash: ```powershell tskill `netstat -ano | grep LISTENING | findstr :8080 | sed -r \u0027s/(\\s+[^\\s]+){4}(.*)/\\1/\u0027` ``` Replace 8080 with the port your server is listening to. If you need to use it often, try adding to your `~/.bashrc` the function: ```powershellfunction killport() { tskill `netstat -ano | findstr LISTENING | findstr :$1 | sed -r \u0027s/^(\\s+[^\\s]+){4}(\\d*)$/\\1/\u0027` } ``` and simply run ```powershellkillport 8080 ```\n\n### Solution 25 - Score: 1\n\nI wrote a tiny node js script for this. Just run it like this: `node killPort.js 8080` or whatever port you need to kill. Save the following to a `killPort.js` file: ```powershellconst { exec } = require(\u0027child_process\u0027); const fs = require(`fs`); const port = process.argv.length \u003e 2 ? process.argv[2] : ``; if (!port || isNaN(port)) console.log(`port is required as an argument and has to be a number`); else { exec(`netstat -ano | findstr :${port}`, (err, stdout, stderr) =\u003e { if (!stdout) console.log(`nobody listens on port ${port}`); else { const res = stdout.split(`\\n`).map(s =\u003e s.trim()); const pid = res.map(s =\u003e s.split(` `).pop()).filter(s =\u003e s).pop(); console.log(`Listener of ${port} is found, its pid is ${pid}, killing it...`); exec(`taskkill /PID ${pid} /F`, (err, stdout, stderr) =\u003e { if (!stdout) console.log(`we tried to kill it, but not sure about the result, please run me again`); else console.log(stdout); }) } }); } ```\n\n### Solution 26 - Score: 1\n\nI am using GitBash and I error like below when ran taskkill //PID XXXX ERROR: The process with PID 7420 could not be terminated. Reason: This process can only be terminated forcefully (with /F option). So used //F like below and worked fine taskkill //F //PID XXXX\n\n### Solution 27 - Score: 0\n\nHere is a script to do it in WSL2 ```powershellPIDS=$(cmd.exe /c netstat -ano | cmd.exe /c findstr :$1 | awk \u0027{print $5}\u0027) for pid in $PIDS do cmd.exe /c taskkill /PID $pid /F done ```\n\n### Solution 28 - Score: -3\n\nWe can avoid this by simple restarting IIS, using the below command: ```powershellIISRESET ```\n\n",
                        "tags":  [
                                     "linux",
                                     "windows",
                                     "powershell",
                                     "ubuntu",
                                     "powershell",
                                     "stackoverflow"
                                 ],
                        "last_updated":  "2025-07-28T17:13:12Z",
                        "title":  "How can I close some specific port on Linux?"
                    }
                ],
    "scraped_at":  "2025-07-28T17:13:12Z"
}
