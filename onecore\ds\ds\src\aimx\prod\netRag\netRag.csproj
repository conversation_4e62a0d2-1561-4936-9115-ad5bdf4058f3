<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <SelfContained>false</SelfContained>
    <TargetOS>windows</TargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.60.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Onnx" Version="1.60.0-alpha" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.EventLog" Version="8.0.0" />
    <PackageReference Include="System.Numerics.Tensors" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Documents\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Models\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <!-- Copy model files from root directory -->
    <None Update="..\model.onnx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>model.onnx</Link>
    </None>
    <None Update="..\vocab.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>vocab.txt</Link>
    </None>
  </ItemGroup>

  <!-- Ensure all dependencies are copied to output directory -->
  <PropertyGroup>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>

</Project>
