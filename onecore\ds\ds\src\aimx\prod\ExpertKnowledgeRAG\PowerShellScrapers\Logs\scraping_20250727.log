[2025-07-27 22:49:13] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 22:49:13] [Error] [Orchestrator] Scraper Microsoft Docs failed: The script 'MicrosoftDocsScraper.ps1' cannot be run because the following modules that are specified by the "#requires" statements of the script are missing: The module 'ScrapingFramework' cannot be found with ModuleVersion '1.0'..
[2025-07-27 22:49:13] [Error] [Orchestrator] Scraper Stack Overflow failed: The script 'StackOverflowScraper.ps1' cannot be run because the following modules that are specified by the "#requires" statements of the script are missing: The module 'ScrapingFramework' cannot be found with ModuleVersion '1.0'..
[2025-07-27 22:49:13] [Error] [Orchestrator] Scraper GitHub Repositories failed: The script 'GitHubScraper.ps1' cannot be run because the following modules that are specified by the "#requires" statements of the script are missing: The module 'ScrapingFramework' cannot be found with ModuleVersion '1.0'..
[2025-07-27 22:49:13] [Information] [Orchestrator] Consolidating 0 total patterns
[2025-07-27 22:49:13] [Information] [Orchestrator] Quality filtering: 0 -> 0 patterns
[2025-07-27 22:49:13] [Error] [Orchestrator] Orchestration failed: Cannot bind argument to parameter 'Path' because it is null.
[2025-07-27 22:56:51] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 22:58:42] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:00:09] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:00:53] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:01:54] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:02:22] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:07:11] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:07:11] [Debug] [ScrapingFramework] Attempting to fetch: https://docs.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/2)
[2025-07-27 23:07:11] [Debug] [ScrapingFramework] Successfully fetched: https://docs.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:08:46] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:08:47] [Debug] [ScrapingFramework] Attempting to fetch: https://docs.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/2)
[2025-07-27 23:08:47] [Debug] [ScrapingFramework] Successfully fetched: https://docs.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:10:42] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:11:18] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:11:18] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/1)
[2025-07-27 23:11:18] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:13:24] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:13:24] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/3)
[2025-07-27 23:13:25] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:15:28] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:15:28] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/3)
[2025-07-27 23:15:28] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:16:21] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:18:32] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:19:15] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:19:17] [Debug] [ScrapingFramework] Attempting to fetch: https://httpbin.org/get (Attempt 1/1)
[2025-07-27 23:19:17] [Debug] [ScrapingFramework] Successfully fetched: https://httpbin.org/get
[2025-07-27 23:19:30] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:20:17] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:20:18] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/1)
[2025-07-27 23:20:18] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:20:18] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/1)
[2025-07-27 23:20:18] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:20:18] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/1)
[2025-07-27 23:20:19] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:20:57] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:20:57] [Information] [MicrosoftDocsScraper] Fetching Active Directory cmdlet list from Microsoft Docs
[2025-07-27 23:20:57] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/3)
[2025-07-27 23:20:57] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:20:58] [Information] [MicrosoftDocsScraper] Found 151 Active Directory cmdlets
[2025-07-27 23:20:58] [Information] [MicrosoftDocsScraper] Filtering to 4 specific cmdlets
[2025-07-27 23:20:58] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADUser
[2025-07-27 23:20:58] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser (Attempt 1/3)
[2025-07-27 23:20:58] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser
[2025-07-27 23:21:00] [Information] [ScrapingFramework] Saved 1 patterns to: .\ScrapedData\scraped_microsoft_docs_20250727_232100.json
[2025-07-27 23:21:00] [Information] [MicrosoftDocsScraper] Successfully scraped 1 patterns from Microsoft Docs
[2025-07-27 23:21:00] [Information] [MicrosoftDocsScraper] Results saved to: .\ScrapedData\scraped_microsoft_docs_20250727_232100.json
[2025-07-27 23:22:06] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:22:06] [Information] [MicrosoftDocsScraper] Fetching Active Directory cmdlet list from Microsoft Docs
[2025-07-27 23:22:07] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/3)
[2025-07-27 23:22:07] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:22:07] [Information] [MicrosoftDocsScraper] Found 151 Active Directory cmdlets
[2025-07-27 23:22:07] [Information] [MicrosoftDocsScraper] Filtering to 0 specific cmdlets
[2025-07-27 23:22:07] [Warning] [MicrosoftDocsScraper] No patterns extracted from Microsoft Docs
[2025-07-27 23:22:18] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:23:00] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:23:37] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:23:37] [Information] [MicrosoftDocsScraper] Fetching Active Directory cmdlet list from Microsoft Docs
[2025-07-27 23:23:37] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/3)
[2025-07-27 23:23:37] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:23:37] [Information] [MicrosoftDocsScraper] Found 151 Active Directory cmdlets
[2025-07-27 23:23:37] [Information] [MicrosoftDocsScraper] Scraping documentation for: Add-ADCentralAccessPolicyMember
[2025-07-27 23:23:37] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adcentralaccesspolicymember (Attempt 1/3)
[2025-07-27 23:23:38] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adcentralaccesspolicymember
[2025-07-27 23:23:39] [Information] [MicrosoftDocsScraper] Scraping documentation for: Add-ADComputerServiceAccount
[2025-07-27 23:23:39] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adcomputerserviceaccount (Attempt 1/3)
[2025-07-27 23:23:40] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adcomputerserviceaccount
[2025-07-27 23:23:41] [Information] [MicrosoftDocsScraper] Scraping documentation for: Add-ADDomainControllerPasswordReplicationPolicy
[2025-07-27 23:23:41] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-addomaincontrollerpasswordreplicationpolicy (Attempt 1/3)
[2025-07-27 23:23:41] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-addomaincontrollerpasswordreplicationpolicy
[2025-07-27 23:23:42] [Information] [MicrosoftDocsScraper] Scraping documentation for: Add-ADFineGrainedPasswordPolicySubject
[2025-07-27 23:23:42] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adfinegrainedpasswordpolicysubject (Attempt 1/3)
[2025-07-27 23:23:43] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adfinegrainedpasswordpolicysubject
[2025-07-27 23:23:44] [Information] [MicrosoftDocsScraper] Scraping documentation for: Add-ADGroupMember
[2025-07-27 23:23:44] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adgroupmember (Attempt 1/3)
[2025-07-27 23:23:44] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adgroupmember
[2025-07-27 23:23:46] [Information] [MicrosoftDocsScraper] Scraping documentation for: Add-ADPrincipalGroupMembership
[2025-07-27 23:23:46] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adprincipalgroupmembership (Attempt 1/3)
[2025-07-27 23:23:46] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adprincipalgroupmembership
[2025-07-27 23:23:47] [Information] [MicrosoftDocsScraper] Scraping documentation for: Add-ADResourcePropertyListMember
[2025-07-27 23:23:47] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adresourcepropertylistmember (Attempt 1/3)
[2025-07-27 23:23:48] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adresourcepropertylistmember
[2025-07-27 23:23:49] [Information] [MicrosoftDocsScraper] Scraping documentation for: Clear-ADAccountExpiration
[2025-07-27 23:23:49] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/clear-adaccountexpiration (Attempt 1/3)
[2025-07-27 23:23:49] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/clear-adaccountexpiration
[2025-07-27 23:23:50] [Information] [MicrosoftDocsScraper] Scraping documentation for: Clear-ADClaimTransformLink
[2025-07-27 23:23:50] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/clear-adclaimtransformlink (Attempt 1/3)
[2025-07-27 23:23:51] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/clear-adclaimtransformlink
[2025-07-27 23:23:52] [Information] [MicrosoftDocsScraper] Scraping documentation for: Complete-ADServiceAccountMigration
[2025-07-27 23:23:52] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/complete-adserviceaccountmigration (Attempt 1/3)
[2025-07-27 23:23:52] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/complete-adserviceaccountmigration
[2025-07-27 23:23:54] [Information] [MicrosoftDocsScraper] Scraping documentation for: Disable-ADAccount
[2025-07-27 23:23:54] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/disable-adaccount (Attempt 1/3)
[2025-07-27 23:23:54] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/disable-adaccount
[2025-07-27 23:23:55] [Information] [MicrosoftDocsScraper] Scraping documentation for: Disable-ADOptionalFeature
[2025-07-27 23:23:55] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/disable-adoptionalfeature (Attempt 1/3)
[2025-07-27 23:23:56] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/disable-adoptionalfeature
[2025-07-27 23:23:57] [Information] [MicrosoftDocsScraper] Scraping documentation for: Enable-ADAccount
[2025-07-27 23:23:57] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/enable-adaccount (Attempt 1/3)
[2025-07-27 23:23:57] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/enable-adaccount
[2025-07-27 23:23:58] [Information] [MicrosoftDocsScraper] Scraping documentation for: Enable-ADOptionalFeature
[2025-07-27 23:23:58] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/enable-adoptionalfeature (Attempt 1/3)
[2025-07-27 23:23:59] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/enable-adoptionalfeature
[2025-07-27 23:24:00] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADAccountAuthorizationGroup
[2025-07-27 23:24:00] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adaccountauthorizationgroup (Attempt 1/3)
[2025-07-27 23:24:01] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adaccountauthorizationgroup
[2025-07-27 23:24:02] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADAccountResultantPasswordReplicationPolicy
[2025-07-27 23:24:02] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adaccountresultantpasswordreplicationpolicy (Attempt 1/3)
[2025-07-27 23:24:02] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adaccountresultantpasswordreplicationpolicy
[2025-07-27 23:24:03] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADAuthenticationPolicy
[2025-07-27 23:24:03] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adauthenticationpolicy (Attempt 1/3)
[2025-07-27 23:24:04] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adauthenticationpolicy
[2025-07-27 23:24:05] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADAuthenticationPolicySilo
[2025-07-27 23:24:05] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adauthenticationpolicysilo (Attempt 1/3)
[2025-07-27 23:24:05] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adauthenticationpolicysilo
[2025-07-27 23:24:07] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADCentralAccessPolicy
[2025-07-27 23:24:07] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adcentralaccesspolicy (Attempt 1/3)
[2025-07-27 23:24:07] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adcentralaccesspolicy
[2025-07-27 23:24:08] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADCentralAccessRule
[2025-07-27 23:24:08] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adcentralaccessrule (Attempt 1/3)
[2025-07-27 23:24:09] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adcentralaccessrule
[2025-07-27 23:24:10] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADClaimTransformPolicy
[2025-07-27 23:24:10] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adclaimtransformpolicy (Attempt 1/3)
[2025-07-27 23:24:10] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adclaimtransformpolicy
[2025-07-27 23:24:12] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADClaimType
[2025-07-27 23:24:12] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adclaimtype (Attempt 1/3)
[2025-07-27 23:24:12] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adclaimtype
[2025-07-27 23:24:13] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADComputer
[2025-07-27 23:24:13] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adcomputer (Attempt 1/3)
[2025-07-27 23:24:14] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adcomputer
[2025-07-27 23:24:15] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADComputerServiceAccount
[2025-07-27 23:24:15] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adcomputerserviceaccount (Attempt 1/3)
[2025-07-27 23:24:16] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adcomputerserviceaccount
[2025-07-27 23:24:17] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADDCCloningExcludedApplicationList
[2025-07-27 23:24:17] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addccloningexcludedapplicationlist (Attempt 1/3)
[2025-07-27 23:24:17] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addccloningexcludedapplicationlist
[2025-07-27 23:24:18] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADDefaultDomainPasswordPolicy
[2025-07-27 23:24:18] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addefaultdomainpasswordpolicy (Attempt 1/3)
[2025-07-27 23:24:19] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addefaultdomainpasswordpolicy
[2025-07-27 23:24:20] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADDomain
[2025-07-27 23:24:20] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addomain (Attempt 1/3)
[2025-07-27 23:24:20] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addomain
[2025-07-27 23:24:21] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADDomainController
[2025-07-27 23:24:21] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addomaincontroller (Attempt 1/3)
[2025-07-27 23:24:22] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addomaincontroller
[2025-07-27 23:24:23] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADDomainControllerPasswordReplicationPolicy
[2025-07-27 23:24:23] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addomaincontrollerpasswordreplicationpolicy (Attempt 1/3)
[2025-07-27 23:24:24] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addomaincontrollerpasswordreplicationpolicy
[2025-07-27 23:24:25] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADDomainControllerPasswordReplicationPolicyUsage
[2025-07-27 23:24:25] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addomaincontrollerpasswordreplicationpolicyusage (Attempt 1/3)
[2025-07-27 23:24:25] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-addomaincontrollerpasswordreplicationpolicyusage
[2025-07-27 23:24:26] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADFineGrainedPasswordPolicy
[2025-07-27 23:24:26] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adfinegrainedpasswordpolicy (Attempt 1/3)
[2025-07-27 23:24:27] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adfinegrainedpasswordpolicy
[2025-07-27 23:24:29] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADFineGrainedPasswordPolicySubject
[2025-07-27 23:24:29] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adfinegrainedpasswordpolicysubject (Attempt 1/3)
[2025-07-27 23:24:29] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adfinegrainedpasswordpolicysubject
[2025-07-27 23:24:30] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADForest
[2025-07-27 23:24:30] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adforest (Attempt 1/3)
[2025-07-27 23:24:31] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adforest
[2025-07-27 23:24:32] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADGroup
[2025-07-27 23:24:32] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroup (Attempt 1/3)
[2025-07-27 23:24:32] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroup
[2025-07-27 23:24:33] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADGroupMember
[2025-07-27 23:24:33] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroupmember (Attempt 1/3)
[2025-07-27 23:24:34] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroupmember
[2025-07-27 23:24:35] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADObject
[2025-07-27 23:24:35] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adobject (Attempt 1/3)
[2025-07-27 23:24:36] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adobject
[2025-07-27 23:24:37] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADOptionalFeature
[2025-07-27 23:24:37] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adoptionalfeature (Attempt 1/3)
[2025-07-27 23:24:38] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adoptionalfeature
[2025-07-27 23:24:39] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADOrganizationalUnit
[2025-07-27 23:24:39] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adorganizationalunit (Attempt 1/3)
[2025-07-27 23:24:40] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adorganizationalunit
[2025-07-27 23:24:41] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADPrincipalGroupMembership
[2025-07-27 23:24:41] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adprincipalgroupmembership (Attempt 1/3)
[2025-07-27 23:24:41] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adprincipalgroupmembership
[2025-07-27 23:24:42] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADReplicationAttributeMetadata
[2025-07-27 23:24:42] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationattributemetadata (Attempt 1/3)
[2025-07-27 23:24:43] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationattributemetadata
[2025-07-27 23:24:44] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADReplicationConnection
[2025-07-27 23:24:44] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationconnection (Attempt 1/3)
[2025-07-27 23:24:45] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationconnection
[2025-07-27 23:24:46] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADReplicationFailure
[2025-07-27 23:24:46] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationfailure (Attempt 1/3)
[2025-07-27 23:24:46] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationfailure
[2025-07-27 23:24:47] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADReplicationPartnerMetadata
[2025-07-27 23:24:47] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationpartnermetadata (Attempt 1/3)
[2025-07-27 23:24:48] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationpartnermetadata
[2025-07-27 23:24:49] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADReplicationQueueOperation
[2025-07-27 23:24:49] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationqueueoperation (Attempt 1/3)
[2025-07-27 23:24:49] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationqueueoperation
[2025-07-27 23:24:50] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADReplicationSite
[2025-07-27 23:24:50] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationsite (Attempt 1/3)
[2025-07-27 23:24:51] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationsite
[2025-07-27 23:24:52] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADReplicationSiteLink
[2025-07-27 23:24:52] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationsitelink (Attempt 1/3)
[2025-07-27 23:24:53] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationsitelink
[2025-07-27 23:24:54] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADReplicationSiteLinkBridge
[2025-07-27 23:24:54] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationsitelinkbridge (Attempt 1/3)
[2025-07-27 23:24:54] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationsitelinkbridge
[2025-07-27 23:24:55] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADReplicationSubnet
[2025-07-27 23:24:55] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationsubnet (Attempt 1/3)
[2025-07-27 23:24:56] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationsubnet
[2025-07-27 23:24:57] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADReplicationUpToDatenessVectorTable
[2025-07-27 23:24:57] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationuptodatenessvectortable (Attempt 1/3)
[2025-07-27 23:24:57] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adreplicationuptodatenessvectortable
[2025-07-27 23:24:59] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADResourceProperty
[2025-07-27 23:24:59] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adresourceproperty (Attempt 1/3)
[2025-07-27 23:24:59] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adresourceproperty
[2025-07-27 23:25:00] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADResourcePropertyList
[2025-07-27 23:25:00] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adresourcepropertylist (Attempt 1/3)
[2025-07-27 23:25:01] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adresourcepropertylist
[2025-07-27 23:25:02] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADResourcePropertyValueType
[2025-07-27 23:25:02] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adresourcepropertyvaluetype (Attempt 1/3)
[2025-07-27 23:25:02] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adresourcepropertyvaluetype
[2025-07-27 23:25:04] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADRootDSE
[2025-07-27 23:25:04] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adrootdse (Attempt 1/3)
[2025-07-27 23:25:04] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adrootdse
[2025-07-27 23:25:05] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADServiceAccount
[2025-07-27 23:25:05] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adserviceaccount (Attempt 1/3)
[2025-07-27 23:25:06] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adserviceaccount
[2025-07-27 23:25:07] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADTrust
[2025-07-27 23:25:07] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust (Attempt 1/3)
[2025-07-27 23:25:07] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust
[2025-07-27 23:25:09] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADUser
[2025-07-27 23:25:09] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser (Attempt 1/3)
[2025-07-27 23:25:09] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser
[2025-07-27 23:25:10] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADUserResultantPasswordPolicy
[2025-07-27 23:25:10] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduserresultantpasswordpolicy (Attempt 1/3)
[2025-07-27 23:25:11] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduserresultantpasswordpolicy
[2025-07-27 23:25:12] [Information] [MicrosoftDocsScraper] Scraping documentation for: Grant-ADAuthenticationPolicySiloAccess
[2025-07-27 23:25:12] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/grant-adauthenticationpolicysiloaccess (Attempt 1/3)
[2025-07-27 23:25:12] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/grant-adauthenticationpolicysiloaccess
[2025-07-27 23:25:13] [Information] [MicrosoftDocsScraper] Scraping documentation for: Install-ADServiceAccount
[2025-07-27 23:25:13] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/install-adserviceaccount (Attempt 1/3)
[2025-07-27 23:25:14] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/install-adserviceaccount
[2025-07-27 23:25:15] [Information] [MicrosoftDocsScraper] Scraping documentation for: Move-ADDirectoryServer
[2025-07-27 23:25:15] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/move-addirectoryserver (Attempt 1/3)
[2025-07-27 23:25:15] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/move-addirectoryserver
[2025-07-27 23:25:17] [Information] [MicrosoftDocsScraper] Scraping documentation for: Move-ADDirectoryServerOperationMasterRole
[2025-07-27 23:25:17] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/move-addirectoryserveroperationmasterrole (Attempt 1/3)
[2025-07-27 23:25:17] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/move-addirectoryserveroperationmasterrole
[2025-07-27 23:25:18] [Information] [MicrosoftDocsScraper] Scraping documentation for: Move-ADObject
[2025-07-27 23:25:18] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/move-adobject (Attempt 1/3)
[2025-07-27 23:25:19] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/move-adobject
[2025-07-27 23:25:20] [Information] [MicrosoftDocsScraper] Scraping documentation for: New-ADAuthenticationPolicy
[2025-07-27 23:25:20] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adauthenticationpolicy (Attempt 1/3)
[2025-07-27 23:25:20] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adauthenticationpolicy
[2025-07-27 23:25:22] [Information] [MicrosoftDocsScraper] Scraping documentation for: New-ADAuthenticationPolicySilo
[2025-07-27 23:25:22] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adauthenticationpolicysilo (Attempt 1/3)
[2025-07-27 23:25:22] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adauthenticationpolicysilo
[2025-07-27 23:25:23] [Information] [MicrosoftDocsScraper] Scraping documentation for: New-ADCentralAccessPolicy
[2025-07-27 23:25:23] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adcentralaccesspolicy (Attempt 1/3)
[2025-07-27 23:25:24] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adcentralaccesspolicy
[2025-07-27 23:25:25] [Information] [MicrosoftDocsScraper] Scraping documentation for: New-ADCentralAccessRule
[2025-07-27 23:25:25] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adcentralaccessrule (Attempt 1/3)
[2025-07-27 23:25:26] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adcentralaccessrule
[2025-07-27 23:25:27] [Information] [MicrosoftDocsScraper] Scraping documentation for: New-ADClaimTransformPolicy
[2025-07-27 23:25:27] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adclaimtransformpolicy (Attempt 1/3)
[2025-07-27 23:25:27] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adclaimtransformpolicy
[2025-07-27 23:25:29] [Information] [MicrosoftDocsScraper] Scraping documentation for: New-ADClaimType
[2025-07-27 23:25:29] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adclaimtype (Attempt 1/3)
[2025-07-27 23:25:29] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adclaimtype
[2025-07-27 23:25:30] [Information] [MicrosoftDocsScraper] Scraping documentation for: New-ADComputer
[2025-07-27 23:25:30] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adcomputer (Attempt 1/3)
[2025-07-27 23:25:31] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/new-adcomputer
[2025-07-27 23:25:46] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:25:46] [Information] [MicrosoftDocsScraper] Fetching Active Directory cmdlet list from Microsoft Docs
[2025-07-27 23:25:46] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/3)
[2025-07-27 23:25:46] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:25:47] [Information] [MicrosoftDocsScraper] Found 151 Active Directory cmdlets
[2025-07-27 23:25:47] [Information] [MicrosoftDocsScraper] Filtering to 0 specific cmdlets
[2025-07-27 23:25:47] [Warning] [MicrosoftDocsScraper] No patterns extracted from Microsoft Docs
[2025-07-27 23:27:08] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:27:08] [Information] [MicrosoftDocsScraper] Fetching Active Directory cmdlet list from Microsoft Docs
[2025-07-27 23:27:08] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/3)
[2025-07-27 23:27:09] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:27:09] [Information] [MicrosoftDocsScraper] Found 151 Active Directory cmdlets
[2025-07-27 23:27:09] [Information] [MicrosoftDocsScraper] Specific cmdlets requested: "Get-ADGroup","Get-ADObject"
[2025-07-27 23:27:09] [Information] [MicrosoftDocsScraper] Total cmdlets before filtering: 151
[2025-07-27 23:27:09] [Information] [MicrosoftDocsScraper] First few cmdlets: Add-ADCentralAccessPolicyMember, Add-ADComputerServiceAccount, Add-ADDomainControllerPasswordReplicationPolicy, Add-ADFineGrainedPasswordPolicySubject, Add-ADGroupMember
[2025-07-27 23:27:09] [Information] [MicrosoftDocsScraper] Filtering to 0 specific cmdlets
[2025-07-27 23:27:09] [Warning] [MicrosoftDocsScraper] No matching cmdlets found!
[2025-07-27 23:27:09] [Warning] [MicrosoftDocsScraper] No patterns extracted from Microsoft Docs
[2025-07-27 23:27:29] [Information] [ScrapingFramework] Scraping framework initialized successfully
[2025-07-27 23:27:29] [Information] [MicrosoftDocsScraper] Fetching Active Directory cmdlet list from Microsoft Docs
[2025-07-27 23:27:29] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/ (Attempt 1/3)
[2025-07-27 23:27:29] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/
[2025-07-27 23:27:29] [Information] [MicrosoftDocsScraper] Found 151 Active Directory cmdlets
[2025-07-27 23:27:29] [Information] [MicrosoftDocsScraper] Specific cmdlets requested: Get-ADGroup, Get-ADObject
[2025-07-27 23:27:29] [Information] [MicrosoftDocsScraper] Total cmdlets before filtering: 151
[2025-07-27 23:27:30] [Information] [MicrosoftDocsScraper] First few cmdlets: Add-ADCentralAccessPolicyMember, Add-ADComputerServiceAccount, Add-ADDomainControllerPasswordReplicationPolicy, Add-ADFineGrainedPasswordPolicySubject, Add-ADGroupMember
[2025-07-27 23:27:30] [Information] [MicrosoftDocsScraper] Filtering to 2 specific cmdlets
[2025-07-27 23:27:30] [Information] [MicrosoftDocsScraper] Found cmdlets: Get-ADGroup, Get-ADObject
[2025-07-27 23:27:30] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADGroup
[2025-07-27 23:27:30] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroup (Attempt 1/3)
[2025-07-27 23:27:30] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroup
[2025-07-27 23:27:31] [Information] [MicrosoftDocsScraper] Scraping documentation for: Get-ADObject
[2025-07-27 23:27:31] [Debug] [ScrapingFramework] Attempting to fetch: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adobject (Attempt 1/3)
[2025-07-27 23:27:32] [Debug] [ScrapingFramework] Successfully fetched: https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adobject
[2025-07-27 23:27:33] [Information] [ScrapingFramework] Saved 2 patterns to: .\ScrapedData\scraped_microsoft_docs_20250727_232733.json
[2025-07-27 23:27:33] [Information] [MicrosoftDocsScraper] Successfully scraped 2 patterns from Microsoft Docs
[2025-07-27 23:27:33] [Information] [MicrosoftDocsScraper] Results saved to: .\ScrapedData\scraped_microsoft_docs_20250727_232733.json
