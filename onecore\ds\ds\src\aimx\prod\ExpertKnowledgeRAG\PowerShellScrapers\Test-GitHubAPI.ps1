# Test GitHub API connectivity
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing GitHub API Connectivity" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
    
    # Test GitHub API
    Write-Host "`nTesting GitHub API..." -ForegroundColor Yellow
    
    $baseUrl = "https://api.github.com/search/repositories"
    $query = "PowerShell ActiveDirectory language:PowerShell"
    $apiUrl = "${baseUrl}?q=${query}&sort=stars&order=desc&per_page=5"
    
    Write-Host "API URL: $apiUrl" -ForegroundColor Gray
    
    # GitHub API requires User-Agent header
    $headers = @{
        'User-Agent' = 'PowerShell-Scraper/1.0'
        'Accept' = 'application/vnd.github.v3+json'
    }
    
    $response = Invoke-WebRequestWithRetry -Uri $apiUrl -Headers $headers -MaxRetries 2
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ GitHub API accessible" -ForegroundColor Green
        Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Gray
        Write-Host "  Content length: $($response.Content.Length)" -ForegroundColor Gray
        
        # Parse JSON response
        $data = $response.Content | ConvertFrom-Json
        
        if ($data.items) {
            Write-Host "✅ Found $($data.items.Count) repositories" -ForegroundColor Green
            Write-Host "  Total count: $($data.total_count)" -ForegroundColor Gray
            
            # Show first few repositories
            Write-Host "`nFirst few repositories:" -ForegroundColor Yellow
            $count = 0
            foreach ($repo in $data.items) {
                if ($count -ge 3) { break }
                Write-Host "  Repo $($count + 1): $($repo.full_name)" -ForegroundColor Cyan
                Write-Host "    Stars: $($repo.stargazers_count), Language: $($repo.language)" -ForegroundColor Gray
                Write-Host "    Description: $($repo.description)" -ForegroundColor DarkGray
                Write-Host "    URL: $($repo.html_url)" -ForegroundColor DarkGray
                $count++
            }
        } else {
            Write-Host "❌ No repositories found in response" -ForegroundColor Red
        }
        
        # Test rate limiting info
        if ($response.Headers['X-RateLimit-Remaining']) {
            Write-Host "`nRate limiting info:" -ForegroundColor Yellow
            Write-Host "  Remaining: $($response.Headers['X-RateLimit-Remaining'])" -ForegroundColor Gray
            Write-Host "  Limit: $($response.Headers['X-RateLimit-Limit'])" -ForegroundColor Gray
        }
        
    } else {
        Write-Host "❌ GitHub API returned status: $($response.StatusCode)" -ForegroundColor Red
    }
    
    Write-Host "`n✅ GitHub API test completed!" -ForegroundColor Green
}
catch {
    Write-Host "`n❌ GitHub API test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    
    # Check if it's a rate limiting issue
    if ($_.Exception.Message -like "*rate limit*" -or $_.Exception.Message -like "*403*") {
        Write-Host "`nNote: This might be a rate limiting issue. GitHub API has limits for unauthenticated requests." -ForegroundColor Yellow
        Write-Host "Consider using a GitHub token for higher rate limits." -ForegroundColor Yellow
    }
}
