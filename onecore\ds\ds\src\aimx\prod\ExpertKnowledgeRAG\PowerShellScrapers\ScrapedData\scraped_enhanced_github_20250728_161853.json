﻿{
    "ScrapedAt":  "2025-07-28T16:18:53Z",
    "BatchId":  "20250728_161853",
    "Patterns":  [
                     {
                         "AllCodeBlocks":  [

                                           ],
                         "RelevanceScore":  0.8996065061142513,
                         "CreatedAt":  "2025-07-28T16:18:49Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_user_management_2095ab99",
                         "Operation":  "read",
                         "Stars":  1143,
                         "UpdatedAt":  "2025-07-28T16:18:49Z",
                         "HasReadme":  true,
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub Repository: ********/adPEAS",
                         "LastUpdated":  "2025-07-24T19:31:50Z",
                         "CodeTemplate":  "Import-Module .\\adPEAS.ps1\n\n. .\\adPEAS.ps1\n\ngc -raw .\\adPEAS.ps1 | iex\n\nIEX (New-Object Net.WebClient).DownloadString(\u0027https://raw.githubusercontent.com/********/adPEAS/main/adPEAS.ps1\u0027)\n\nInvoke-adPEAS\n\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Outputfile \u0027C:\\temp\\adPEAS_outputfile\u0027 -NoColor\n\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027\n\n$SecPassword = ConvertTo-SecureString \u0027Passw0rd1!\u0027 -AsPlainText -Force\n$Cred = New-Object System.Management.Automation.PSCredential(\u0027contoso\\johndoe\u0027, $SecPassword)\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Cred $Cred\n\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027 -Username \u0027contoso\\johndoe\u0027 -Password \u0027Passw0rd1!\u0027 -Force\n\nInvoke-adPEAS -Module Domain\n\nInvoke-adPEAS -Module Rights\n\nInvoke-adPEAS -Module GPO\n\nInvoke-adPEAS -Module ADCS\n\nInvoke-adPEAS -Module Creds\n\nInvoke-adPEAS -Module Delegation\n\nInvoke-adPEAS -Module Accounts\n\nInvoke-adPEAS -Module Computer\n\nInvoke-adPEAS -Module Bloodhound\n\nInvoke-adPEAS -Module Bloodhound -BloodHoundLegacy\n\nInvoke-adPEAS -Module Bloodhound -Scope All",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  135,
                         "PowerShellFiles":  2,
                         "Domain":  "user_management",
                         "RepoSize":  79150,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "user_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: ********/adPEAS\n\n**Stars:** 1143 | **Language:** PowerShell | **Forks:** 135\n\n**Description:** Powershell tool to automate Active Directory enumeration.\n\n**URL:** https://github.com/********/adPEAS\n\n**Clone URL:** https://github.com/********/adPEAS.git\n\n## README\n\n# adPEAS\n\n![](https://github.com/********/adPEAS/raw/main/images/adPEAS_large.jpg)\n\nadPEAS is a Powershell tool to automate Active Directory enumeration.\nIn fact, adPEAS is like a wrapper for different other cool projects...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:18:49Z",
                                             "Url":  "https://github.com/********/adPEAS",
                                             "ScrapedAt":  "2025-07-28T16:18:49Z",
                                             "Id":  "c85ff980-9650-4af7-9a9c-ccb024b96433",
                                             "Author":  "********",
                                             "Title":  "GitHub Repository: ********/adPEAS"
                                         }
                                     ],
                         "Topics":  [

                                    ],
                         "Content":  "# Repository: ********/adPEAS\n\n**Stars:** 1143 | **Language:** PowerShell | **Forks:** 135\n\n**Description:** Powershell tool to automate Active Directory enumeration.\n\n**URL:** https://github.com/********/adPEAS\n\n**Clone URL:** https://github.com/********/adPEAS.git\n\n## README\n\n# adPEAS\n\n![](https://github.com/********/adPEAS/raw/main/images/adPEAS_large.jpg)\n\nadPEAS is a Powershell tool to automate Active Directory enumeration.\nIn fact, adPEAS is like a wrapper for different other cool projects like PowerView, PoshADCS, BloodHound stuff and some own written lines of code.\n\nAs said, adPEAS is a wrapper for other tools. They are almost all written in pure Powershell but some of them are included as C# code in a compressed binary blob.\n\nadPEAS-Light is a version without Bloodhound and it is more likely that it will not be blocked by an AV solution.\n\n# How It Works\n\nadPEAS can be run simply by starting the script via _invoke-adPEAS_ if it is started on a domain joined computer.\nIf the system you are running adPEAS from is not domain joined or you want to enumerate another domain, use a certain domain controller to connect to, use different credentials or just to enumerate for credential exposure only, you can do it by using defined parameters.\n\n## adPEAS Modules\n\nadPEAS consists of the following enumeration modules:\n* Domain - Searching for basic Active Directory information, like Domain Controllers, Sites und Subnets, Trusts and Password/Kerberos policy\n* Rights - Searching for specific Active Directory rights and permissions, like LAPS, DCSync and adding computer to domain\n* GPO -  Searching for basic GPO related things, like local group membership on domain computer\n* ADCS - Searching for basic Active Directory Certificate Services information, like CA Name, CA Server and vulnerable Templates\n* Creds - Searching for different kind of credential exposure, like ASREPRoast, Kerberoasting, GroupPolicies, Netlogon scripts, LAPS, gMSA, certain legacy attributes, e.g. UnixPassword, etc.\n* Delegation - Searching for delegation issues, like \u0027Constrained Delegation\u0027, \u0027Unconstrained Delegation\u0027 and \u0027Resource Based Constrained Delegation\u0027, for computer and user accounts\n* Accounts - Searching for non-disabled high privileged user accounts in predefined groups and account issues like e.g. old passwords\n* Computer - Enumerating Domain Controllers, Certificate Services, Exchange Server and outdated OS versions like Windows Server 2008R2, etc.\n* BloodHound - Enumerating Active Directory with the SharpHound collector for BloodHound Community Edition or BloodHound-Legacy\n\n# Some How To Use Examples\n## Simple usage with generic program parameters\nFirst you have to load adPEAS in Powershell...\n```\nImport-Module .\\adPEAS.ps1\n```\nor\n```\n. .\\adPEAS.ps1\n```\nor\n```\ngc -raw .\\adPEAS.ps1 | iex\n```\nor\n```\nIEX (New-Object Net.WebClient).DownloadString(\u0027https://raw.githubusercontent.com/********/adPEAS/main/adPEAS.ps1\u0027)\n```\n\nStart adPEAS with all enumeration modules and enumerate the domain the logged-on user and computer is connected to.\n```\nInvoke-adPEAS\n```\n\nStart adPEAS with all enumeration modules and enumerate the domain \u0027contoso.com\u0027. In addition it writes all output without any ANSI color codes to a file.\n```\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Outputfile \u0027C:\\temp\\adPEAS_outputfile\u0027 -NoColor\n```\n\nStart adPEAS with all enumeration modules, enumerate the domain \u0027contoso.com\u0027 and use the domain controller \u0027dc1.contoso.com\u0027 for almost all enumeration requests.\n```\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027\n```\n\nStart adPEAS with all enumeration modules, enumerate the domain \u0027contoso.com\u0027 and use the passed PSCredential object during enumeration.\n```\n$SecPassword = ConvertTo-SecureString \u0027Passw0rd1!\u0027 -AsPlainText -Force\n$Cred = New-Object System.Management.Automation.PSCredential(\u0027contoso\\johndoe\u0027, $SecPassword)\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Cred $Cred\n```\n\nStart adPEAS with all enumeration modules, enumerate the domain \u0027contoso.com\u0027 by using the domain controller \u0027dc1.contoso.com\u0027 and use the username \u0027contoso\\johndoe\u0027 with password \u0027Passw0rd1!\u0027 during enumeration. If, due to DNS issues Active Directory detection fails, the switch -Force forces adPEAS to ignore those issues and try to get still as much information as possible.\n```\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027 -Username \u0027contoso\\johndoe\u0027 -Password \u0027Passw0rd1!\u0027 -Force\n```\n\n## Usage with a single enumeration module\n### All modules below can be combined with all generic program parameters explained above.\n\nEnumerates basic Active Directory information, like Domain Controllers, Password Policy, Sites and Subnets and Trusts.\n```\nInvoke-adPEAS -Module Domain\n```\n\nEnumerates specific Active Directory rights and permissions, like LAPS, DCSync and adding computer to domain.\n```\nInvoke-adPEAS -Module Rights\n```\n\nEnumerates basic GPO information, like set local group membership on domain computer.\n```\nInvoke-adPEAS -Module GPO\n```\n\nEnumerates basic Active Directory Certificate Services information, like CA Name, CA Server and common Template vulnerabilities.\n```\nInvoke-adPEAS -Module ADCS\n```\n\nEnumerates credential exposure issues, like ASREPRoast, Kerberoasting, Linux/Unix password attributes, gMSA, LAPS (if your account has the rights to read it), Group Policies, Netlogon scripts.\n```\nInvoke-adPEAS -Module Creds\n```\n\nEnumerates delegation issues, like \u0027Unconstrained Delegation\u0027, \u0027Constrained Delegation\u0027, \u0027Resource Based Constrained Delegation\u0027 for user and computer objects.\n```\nInvoke-adPEAS -Module Delegation\n```\n\nEnumerates users in high privileged groups which are NOT disabled, like Administrators, Domain Admins, Enterprise Admins, Group Policy Creators, DNS Admins, Account Operators, Server Operators, Printer Operators, Backup Operators, Hyper-V Admins, Remote Management Users und CERT Publishers.\n```\nInvoke-adPEAS -Module Accounts\n```\n\nEnumerates installed Domain Controllers, Active Directory Certificate Services, Exchange Server and outdated OS versions like Windows Server 2008R2.\n```\nInvoke-adPEAS -Module Computer\n```\n\nStarts Bloodhound enumeration for BloodHound Community Edition (\u003e= version 5.0) with the scope DCOnly. Output ZIP files are stored in the same directory adPEAS is started from.\n```\nInvoke-adPEAS -Module Bloodhound\n```\n\nStarts Bloodhound enumeration for BloodHound-Legacy (up to version 4.3.1) with the scope DCOnly. Output ZIP files are stored in the same directory adPEAS is started from.\n```\nInvoke-adPEAS -Module Bloodhound -BloodHoundLegacy\n```\n\nStarts Bloodhound enumeration with the scope All. With this option the SharpHound collector will contact each member computer of the domain. Output ZIP files are stored in the same directory adPEAS is started from.\n```\nInvoke-adPEAS -Module Bloodhound -Scope All\n```\n\n## Special thanks go to...\n* Will Schroeder @harmjoy, for his great PowerView\n* Charlie Clark @exploitph, for his ongoing work on PowerView\n* Christoph Falta @cfalta, for his inspiring work on PoshADCS\n* Dirk-jan @_dirkjan, for his great AD and Windows research\n* SpecterOps, for their fantastic BloodHound\n* and all the people who inspired me on my journey...\n\n## PowerShell Files (2 files)\n\n"
                     },
                     {
                         "AllCodeBlocks":  [

                                           ],
                         "RelevanceScore":  0.74103762920994987,
                         "CreatedAt":  "2025-07-28T16:18:51Z",
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_user_management_aeaf0240",
                         "Operation":  "read",
                         "Stars":  505,
                         "UpdatedAt":  "2025-07-28T16:18:51Z",
                         "HasReadme":  true,
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub Repository: ANSSI-FR/ADTimeline",
                         "LastUpdated":  "2025-07-20T15:22:59Z",
                         "CodeTemplate":  "PS\u003e import-csv timeline_%DOMAINFQDN%.csv -delimiter \";\"\nPS\u003e import-clixml ADobjects_%DOMAINFQDN%.xml\nPS\u003e import-clixml gcADobjects_%DOMAINFQDN%.xml\n\nPS\u003e./ADTimeline -customgroups \"VIP-group1,ESX-Admins,Tier1-admins\"\n\nPS\u003e$customgroups = get-content customgroups.txt\nPS\u003e./ADTimeline -customgroups $customgroups\n\n$groupscustom = (\"VIP-group1\",\"ESX-Admis\",\"Tier1-admins\")\n\nDOS\nPS\u003e .\\ADTimeline.ps1 -server \u003cGLOBAL CATALOG FQDN\u003e\n\nDOS\nC:\\Windows\\System32\u003e dsamain.exe -dbpath \u003cNTDS.DIT path\u003e -ldapport 3266 -allownonadminaccess\n\nDOS\nPS\u003e .\\ADTimeline.ps1 -server \"127.0.0.1:3266\"\n\nDOS\nPS\u003e.\\ADTimeline -nofwdSMTPaltRecipient\n\npowershell\nPS\u003e import-csv timeline_%DOMAINFQDN%.csv -delimiter \";\"\nPS\u003e import-clixml ADobjects_%DOMAINFQDN%.xml\nPS\u003e import-clixml gcADobjects_%DOMAINFQDN%.xml\n\npowershell\nPS\u003e./ADTimeline -customgroups \"VIP-group1,ESX-Admins,Tier1-admins\"\n\npowershell\nPS\u003e$customgroups = get-content customgroups.txt\nPS\u003e./ADTimeline -customgroups $customgroups\n\npowershell\n$groupscustom = (\"VIP-group1\",\"ESX-Admis\",\"Tier1-admins\")\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[ search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as TargetUserSid | fields TargetUserSid ] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullName , values(LogonType) as logonTypes, values(IpAddress) as IpAdresses, values(Computer) as Computers, dc(Computer) as CountComputers, dc(IpAddress) as CountIpAdresses by TargetUserSid\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4688\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SamAccountName as TargetUserName | fields TargetUserName] \nOR [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as SubjectUserSid | fields SubjectUserSid] \n|  stats values(CommandLine) by Computer, TargetUserName,SubjectUserName\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"*PowerShell*\"  \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | dedup SamAccountName | return 1000 $SamAccountName]\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4769\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt) earliest = 1 latest = now() | rename SID as ServiceSid | fields ServiceSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName  \n|  eval time=strftime(_time,\"%Y-%m-%d %H:%M:%S\") \n|  stats list(ServiceName) as Services, dc(ServiceName) as nbServices, list(time) as time by IpAddress, TicketEncryptionType \n| sort -nbServices\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4688\" \n[search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SamAccountName as TargetUserName | fields TargetUserName ] \nOR  [search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SID as SubjectUserSid | fields SubjectUserSid ] \n|  stats values(CommandLine) as cmdlines, values(TargetUserName) as TargetSubjectNames, values(SubjectUserName) as SubjectUserNames by Computer\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects   (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now() | rename SID as TargetUserSid  | fields TargetUserSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") earliest = 1 latest = now() |  eval eval_asrep_bit = floor(userAccountControl / pow(2, 22)) %2 | search eval_asrep_bit = 1 |  rename SID as TargetUserSid  | fields TargetUserSid]  \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\" earliest = 1 latest = now() | eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0 |   rename dNSHostName as Computer | fields Computer] \n|  search *   [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | eval canbedelagated = round(((userAccountControl / pow(2, 20)) %2), 0) | search canbedelagated = 0 | rename SID as TargetUserSid  | fields TargetUserSid ] \n|  strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName \n| table  _time, Computer, TargetFullName, IpAddress, LogonType, LogonProcessName\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" TargetUserName = \"*$\" NOT TargetUserSid=\"S-1-5-18\"\n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\"  earliest = 1 latest = now() |  eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0  |  rename dNSHostName as Computer | fields Computer]\n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(LogonType), values(IpAddress), values(LogonProcessName) count by Computer, TargetFullName",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  65,
                         "PowerShellFiles":  null,
                         "Domain":  "user_management",
                         "RepoSize":  1041,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "user_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: ANSSI-FR/ADTimeline\n\n**Stars:** 505 | **Language:** PowerShell | **Forks:** 65\n\n**Description:** Timeline of Active Directory changes with replication metadata\n\n**URL:** https://github.com/ANSSI-FR/ADTimeline\n\n**Clone URL:** https://github.com/ANSSI-FR/ADTimeline.git\n\n**Topics:** active-directory, dfir, forensics, powershell, splunk, timeline, windows\n\n## README\n\n![ADTimeline](./logo.png)\n---\n# Table of contents:\n1. [The ADTimeline PowerShell script](#thescript)\n    1. [Description...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:18:51Z",
                                             "Url":  "https://github.com/ANSSI-FR/ADTimeline",
                                             "ScrapedAt":  "2025-07-28T16:18:51Z",
                                             "Id":  "ad003a8d-650a-4e2b-a4d3-075b59f1dfab",
                                             "Author":  "ANSSI-FR",
                                             "Title":  "GitHub Repository: ANSSI-FR/ADTimeline"
                                         }
                                     ],
                         "Topics":  [
                                        "active-directory",
                                        "dfir",
                                        "forensics",
                                        "powershell",
                                        "splunk",
                                        "timeline",
                                        "windows"
                                    ],
                         "Content":  "# Repository: ANSSI-FR/ADTimeline\n\n**Stars:** 505 | **Language:** PowerShell | **Forks:** 65\n\n**Description:** Timeline of Active Directory changes with replication metadata\n\n**URL:** https://github.com/ANSSI-FR/ADTimeline\n\n**Clone URL:** https://github.com/ANSSI-FR/ADTimeline.git\n\n**Topics:** active-directory, dfir, forensics, powershell, splunk, timeline, windows\n\n## README\n\n![ADTimeline](./logo.png)\n---\n# Table of contents:\n1. [The ADTimeline PowerShell script](#thescript)\n    1. [Description](#description)\n    2. [Prerequisites](#prerequisites)\n    3. [Usage](#usage)\n    4. [Files generated](#files)\n    5. [Custom groups](#groups)\n2. [The ADTimeline App for Splunk](#theapp)\n    1. [Description](#descriptionsplk)\n    2. [Sourcetypes](#sourcetype)\n    3. [AD General information dashboards](#infradashboards)\n    4. [AD threat hunting dashboards](#threathuntdashboards)\n    5. [Enhance your traditional event logs threat hunting with ADTimeline](#threathuntevtx)\n\n# The ADTimeline PowerShell script:  \u003ca name=\"thescript\"\u003e\u003c/a\u003e\n\n## Description: \u003ca name=\"description\"\u003e\u003c/a\u003e\n\nThe ADTimeline script generates a timeline based on Active Directory replication metadata for objects considered of interest.  \nReplication metadata gives you the time at which each replicated attribute for a given object was last changed. As a result the timeline of modifications is partial. For each modification of a replicated attribute a version number is incremented.  \nADTimeline was first presented at the [CoRI\u0026IN 2019](https://www.cecyf.fr/coriin/coriin-2019/) (Conférence sur la réponse aux incidents et l’investigation numérique). Slides of the presentation, in french language,  are available [here](https://cyber.gouv.fr/publications/investigation-numerique-sur-lannuaire-active-directory-avec-les-metadonnees-de). It was also presented at the [Amsterdam 2019 FIRST Technical Colloquium](https://www.first.org/events/colloquia/amsterdam2019/program#pActive-Directory-forensics-with-replication-metadata-ADTimeline-tool), slides in english are available [here](https://cyber.gouv.fr/en/actualites/adtimeline-active-directory-forensics-replication-metadata-first-technical-colloquium).\n\nObjects considered of interest retrieved by the script include:\n\n- Schema and configuration partition root objects.\n- Domain root and objects located directly under the root.\n- Objects having an ACE on the domain root.\n- Domain roots located in the AD forest.\n- Domain trusts.\n- Deleted users (i.e. tombstoned).\n- Objects protected by the SDProp process (i.e. AdminCount equals 1).\n- The Guest account.\n- The AdminSDHolder object.\n- Objects having an ACE on the AdminSDHolder object.\n- Class Schema objects.\n- Existing and deleted Group Policy objects.\n- DPAPI secrets.\n- Domain controllers (Computer objects, ntdsdsa and server objects).\n- DNS zones.\n- WMI filters.\n- Accounts with suspicious SIDHistory (scope is forest wide).\n- Sites.\n- Organizational Units.\n- Objects with Kerberos delegation enabled.\n- Extended rights.\n- Schema attributes with particular SearchFlags (Do not audit or confidential).\n- Kerberoastable user accounts (SPN value).\n- AS-REP roastable accounts (UserAccountControl value).\n- Authentication policy silos.\n- CertificationAuthority and pKIEnrollmentService objects.\n- Cross Reference containers.\n- Exchange RBAC roles and accounts assigned to a role.\n- Exchange mail flow configuration objects.\n- Exchange mailbox databases objects.\n- Exchange Mailbox Replication Service objects\n- Deleted objects under the configuration partition.\n- Dynamic objects.\n- The directory service and RID manager objects.\n- The Pre Windows 2000 compatible access, Cert publishers, GPO creator owners and DNS Admins groups.\n- ADFS DKM containers.\n- Service connection point objects considered of interest.\n- Custom groups which have to be manually defined.\n- User objects with mail forwarder enabled (msExchGenericForwardingAddress and altRecipient attributes).\n\n## Prerequisites: \u003ca name=\"prerequisites\"\u003e\u003c/a\u003e\n\n- The account launching the script should be able to read objects in the tombstone (Deleted Objects Container) and some parts of the Exchange settings located in the configuration partition (View-Only Organization management). Delegation can be tricky to setup (especially for reading the tombstone). That is why we advise you to run the script with a domain admin account. If you launch the script as a standard user, it will process the timeline without the objects mentioned.\n- Computer should run Windows NT 6.1 or later with PowerShell 2.0 or later and have the Active Directory Powershell module installed (part of RSAT-AD-Tools).\n- If you enabled PowerShell Constrained Language Mode the script might fail (calling $error.clear()). Consider whitelisting the script via your device guard policy.\n- If you are using offline mode install the ADLDS role on a Windows Server edition in order to use dsamain.exe and mount the NTDS database.\n\n    The version of the Windows Server you install the role on should be the same as the version of the Windows Server which the ntds.dit came from. If you do not know that version and you have the SOFTWARE hive available, you can look at the CurrentVersion key.\n    \n    If you can not mount the ntds.dit file with dsamain.exe, this might be because the NTDS dump is corrupted. In that case, you can follow [advice from cert-cwatch](https://github.com/ANSSI-FR/ADTimeline/issues/17#issuecomment-1984049537).\n\n## Usage: \u003ca name=\"usage\"\u003e\u003c/a\u003e\n\nIn online mode no argument is mandatory and the closest global catalog is used for processing. If no global catalog is found run the script with the server argument :\n```DOS\nPS\u003e .\\ADTimeline.ps1 -server \u003cGLOBAL CATALOG FQDN\u003e\n```\nIn offline mode: Replay if necessary transaction logs of the NTDS database, mount it on your analysis machine (ADLDS + RSAT-AD-Tools installed) and use 3266 as LDAP port.\n```DOS\nC:\\Windows\\System32\u003e dsamain.exe -dbpath \u003cNTDS.DIT path\u003e -ldapport 3266 -allownonadminaccess\n```\nIf necessary use the allowupgrade switch.\n\nLaunch the script targetting localhost on port 3266:\n```DOS\nPS\u003e .\\ADTimeline.ps1 -server \"127.0.0.1:3266\"\n```\n\nIf you encounter performance issues when running against a large MSExchange organization with forwarders massively used, use the nofwdSMTP parameter:\n```DOS\nPS\u003e.\\ADTimeline -nofwdSMTPaltRecipient\n```\n## Files generated \u003ca name=\"files\"\u003e\u003c/a\u003e\n\nOutput files are generated in the current directory:\n\n- timeline_%DOMAINFQDN%.csv: The timeline generated with the AD replication metadata of objects retrieved.\n- logfile_%DOMAINFQDN%.log: Script log file. You will also find various information on the domain.\n- ADobjects_%DOMAINFQDN%.xml: Objects of interest retrieved via LDAP.\n- gcADobjects_%DOMAINFQDN%.xml: Objects of interest retrieved via the Global Catalog.\n\n\nTo import files for analysis with powershell. \n```powershell\nPS\u003e import-csv timeline_%DOMAINFQDN%.csv -delimiter \";\"\nPS\u003e import-clixml ADobjects_%DOMAINFQDN%.xml\nPS\u003e import-clixml gcADobjects_%DOMAINFQDN%.xml\n```\nThe analysis with the ADTimeline for Splunk is a better solution.\n\n## Custom groups \u003ca name=\"groups\"\u003e\u003c/a\u003e\n\nIf you want to include custom AD groups in the timeline (for example virtualization admin groups, network admins, VIP groups...) use the *Customgroups* parameter.\n\n*Customgroups* parameter can be a string with multiple group comma separated (no space):\n```powershell\nPS\u003e./ADTimeline -customgroups \"VIP-group1,ESX-Admins,Tier1-admins\"\n```\n*Customgroups* parameter can also be an array, in case you import the list from a file (one group per line):\n```powershell\nPS\u003e$customgroups = get-content customgroups.txt\nPS\u003e./ADTimeline -customgroups $customgroups\n```\nIf you do not want to use a parameter you can also uncomment and edit the following array at the  begining of the script:\n```powershell\n$groupscustom = (\"VIP-group1\",\"ESX-Admis\",\"Tier1-admins\")\n```\n\n# The ADTimeline App for Splunk: \u003ca name=\"theapp\"\u003e\u003c/a\u003e\n\n## Description: \u003ca name=\"descriptionsplk\"\u003e\u003c/a\u003e\n\nThe ADTimeline application for Splunk processes and analyses the Active Directory data collected by the ADTimeline PowerShell script. The app was presented at the 32nd annual FIRST Conference, a recording of the presentation is available [here](https://www.first.org/conference/2020/recordings).\n\nThe app\u0027s \"Getting started\" page will give you the instructions for the import process.\n\nOnce indexed the dashboards provided by the app will help the DFIR analyst to spot some Acitve Directory persistence mechanisms, misconfigurations, security audit logging bypass, mail exfiltration, brute force attacks ...\n\nThe app is also packaged and available on [Splunkbase](https://splunkbase.splunk.com/app/4897/). It has no prerequisite and will work with a [free Splunk](https://docs.splunk.com/Documentation/Splunk/latest/Admin/MoreaboutSplunkFree) license.\n\n![Splunkapp](./SA-ADTimeline.png)\n\n## Sourcetypes: \u003ca name=\"sourcetype\"\u003e\u003c/a\u003e\n\nAfter processing the ADTimeline script you should have two or three files to import in Splunk (%DOMAINFQDN% is the Active Directory fully qualified domain name):\n\n- timeline_%DOMAINFQDN%.csv: The timeline generated with the AD replication metadata of objects retrieved. The corresponding source type is *adtimeline*.\n- ADobjects_%DOMAINFQDN%.xml: Objects of interest retrieved via LDAP. The corresponding sourcetype is *adobjects*.\n- gcADobjects_%DOMAINFQDN%.xml: If any, objects of interest retrieved via the Global Catalog. The corresponding source type is *gcobjects*.\n\n### The adtimeline sourcetype:\n\n The *adtimeline* sourcetype is the data from the timeline_%DOMAINFQDN%.csv file, which is the Active Directory timeline built with replication metadata for objects considered of interest.\n\nThe timestamp value is the ftimeLastOriginatingChange value of the replication metadata, which is the time the attribute was last changed, time is UTC.\n\nThe extracted fields are:\n\n- Name: LDAP object name.\n- pszAttributeName: The attribute name.\n- dwVersion: Counter incremented every time the attribute is changed.\n- DN: LDAP object DistinguishedName.\n- WhenCreated: LDAP object creation time.\n- ObjectClass and ObjectCategory: LDAP object type (user, computer, group...)\n- SamAccountName and SID: Account Name and security identifier, only applies to users, computers and groups.\n- usnOriginatingChange: USN on the originating server at which the last change to this attribute was made.\n- pszLastOriginatingDsaDN: DC on which the last change was made to this attribute.\n- uuidLastOriginatingDsaInvocationID: ID corresponding to the pszLastOriginatingDsaDN.\n- usnLocalChange: USN on the destination server (the server your LDAP bind is made) at which the last change to this attribute was applied.\n- Member: Only applies to the group ObjectClass and when the attribute name is member. Contains the value of the group member DistinguishedName.\n- ftimeCreated: Only applies to group ObjectClass and when the attribute name is member. Contains the time the member was added in the group.\n- ftimeDeleted: Only applies to group ObjectClass and when the attribute name is member. Contains the time the member was removed from the group.\n\n### The adobjects sourcetype:\n\nThe *adobjects* sourcetype is the data from the ADobjects_%DOMAINFQDN%.xml file, which is an export of the Active Directory objects considered of interested and retrieved via the LDAP protocol.\n\nThe timestamp value is the createTimeStamp attribute value, time zone is specified in the attribute value.\n\nThe extracted fields are:\n\n- Name: LDAP object name.\n- DN: LDAP object DistinguishedName.\n- DisplayName: LDAP object displayname.\n- WhenCreated: LDAP object creation time.\n- ObjectClass and ObjectCategory: LDAP object type (user, computer, group...)\n- SamAccountName and SID: Account Name and security identifier, only applies to users, computers and groups.\n- Members and MemberOf: Members of a group ObjectClass can be users, computers or groups and its linked attribute MemberOf which applies to groups, users and computers.\n- Owner, AccessToString and SDDL: Are values computed from the nTSecurityDescriptor attribute\n- adminCount: Privileged accounts protected by the SDProp process.\n- userAccountControl: Attribute which contains a range of flags which define some important basic properties of a computer or user object.\n- lastLogonTimestamp: This attribute is not updated with all logon types or at every logon but is replicated and gives you an idea of wether a user or computer account has recently logged on to the domain.\n- dNSHostName: DNS hostname attribute of a computer account.\n- SPNs: List of Service Principal Names of a computer or user account.\n\n### The gcobjects sourcetype:\n\nThe *gcobjects* sourcetype is the data from the gcADobjects_%DOMAINFQDN%.xml file, which is an export of the Active Directory objects within the forest but outside the current domain and considered of interested, those objects are retrieved via the Global Catalog protocol.\n\nThe timestamp value is the WhenCreated attribute value, time zone is UTC.\n\nThe extracted fields are:\n\n- Name: LDAP object name.\n- DN: LDAP object DistinguishedName.\n- DisplayName: LDAP object displayname.\n- WhenCreated: LDAP object creation time.\n- ObjectClass and ObjectCategory: LDAP object type (user, computer, group...)\n- SamAccountName and SID: Account Name and security identifier, only applies to users, computers and groups.\n- userAccountControl: Attribute which contains a range of flags which define some important basic properties of a computer or user object.\n- lastLogonTimestamp: This attribute is not updated with all logon types or at every logon but is replicated and gives you an idea if a user or computer account has recently logged onto the domain.\n- dNSHostName: DNS hostname attribute of a computer account.\n- SPNs: List of Service Principal Names of a computer or user account.\n\n## AD General information dashboards: \u003ca name=\"infradashboards\"\u003e\u003c/a\u003e\n\n### The Active Directory Infrastructure dashboard:\n\nThis dashboard analyses Adtimeline data in order to create some panels giving you information on the Windows domain infrastructure.\n\nThe different panels are:\n\n- General information: Information about the Schema version and functional levels. Depending on the result some AD security features may or may not be available. The Domain Controllers are also listed in this panel\n- Microsoft infrastructure products: Tells you if some important Microsoft Infrastructure components such as Exchange on premises, Active Directory Federation Services or Active Directory Certificate Services are installed. Please consider monitoring events related to those services (MSExchange CmdletLogs, ADFS auditing...)\n- Domain Trusts: List domain trusts by type and direction. Run ADTimeline on all your trusted domains, but most importantly make sure they are audited, monitored and secured as rigorously as the domain you are analyzing.\n- ADDS security features: Tells you if some security features are enabled or not. First feature is the AD Recycle bin which gives the administrator the ability to easily recover deleted objects, it will also change the time after an object is removed from the AD database after deletion. Second feature tells you if the schema extension for the Local Admin Password Solution was performed, if yes sysadmins can enable password randomization for local administrators accounts in order to mitigate lateral movement. Another feature is authentication silos which can help to restrict privileged user account logons in order to mitigate privilege escalation by implementing a tiered administrative model. The last feature is the Protected Users group, with a DFL 2012R2 or more the members of this group receive some additional hardening\n- Service Connection Points: Inventory of serviceConnectionPoint (SCP) object class. SCP make it easy for a service to publish service-specific data in the directory Clients of the service use the data in an SCP to locate an instance of the service. Infrastructure assets such as RDS Gateway, SCCM, VMWare Vcenter, some Backup solutions publish an SCP in the directory.\n- Active Directory infrastructure timeline: Displays a timeline of the infrastructure changes listed above. This timeline tells you the story of the evolution of your infrastructure.\n\n### The sensitive accounts dashboard:\n\nThis dashboard provides an inventory of the privileged accounts in the domain and accounts prone to common attack scenarios due to their configuration.\n\n The different panels are:\n\n- Admin Accounts: This panel lists the accounts where the Admincount attribute value equals 1. Those accounts have their ACL protected by the SDProp process and it means the account has or had at some point high privileges in Active Directory. The first table lists them and provides some information about the accounts, the second table displays a timeline of modifications for some attributes of these accounts.\n- Accounts sensitive to Kerberoast attacks: Kerberoasting is an attack method that allows an attacker to crack the passwords of service accounts in Active Directory offline. The chart is a ratio of accounts prone to this attack and whether or not they are privileged accounts. The table lists them and provides some information about the accounts. Use least privilege principle for those accounts and consider using Group Managed Service Accounts.\n- Accounts sensitive to AS-REP Roast attacks: AS-REP Roast is an attack method that allows an attacker to crack the passwords of accounts in Active Directory offline. The chart is a ratio of accounts prone to this attack and whether or not they are privileged accounts. The table lists them and provides some information about the accounts. Use least privilege principle for those accounts.\n- Sensitive default accounts: Some general information about the default administrator, guest and krbtgt accounts. Administrator can be disabled or renamed as a measure against account lockout. Guest account must be disabled and krbtgt password should be changed on a regular schedule.\n- Accounts trusted for delegation: Kerberos Delegation is a feature that allows an application to reuse the end-user credentials to access resources hosted on a different server. An account trusted for unconstrained delegation is allowed to impersonate almost any user to any service within the network, whereas an account trusted for constrained delegation is allowed to impersonate almost any user for a given service within the network. The chart is a ratio of accounts trusted for constrained/unconstrained delegation. The tables list those accounts, the service name is given for accounts trusted for constrained delegation. A table listing objects with resource based constrained delegation configured is also displayed\n\n## AD threat hunting dashboards: \u003ca name=\"threathuntdashboards\"\u003e\u003c/a\u003e\n\n### The investigate timeframe dashboard:\n\nUse this dashboard to investigate a particular timeframe.\n\n The different panels are:\n\n- AD Timeline: A table displaying the timeline for the given timeframe.\n- Global stats: Global statistics on modifications occurring during the given timeframe, including modifications by ObjectClass, by pszAttributeName, by Originating DC, by time (i.e. day of the week or hour of the day) and finally stats on deletions by ObjectClass.\n- Items created and deleted within timeframe: A table displaying the creations and deletions of the same object within the given timeframe. A first chart gives you stats about object lifetimes in hours and a second one figures by ObjectClass.\n- Objects added or removed from groups or ACL modifications within timeframe: This table focuses on the Member and nTSecurityDescriptor attributes, which can help detect an elevation of privilege for a specific account or a backdoor setup by the attacker, the DistinguishedName value of the member and the time the member was added or removed from the group is given in that table. Which makes it more detailed than the above AD Timeline panel. A chart displaying nTSecurityDescriptor modifications by ObjectClass and another displaying the number of times an object was added or removed from a group are given\n- GPOs modifications within timeframe: A GPO can used by an attacker in various ways, for example to inject malicious code in logon/startup scripts, deploy malware at scale with an immediate scheduled task, setup a backdoor by modifying the nTSecurityDescriptor... For each attribute modification this table gives you the current client side extensions of the GPO and where the object is linked (OU, site or domain root).\n\n### The track suspicious activity dashboard\n\nThis dashboard analyses the Active Directory timeline and highlights some modifications which can be a sign of a suspicious activity, the modifications spoted can also be legitimate and need a triage analysis.\n\n The different panels are:\n\n- ACL modifications: This panel does not replace a thorough analysis of the Active Directory permissions with tools such as AD Control Paths. The panel contains a graph displaying a weekly timeline of ACL modifications per ObjectClass which occured one year back, some tables are focusing on the domain root and AdminSDHolder objects where permissions can be used as backdoor by an attacker. Finally, some statistics by ObjectClass and by least frequent owners are displayed.\n- Accounts: This panel show account modifications which can a be sign of suspicious activity such as users added and removed from groups, some charts provide stats by number of times the account was added or removed, membership time in days, Organizational unit where accounts are located (an account from the \"non_privileged_users\" OU added and removed from a privileged group can be a sign of suspicious activity). There are some graphs, the first graph shows a timeline of accounts lockouts in order to highlight brute force attacks, the second graph shows SID history editions which can be suspicious outside a domain migration period, the next graph analyses all the different attributes modified on an account during a password change (supplementalCredentials, lmPwdHistory, unicodePwd...) and checks they are modified at the same time. A table displays resource based constrained delegation setup on privileged account or domain controller computer objects, which can be a backdoor setup by an attacker. Finally a chart displays domain controller computer objects password change frequency, an attacker could modify the DC registry to disable computer password change and use this password as a backdoor.\n- GPOs: A table of GPOs modifications having an audit client side extension is displayed, an attacker could change the audit settings on the domain to perform malicious actions with stealth. Finally modifications which could result in a GPO processing malfunctioning are displayed, this includes gPCFunctionalityVersion, gPCFileSysPath or versionNumber attribute modification.\n- DCshadow detection: The DCshadow is an attack which allows an attacker to push modifications in Active Directory and bypass traditional alerting by installing a fake DC. It was first presented by Vincent Le Toux and Benjamin Delpy at the BlueHat IL 2018 conference. The first graph will try to detect the installation of the fake DC by analyzing server and nTDSDSA ObjectClass. The two following tables will try to detect replication metadata tampering by analyzing usnOriginatingChange and usnLocalChange values which should increment through the time.\n- Schema and configuration partition suspicious modifications: The first graph displays Active Directory attribute modifications related to the configuration and schema partitions which can lower the security of the domain, used as backdoor by an attacker or hide information to the security team. The second graph is relevant if you have Exchange on premises and track modifications in the configuration partition which can be a sign of mail exfiltration.\n\n### The track suspicious Exchange activity dashboard\n\nThis dashboard analyses your Exchange onprem Active Directory objects and highlights some modifications which can be a sign of a suspicious activity, the modifications spotted can also be legitimate and need a triage analysis.\n\n The different panels are:\n\n- Microsoft Exchange infrastructure: Exchange version and servers.\n- Possible mail exfiltration: Mail forwarders setup on mailboxes, transport rules, remote domains, maibox export requests, content searches...\n- Persistence: RBAC roles and ACL modifications on Exchange objects.\n- MsExchangeSecurityGroups: Timeline and group membership of builtin Exchange security groups.\n- Phishing:Modifications on transport rules related to SCL and disclaimer.\n\n## Enhance your traditional event logs threat hunting with ADTimeline: \u003ca name=\"threathuntevtx\"\u003e\u003c/a\u003e\n\nThe *adobjects* sourcetype is a set of data which can be used to uncover suspicious activity by performing Active Directory educated queries on the Windows event logs. We assume the sourcetype used for event logs is called *winevent* and its *EventData* part has the correct field extraction applied, for example *EventID 4624* has among other fields *TargetUserName* and *TargetUserSid* extracted:\n\n![EVtx](./SA-ADTimeline/appserver/static/images/tuto10.png)\n\nYou can perfrom similar queries with the [Splunk App for Windows Infrastructure](https://docs.splunk.com/Documentation/MSApp/2.0.0/Reference/Aboutthismanual) and the [Splunk Supporting Add-on for Active Directory](https://docs.splunk.com/Documentation/SA-LdapSearch/3.0.0/User/AbouttheSplunkSupportingAdd-onforActiveDirectory). Here are some queries using ADTimeline data and Windows event logs which can help with your threat hunt.\n\n- Statistics on privileged accounts logons:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[ search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as TargetUserSid | fields TargetUserSid ] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullName , values(LogonType) as logonTypes, values(IpAddress) as IpAdresses, values(Computer) as Computers, dc(Computer) as CountComputers, dc(IpAddress) as CountIpAdresses by TargetUserSid\n```\n- Get processes running under a privileged account, [detailed process auditing](https://docs.microsoft.com/en-us/windows-server/identity/ad-ds/manage/component-updates/command-line-process-auditing) should be enabled:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4688\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SamAccountName as TargetUserName | fields TargetUserName] \nOR [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as SubjectUserSid | fields SubjectUserSid] \n|  stats values(CommandLine) by Computer, TargetUserName,SubjectUserName\n```\n- Get all privileged accounts PowerShell activity eventlogs:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"*PowerShell*\"  \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | dedup SamAccountName | return 1000 $SamAccountName]\n```\n- Detect Kerberoasting possible activity:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4769\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt) earliest = 1 latest = now() | rename SID as ServiceSid | fields ServiceSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName  \n|  eval time=strftime(_time,\"%Y-%m-%d %H:%M:%S\") \n|  stats list(ServiceName) as Services, dc(ServiceName) as nbServices, list(time) as time by IpAddress, TicketEncryptionType \n| sort -nbServices\n```\n\n- Detect abnormal processes running under Kerberoastable accounts, [detailed process auditing](https://docs.microsoft.com/en-us/windows-server/identity/ad-ds/manage/component-updates/command-line-process-auditing) should be enabled:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4688\" \n[search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SamAccountName as TargetUserName | fields TargetUserName ] \nOR  [search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SID as SubjectUserSid | fields SubjectUserSid ] \n|  stats values(CommandLine) as cmdlines, values(TargetUserName) as TargetSubjectNames, values(SubjectUserName) as SubjectUserNames by Computer\n```\n- Detect abnormal Kerberoastable user account logons:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects   (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now() | rename SID as TargetUserSid  | fields TargetUserSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n```\n\n- Detect abnormal AS-REP roastable user account logons:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") earliest = 1 latest = now() |  eval eval_asrep_bit = floor(userAccountControl / pow(2, 22)) %2 | search eval_asrep_bit = 1 |  rename SID as TargetUserSid  | fields TargetUserSid]  \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n```\n- Privileged accounts with flag \"cannot be delegated\" not set authenticating against computer configured for unconstrained delegation:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\" earliest = 1 latest = now() | eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0 |   rename dNSHostName as Computer | fields Computer] \n|  search *   [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | eval canbedelagated = round(((userAccountControl / pow(2, 20)) %2), 0) | search canbedelagated = 0 | rename SID as TargetUserSid  | fields TargetUserSid ] \n|  strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName \n| table  _time, Computer, TargetFullName, IpAddress, LogonType, LogonProcessName\n```\n\n- Detect possible [printer bug](https://posts.specterops.io/not-a-security-boundary-breaking-forest-trusts-cd125829518d) triggering:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" TargetUserName = \"*$\" NOT TargetUserSid=\"S-1-5-18\"\n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\"  earliest = 1 latest = now() |  eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0  |  rename dNSHostName as Computer | fields Computer]\n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(LogonType), values(IpAddress), values(LogonProcessName) count by Computer, TargetFullName\n```\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"
                     }
                 ],
    "SourceType":  "enhanced_github",
    "TotalPatterns":  2
}
