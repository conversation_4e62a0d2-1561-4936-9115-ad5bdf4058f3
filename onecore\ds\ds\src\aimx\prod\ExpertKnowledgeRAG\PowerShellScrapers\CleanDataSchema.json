{"schema_version": "1.0", "description": "Clean, RAG-optimized data schema for PowerShell knowledge base", "knowledge_entry": {"id": "unique_identifier", "title": "Human readable title", "content": "Full text content for RAG search - no HTML, clean markdown", "code_examples": [{"description": "What this code does", "code": "PowerShell code block", "language": "powershell"}], "source": {"type": "stackoverflow|microsoft_docs|github|blog", "url": "original_url", "credibility": 0.95}, "tags": ["powershell", "active-directory", "cmdlet-name"], "cmdlets": ["Get-ADUser", "Set-<PERSON><PERSON><PERSON>"], "last_updated": "2025-07-28T16:21:13Z"}, "example_entries": [{"id": "stackoverflow_execution_policy", "title": "PowerShell Execution Policy Issues and Solutions", "content": "When PowerShell says 'execution of scripts is disabled on this system', it means the execution policy is set to Restricted. This is a security feature that prevents unauthorized script execution.\n\nTo fix this issue:\n1. Run PowerShell as Administrator\n2. Use Set-ExecutionPolicy to change the policy\n3. Choose appropriate scope (CurrentUser vs LocalMachine)\n\nBest practices:\n- Use RemoteSigned for most scenarios\n- Avoid Unrestricted unless absolutely necessary\n- Use CurrentUser scope when you don't have admin rights", "code_examples": [{"description": "Check current execution policy", "code": "Get-ExecutionPolicy", "language": "powershell"}, {"description": "Set execution policy for current user", "code": "Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned", "language": "powershell"}, {"description": "Bypass execution policy for single script", "code": "powershell -ExecutionPolicy Bypass -File script.ps1", "language": "powershell"}], "source": {"type": "stackoverflow", "url": "https://stackoverflow.com/questions/4037939/powershell-says-execution-of-scripts-is-disabled-on-this-system", "credibility": 0.9}, "tags": ["powershell", "execution-policy", "security", "troubleshooting"], "cmdlets": ["Get-ExecutionPolicy", "Set-ExecutionPolicy"], "last_updated": "2025-07-28T16:21:13Z"}]}