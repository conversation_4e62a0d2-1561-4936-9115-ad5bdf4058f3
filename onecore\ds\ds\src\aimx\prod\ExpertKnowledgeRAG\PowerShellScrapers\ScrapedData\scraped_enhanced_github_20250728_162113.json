﻿{
    "ScrapedAt":  "2025-07-28T16:21:13Z",
    "BatchId":  "20250728_162113",
    "Patterns":  [
                     {
                         "AllCodeBlocks":  [

                                           ],
                         "RelevanceScore":  0.8996065061142513,
                         "CreatedAt":  "2025-07-28T16:20:58Z",
                         "PatternType":  "workflow",
                         "Id":  "pattern_user_management_c2c64a61",
                         "Operation":  "read",
                         "Stars":  1143,
                         "UpdatedAt":  "2025-07-28T16:20:58Z",
                         "HasReadme":  true,
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub Repository: ********/adPEAS",
                         "LastUpdated":  "2025-07-24T19:31:50Z",
                         "CodeTemplate":  "Import-Module .\\adPEAS.ps1\n\n. .\\adPEAS.ps1\n\ngc -raw .\\adPEAS.ps1 | iex\n\nIEX (New-Object Net.WebClient).DownloadString(\u0027https://raw.githubusercontent.com/********/adPEAS/main/adPEAS.ps1\u0027)\n\nInvoke-adPEAS\n\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Outputfile \u0027C:\\temp\\adPEAS_outputfile\u0027 -NoColor\n\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027\n\n$SecPassword = ConvertTo-SecureString \u0027Passw0rd1!\u0027 -AsPlainText -Force\n$Cred = New-Object System.Management.Automation.PSCredential(\u0027contoso\\johndoe\u0027, $SecPassword)\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Cred $Cred\n\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027 -Username \u0027contoso\\johndoe\u0027 -Password \u0027Passw0rd1!\u0027 -Force\n\nInvoke-adPEAS -Module Domain\n\nInvoke-adPEAS -Module Rights\n\nInvoke-adPEAS -Module GPO\n\nInvoke-adPEAS -Module ADCS\n\nInvoke-adPEAS -Module Creds\n\nInvoke-adPEAS -Module Delegation\n\nInvoke-adPEAS -Module Accounts\n\nInvoke-adPEAS -Module Computer\n\nInvoke-adPEAS -Module Bloodhound\n\nInvoke-adPEAS -Module Bloodhound -BloodHoundLegacy\n\nInvoke-adPEAS -Module Bloodhound -Scope All",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  135,
                         "PowerShellFiles":  2,
                         "Domain":  "user_management",
                         "RepoSize":  79150,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "user_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: ********/adPEAS\n\n**Stars:** 1143 | **Language:** PowerShell | **Forks:** 135\n\n**Description:** Powershell tool to automate Active Directory enumeration.\n\n**URL:** https://github.com/********/adPEAS\n\n**Clone URL:** https://github.com/********/adPEAS.git\n\n## README\n\n# adPEAS\n\n![](https://github.com/********/adPEAS/raw/main/images/adPEAS_large.jpg)\n\nadPEAS is a Powershell tool to automate Active Directory enumeration.\nIn fact, adPEAS is like a wrapper for different other cool projects...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:20:58Z",
                                             "Url":  "https://github.com/********/adPEAS",
                                             "ScrapedAt":  "2025-07-28T16:20:58Z",
                                             "Id":  "2913849b-96a6-49a0-92ac-d85f91bd3dbd",
                                             "Author":  "********",
                                             "Title":  "GitHub Repository: ********/adPEAS"
                                         }
                                     ],
                         "Topics":  [

                                    ],
                         "Content":  "# Repository: ********/adPEAS\n\n**Stars:** 1143 | **Language:** PowerShell | **Forks:** 135\n\n**Description:** Powershell tool to automate Active Directory enumeration.\n\n**URL:** https://github.com/********/adPEAS\n\n**Clone URL:** https://github.com/********/adPEAS.git\n\n## README\n\n# adPEAS\n\n![](https://github.com/********/adPEAS/raw/main/images/adPEAS_large.jpg)\n\nadPEAS is a Powershell tool to automate Active Directory enumeration.\nIn fact, adPEAS is like a wrapper for different other cool projects like PowerView, PoshADCS, BloodHound stuff and some own written lines of code.\n\nAs said, adPEAS is a wrapper for other tools. They are almost all written in pure Powershell but some of them are included as C# code in a compressed binary blob.\n\nadPEAS-Light is a version without Bloodhound and it is more likely that it will not be blocked by an AV solution.\n\n# How It Works\n\nadPEAS can be run simply by starting the script via _invoke-adPEAS_ if it is started on a domain joined computer.\nIf the system you are running adPEAS from is not domain joined or you want to enumerate another domain, use a certain domain controller to connect to, use different credentials or just to enumerate for credential exposure only, you can do it by using defined parameters.\n\n## adPEAS Modules\n\nadPEAS consists of the following enumeration modules:\n* Domain - Searching for basic Active Directory information, like Domain Controllers, Sites und Subnets, Trusts and Password/Kerberos policy\n* Rights - Searching for specific Active Directory rights and permissions, like LAPS, DCSync and adding computer to domain\n* GPO -  Searching for basic GPO related things, like local group membership on domain computer\n* ADCS - Searching for basic Active Directory Certificate Services information, like CA Name, CA Server and vulnerable Templates\n* Creds - Searching for different kind of credential exposure, like ASREPRoast, Kerberoasting, GroupPolicies, Netlogon scripts, LAPS, gMSA, certain legacy attributes, e.g. UnixPassword, etc.\n* Delegation - Searching for delegation issues, like \u0027Constrained Delegation\u0027, \u0027Unconstrained Delegation\u0027 and \u0027Resource Based Constrained Delegation\u0027, for computer and user accounts\n* Accounts - Searching for non-disabled high privileged user accounts in predefined groups and account issues like e.g. old passwords\n* Computer - Enumerating Domain Controllers, Certificate Services, Exchange Server and outdated OS versions like Windows Server 2008R2, etc.\n* BloodHound - Enumerating Active Directory with the SharpHound collector for BloodHound Community Edition or BloodHound-Legacy\n\n# Some How To Use Examples\n## Simple usage with generic program parameters\nFirst you have to load adPEAS in Powershell...\n```\nImport-Module .\\adPEAS.ps1\n```\nor\n```\n. .\\adPEAS.ps1\n```\nor\n```\ngc -raw .\\adPEAS.ps1 | iex\n```\nor\n```\nIEX (New-Object Net.WebClient).DownloadString(\u0027https://raw.githubusercontent.com/********/adPEAS/main/adPEAS.ps1\u0027)\n```\n\nStart adPEAS with all enumeration modules and enumerate the domain the logged-on user and computer is connected to.\n```\nInvoke-adPEAS\n```\n\nStart adPEAS with all enumeration modules and enumerate the domain \u0027contoso.com\u0027. In addition it writes all output without any ANSI color codes to a file.\n```\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Outputfile \u0027C:\\temp\\adPEAS_outputfile\u0027 -NoColor\n```\n\nStart adPEAS with all enumeration modules, enumerate the domain \u0027contoso.com\u0027 and use the domain controller \u0027dc1.contoso.com\u0027 for almost all enumeration requests.\n```\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027\n```\n\nStart adPEAS with all enumeration modules, enumerate the domain \u0027contoso.com\u0027 and use the passed PSCredential object during enumeration.\n```\n$SecPassword = ConvertTo-SecureString \u0027Passw0rd1!\u0027 -AsPlainText -Force\n$Cred = New-Object System.Management.Automation.PSCredential(\u0027contoso\\johndoe\u0027, $SecPassword)\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Cred $Cred\n```\n\nStart adPEAS with all enumeration modules, enumerate the domain \u0027contoso.com\u0027 by using the domain controller \u0027dc1.contoso.com\u0027 and use the username \u0027contoso\\johndoe\u0027 with password \u0027Passw0rd1!\u0027 during enumeration. If, due to DNS issues Active Directory detection fails, the switch -Force forces adPEAS to ignore those issues and try to get still as much information as possible.\n```\nInvoke-adPEAS -Domain \u0027contoso.com\u0027 -Server \u0027dc1.contoso.com\u0027 -Username \u0027contoso\\johndoe\u0027 -Password \u0027Passw0rd1!\u0027 -Force\n```\n\n## Usage with a single enumeration module\n### All modules below can be combined with all generic program parameters explained above.\n\nEnumerates basic Active Directory information, like Domain Controllers, Password Policy, Sites and Subnets and Trusts.\n```\nInvoke-adPEAS -Module Domain\n```\n\nEnumerates specific Active Directory rights and permissions, like LAPS, DCSync and adding computer to domain.\n```\nInvoke-adPEAS -Module Rights\n```\n\nEnumerates basic GPO information, like set local group membership on domain computer.\n```\nInvoke-adPEAS -Module GPO\n```\n\nEnumerates basic Active Directory Certificate Services information, like CA Name, CA Server and common Template vulnerabilities.\n```\nInvoke-adPEAS -Module ADCS\n```\n\nEnumerates credential exposure issues, like ASREPRoast, Kerberoasting, Linux/Unix password attributes, gMSA, LAPS (if your account has the rights to read it), Group Policies, Netlogon scripts.\n```\nInvoke-adPEAS -Module Creds\n```\n\nEnumerates delegation issues, like \u0027Unconstrained Delegation\u0027, \u0027Constrained Delegation\u0027, \u0027Resource Based Constrained Delegation\u0027 for user and computer objects.\n```\nInvoke-adPEAS -Module Delegation\n```\n\nEnumerates users in high privileged groups which are NOT disabled, like Administrators, Domain Admins, Enterprise Admins, Group Policy Creators, DNS Admins, Account Operators, Server Operators, Printer Operators, Backup Operators, Hyper-V Admins, Remote Management Users und CERT Publishers.\n```\nInvoke-adPEAS -Module Accounts\n```\n\nEnumerates installed Domain Controllers, Active Directory Certificate Services, Exchange Server and outdated OS versions like Windows Server 2008R2.\n```\nInvoke-adPEAS -Module Computer\n```\n\nStarts Bloodhound enumeration for BloodHound Community Edition (\u003e= version 5.0) with the scope DCOnly. Output ZIP files are stored in the same directory adPEAS is started from.\n```\nInvoke-adPEAS -Module Bloodhound\n```\n\nStarts Bloodhound enumeration for BloodHound-Legacy (up to version 4.3.1) with the scope DCOnly. Output ZIP files are stored in the same directory adPEAS is started from.\n```\nInvoke-adPEAS -Module Bloodhound -BloodHoundLegacy\n```\n\nStarts Bloodhound enumeration with the scope All. With this option the SharpHound collector will contact each member computer of the domain. Output ZIP files are stored in the same directory adPEAS is started from.\n```\nInvoke-adPEAS -Module Bloodhound -Scope All\n```\n\n## Special thanks go to...\n* Will Schroeder @harmjoy, for his great PowerView\n* Charlie Clark @exploitph, for his ongoing work on PowerView\n* Christoph Falta @cfalta, for his inspiring work on PoshADCS\n* Dirk-jan @_dirkjan, for his great AD and Windows research\n* SpecterOps, for their fantastic BloodHound\n* and all the people who inspired me on my journey...\n\n## PowerShell Files (2 files)\n\n"
                     },
                     {
                         "AllCodeBlocks":  [

                                           ],
                         "RelevanceScore":  0.74103762920994987,
                         "CreatedAt":  "2025-07-28T16:21:00Z",
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_user_management_a5a67a6a",
                         "Operation":  "read",
                         "Stars":  505,
                         "UpdatedAt":  "2025-07-28T16:21:00Z",
                         "HasReadme":  true,
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub Repository: ANSSI-FR/ADTimeline",
                         "LastUpdated":  "2025-07-20T15:22:59Z",
                         "CodeTemplate":  "PS\u003e import-csv timeline_%DOMAINFQDN%.csv -delimiter \";\"\nPS\u003e import-clixml ADobjects_%DOMAINFQDN%.xml\nPS\u003e import-clixml gcADobjects_%DOMAINFQDN%.xml\n\nPS\u003e./ADTimeline -customgroups \"VIP-group1,ESX-Admins,Tier1-admins\"\n\nPS\u003e$customgroups = get-content customgroups.txt\nPS\u003e./ADTimeline -customgroups $customgroups\n\n$groupscustom = (\"VIP-group1\",\"ESX-Admis\",\"Tier1-admins\")\n\nDOS\nPS\u003e .\\ADTimeline.ps1 -server \u003cGLOBAL CATALOG FQDN\u003e\n\nDOS\nC:\\Windows\\System32\u003e dsamain.exe -dbpath \u003cNTDS.DIT path\u003e -ldapport 3266 -allownonadminaccess\n\nDOS\nPS\u003e .\\ADTimeline.ps1 -server \"127.0.0.1:3266\"\n\nDOS\nPS\u003e.\\ADTimeline -nofwdSMTPaltRecipient\n\npowershell\nPS\u003e import-csv timeline_%DOMAINFQDN%.csv -delimiter \";\"\nPS\u003e import-clixml ADobjects_%DOMAINFQDN%.xml\nPS\u003e import-clixml gcADobjects_%DOMAINFQDN%.xml\n\npowershell\nPS\u003e./ADTimeline -customgroups \"VIP-group1,ESX-Admins,Tier1-admins\"\n\npowershell\nPS\u003e$customgroups = get-content customgroups.txt\nPS\u003e./ADTimeline -customgroups $customgroups\n\npowershell\n$groupscustom = (\"VIP-group1\",\"ESX-Admis\",\"Tier1-admins\")\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[ search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as TargetUserSid | fields TargetUserSid ] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullName , values(LogonType) as logonTypes, values(IpAddress) as IpAdresses, values(Computer) as Computers, dc(Computer) as CountComputers, dc(IpAddress) as CountIpAdresses by TargetUserSid\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4688\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SamAccountName as TargetUserName | fields TargetUserName] \nOR [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as SubjectUserSid | fields SubjectUserSid] \n|  stats values(CommandLine) by Computer, TargetUserName,SubjectUserName\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"*PowerShell*\"  \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | dedup SamAccountName | return 1000 $SamAccountName]\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4769\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt) earliest = 1 latest = now() | rename SID as ServiceSid | fields ServiceSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName  \n|  eval time=strftime(_time,\"%Y-%m-%d %H:%M:%S\") \n|  stats list(ServiceName) as Services, dc(ServiceName) as nbServices, list(time) as time by IpAddress, TicketEncryptionType \n| sort -nbServices\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4688\" \n[search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SamAccountName as TargetUserName | fields TargetUserName ] \nOR  [search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SID as SubjectUserSid | fields SubjectUserSid ] \n|  stats values(CommandLine) as cmdlines, values(TargetUserName) as TargetSubjectNames, values(SubjectUserName) as SubjectUserNames by Computer\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects   (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now() | rename SID as TargetUserSid  | fields TargetUserSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n\nvb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") earliest = 1 latest = now() |  eval eval_asrep_bit = floor(userAccountControl / pow(2, 22)) %2 | search eval_asrep_bit = 1 |  rename SID as TargetUserSid  | fields TargetUserSid]  \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\" earliest = 1 latest = now() | eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0 |   rename dNSHostName as Computer | fields Computer] \n|  search *   [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | eval canbedelagated = round(((userAccountControl / pow(2, 20)) %2), 0) | search canbedelagated = 0 | rename SID as TargetUserSid  | fields TargetUserSid ] \n|  strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName \n| table  _time, Computer, TargetFullName, IpAddress, LogonType, LogonProcessName\n\nvb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" TargetUserName = \"*$\" NOT TargetUserSid=\"S-1-5-18\"\n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\"  earliest = 1 latest = now() |  eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0  |  rename dNSHostName as Computer | fields Computer]\n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(LogonType), values(IpAddress), values(LogonProcessName) count by Computer, TargetFullName",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  65,
                         "PowerShellFiles":  null,
                         "Domain":  "user_management",
                         "RepoSize":  1041,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "user_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: ANSSI-FR/ADTimeline\n\n**Stars:** 505 | **Language:** PowerShell | **Forks:** 65\n\n**Description:** Timeline of Active Directory changes with replication metadata\n\n**URL:** https://github.com/ANSSI-FR/ADTimeline\n\n**Clone URL:** https://github.com/ANSSI-FR/ADTimeline.git\n\n**Topics:** active-directory, dfir, forensics, powershell, splunk, timeline, windows\n\n## README\n\n![ADTimeline](./logo.png)\n---\n# Table of contents:\n1. [The ADTimeline PowerShell script](#thescript)\n    1. [Description...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:21:00Z",
                                             "Url":  "https://github.com/ANSSI-FR/ADTimeline",
                                             "ScrapedAt":  "2025-07-28T16:21:00Z",
                                             "Id":  "9f432579-9f39-456f-967b-f5e4b0160cf9",
                                             "Author":  "ANSSI-FR",
                                             "Title":  "GitHub Repository: ANSSI-FR/ADTimeline"
                                         }
                                     ],
                         "Topics":  [
                                        "active-directory",
                                        "dfir",
                                        "forensics",
                                        "powershell",
                                        "splunk",
                                        "timeline",
                                        "windows"
                                    ],
                         "Content":  "# Repository: ANSSI-FR/ADTimeline\n\n**Stars:** 505 | **Language:** PowerShell | **Forks:** 65\n\n**Description:** Timeline of Active Directory changes with replication metadata\n\n**URL:** https://github.com/ANSSI-FR/ADTimeline\n\n**Clone URL:** https://github.com/ANSSI-FR/ADTimeline.git\n\n**Topics:** active-directory, dfir, forensics, powershell, splunk, timeline, windows\n\n## README\n\n![ADTimeline](./logo.png)\n---\n# Table of contents:\n1. [The ADTimeline PowerShell script](#thescript)\n    1. [Description](#description)\n    2. [Prerequisites](#prerequisites)\n    3. [Usage](#usage)\n    4. [Files generated](#files)\n    5. [Custom groups](#groups)\n2. [The ADTimeline App for Splunk](#theapp)\n    1. [Description](#descriptionsplk)\n    2. [Sourcetypes](#sourcetype)\n    3. [AD General information dashboards](#infradashboards)\n    4. [AD threat hunting dashboards](#threathuntdashboards)\n    5. [Enhance your traditional event logs threat hunting with ADTimeline](#threathuntevtx)\n\n# The ADTimeline PowerShell script:  \u003ca name=\"thescript\"\u003e\u003c/a\u003e\n\n## Description: \u003ca name=\"description\"\u003e\u003c/a\u003e\n\nThe ADTimeline script generates a timeline based on Active Directory replication metadata for objects considered of interest.  \nReplication metadata gives you the time at which each replicated attribute for a given object was last changed. As a result the timeline of modifications is partial. For each modification of a replicated attribute a version number is incremented.  \nADTimeline was first presented at the [CoRI\u0026IN 2019](https://www.cecyf.fr/coriin/coriin-2019/) (Conférence sur la réponse aux incidents et l’investigation numérique). Slides of the presentation, in french language,  are available [here](https://cyber.gouv.fr/publications/investigation-numerique-sur-lannuaire-active-directory-avec-les-metadonnees-de). It was also presented at the [Amsterdam 2019 FIRST Technical Colloquium](https://www.first.org/events/colloquia/amsterdam2019/program#pActive-Directory-forensics-with-replication-metadata-ADTimeline-tool), slides in english are available [here](https://cyber.gouv.fr/en/actualites/adtimeline-active-directory-forensics-replication-metadata-first-technical-colloquium).\n\nObjects considered of interest retrieved by the script include:\n\n- Schema and configuration partition root objects.\n- Domain root and objects located directly under the root.\n- Objects having an ACE on the domain root.\n- Domain roots located in the AD forest.\n- Domain trusts.\n- Deleted users (i.e. tombstoned).\n- Objects protected by the SDProp process (i.e. AdminCount equals 1).\n- The Guest account.\n- The AdminSDHolder object.\n- Objects having an ACE on the AdminSDHolder object.\n- Class Schema objects.\n- Existing and deleted Group Policy objects.\n- DPAPI secrets.\n- Domain controllers (Computer objects, ntdsdsa and server objects).\n- DNS zones.\n- WMI filters.\n- Accounts with suspicious SIDHistory (scope is forest wide).\n- Sites.\n- Organizational Units.\n- Objects with Kerberos delegation enabled.\n- Extended rights.\n- Schema attributes with particular SearchFlags (Do not audit or confidential).\n- Kerberoastable user accounts (SPN value).\n- AS-REP roastable accounts (UserAccountControl value).\n- Authentication policy silos.\n- CertificationAuthority and pKIEnrollmentService objects.\n- Cross Reference containers.\n- Exchange RBAC roles and accounts assigned to a role.\n- Exchange mail flow configuration objects.\n- Exchange mailbox databases objects.\n- Exchange Mailbox Replication Service objects\n- Deleted objects under the configuration partition.\n- Dynamic objects.\n- The directory service and RID manager objects.\n- The Pre Windows 2000 compatible access, Cert publishers, GPO creator owners and DNS Admins groups.\n- ADFS DKM containers.\n- Service connection point objects considered of interest.\n- Custom groups which have to be manually defined.\n- User objects with mail forwarder enabled (msExchGenericForwardingAddress and altRecipient attributes).\n\n## Prerequisites: \u003ca name=\"prerequisites\"\u003e\u003c/a\u003e\n\n- The account launching the script should be able to read objects in the tombstone (Deleted Objects Container) and some parts of the Exchange settings located in the configuration partition (View-Only Organization management). Delegation can be tricky to setup (especially for reading the tombstone). That is why we advise you to run the script with a domain admin account. If you launch the script as a standard user, it will process the timeline without the objects mentioned.\n- Computer should run Windows NT 6.1 or later with PowerShell 2.0 or later and have the Active Directory Powershell module installed (part of RSAT-AD-Tools).\n- If you enabled PowerShell Constrained Language Mode the script might fail (calling $error.clear()). Consider whitelisting the script via your device guard policy.\n- If you are using offline mode install the ADLDS role on a Windows Server edition in order to use dsamain.exe and mount the NTDS database.\n\n    The version of the Windows Server you install the role on should be the same as the version of the Windows Server which the ntds.dit came from. If you do not know that version and you have the SOFTWARE hive available, you can look at the CurrentVersion key.\n    \n    If you can not mount the ntds.dit file with dsamain.exe, this might be because the NTDS dump is corrupted. In that case, you can follow [advice from cert-cwatch](https://github.com/ANSSI-FR/ADTimeline/issues/17#issuecomment-1984049537).\n\n## Usage: \u003ca name=\"usage\"\u003e\u003c/a\u003e\n\nIn online mode no argument is mandatory and the closest global catalog is used for processing. If no global catalog is found run the script with the server argument :\n```DOS\nPS\u003e .\\ADTimeline.ps1 -server \u003cGLOBAL CATALOG FQDN\u003e\n```\nIn offline mode: Replay if necessary transaction logs of the NTDS database, mount it on your analysis machine (ADLDS + RSAT-AD-Tools installed) and use 3266 as LDAP port.\n```DOS\nC:\\Windows\\System32\u003e dsamain.exe -dbpath \u003cNTDS.DIT path\u003e -ldapport 3266 -allownonadminaccess\n```\nIf necessary use the allowupgrade switch.\n\nLaunch the script targetting localhost on port 3266:\n```DOS\nPS\u003e .\\ADTimeline.ps1 -server \"127.0.0.1:3266\"\n```\n\nIf you encounter performance issues when running against a large MSExchange organization with forwarders massively used, use the nofwdSMTP parameter:\n```DOS\nPS\u003e.\\ADTimeline -nofwdSMTPaltRecipient\n```\n## Files generated \u003ca name=\"files\"\u003e\u003c/a\u003e\n\nOutput files are generated in the current directory:\n\n- timeline_%DOMAINFQDN%.csv: The timeline generated with the AD replication metadata of objects retrieved.\n- logfile_%DOMAINFQDN%.log: Script log file. You will also find various information on the domain.\n- ADobjects_%DOMAINFQDN%.xml: Objects of interest retrieved via LDAP.\n- gcADobjects_%DOMAINFQDN%.xml: Objects of interest retrieved via the Global Catalog.\n\n\nTo import files for analysis with powershell. \n```powershell\nPS\u003e import-csv timeline_%DOMAINFQDN%.csv -delimiter \";\"\nPS\u003e import-clixml ADobjects_%DOMAINFQDN%.xml\nPS\u003e import-clixml gcADobjects_%DOMAINFQDN%.xml\n```\nThe analysis with the ADTimeline for Splunk is a better solution.\n\n## Custom groups \u003ca name=\"groups\"\u003e\u003c/a\u003e\n\nIf you want to include custom AD groups in the timeline (for example virtualization admin groups, network admins, VIP groups...) use the *Customgroups* parameter.\n\n*Customgroups* parameter can be a string with multiple group comma separated (no space):\n```powershell\nPS\u003e./ADTimeline -customgroups \"VIP-group1,ESX-Admins,Tier1-admins\"\n```\n*Customgroups* parameter can also be an array, in case you import the list from a file (one group per line):\n```powershell\nPS\u003e$customgroups = get-content customgroups.txt\nPS\u003e./ADTimeline -customgroups $customgroups\n```\nIf you do not want to use a parameter you can also uncomment and edit the following array at the  begining of the script:\n```powershell\n$groupscustom = (\"VIP-group1\",\"ESX-Admis\",\"Tier1-admins\")\n```\n\n# The ADTimeline App for Splunk: \u003ca name=\"theapp\"\u003e\u003c/a\u003e\n\n## Description: \u003ca name=\"descriptionsplk\"\u003e\u003c/a\u003e\n\nThe ADTimeline application for Splunk processes and analyses the Active Directory data collected by the ADTimeline PowerShell script. The app was presented at the 32nd annual FIRST Conference, a recording of the presentation is available [here](https://www.first.org/conference/2020/recordings).\n\nThe app\u0027s \"Getting started\" page will give you the instructions for the import process.\n\nOnce indexed the dashboards provided by the app will help the DFIR analyst to spot some Acitve Directory persistence mechanisms, misconfigurations, security audit logging bypass, mail exfiltration, brute force attacks ...\n\nThe app is also packaged and available on [Splunkbase](https://splunkbase.splunk.com/app/4897/). It has no prerequisite and will work with a [free Splunk](https://docs.splunk.com/Documentation/Splunk/latest/Admin/MoreaboutSplunkFree) license.\n\n![Splunkapp](./SA-ADTimeline.png)\n\n## Sourcetypes: \u003ca name=\"sourcetype\"\u003e\u003c/a\u003e\n\nAfter processing the ADTimeline script you should have two or three files to import in Splunk (%DOMAINFQDN% is the Active Directory fully qualified domain name):\n\n- timeline_%DOMAINFQDN%.csv: The timeline generated with the AD replication metadata of objects retrieved. The corresponding source type is *adtimeline*.\n- ADobjects_%DOMAINFQDN%.xml: Objects of interest retrieved via LDAP. The corresponding sourcetype is *adobjects*.\n- gcADobjects_%DOMAINFQDN%.xml: If any, objects of interest retrieved via the Global Catalog. The corresponding source type is *gcobjects*.\n\n### The adtimeline sourcetype:\n\n The *adtimeline* sourcetype is the data from the timeline_%DOMAINFQDN%.csv file, which is the Active Directory timeline built with replication metadata for objects considered of interest.\n\nThe timestamp value is the ftimeLastOriginatingChange value of the replication metadata, which is the time the attribute was last changed, time is UTC.\n\nThe extracted fields are:\n\n- Name: LDAP object name.\n- pszAttributeName: The attribute name.\n- dwVersion: Counter incremented every time the attribute is changed.\n- DN: LDAP object DistinguishedName.\n- WhenCreated: LDAP object creation time.\n- ObjectClass and ObjectCategory: LDAP object type (user, computer, group...)\n- SamAccountName and SID: Account Name and security identifier, only applies to users, computers and groups.\n- usnOriginatingChange: USN on the originating server at which the last change to this attribute was made.\n- pszLastOriginatingDsaDN: DC on which the last change was made to this attribute.\n- uuidLastOriginatingDsaInvocationID: ID corresponding to the pszLastOriginatingDsaDN.\n- usnLocalChange: USN on the destination server (the server your LDAP bind is made) at which the last change to this attribute was applied.\n- Member: Only applies to the group ObjectClass and when the attribute name is member. Contains the value of the group member DistinguishedName.\n- ftimeCreated: Only applies to group ObjectClass and when the attribute name is member. Contains the time the member was added in the group.\n- ftimeDeleted: Only applies to group ObjectClass and when the attribute name is member. Contains the time the member was removed from the group.\n\n### The adobjects sourcetype:\n\nThe *adobjects* sourcetype is the data from the ADobjects_%DOMAINFQDN%.xml file, which is an export of the Active Directory objects considered of interested and retrieved via the LDAP protocol.\n\nThe timestamp value is the createTimeStamp attribute value, time zone is specified in the attribute value.\n\nThe extracted fields are:\n\n- Name: LDAP object name.\n- DN: LDAP object DistinguishedName.\n- DisplayName: LDAP object displayname.\n- WhenCreated: LDAP object creation time.\n- ObjectClass and ObjectCategory: LDAP object type (user, computer, group...)\n- SamAccountName and SID: Account Name and security identifier, only applies to users, computers and groups.\n- Members and MemberOf: Members of a group ObjectClass can be users, computers or groups and its linked attribute MemberOf which applies to groups, users and computers.\n- Owner, AccessToString and SDDL: Are values computed from the nTSecurityDescriptor attribute\n- adminCount: Privileged accounts protected by the SDProp process.\n- userAccountControl: Attribute which contains a range of flags which define some important basic properties of a computer or user object.\n- lastLogonTimestamp: This attribute is not updated with all logon types or at every logon but is replicated and gives you an idea of wether a user or computer account has recently logged on to the domain.\n- dNSHostName: DNS hostname attribute of a computer account.\n- SPNs: List of Service Principal Names of a computer or user account.\n\n### The gcobjects sourcetype:\n\nThe *gcobjects* sourcetype is the data from the gcADobjects_%DOMAINFQDN%.xml file, which is an export of the Active Directory objects within the forest but outside the current domain and considered of interested, those objects are retrieved via the Global Catalog protocol.\n\nThe timestamp value is the WhenCreated attribute value, time zone is UTC.\n\nThe extracted fields are:\n\n- Name: LDAP object name.\n- DN: LDAP object DistinguishedName.\n- DisplayName: LDAP object displayname.\n- WhenCreated: LDAP object creation time.\n- ObjectClass and ObjectCategory: LDAP object type (user, computer, group...)\n- SamAccountName and SID: Account Name and security identifier, only applies to users, computers and groups.\n- userAccountControl: Attribute which contains a range of flags which define some important basic properties of a computer or user object.\n- lastLogonTimestamp: This attribute is not updated with all logon types or at every logon but is replicated and gives you an idea if a user or computer account has recently logged onto the domain.\n- dNSHostName: DNS hostname attribute of a computer account.\n- SPNs: List of Service Principal Names of a computer or user account.\n\n## AD General information dashboards: \u003ca name=\"infradashboards\"\u003e\u003c/a\u003e\n\n### The Active Directory Infrastructure dashboard:\n\nThis dashboard analyses Adtimeline data in order to create some panels giving you information on the Windows domain infrastructure.\n\nThe different panels are:\n\n- General information: Information about the Schema version and functional levels. Depending on the result some AD security features may or may not be available. The Domain Controllers are also listed in this panel\n- Microsoft infrastructure products: Tells you if some important Microsoft Infrastructure components such as Exchange on premises, Active Directory Federation Services or Active Directory Certificate Services are installed. Please consider monitoring events related to those services (MSExchange CmdletLogs, ADFS auditing...)\n- Domain Trusts: List domain trusts by type and direction. Run ADTimeline on all your trusted domains, but most importantly make sure they are audited, monitored and secured as rigorously as the domain you are analyzing.\n- ADDS security features: Tells you if some security features are enabled or not. First feature is the AD Recycle bin which gives the administrator the ability to easily recover deleted objects, it will also change the time after an object is removed from the AD database after deletion. Second feature tells you if the schema extension for the Local Admin Password Solution was performed, if yes sysadmins can enable password randomization for local administrators accounts in order to mitigate lateral movement. Another feature is authentication silos which can help to restrict privileged user account logons in order to mitigate privilege escalation by implementing a tiered administrative model. The last feature is the Protected Users group, with a DFL 2012R2 or more the members of this group receive some additional hardening\n- Service Connection Points: Inventory of serviceConnectionPoint (SCP) object class. SCP make it easy for a service to publish service-specific data in the directory Clients of the service use the data in an SCP to locate an instance of the service. Infrastructure assets such as RDS Gateway, SCCM, VMWare Vcenter, some Backup solutions publish an SCP in the directory.\n- Active Directory infrastructure timeline: Displays a timeline of the infrastructure changes listed above. This timeline tells you the story of the evolution of your infrastructure.\n\n### The sensitive accounts dashboard:\n\nThis dashboard provides an inventory of the privileged accounts in the domain and accounts prone to common attack scenarios due to their configuration.\n\n The different panels are:\n\n- Admin Accounts: This panel lists the accounts where the Admincount attribute value equals 1. Those accounts have their ACL protected by the SDProp process and it means the account has or had at some point high privileges in Active Directory. The first table lists them and provides some information about the accounts, the second table displays a timeline of modifications for some attributes of these accounts.\n- Accounts sensitive to Kerberoast attacks: Kerberoasting is an attack method that allows an attacker to crack the passwords of service accounts in Active Directory offline. The chart is a ratio of accounts prone to this attack and whether or not they are privileged accounts. The table lists them and provides some information about the accounts. Use least privilege principle for those accounts and consider using Group Managed Service Accounts.\n- Accounts sensitive to AS-REP Roast attacks: AS-REP Roast is an attack method that allows an attacker to crack the passwords of accounts in Active Directory offline. The chart is a ratio of accounts prone to this attack and whether or not they are privileged accounts. The table lists them and provides some information about the accounts. Use least privilege principle for those accounts.\n- Sensitive default accounts: Some general information about the default administrator, guest and krbtgt accounts. Administrator can be disabled or renamed as a measure against account lockout. Guest account must be disabled and krbtgt password should be changed on a regular schedule.\n- Accounts trusted for delegation: Kerberos Delegation is a feature that allows an application to reuse the end-user credentials to access resources hosted on a different server. An account trusted for unconstrained delegation is allowed to impersonate almost any user to any service within the network, whereas an account trusted for constrained delegation is allowed to impersonate almost any user for a given service within the network. The chart is a ratio of accounts trusted for constrained/unconstrained delegation. The tables list those accounts, the service name is given for accounts trusted for constrained delegation. A table listing objects with resource based constrained delegation configured is also displayed\n\n## AD threat hunting dashboards: \u003ca name=\"threathuntdashboards\"\u003e\u003c/a\u003e\n\n### The investigate timeframe dashboard:\n\nUse this dashboard to investigate a particular timeframe.\n\n The different panels are:\n\n- AD Timeline: A table displaying the timeline for the given timeframe.\n- Global stats: Global statistics on modifications occurring during the given timeframe, including modifications by ObjectClass, by pszAttributeName, by Originating DC, by time (i.e. day of the week or hour of the day) and finally stats on deletions by ObjectClass.\n- Items created and deleted within timeframe: A table displaying the creations and deletions of the same object within the given timeframe. A first chart gives you stats about object lifetimes in hours and a second one figures by ObjectClass.\n- Objects added or removed from groups or ACL modifications within timeframe: This table focuses on the Member and nTSecurityDescriptor attributes, which can help detect an elevation of privilege for a specific account or a backdoor setup by the attacker, the DistinguishedName value of the member and the time the member was added or removed from the group is given in that table. Which makes it more detailed than the above AD Timeline panel. A chart displaying nTSecurityDescriptor modifications by ObjectClass and another displaying the number of times an object was added or removed from a group are given\n- GPOs modifications within timeframe: A GPO can used by an attacker in various ways, for example to inject malicious code in logon/startup scripts, deploy malware at scale with an immediate scheduled task, setup a backdoor by modifying the nTSecurityDescriptor... For each attribute modification this table gives you the current client side extensions of the GPO and where the object is linked (OU, site or domain root).\n\n### The track suspicious activity dashboard\n\nThis dashboard analyses the Active Directory timeline and highlights some modifications which can be a sign of a suspicious activity, the modifications spoted can also be legitimate and need a triage analysis.\n\n The different panels are:\n\n- ACL modifications: This panel does not replace a thorough analysis of the Active Directory permissions with tools such as AD Control Paths. The panel contains a graph displaying a weekly timeline of ACL modifications per ObjectClass which occured one year back, some tables are focusing on the domain root and AdminSDHolder objects where permissions can be used as backdoor by an attacker. Finally, some statistics by ObjectClass and by least frequent owners are displayed.\n- Accounts: This panel show account modifications which can a be sign of suspicious activity such as users added and removed from groups, some charts provide stats by number of times the account was added or removed, membership time in days, Organizational unit where accounts are located (an account from the \"non_privileged_users\" OU added and removed from a privileged group can be a sign of suspicious activity). There are some graphs, the first graph shows a timeline of accounts lockouts in order to highlight brute force attacks, the second graph shows SID history editions which can be suspicious outside a domain migration period, the next graph analyses all the different attributes modified on an account during a password change (supplementalCredentials, lmPwdHistory, unicodePwd...) and checks they are modified at the same time. A table displays resource based constrained delegation setup on privileged account or domain controller computer objects, which can be a backdoor setup by an attacker. Finally a chart displays domain controller computer objects password change frequency, an attacker could modify the DC registry to disable computer password change and use this password as a backdoor.\n- GPOs: A table of GPOs modifications having an audit client side extension is displayed, an attacker could change the audit settings on the domain to perform malicious actions with stealth. Finally modifications which could result in a GPO processing malfunctioning are displayed, this includes gPCFunctionalityVersion, gPCFileSysPath or versionNumber attribute modification.\n- DCshadow detection: The DCshadow is an attack which allows an attacker to push modifications in Active Directory and bypass traditional alerting by installing a fake DC. It was first presented by Vincent Le Toux and Benjamin Delpy at the BlueHat IL 2018 conference. The first graph will try to detect the installation of the fake DC by analyzing server and nTDSDSA ObjectClass. The two following tables will try to detect replication metadata tampering by analyzing usnOriginatingChange and usnLocalChange values which should increment through the time.\n- Schema and configuration partition suspicious modifications: The first graph displays Active Directory attribute modifications related to the configuration and schema partitions which can lower the security of the domain, used as backdoor by an attacker or hide information to the security team. The second graph is relevant if you have Exchange on premises and track modifications in the configuration partition which can be a sign of mail exfiltration.\n\n### The track suspicious Exchange activity dashboard\n\nThis dashboard analyses your Exchange onprem Active Directory objects and highlights some modifications which can be a sign of a suspicious activity, the modifications spotted can also be legitimate and need a triage analysis.\n\n The different panels are:\n\n- Microsoft Exchange infrastructure: Exchange version and servers.\n- Possible mail exfiltration: Mail forwarders setup on mailboxes, transport rules, remote domains, maibox export requests, content searches...\n- Persistence: RBAC roles and ACL modifications on Exchange objects.\n- MsExchangeSecurityGroups: Timeline and group membership of builtin Exchange security groups.\n- Phishing:Modifications on transport rules related to SCL and disclaimer.\n\n## Enhance your traditional event logs threat hunting with ADTimeline: \u003ca name=\"threathuntevtx\"\u003e\u003c/a\u003e\n\nThe *adobjects* sourcetype is a set of data which can be used to uncover suspicious activity by performing Active Directory educated queries on the Windows event logs. We assume the sourcetype used for event logs is called *winevent* and its *EventData* part has the correct field extraction applied, for example *EventID 4624* has among other fields *TargetUserName* and *TargetUserSid* extracted:\n\n![EVtx](./SA-ADTimeline/appserver/static/images/tuto10.png)\n\nYou can perfrom similar queries with the [Splunk App for Windows Infrastructure](https://docs.splunk.com/Documentation/MSApp/2.0.0/Reference/Aboutthismanual) and the [Splunk Supporting Add-on for Active Directory](https://docs.splunk.com/Documentation/SA-LdapSearch/3.0.0/User/AbouttheSplunkSupportingAdd-onforActiveDirectory). Here are some queries using ADTimeline data and Windows event logs which can help with your threat hunt.\n\n- Statistics on privileged accounts logons:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[ search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as TargetUserSid | fields TargetUserSid ] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullName , values(LogonType) as logonTypes, values(IpAddress) as IpAdresses, values(Computer) as Computers, dc(Computer) as CountComputers, dc(IpAddress) as CountIpAdresses by TargetUserSid\n```\n- Get processes running under a privileged account, [detailed process auditing](https://docs.microsoft.com/en-us/windows-server/identity/ad-ds/manage/component-updates/command-line-process-auditing) should be enabled:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4688\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SamAccountName as TargetUserName | fields TargetUserName] \nOR [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | rename SID as SubjectUserSid | fields SubjectUserSid] \n|  stats values(CommandLine) by Computer, TargetUserName,SubjectUserName\n```\n- Get all privileged accounts PowerShell activity eventlogs:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"*PowerShell*\"  \n[search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | dedup SamAccountName | return 1000 $SamAccountName]\n```\n- Detect Kerberoasting possible activity:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4769\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt) earliest = 1 latest = now() | rename SID as ServiceSid | fields ServiceSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName  \n|  eval time=strftime(_time,\"%Y-%m-%d %H:%M:%S\") \n|  stats list(ServiceName) as Services, dc(ServiceName) as nbServices, list(time) as time by IpAddress, TicketEncryptionType \n| sort -nbServices\n```\n\n- Detect abnormal processes running under Kerberoastable accounts, [detailed process auditing](https://docs.microsoft.com/en-us/windows-server/identity/ad-ds/manage/component-updates/command-line-process-auditing) should be enabled:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4688\" \n[search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SamAccountName as TargetUserName | fields TargetUserName ] \nOR  [search index=\"*\" sourcetype=adobjects  (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now()   | rename SID as SubjectUserSid | fields SubjectUserSid ] \n|  stats values(CommandLine) as cmdlines, values(TargetUserName) as TargetSubjectNames, values(SubjectUserName) as SubjectUserNames by Computer\n```\n- Detect abnormal Kerberoastable user account logons:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects   (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND (SPNs=* AND NOT Name=krbtgt)  earliest = 1 latest = now() | rename SID as TargetUserSid  | fields TargetUserSid] \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n```\n\n- Detect abnormal AS-REP roastable user account logons:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" Channel=\"Security\" EventID=\"4624\" \n[search index=\"*\" sourcetype=adobjects (ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") earliest = 1 latest = now() |  eval eval_asrep_bit = floor(userAccountControl / pow(2, 22)) %2 | search eval_asrep_bit = 1 |  rename SID as TargetUserSid  | fields TargetUserSid]  \n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(TargetFullName) as TargetFullNames, values(IpAddress) as IpAddresses by LogonType, Computer\n```\n- Privileged accounts with flag \"cannot be delegated\" not set authenticating against computer configured for unconstrained delegation:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" \n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\" earliest = 1 latest = now() | eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0 |   rename dNSHostName as Computer | fields Computer] \n|  search *   [search index=\"*\" sourcetype=adobjects  ((ObjectClass = \"user\" OR ObjectClass = \"inetOrgPerson\") AND adminCount=1) earliest = 1 latest = now() | eval canbedelagated = round(((userAccountControl / pow(2, 20)) %2), 0) | search canbedelagated = 0 | rename SID as TargetUserSid  | fields TargetUserSid ] \n|  strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName \n| table  _time, Computer, TargetFullName, IpAddress, LogonType, LogonProcessName\n```\n\n- Detect possible [printer bug](https://posts.specterops.io/not-a-security-boundary-breaking-forest-trusts-cd125829518d) triggering:\n\n```vb\nindex=\"*\" sourcetype=\"winevent\" EventID=\"4624\" Channel=\"Security\" TargetUserName = \"*$\" NOT TargetUserSid=\"S-1-5-18\"\n[search index=\"*\" sourcetype=adobjects ObjectClass = \"Computer\"  earliest = 1 latest = now() |  eval eval_deleg_bit = floor(userAccountControl / pow(2, 19)) %2  | search  eval_deleg_bit = 1 | eval eval_dc_bit = floor(userAccountControl / pow(2, 13)) %2 |  eval eval_rodc_bit = floor(userAccountControl / pow(2, 26)) %2 | search eval_dc_bit = 0 AND eval_rodc_bit = 0  |  rename dNSHostName as Computer | fields Computer]\n| strcat TargetDomainName \"\\\\\" TargetUserName TargetFullName | stats values(LogonType), values(IpAddress), values(LogonProcessName) count by Computer, TargetFullName\n```\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"
                     },
                     {
                         "AllCodeBlocks":  [

                                           ],
                         "RelevanceScore":  1,
                         "CreatedAt":  "2025-07-28T16:21:02Z",
                         "PatternType":  "best_practice",
                         "Id":  "pattern_domain_management_4fccb4e0",
                         "Operation":  "read",
                         "Stars":  2114,
                         "UpdatedAt":  "2025-07-28T16:21:02Z",
                         "HasReadme":  true,
                         "BestPractices":  [
                                               "Repository contains documented best practices"
                                           ],
                         "Title":  "GitHub Repository: AutomatedLab/AutomatedLab",
                         "LastUpdated":  "2025-07-27T07:26:53Z",
                         "CodeTemplate":  "",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  380,
                         "PowerShellFiles":  null,
                         "Domain":  "domain_management",
                         "RepoSize":  39743,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "domain_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: AutomatedLab/AutomatedLab\n\n**Stars:** 2114 | **Language:** PowerShell | **Forks:** 380\n\n**Description:** AutomatedLab is a provisioning solution and framework that lets you deploy complex labs on HyperV and Azure with simple PowerShell scripts. It supports all Windows operating systems from 2008 R2 to 2022, some Linux distributions and various products like AD, Exchange, PKI, IIS, etc.\n\n**URL:** https://github.com/AutomatedLab/AutomatedLab\n\n**Clone URL:** https://github.com/Automat...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:21:02Z",
                                             "Url":  "https://github.com/AutomatedLab/AutomatedLab",
                                             "ScrapedAt":  "2025-07-28T16:21:02Z",
                                             "Id":  "899c493c-6ac4-4113-a08f-49e784095ae2",
                                             "Author":  "AutomatedLab",
                                             "Title":  "GitHub Repository: AutomatedLab/AutomatedLab"
                                         }
                                     ],
                         "Topics":  [
                                        "active",
                                        "active-directory",
                                        "automated-deployment",
                                        "azure",
                                        "deployment",
                                        "directory",
                                        "domain-controller",
                                        "exchange",
                                        "hyper",
                                        "hyperv",
                                        "lab-machine",
                                        "pki",
                                        "powershell",
                                        "scripted",
                                        "scripted-deployment"
                                    ],
                         "Content":  "# Repository: AutomatedLab/AutomatedLab\n\n**Stars:** 2114 | **Language:** PowerShell | **Forks:** 380\n\n**Description:** AutomatedLab is a provisioning solution and framework that lets you deploy complex labs on HyperV and Azure with simple PowerShell scripts. It supports all Windows operating systems from 2008 R2 to 2022, some Linux distributions and various products like AD, Exchange, PKI, IIS, etc.\n\n**URL:** https://github.com/AutomatedLab/AutomatedLab\n\n**Clone URL:** https://github.com/AutomatedLab/AutomatedLab.git\n\n**Topics:** active, active-directory, automated-deployment, azure, deployment, directory, domain-controller, exchange, hyper, hyperv, lab-machine, pki, powershell, scripted, scripted-deployment\n\n## README\n\n# AutomatedLab\n\nBuild | Status | Last Commit | Latest Release\n--- | --- | --- | ---\nDevelop | [![Build status dev](https://ci.appveyor.com/api/projects/status/9yynk81k3k05nasp/branch/develop?svg=true)](https://ci.appveyor.com/project/automatedlab/automatedlab) | [![GitHub last commit](https://img.shields.io/github/last-commit/AutomatedLab/AutomatedLab/develop.svg)](https://github.com/AutomatedLab/AutomatedLab/tree/develop/)\nMaster | [![Build status](https://ci.appveyor.com/api/projects/status/9yynk81k3k05nasp/branch/master?svg=true)](https://ci.appveyor.com/project/automatedlab/automatedlab) | [![GitHub last commit](https://img.shields.io/github/last-commit/AutomatedLab/AutomatedLab/master.svg)](https://github.com/AutomatedLab/AutomatedLab/tree/master/) | [![GitHub release](https://img.shields.io/github/release/AutomatedLab/AutomatedLab.svg)](https://github.com/AutomatedLab/AutomatedLab/releases)[![PowerShell Gallery](https://img.shields.io/powershellgallery/v/AutomatedLab.svg)](https://www.powershellgallery.com/packages/AutomatedLab/)\n\n[![GitHub issues](https://img.shields.io/github/issues/AutomatedLab/AutomatedLab.svg)](https://github.com/AutomatedLab/AutomatedLab/issues)\n[![Downloads](https://img.shields.io/github/downloads/AutomatedLab/AutomatedLab/total.svg?label=Downloads\u0026maxAge=999)](https://github.com/AutomatedLab/AutomatedLab/releases)\n[![PowerShell Gallery](https://img.shields.io/powershellgallery/dt/AutomatedLab.svg)](https://www.powershellgallery.com/packages/AutomatedLab/)\n\n## Project Summary\n\nAutomatedLab (AL) enables you to setup test and lab environments on Hyper-v or Azure with multiple products or just a single VM in a very short time. There are only two requirements you need to make sure: You need the DVD ISO images and a Hyper-V host or an Azure subscription.\n\n## Sponsors\n\nHuge thanks to our regular sponsor [@chocolatey](https://github.com/chocolatey)! In case you have lived under a rock (or worked mainly on non-Windows systems) for the past decade, Chocolatey\nis a brilliant package management system with a public binary package gallery as well as self-hosted options complete with enterprise-grade support. Give it a try with\na local lab environment - it could not be easier 😊 Start with a Domain Controller and a Certificate Authority to validate not only package distribution, but also digital signatures. Our samples\nsuch as the [NuGet server](https://automatedlab.org/en/latest/Wiki/SampleScripts/Scenarios/en-us/NuGetServer/) or [Azure DevOps](https://github.com/dsccommunity/DscWorkshop/tree/main/Lab) all include galleries that Chocolatey can use for publishing and downloading packages.\n\n### Requirements\n\nApart from the module itself your system needs to meet the following requirements:\n\n- Windows Management Framework 5+ (Windows)\n- .NET 4.7.1 (Windows PowerShell) or .NET Core 2.x (PowerShell 6+)\n- OS\n  - Windows Server 2012 R2+/Windows 8.1+ (Hyper-V, Azure)\n  - Linux (Azure)\n  - macOS (Azure, best-effort support since neither @raandree nor @nyanhp have Apple devices)\n- Recommended OS language is en-us\n- Admin privileges are required on Windows\n- ISO files for all operating systems (Hyper-V only) and roles to be deployed (Hyper-V, Azure)\n- Intel VT-x or AMD/V capable CPU\n- A decent amount of RAM\n- Low-Latency high-throughput storage (No spinning disks please, as there are issues related to them)\n\n#### Windows\n\n- Windows Management Framework 5+ or ideally PowerShell 7\n- Windows Server 2012 R2+/Windows 8.1+\n- Recommended OS language is en-us\n- Admin privileges are required\n\n#### Linux, macOS\n\n- Fedora, Ubuntu, Ubuntu WSL \u0026 Azure Cloud Shell supported\n- macOS supported on best effort due to lack of Apple devices - feel free to sponsor two though :D\n- Tested on Ubuntu and Fedora. Due to fragmented nature of Linux distributions, we cannot support much else.\n- PowerShell Core 6+\n- SSH or gss-ntlmssp to enable remoting (*mandatory - no remoting, no way for AutomatedLab to do its thing*)\n  - If in doubt, try to `Install-Module PSWSMAN; Install-WSMAN` - no success warranted\n- IP and route commands available\n- **Azure subscription**\n  - At the moment, AutomatedLab only works using Azure when using Linux.\n  - KVM planned for a later date by virtue of libvirt.\n\n### Download AutomatedLab\n\nThere are two options installing AutomatedLab:\n\n- You can use the [MSI installer](https://github.com/AutomatedLab/AutomatedLab/releases) published on GitHub.\n- Or you install from the [PowerShell Gallery](https://www.powershellgallery.com/packages/AutomatedLab/) using the cmdlet Install-Module. Please refer to the [wiki](https://automatedlab.org/en/latest) for some details.\n\n### [1. Installation](https://automatedlab.org/en/latest/Wiki/Basic/install/)\n\n### [2. Getting started](https://automatedlab.org/en/latest/Wiki/Basic/gettingstarted/)\n\n### [3. Contributing](/CONTRIBUTING.md)\n\n### [Version History](/CHANGELOG.md)\n\n### Supported products\n\nThis solution supports setting up virtual machines with the following products\n\n- Windows 7, 2008 R2, 8 / 8.1 and 2012 / 2012 R2, 10 / 2016, 2019, 2022\n- SQL Server 2008, 2008R2, 2012, 2014, 2016, 2017, 2019\n- Visual Studio 2012, 2013, 2015\n- Team Foundation Services 2018, Azure DevOps Server\n- Exchange 2013, 2016, 2019\n- System Center Orchestrator 2012\n- System Center Operations Manager 2019\n- System Center Service Manager 2019\n- Microsoft Endpoint Manager Configuration Manager 1902 (and newer)\n- MDT\n- ProGet (Private PowerShell Gallery)\n- Office 2013, 2016, 2019\n- DSC Pull Server (with SQL Reporting)\n- Dynamics 365\n- Remote Desktop Services including HTML5 web client\n\n### Feature List\n\n- AutomatedLab (AL) makes the setup of labs extremely easy. Setting up a lab with just a single machine is [only 3 lines](/LabSources/SampleScripts/Introduction/01%20Single%20Win10%20Client.ps1). And even [complex labs](/LabSources/SampleScripts/HyperV/BigLab%202012R2%20EX%20SQL%20ORCH%20VS%20OFF.ps1) can be defined with about 100 lines (see [sample scripts](https://github.com/AutomatedLab/AutomatedLab/tree/master/LabSources/SampleScripts)).\n- Labs on Azure can be connected to each other or connected to a Hyper-V lab [using a single command](https://github.com/AutomatedLab/AutomatedLab/wiki/Connect-on-premises-and-cloud-labs).\n- AL can be used to setup scenarios to demo a [PowerShell Gallery using Inedo ProGet](/LabSources/SampleScripts/Scenarios/ProGet%20Lab%20-%20HyperV.ps1), [PowerShell DSC Pull Server scenarios](/LabSources/SampleScripts/Scenarios/DSC%20Pull%20Scenario%201%20(Pull%20Configuration).ps1), ADFS or a lab with [3 Active Directory forests trusting each other](/LabSources/SampleScripts/Scenarios/Multi-AD%20Forest%20with%20Trusts.ps1).\n- Create, restore and remove snapshots of some or all lab machines with one cmdlet (Checkpoint-LabVM, Restore-LabVMSnapshot, Remove-LabVMSnapshot).\n- Install Windows Features on one, some or all lab machines with one line of code (Install-LabWindowsFeature).\n- Install software to a bunch of lab machines with just one cmdlet (Install-LabSoftwarePackages). You only need to know the argument to make the MSI or EXE go into silent installation mode. This can also work in parallel thanks to PowerShell workflows.\n- Run any custom activity (Script or ScriptBlock) on a number of lab machines (Invoke-LabCommand). You do not have to care about credentials or double-hop authentication issues as CredSsp is always enabled and can be used with the UseCredSsp switch.\n- Creating a [virtual environment that is connected to the internet](/LabSources/SampleScripts/Introduction/05%20Single%20domain-joined%20server%20(internet%20facing).ps1) was never easier. The only requirements are defining an external facing virtual switch and a machine with two network cards that acts as the router. AL takes care about all the configuration details like setting the gateway on all machines and also the DNS settings (see introduction script [05 Single domain-joined server (internet facing).ps1](/LabSources/SampleScripts/Introduction/05%20Single%20domain-joined%20server%20(internet%20facing).ps1)).\n- AL offers offline patching with a single command. As all machines a based on one disk per OS, it is much more efficient to patch the ISO files that are used to create the base images (Update-LabIsoImage). See script [11 ISO Offline Patching.ps1](/LabSources/SampleScripts/Introduction/11%20ISO%20Offline%20Patching.ps1) for more details.\n- If a lab is no longer required, one command is enough to remove everything to be ready to start from scratch (Remove-Lab)\n\n### Local build\n\nWhile we frequently release prereleases to the PowerShell Gallery, you might be interested\nto build the entire module locally yourself.\n\nWhile the steps remain the same, the prerequisites are slightly different on Windows and Linux\n\nWindows:\n  - WiX 3 targets installed properly (!)\n  - .NET SDKs 4.6.2 and 6.0\nLinux:\n  - .NET 6.0\n\nAfter the prerequisites are satisfied, you can:\n- `./.build/01-prerequisites.ps1`\n- `./.build/02-build.ps1`\n- `./.build/03-validate.ps1` - Optionally validate the built module\n\nThe fourth step, publishing, relies on AppVeyor. The built module will be stores in `./publish`,\nthe installer can be found in `./Install/bin\n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "﻿$ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n$PrimaryModule = Get-ChildItem -Path $PSScriptRoot -Filter \u0027*.psd1\u0027 -Recurse -ErrorAction SilentlyContinue -Depth 1\nif (-not $PrimaryModule) {\n    throw \"Path $PSScriptRoot doesn\u0027t contain PSD1 files. Failing tests.\"\n}\nif ($PrimaryModule.Count -ne 1) {\n    throw \u0027More than one PSD1 files detected. Failing tests.\u0027\n}\n$PSDInformation = Import-PowerShellDataFile -Path $PrimaryModule.FullName\n$RequiredModules = @(\n    \u0027Pester\u0027\n    \u0027PSWriteColor\u0027\n    \u0027PSParseHTML\u0027\n    if ($PSDInformation.RequiredModules) {\n        $PSDInformation.RequiredModules\n    }\n)\nforeach ($Module in $RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        $Exists = Get-Module -ListAvailable -Name $Module.ModuleName\n        if (-not $Exists) {\n            Write-Warning \"$ModuleName - Downloading $($Module.ModuleName) from PSGallery\"\n            Install-Module -Name $Module.ModuleName -Force -SkipPublisherCheck\n        }\n    } else {\n        $Exists = Get-Module -ListAvailable $Module -ErrorAction SilentlyContinue\n        if (-not $Exists) {\n            Install-Module -Name $Module -Force -SkipPublisherCheck\n        }\n    }\n}\n\nWrite-Color \u0027ModuleName: \u0027, $ModuleName, \u0027 Version: \u0027, $PSDInformation.ModuleVersion -Color Yellow, Green, Yellow, Green -LinesBefore 2\nWrite-Color \u0027PowerShell Version: \u0027, $PSVersionTable.PSVersion -Color Yellow, Green\nWrite-Color \u0027PowerShell Edition: \u0027, $PSVersionTable.PSEdition -Color Yellow, Green\nWrite-Color \u0027Required modules: \u0027 -Color Yellow\nforeach ($Module in $PSDInformation.RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        Write-Color \u0027   [\u003e] \u0027, $Module.ModuleName, \u0027 Version: \u0027, $Module.ModuleVersion -Color Yellow, Green, Yellow, Green\n    } else {\n        Write-Color \u0027   [\u003e] \u0027, $Module -Color Yellow, Green\n    }\n}\nWrite-Color\n\nImport-Module $PSScriptRoot\\*.psd1 -Force\nImport-Module Pester -Force\n$Configuration = [PesterConfiguration]::Default\n$Configuration.Run.Path = \"$PSScriptRoot\\Tests\"\n$Configuration.Run.Exit = $true\n$Configuration.Should.ErrorAction = \u0027Continue\u0027\n$Configuration.CodeCoverage.Enabled = $false\n$Configuration.Output.Verbosity = \u0027Detailed\u0027\n$Result = Invoke-Pester -Configuration $Configuration\n#$result = Invoke-Pester -Script $PSScriptRoot\\Tests -Verbose -Output Detailed #-EnableExit\n\nif ($Result.FailedCount -gt 0) {\n    throw \"$($Result.FailedCount) tests failed.\"\n}",
                                               "﻿@{\n    AliasesToExport      = @(\u0027Get-GPOZaurrSysvol\u0027, \u0027Get-GPOZaurrFilesPolicyDefinitions\u0027, \u0027Show-GPOZaurr\u0027, \u0027Show-GPO\u0027, \u0027Find-GPO\u0027, \u0027Remove-GPOZaurrOrphaned\u0027)\n    Author               = \u0027Przemyslaw Klys\u0027\n    CmdletsToExport      = @()\n    CompanyName          = \u0027Evotec\u0027\n    CompatiblePSEditions = @(\u0027Desktop\u0027)\n    Copyright            = \u0027(c) 2011 - 2024 Przemyslaw Klys @ Evotec. All rights reserved.\u0027\n    Description          = \u0027Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them.\u0027\n    FunctionsToExport    = @(\u0027Add-GPOPermission\u0027, \u0027Add-GPOZaurrPermission\u0027, \u0027Backup-GPOZaurr\u0027, \u0027Clear-GPOZaurrSysvolDFSR\u0027, \u0027ConvertFrom-CSExtension\u0027, \u0027Export-GPOZaurrContent\u0027, \u0027Find-CSExtension\u0027, \u0027Get-GPOZaurr\u0027, \u0027Get-GPOZaurrAD\u0027, \u0027Get-GPOZaurrBackupInformation\u0027, \u0027Get-GPOZaurrBroken\u0027, \u0027Get-GPOZaurrBrokenLink\u0027, \u0027Get-GPOZaurrDictionary\u0027, \u0027Get-GPOZaurrDuplicateObject\u0027, \u0027Get-GPOZaurrFiles\u0027, \u0027Get-GPOZaurrFilesPolicyDefinition\u0027, \u0027Get-GPOZaurrFolders\u0027, \u0027Get-GPOZaurrInheritance\u0027, \u0027Get-GPOZaurrLegacyFiles\u0027, \u0027Get-GPOZaurrLink\u0027, \u0027Get-GPOZaurrLinkSummary\u0027, \u0027Get-GPOZaurrMissingFiles\u0027, \u0027Get-GPOZaurrNetLogon\u0027, \u0027Get-GPOZaurrOrganizationalUnit\u0027, \u0027Get-GPOZaurrOwner\u0027, \u0027Get-GPOZaurrPassword\u0027, \u0027Get-GPOZaurrPermission\u0027, \u0027Get-GPOZaurrPermissionAnalysis\u0027, \u0027Get-GPOZaurrPermissionConsistency\u0027, \u0027Get-GPOZaurrPermissionIssue\u0027, \u0027Get-GPOZaurrPermissionRoot\u0027, \u0027Get-GPOZaurrPermissionSummary\u0027, \u0027Get-GPOZaurrRedirect\u0027, \u0027Get-GPOZaurrSysvolDFSR\u0027, \u0027Get-GPOZaurrUpdates\u0027, \u0027Get-GPOZaurrWMI\u0027, \u0027Invoke-GPOZaurr\u0027, \u0027Invoke-GPOZaurrContent\u0027, \u0027Invoke-GPOZaurrPermission\u0027, \u0027Invoke-GPOZaurrSupport\u0027, \u0027New-GPOZaurrWMI\u0027, \u0027Optimize-GPOZaurr\u0027, \u0027Remove-GPOPermission\u0027, \u0027Remove-GPOZaurr\u0027, \u0027Remove-GPOZaurrBroken\u0027, \u0027Remove-GPOZaurrDuplicateObject\u0027, \u0027Remove-GPOZaurrFolders\u0027, \u0027Remove-GPOZaurrLegacyFiles\u0027, \u0027Remove-GPOZaurrLinkEmptyOU\u0027, \u0027Remove-GPOZaurrPermission\u0027, \u0027Remove-GPOZaurrWMI\u0027, \u0027Repair-GPOZaurrBrokenLink\u0027, \u0027Repair-GPOZaurrNetLogonOwner\u0027, \u0027Repair-GPOZaurrPermission\u0027, \u0027Repair-GPOZaurrPermissionConsistency\u0027, \u0027Restore-GPOZaurr\u0027, \u0027Save-GPOZaurrFiles\u0027, \u0027Set-GPOOwner\u0027, \u0027Set-GPOZaurrOwner\u0027, \u0027Set-GPOZaurrStatus\u0027, \u0027Skip-GroupPolicy\u0027)\n    GUID                 = \u0027f7d4c9e4-0298-4f51-ad77-e8e3febebbde\u0027\n    ModuleVersion        = \u00271.1.9\u0027\n    PowerShellVersion    = \u00275.1\u0027\n    PrivateData          = @{\n        PSData = @{\n            ExternalModuleDependencies = @(\u0027CimCmdlets\u0027, \u0027Microsoft.PowerShell.Management\u0027, \u0027Microsoft.PowerShell.Utility\u0027, \u0027Microsoft.PowerShell.Security\u0027)\n            ProjectUri                 = \u0027https://github.com/EvotecIT/GPOZaurr\u0027\n            Tags                       = @(\u0027Windows\u0027, \u0027ActiveDirectory\u0027, \u0027GPO\u0027, \u0027GroupPolicy\u0027)\n        }\n    }\n    RequiredModules      = @(@{\n            Guid          = \u00270b0ba5c5-ec85-4c2b-a718-874e55a8bc3f\u0027\n            ModuleName    = \u0027PSWriteColor\u0027\n            ModuleVersion = \u00271.0.1\u0027\n        }, @{\n            Guid          = \u0027ee272aa8-baaa-4edf-9f45-b6d6f7d844fe\u0027\n            ModuleName    = \u0027PSSharedGoods\u0027\n            ModuleVersion = \u00270.0.301\u0027\n        }, @{\n            Guid          = \u00279fc9fd61-7f11-4f4b-a527-084086f1905f\u0027\n            ModuleName    = \u0027ADEssentials\u0027\n            ModuleVersion = \u00270.0.226\u0027\n        }, @{\n            Guid          = \u0027a7bdf640-f5cb-4acf-9de0-365b322d245c\u0027\n            ModuleName    = \u0027PSWriteHTML\u0027\n            ModuleVersion = \u00271.27.0\u0027\n        }, \u0027CimCmdlets\u0027, \u0027Microsoft.PowerShell.Management\u0027, \u0027Microsoft.PowerShell.Utility\u0027, \u0027Microsoft.PowerShell.Security\u0027)\n    RootModule           = \u0027GPOZaurr.psm1\u0027\n}",
                                               "﻿#Get public and private function definition files.\n$Public = @( Get-ChildItem -Path $PSScriptRoot\\Public\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Private = @( Get-ChildItem -Path $PSScriptRoot\\Private\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n\n$AssemblyFolders = Get-ChildItem -Path $PSScriptRoot\\Lib -Directory -ErrorAction SilentlyContinue\nif ($AssemblyFolders.BaseName -contains \u0027Standard\u0027) {\n    $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Standard\\*.dll -ErrorAction SilentlyContinue )\n} else {\n    if ($PSEdition -eq \u0027Core\u0027) {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Core\\*.dll -ErrorAction SilentlyContinue )\n    } else {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Default\\*.dll -ErrorAction SilentlyContinue )\n    }\n}\n$FoundErrors = @(\n    Foreach ($Import in @($Assembly)) {\n        try {\n            Add-Type -Path $Import.Fullname -ErrorAction Stop\n        } catch [System.Reflection.ReflectionTypeLoadException] {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        } catch {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        }\n    }\n    #Dot source the files\n    Foreach ($Import in @($Private + $Public)) {\n        Try {\n            . $Import.Fullname\n        } Catch {\n            Write-Error -Message \"Failed to import functions from $($import.Fullname): $_\"\n            $true\n        }\n    }\n)\n\nif ($FoundErrors.Count -gt 0) {\n    $ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n    Write-Warning \"Importing module $ModuleName failed. Fix errors before continuing.\"\n    break\n}\n\nExport-ModuleMember -Function \u0027*\u0027 -Alias \u0027*\u0027"
                                           ],
                         "RelevanceScore":  0.86219387204838949,
                         "CreatedAt":  "2025-07-28T16:21:07Z",
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_user_management_154a279f",
                         "Operation":  "read",
                         "Stars":  1048,
                         "UpdatedAt":  "2025-07-28T16:21:07Z",
                         "HasReadme":  true,
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub Repository: EvotecIT/GPOZaurr",
                         "LastUpdated":  "2025-07-17T07:58:09Z",
                         "CodeTemplate":  "﻿$ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n$PrimaryModule = Get-ChildItem -Path $PSScriptRoot -Filter \u0027*.psd1\u0027 -Recurse -ErrorAction SilentlyContinue -Depth 1\nif (-not $PrimaryModule) {\n    throw \"Path $PSScriptRoot doesn\u0027t contain PSD1 files. Failing tests.\"\n}\nif ($PrimaryModule.Count -ne 1) {\n    throw \u0027More than one PSD1 files detected. Failing tests.\u0027\n}\n$PSDInformation = Import-PowerShellDataFile -Path $PrimaryModule.FullName\n$RequiredModules = @(\n    \u0027Pester\u0027\n    \u0027PSWriteColor\u0027\n    \u0027PSParseHTML\u0027\n    if ($PSDInformation.RequiredModules) {\n        $PSDInformation.RequiredModules\n    }\n)\nforeach ($Module in $RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        $Exists = Get-Module -ListAvailable -Name $Module.ModuleName\n        if (-not $Exists) {\n            Write-Warning \"$ModuleName - Downloading $($Module.ModuleName) from PSGallery\"\n            Install-Module -Name $Module.ModuleName -Force -SkipPublisherCheck\n        }\n    } else {\n        $Exists = Get-Module -ListAvailable $Module -ErrorAction SilentlyContinue\n        if (-not $Exists) {\n            Install-Module -Name $Module -Force -SkipPublisherCheck\n        }\n    }\n}\n\nWrite-Color \u0027ModuleName: \u0027, $ModuleName, \u0027 Version: \u0027, $PSDInformation.ModuleVersion -Color Yellow, Green, Yellow, Green -LinesBefore 2\nWrite-Color \u0027PowerShell Version: \u0027, $PSVersionTable.PSVersion -Color Yellow, Green\nWrite-Color \u0027PowerShell Edition: \u0027, $PSVersionTable.PSEdition -Color Yellow, Green\nWrite-Color \u0027Required modules: \u0027 -Color Yellow\nforeach ($Module in $PSDInformation.RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        Write-Color \u0027   [\u003e] \u0027, $Module.ModuleName, \u0027 Version: \u0027, $Module.ModuleVersion -Color Yellow, Green, Yellow, Green\n    } else {\n        Write-Color \u0027   [\u003e] \u0027, $Module -Color Yellow, Green\n    }\n}\nWrite-Color\n\nImport-Module $PSScriptRoot\\*.psd1 -Force\nImport-Module Pester -Force\n$Configuration = [PesterConfiguration]::Default\n$Configuration.Run.Path = \"$PSScriptRoot\\Tests\"\n$Configuration.Run.Exit = $true\n$Configuration.Should.ErrorAction = \u0027Continue\u0027\n$Configuration.CodeCoverage.Enabled = $false\n$Configuration.Output.Verbosity = \u0027Detailed\u0027\n$Result = Invoke-Pester -Configuration $Configuration\n#$result = Invoke-Pester -Script $PSScriptRoot\\Tests -Verbose -Output Detailed #-EnableExit\n\nif ($Result.FailedCount -gt 0) {\n    throw \"$($Result.FailedCount) tests failed.\"\n}",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  107,
                         "PowerShellFiles":  3,
                         "Domain":  "user_management",
                         "RepoSize":  1753,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "user_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: EvotecIT/GPOZaurr\n\n**Stars:** 1048 | **Language:** PowerShell | **Forks:** 107\n\n**Description:** Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them. \n\n**URL:** https://github.com/EvotecIT/GPOZaurr\n\n**Clone URL:** https://github.com/EvotecIT/GPOZaurr.git\n\n**Topics:** activedirectory, gpo, group-policy, hacktoberfest, powershell\n\n## README\n\n﻿\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://www.po...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:21:07Z",
                                             "Url":  "https://github.com/EvotecIT/GPOZaurr",
                                             "ScrapedAt":  "2025-07-28T16:21:07Z",
                                             "Id":  "a0a4d178-4d85-4d09-8f2a-6bcfb25c3ff4",
                                             "Author":  "EvotecIT",
                                             "Title":  "GitHub Repository: EvotecIT/GPOZaurr"
                                         }
                                     ],
                         "Topics":  [
                                        "activedirectory",
                                        "gpo",
                                        "group-policy",
                                        "hacktoberfest",
                                        "powershell"
                                    ],
                         "Content":  "# Repository: EvotecIT/GPOZaurr\n\n**Stars:** 1048 | **Language:** PowerShell | **Forks:** 107\n\n**Description:** Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them. \n\n**URL:** https://github.com/EvotecIT/GPOZaurr\n\n**Clone URL:** https://github.com/EvotecIT/GPOZaurr.git\n\n**Topics:** activedirectory, gpo, group-policy, hacktoberfest, powershell\n\n## README\n\n﻿\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/v/GPOZaurr.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/vpre/GPOZaurr.svg?label=powershell%20gallery%20preview\u0026colorB=yellow\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/github/license/EvotecIT/GPOZaurr.svg\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/p/GPOZaurr.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/github/languages/top/evotecit/GPOZaurr.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/github/languages/code-size/evotecit/GPOZaurr.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/GPOZaurr\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/dt/GPOZaurr.svg\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://twitter.com/PrzemyslawKlys\"\u003e\u003cimg src=\"https://img.shields.io/twitter/follow/PrzemyslawKlys.svg?label=Twitter%20%40PrzemyslawKlys\u0026style=social\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://evotec.xyz/hub\"\u003e\u003cimg src=\"https://img.shields.io/badge/Blog-evotec.xyz-2A6496.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.linkedin.com/in/pklys\"\u003e\u003cimg src=\"https://img.shields.io/badge/LinkedIn-pklys-0077B5.svg?logo=LinkedIn\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n# GPOZaurr\n\nGroup Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them.\n**GPOZaurr** provides 360 degrees of information about Group Policies and their settings.\n\nJust a single command (`Invoke-GPOZaurr`) provides following reports:\n\n- GPOBroken\n- GPOBrokenLink\n- GPOOwners\n- GPOConsistency\n- GPODuplicates\n- GPOOrganizationalUnit\n- GPOList\n- GPOLinks\n- GPOPassword\n- GPOPermissions\n- GPOPermissionsAdministrative\n- GPOPermissionsRead\n- GPOPermissionsRoot\n- GPOPermissionsUnknown\n- GPOFiles\n- GPOBlockedInheritance\n- GPOAnalysis\n- GPOUpdates\n- NetLogonOwners\n- NetLogonPermissions\n- SysVolLegacyFiles\n\nBut that\u0027s not all.\nThere are over 50 other commands available that make it even more powerful helping with day to day tasks to manage Group Policies.\n\nTo understand the usage of `Invoke-GPOZaurr` I\u0027ve created blog post you may find useful\n\n- [The only command you will ever need to understand and fix your Group Policies (GPO)](https://evotec.xyz/the-only-command-you-will-ever-need-to-understand-and-fix-your-group-policies-gpo/)\n\n## Installing\n\nGPOZaurr requires `RSAT` installed to provide results. If you don\u0027t have them you can install them as below. Keep in mind it also installs GUI tools so it shouldn\u0027t be installed on user workstations.\n\n```powershell\n# Windows 10 Latest\nAdd-WindowsCapability -Online -Name \u0027Rsat.ActiveDirectory.DS-LDS.Tools~~~~*******\u0027\nAdd-WindowsCapability -Online -Name \u0027Rsat.GroupPolicy.Management.Tools~~~~*******\u0027\n```\n\nFinally just install module:\n\n```powershell\nInstall-Module -Name GPOZaurr -AllowClobber -Force\n```\n\nForce and AllowClobber aren\u0027t necessary, but they do skip errors in case some appear.\n\n## Updating\n\n```powershell\nUpdate-Module -Name GPOZaurr\n```\n\nThat\u0027s it. Whenever there\u0027s a new version, you run the command, and you can enjoy it. Remember that you may need to close, reopen PowerShell session if you have already used module before updating it.\n\n**The essential thing** is if something works for you on production, keep using it till you test the new version on a test computer. I do changes that may not be big, but big enough that auto-update may break your code. For example, small rename to a parameter and your code stops working! Be responsible!\n\n## PowerShell Files (3 files)\n\n### GPOZaurr.Tests.ps1\n\n**Path:** GPOZaurr.Tests.ps1\n\n``powershell\n﻿$ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n$PrimaryModule = Get-ChildItem -Path $PSScriptRoot -Filter \u0027*.psd1\u0027 -Recurse -ErrorAction SilentlyContinue -Depth 1\nif (-not $PrimaryModule) {\n    throw \"Path $PSScriptRoot doesn\u0027t contain PSD1 files. Failing tests.\"\n}\nif ($PrimaryModule.Count -ne 1) {\n    throw \u0027More than one PSD1 files detected. Failing tests.\u0027\n}\n$PSDInformation = Import-PowerShellDataFile -Path $PrimaryModule.FullName\n$RequiredModules = @(\n    \u0027Pester\u0027\n    \u0027PSWriteColor\u0027\n    \u0027PSParseHTML\u0027\n    if ($PSDInformation.RequiredModules) {\n        $PSDInformation.RequiredModules\n    }\n)\nforeach ($Module in $RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        $Exists = Get-Module -ListAvailable -Name $Module.ModuleName\n        if (-not $Exists) {\n            Write-Warning \"$ModuleName - Downloading $($Module.ModuleName) from PSGallery\"\n            Install-Module -Name $Module.ModuleName -Force -SkipPublisherCheck\n        }\n    } else {\n        $Exists = Get-Module -ListAvailable $Module -ErrorAction SilentlyContinue\n        if (-not $Exists) {\n            Install-Module -Name $Module -Force -SkipPublisherCheck\n        }\n    }\n}\n\nWrite-Color \u0027ModuleName: \u0027, $ModuleName, \u0027 Version: \u0027, $PSDInformation.ModuleVersion -Color Yellow, Green, Yellow, Green -LinesBefore 2\nWrite-Color \u0027PowerShell Version: \u0027, $PSVersionTable.PSVersion -Color Yellow, Green\nWrite-Color \u0027PowerShell Edition: \u0027, $PSVersionTable.PSEdition -Color Yellow, Green\nWrite-Color \u0027Required modules: \u0027 -Color Yellow\nforeach ($Module in $PSDInformation.RequiredModules) {\n    if ($Module -is [System.Collections.IDictionary]) {\n        Write-Color \u0027   [\u003e] \u0027, $Module.ModuleName, \u0027 Version: \u0027, $Module.ModuleVersion -Color Yellow, Green, Yellow, Green\n    } else {\n        Write-Color \u0027   [\u003e] \u0027, $Module -Color Yellow, Green\n    }\n}\nWrite-Color\n\nImport-Module $PSScriptRoot\\*.psd1 -Force\nImport-Module Pester -Force\n$Configuration = [PesterConfiguration]::Default\n$Configuration.Run.Path = \"$PSScriptRoot\\Tests\"\n$Configuration.Run.Exit = $true\n$Configuration.Should.ErrorAction = \u0027Continue\u0027\n$Configuration.CodeCoverage.Enabled = $false\n$Configuration.Output.Verbosity = \u0027Detailed\u0027\n$Result = Invoke-Pester -Configuration $Configuration\n#$result = Invoke-Pester -Script $PSScriptRoot\\Tests -Verbose -Output Detailed #-EnableExit\n\nif ($Result.FailedCount -gt 0) {\n    throw \"$($Result.FailedCount) tests failed.\"\n}\n``n\n### GPOZaurr.psd1\n\n**Path:** GPOZaurr.psd1\n\n``powershell\n﻿@{\n    AliasesToExport      = @(\u0027Get-GPOZaurrSysvol\u0027, \u0027Get-GPOZaurrFilesPolicyDefinitions\u0027, \u0027Show-GPOZaurr\u0027, \u0027Show-GPO\u0027, \u0027Find-GPO\u0027, \u0027Remove-GPOZaurrOrphaned\u0027)\n    Author               = \u0027Przemyslaw Klys\u0027\n    CmdletsToExport      = @()\n    CompanyName          = \u0027Evotec\u0027\n    CompatiblePSEditions = @(\u0027Desktop\u0027)\n    Copyright            = \u0027(c) 2011 - 2024 Przemyslaw Klys @ Evotec. All rights reserved.\u0027\n    Description          = \u0027Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them.\u0027\n    FunctionsToExport    = @(\u0027Add-GPOPermission\u0027, \u0027Add-GPOZaurrPermission\u0027, \u0027Backup-GPOZaurr\u0027, \u0027Clear-GPOZaurrSysvolDFSR\u0027, \u0027ConvertFrom-CSExtension\u0027, \u0027Export-GPOZaurrContent\u0027, \u0027Find-CSExtension\u0027, \u0027Get-GPOZaurr\u0027, \u0027Get-GPOZaurrAD\u0027, \u0027Get-GPOZaurrBackupInformation\u0027, \u0027Get-GPOZaurrBroken\u0027, \u0027Get-GPOZaurrBrokenLink\u0027, \u0027Get-GPOZaurrDictionary\u0027, \u0027Get-GPOZaurrDuplicateObject\u0027, \u0027Get-GPOZaurrFiles\u0027, \u0027Get-GPOZaurrFilesPolicyDefinition\u0027, \u0027Get-GPOZaurrFolders\u0027, \u0027Get-GPOZaurrInheritance\u0027, \u0027Get-GPOZaurrLegacyFiles\u0027, \u0027Get-GPOZaurrLink\u0027, \u0027Get-GPOZaurrLinkSummary\u0027, \u0027Get-GPOZaurrMissingFiles\u0027, \u0027Get-GPOZaurrNetLogon\u0027, \u0027Get-GPOZaurrOrganizationalUnit\u0027, \u0027Get-GPOZaurrOwner\u0027, \u0027Get-GPOZaurrPassword\u0027, \u0027Get-GPOZaurrPermission\u0027, \u0027Get-GPOZaurrPermissionAnalysis\u0027, \u0027Get-GPOZaurrPermissionConsistency\u0027, \u0027Get-GPOZaurrPermissionIssue\u0027, \u0027Get-GPOZaurrPermissionRoot\u0027, \u0027Get-GPOZaurrPermissionSummary\u0027, \u0027Get-GPOZaurrRedirect\u0027, \u0027Get-GPOZaurrSysvolDFSR\u0027, \u0027Get-GPOZaurrUpdates\u0027, \u0027Get-GPOZaurrWMI\u0027, \u0027Invoke-GPOZaurr\u0027, \u0027Invoke-GPOZaurrContent\u0027, \u0027Invoke-GPOZaurrPermission\u0027, \u0027Invoke-GPOZaurrSupport\u0027, \u0027New-GPOZaurrWMI\u0027, \u0027Optimize-GPOZaurr\u0027, \u0027Remove-GPOPermission\u0027, \u0027Remove-GPOZaurr\u0027, \u0027Remove-GPOZaurrBroken\u0027, \u0027Remove-GPOZaurrDuplicateObject\u0027, \u0027Remove-GPOZaurrFolders\u0027, \u0027Remove-GPOZaurrLegacyFiles\u0027, \u0027Remove-GPOZaurrLinkEmptyOU\u0027, \u0027Remove-GPOZaurrPermission\u0027, \u0027Remove-GPOZaurrWMI\u0027, \u0027Repair-GPOZaurrBrokenLink\u0027, \u0027Repair-GPOZaurrNetLogonOwner\u0027, \u0027Repair-GPOZaurrPermission\u0027, \u0027Repair-GPOZaurrPermissionConsistency\u0027, \u0027Restore-GPOZaurr\u0027, \u0027Save-GPOZaurrFiles\u0027, \u0027Set-GPOOwner\u0027, \u0027Set-GPOZaurrOwner\u0027, \u0027Set-GPOZaurrStatus\u0027, \u0027Skip-GroupPolicy\u0027)\n    GUID                 = \u0027f7d4c9e4-0298-4f51-ad77-e8e3febebbde\u0027\n    ModuleVersion        = \u00271.1.9\u0027\n    PowerShellVersion    = \u00275.1\u0027\n    PrivateData          = @{\n        PSData = @{\n            ExternalModuleDependencies = @(\u0027CimCmdlets\u0027, \u0027Microsoft.PowerShell.Management\u0027, \u0027Microsoft.PowerShell.Utility\u0027, \u0027Microsoft.PowerShell.Security\u0027)\n            ProjectUri                 = \u0027https://github.com/EvotecIT/GPOZaurr\u0027\n            Tags                       = @(\u0027Windows\u0027, \u0027ActiveDirectory\u0027, \u0027GPO\u0027, \u0027GroupPolicy\u0027)\n        }\n    }\n    RequiredModules      = @(@{\n            Guid          = \u00270b0ba5c5-ec85-4c2b-a718-874e55a8bc3f\u0027\n            ModuleName    = \u0027PSWriteColor\u0027\n            ModuleVersion = \u00271.0.1\u0027\n        }, @{\n            Guid          = \u0027ee272aa8-baaa-4edf-9f45-b6d6f7d844fe\u0027\n            ModuleName    = \u0027PSSharedGoods\u0027\n            ModuleVersion = \u00270.0.301\u0027\n        }, @{\n            Guid          = \u00279fc9fd61-7f11-4f4b-a527-084086f1905f\u0027\n            ModuleName    = \u0027ADEssentials\u0027\n            ModuleVersion = \u00270.0.226\u0027\n        }, @{\n            Guid          = \u0027a7bdf640-f5cb-4acf-9de0-365b322d245c\u0027\n            ModuleName    = \u0027PSWriteHTML\u0027\n            ModuleVersion = \u00271.27.0\u0027\n        }, \u0027CimCmdlets\u0027, \u0027Microsoft.PowerShell.Management\u0027, \u0027Microsoft.PowerShell.Utility\u0027, \u0027Microsoft.PowerShell.Security\u0027)\n    RootModule           = \u0027GPOZaurr.psm1\u0027\n}\n``n\n### GPOZaurr.psm1\n\n**Path:** GPOZaurr.psm1\n\n``powershell\n﻿#Get public and private function definition files.\n$Public = @( Get-ChildItem -Path $PSScriptRoot\\Public\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Private = @( Get-ChildItem -Path $PSScriptRoot\\Private\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n\n$AssemblyFolders = Get-ChildItem -Path $PSScriptRoot\\Lib -Directory -ErrorAction SilentlyContinue\nif ($AssemblyFolders.BaseName -contains \u0027Standard\u0027) {\n    $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Standard\\*.dll -ErrorAction SilentlyContinue )\n} else {\n    if ($PSEdition -eq \u0027Core\u0027) {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Core\\*.dll -ErrorAction SilentlyContinue )\n    } else {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Default\\*.dll -ErrorAction SilentlyContinue )\n    }\n}\n$FoundErrors = @(\n    Foreach ($Import in @($Assembly)) {\n        try {\n            Add-Type -Path $Import.Fullname -ErrorAction Stop\n        } catch [System.Reflection.ReflectionTypeLoadException] {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        } catch {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        }\n    }\n    #Dot source the files\n    Foreach ($Import in @($Private + $Public)) {\n        Try {\n            . $Import.Fullname\n        } Catch {\n            Write-Error -Message \"Failed to import functions from $($import.Fullname): $_\"\n            $true\n        }\n    }\n)\n\nif ($FoundErrors.Count -gt 0) {\n    $ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n    Write-Warning \"Importing module $ModuleName failed. Fix errors before continuing.\"\n    break\n}\n\nExport-ModuleMember -Function \u0027*\u0027 -Alias \u0027*\u0027\n``n\n"
                     },
                     {
                         "AllCodeBlocks":  [
                                               "﻿@{\n    AliasesToExport   = @()\n    Author            = \u0027Przemyslaw Klys\u0027\n    CmdletsToExport   = @()\n    CompanyName       = \u0027Evotec\u0027\n    Copyright         = \u0027(c) 2011 - 2022 Przemyslaw Klys @ Evotec. All rights reserved.\u0027\n    Description       = \u0027PSWinReportingV2 is fast and efficient Event Viewing, Event Reporting and Event Collecting tool. It\u0027\u0027s version 2 of known PSWinReporting PowerShell module and can work next to it.\u0027\n    FunctionsToExport = @(\u0027Add-EventsDefinitions\u0027, \u0027Add-WinTaskScheduledForwarder\u0027, \u0027Find-Events\u0027, \u0027New-WinSubscriptionTemplates\u0027, \u0027Remove-WinTaskScheduledForwarder\u0027, \u0027Start-WinNotifications\u0027, \u0027Start-WinReporting\u0027, \u0027Start-WinSubscriptionService\u0027)\n    GUID              = \u0027ea2bd8d2-cca1-4dc3-9e1c-ff80b06e8fbe\u0027\n    ModuleVersion     = \u00272.0.23\u0027\n    PrivateData       = @{\n        PSData = @{\n            Tags       = @(\u0027PSWinReporting\u0027, \u0027ActiveDirectory\u0027, \u0027Events\u0027, \u0027Reporting\u0027, \u0027Windows\u0027, \u0027EventLog\u0027)\n            ProjectUri = \u0027https://github.com/EvotecIT/PSWinReporting\u0027\n            IconUri    = \u0027https://evotec.xyz/wp-content/uploads/2018/10/PSWinReporting.png\u0027\n        }\n    }\n    RequiredModules   = @(@{\n            ModuleVersion = \u00271.0.22\u0027\n            ModuleName    = \u0027PSEventViewer\u0027\n            Guid          = \u00275df72a79-cdf6-4add-b38d-bcacf26fb7bc\u0027\n        }, @{\n            ModuleVersion = \u00270.0.254\u0027\n            ModuleName    = \u0027PSSharedGoods\u0027\n            Guid          = \u0027ee272aa8-baaa-4edf-9f45-b6d6f7d844fe\u0027\n        }, @{\n            ModuleVersion = \u00270.1.15\u0027\n            ModuleName    = \u0027PSWriteExcel\u0027\n            Guid          = \u002782232c6a-27f1-435d-a496-929f7221334b\u0027\n        }, @{\n            ModuleVersion = \u00270.0.180\u0027\n            ModuleName    = \u0027PSWriteHTML\u0027\n            Guid          = \u0027a7bdf640-f5cb-4acf-9de0-365b322d245c\u0027\n        })\n    RootModule        = \u0027PSWinReportingV2.psm1\u0027\n}",
                                               "#Get public and private function definition files.\n$Public = @( Get-ChildItem -Path $PSScriptRoot\\Public\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Private = @( Get-ChildItem -Path $PSScriptRoot\\Private\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Classes = @( Get-ChildItem -Path $PSScriptRoot\\Classes\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Enums = @( Get-ChildItem -Path $PSScriptRoot\\Enums\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n\n$AssemblyFolders = Get-ChildItem -Path $PSScriptRoot\\Lib -Directory -ErrorAction SilentlyContinue\nif ($AssemblyFolders.BaseName -contains \u0027Standard\u0027) {\n    $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Standard\\*.dll -ErrorAction SilentlyContinue )\n} else {\n    if ($PSEdition -eq \u0027Core\u0027) {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Core\\*.dll -ErrorAction SilentlyContinue )\n    } else {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Default\\*.dll -ErrorAction SilentlyContinue )\n    }\n}\n$FoundErrors = @(\n    Foreach ($Import in @($Assembly)) {\n        try {\n            Add-Type -Path $Import.Fullname -ErrorAction Stop\n        } catch [System.Reflection.ReflectionTypeLoadException] {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        } catch {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        }\n    }\n    #Dot source the files\n    Foreach ($Import in @($Private + $Public + $Classes + $Enums)) {\n        Try {\n            . $Import.Fullname\n        } Catch {\n            Write-Error -Message \"Failed to import functions from $($import.Fullname): $_\"\n            $true\n        }\n    }\n)\n\nif ($FoundErrors.Count -gt 0) {\n    $ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n    Write-Warning \"Importing module $ModuleName failed. Fix errors before continuing.\"\n    break\n}\n\nExport-ModuleMember -Function \u0027*\u0027 -Alias \u0027*\u0027"
                                           ],
                         "RelevanceScore":  0.78618222259572057,
                         "CreatedAt":  "2025-07-28T16:21:10Z",
                         "PatternType":  "common_mistake",
                         "Id":  "pattern_policy_management_1605ec04",
                         "Operation":  "read",
                         "Stars":  718,
                         "UpdatedAt":  "2025-07-28T16:21:10Z",
                         "HasReadme":  true,
                         "BestPractices":  [

                                           ],
                         "Title":  "GitHub Repository: EvotecIT/PSWinReporting",
                         "LastUpdated":  "2025-07-25T06:36:41Z",
                         "CodeTemplate":  "﻿@{\n    AliasesToExport   = @()\n    Author            = \u0027Przemyslaw Klys\u0027\n    CmdletsToExport   = @()\n    CompanyName       = \u0027Evotec\u0027\n    Copyright         = \u0027(c) 2011 - 2022 Przemyslaw Klys @ Evotec. All rights reserved.\u0027\n    Description       = \u0027PSWinReportingV2 is fast and efficient Event Viewing, Event Reporting and Event Collecting tool. It\u0027\u0027s version 2 of known PSWinReporting PowerShell module and can work next to it.\u0027\n    FunctionsToExport = @(\u0027Add-EventsDefinitions\u0027, \u0027Add-WinTaskScheduledForwarder\u0027, \u0027Find-Events\u0027, \u0027New-WinSubscriptionTemplates\u0027, \u0027Remove-WinTaskScheduledForwarder\u0027, \u0027Start-WinNotifications\u0027, \u0027Start-WinReporting\u0027, \u0027Start-WinSubscriptionService\u0027)\n    GUID              = \u0027ea2bd8d2-cca1-4dc3-9e1c-ff80b06e8fbe\u0027\n    ModuleVersion     = \u00272.0.23\u0027\n    PrivateData       = @{\n        PSData = @{\n            Tags       = @(\u0027PSWinReporting\u0027, \u0027ActiveDirectory\u0027, \u0027Events\u0027, \u0027Reporting\u0027, \u0027Windows\u0027, \u0027EventLog\u0027)\n            ProjectUri = \u0027https://github.com/EvotecIT/PSWinReporting\u0027\n            IconUri    = \u0027https://evotec.xyz/wp-content/uploads/2018/10/PSWinReporting.png\u0027\n        }\n    }\n    RequiredModules   = @(@{\n            ModuleVersion = \u00271.0.22\u0027\n            ModuleName    = \u0027PSEventViewer\u0027\n            Guid          = \u00275df72a79-cdf6-4add-b38d-bcacf26fb7bc\u0027\n        }, @{\n            ModuleVersion = \u00270.0.254\u0027\n            ModuleName    = \u0027PSSharedGoods\u0027\n            Guid          = \u0027ee272aa8-baaa-4edf-9f45-b6d6f7d844fe\u0027\n        }, @{\n            ModuleVersion = \u00270.1.15\u0027\n            ModuleName    = \u0027PSWriteExcel\u0027\n            Guid          = \u002782232c6a-27f1-435d-a496-929f7221334b\u0027\n        }, @{\n            ModuleVersion = \u00270.0.180\u0027\n            ModuleName    = \u0027PSWriteHTML\u0027\n            Guid          = \u0027a7bdf640-f5cb-4acf-9de0-365b322d245c\u0027\n        })\n    RootModule        = \u0027PSWinReportingV2.psm1\u0027\n}",
                         "CommonMistakes":  [

                                            ],
                         "Forks":  72,
                         "PowerShellFiles":  2,
                         "Domain":  "policy_management",
                         "RepoSize":  9383,
                         "RequiredParameters":  {

                                                },
                         "PerformanceTips":  [

                                             ],
                         "CreatedBy":  "PowerShellScraper",
                         "UsageCount":  0,
                         "CredibilityScore":  1,
                         "Tags":  [
                                      "powershell",
                                      "active-directory",
                                      "policy_management",
                                      "read"
                                  ],
                         "Abstract":  "# Repository: EvotecIT/PSWinReporting\n\n**Stars:** 718 | **Language:** PowerShell | **Forks:** 72\n\n**Description:** This PowerShell Module has multiple functionalities, but one of the signature features of this module is the ability to parse Security logs on Domain Controllers providing easy to use access to AD Events.\n\n**URL:** https://github.com/EvotecIT/PSWinReporting\n\n**Clone URL:** https://github.com/EvotecIT/PSWinReporting.git\n\n**Topics:** activedirectory, hacktoberfest, powershell, powersh...",
                         "Language":  "PowerShell",
                         "Sources":  [
                                         {
                                             "SourceType":  "github_repository",
                                             "CredibilityScore":  0.8,
                                             "PublishedAt":  "2025-07-28T16:21:10Z",
                                             "Url":  "https://github.com/EvotecIT/PSWinReporting",
                                             "ScrapedAt":  "2025-07-28T16:21:10Z",
                                             "Id":  "efa0c3a7-488e-437c-aa2f-294a4ca08696",
                                             "Author":  "EvotecIT",
                                             "Title":  "GitHub Repository: EvotecIT/PSWinReporting"
                                         }
                                     ],
                         "Topics":  [
                                        "activedirectory",
                                        "hacktoberfest",
                                        "powershell",
                                        "powershell-module",
                                        "reporting",
                                        "windows"
                                    ],
                         "Content":  "# Repository: EvotecIT/PSWinReporting\n\n**Stars:** 718 | **Language:** PowerShell | **Forks:** 72\n\n**Description:** This PowerShell Module has multiple functionalities, but one of the signature features of this module is the ability to parse Security logs on Domain Controllers providing easy to use access to AD Events.\n\n**URL:** https://github.com/EvotecIT/PSWinReporting\n\n**Clone URL:** https://github.com/EvotecIT/PSWinReporting.git\n\n**Topics:** activedirectory, hacktoberfest, powershell, powershell-module, reporting, windows\n\n## README\n\n﻿\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/PSWinReportingV2\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/v/PSWinReportingV2.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/PSWinReportingV2\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/vpre/PSWinReportingV2.svg?label=powershell%20gallery%20preview\u0026colorB=yellow\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/PSWinReporting\"\u003e\u003cimg src=\"https://img.shields.io/github/license/EvotecIT/PSWinReporting.svg\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/PSWinReportingV2\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/p/PSWinReportingV2.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/PSWinReporting\"\u003e\u003cimg src=\"https://img.shields.io/github/languages/top/evotecit/PSWinReporting.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://github.com/EvotecIT/PSWinReporting\"\u003e\u003cimg src=\"https://img.shields.io/github/languages/code-size/evotecit/PSWinReporting.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/PSWinReporting\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/dt/PSWinReporting.svg?label=downloads%20PSWinReporting\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.powershellgallery.com/packages/PSWinReportingV2\"\u003e\u003cimg src=\"https://img.shields.io/powershellgallery/dt/PSWinReportingv2?label=downloads%20PSWinReportingV2\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n\u003cp align=\"center\"\u003e\n  \u003ca href=\"https://twitter.com/PrzemyslawKlys\"\u003e\u003cimg src=\"https://img.shields.io/twitter/follow/PrzemyslawKlys.svg?label=Twitter%20%40PrzemyslawKlys\u0026style=social\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://evotec.xyz/hub\"\u003e\u003cimg src=\"https://img.shields.io/badge/Blog-evotec.xyz-2A6496.svg\"\u003e\u003c/a\u003e\n  \u003ca href=\"https://www.linkedin.com/in/pklys\"\u003e\u003cimg src=\"https://img.shields.io/badge/LinkedIn-pklys-0077B5.svg?logo=LinkedIn\"\u003e\u003c/a\u003e\n\u003c/p\u003e\n\n# PSWinReporting\n\n**PSWinReporting** is a little PowerShell module that solves the problem of monitoring and reading **Windows Events**. It allows you to set up monitoring of **Domain Controllers** (and from **2.X** any other servers) for events that happen on them. By default, it comes with **built-in Active Directory** events supports, but since **2.0** you can configure it to monitor anything. You can set up reporting on any types of events and have emails delivered with a summary of hourly, daily, weekly, monthly, or quarterly changes. It also supports sending notifications to Microsoft Teams, Slack, and Discord. Make sure to go thru related articles as they have all the KNOW HOW which is quite useful if you want to get everything from this module.\n\nThe full project description is available on my website - [Full project description](https://evotec.xyz/hub/scripts/pswinreporting-powershell-module/).\n\nCurrently, there are 2 branches of PSWinReporting.\n\n- [x] Legacy branch - available in PS Gallery as [PSWinReporting](https://www.powershellgallery.com/packages/PSWinReporting/) - `Install-Module -Name \u0027PSWinReporting\u0027 -Force`\n- [x] Master branch - available in PS Gallery as [PSWinReportingV2](https://www.powershellgallery.com/packages/PSWinReportingV2/) - `Install-Module -Name \u0027PSWinReportingV2\u0027 -Force`\n\nI\u0027ve decided that both PowerShell modules can coexist together, especially for scenarios for people who want to switch, but don\u0027t want to do it right away. This way, you can keep using old version as is, and slowly fix your other stuff, or use new `Find-Events` command. I\u0027ve slightly renamed the commands for V2 release.\n\n## PSWinReportingV2 - Master Edition\n\nMaster edition is a complete rewrite and a new beginning. It provides the same functionality as **Legacy 1.X** version and then some more.\n\n- [x] Ability to translate report and have it suite your needs\n- [x] Ability to completely modify events monitoring\n- [x] Ability to monitor any servers, for any events using simple to use schema\n- [x] Ability to target multiple servers, computers or files at the same time\n\n\nAt this moment there is no documentation for PSWinReportingV2 except for those articles below. Feel free to explore Examples if you\u0027re eager to try the new version — otherwise fallback to PSWinReporting **Legacy Edition**.\n\n- [x] [Find-Events - The only PowerShell Command you will ever need to find out who did what in Active Directory](https://evotec.xyz/the-only-powershell-command-you-will-ever-need-to-find-out-who-did-what-in-active-directory/)\n\n### Built-in Active Directory Reports\n\nPSWinReporting comes with predefined, built-in reports. Those are for `Find-Events`. Those also come defined in example configuration script which you can use straight away after verifying everything is as per your requirement.\n\n- [x] ADComputerChangesDetailed\n- [x] ADComputerCreatedChanged\n- [x] ADComputerDeleted\n- [x] ADGroupChanges\n- [x] ADGroupChangesDetailed\n- [x] ADGroupCreateDelete\n- [x] ADGroupEnumeration\n- [x] ADGroupMembershipChanges\n- [x] ADGroupPolicyChanges\n- [x] ADLogsClearedOther\n- [x] ADLogsClearedSecurity\n- [x] ADUserChanges\n- [x] ADUserChangesDetailed\n- [x] ADUserLockouts\n- [x] ADUserLogon\n- [x] ADUserLogonKerberos\n- [x] ADUserStatus\n- [x] ADUserUnlocked\n- [X] ADOrganizationalUnitChangesDetailed (added in 2.0.10)\n- [x] OSStartupShutdownCrash (added in 2.0.12) - covers startup, shutdown and crashes - probably needs some work on the engine later on to allow field merging\n- [x] OSCrash (added in 2.0.12) - covers system crashes\n- [x] NetworkAccessAuthenticationPolicy (added in 2.0.12) - covers authorizations approved/denied for WIFI and ETHERNET\n\n### Built-in Reporting Times\n\nPSWinReporting comes with predefined report times. This means you can use **True/False** to enable/disable period. In case of `Find-Events`, you can use defined times (checked only) from **DatesRange** parameter.\n\n- [ ] CurrentDay\n- [ ] CurrentDayMinusDayX\n- [ ] CurrentDayMinuxDaysX\n- [x] CurrentHour\n- [x] CurrentMonth\n- [x] CurrentQuarter\n- [ ] CustomDate\n- [x] Everything\n- [x] Last14days\n- [x] Last3days\n- [x] Last7days\n- [ ] OnDay\n- [x] PastDay\n- [x] PastHour\n- [x] PastMonth\n- [x] PastQuarter\n\nOf course, you can also define **DateFrom**, **DateTo** parameters for custom use when using `Find-Events` command.\n\n## PSWinReporting - Legacy Edition\n\n***Legacy edition*** will continue it\u0027s life as ***1.X.X***. If you want to keep on using it, feel free, but it\u0027s highly encouraged to use ***2.x.x*** when it\u0027s fully functional with all features. Code is available as [Legacy Branch](https://github.com/EvotecIT/PSWinReporting/tree/Legacy). Following links can help in understanding how it works and how to set it up:\n\n- [Review of features coming in 2.0 along with some features description for the current version](https://evotec.xyz/pswinreporting-1-8-split-of-branches-legacy-vs-new-hope/) - Overview of configuration and features.\n- [Review of new features in PSWinReporting 1.7](https://evotec.xyz/pswinreporting-forwarders-microsoft-teams-slack-microsoft-sql-and-more/) - Lots of changes, review required. Microsoft Teams, Slack, SQL and forwarders support\n- [Review of new features in PSWinReporting 1.0](https://evotec.xyz/pswinreporting-1-0-is-out/) - Lots of changes, review required.\n- [Last version of Get-EventsLibrary.ps1](https://evotec.xyz/get-eventslibrary-ps1-monitoring-events-powershell/) - This is the actual code base for the old version. Just in caseâ€¦\n- [Blog post about version 0.8](https://evotec.xyz/whats-new-event-monitoring-0-8/) - Updates from feedback. Last version before the name change.\n- [Blog post about version 0.7](https://evotec.xyz/whats-new-event-monitoring-v0-7/) - Updates from feedback.\n- [Blog post about version 0.6](https://evotec.xyz/whats-new-event-monitoring-v0-6/) - Updates from feedback.\n- [Blog post about initial version and the differences in monitoring approach](https://evotec.xyz/monitoring-active-directory-changes-on-users-and-groups-with-powershell/)\n\nFollowing AD Events are supported:\n\n- [x] Group create, delete, modify (Who / When / What)\n- [x] Group membership changes (Who / When / What)\n- [x] User changes (Who / When / What)\n- [x] User created / deleted (Who / When)\n- [x] User password changes (Who / When)\n- [x] User lockouts (Who / When / Where)\n- [x] Computer Created / Modified (Who / When / Where)\n- [x] Computer Deleted (Who / When / Where)\n- [x] Event Log Backup (Who / When)\n- [x] Event Log Clear (Who / When)\n\nFeatures:\n\n- [x] Support for Event Forwarding - monitoring one event log instead of scanning all domain controllers\n- [x] Support for Microsoft Teams - Sending events as they happen to Microsoft Teams (only supported when forwarders are in use)\n- [x] Support for Slack - Sending events as they happen to Slack (only supported when forwarders are in use)\n- [x] Support for Microsoft SQL - Sending events directly to SQL (some people prefer it that way)\n- [x] Support for backing up old archived logs (moves logs from Domain Controllers into chosen place)\n- [x] Support for re-scanning logs from files - a way to recheck your logs for missing information\n\n### Example - Script running\n\n![image](https://evotec.xyz/wp-content/uploads/2018/06/2018-06-10_11-20-08.gif.pagespeed.ce.xrLSOGTIkk.gif)\n\n### Example - Email Report\n\n![image](https://evotec.xyz/wp-content/uploads/2018/06/PSWinReporting1.0-Example1.png)\n\n### Example - Microsoft Teams\n\n![image](https://evotec.xyz/wp-content/uploads/2018/09/img_5b9e830101081.png)\n\n### Example - Slack\n\n![image](https://evotec.xyz/wp-content/uploads/2018/09/img_5b9e7041638f5.png)\n\n\n## PowerShell Files (2 files)\n\n### PSWinReportingV2.psd1\n\n**Path:** PSWinReportingV2.psd1\n\n``powershell\n﻿@{\n    AliasesToExport   = @()\n    Author            = \u0027Przemyslaw Klys\u0027\n    CmdletsToExport   = @()\n    CompanyName       = \u0027Evotec\u0027\n    Copyright         = \u0027(c) 2011 - 2022 Przemyslaw Klys @ Evotec. All rights reserved.\u0027\n    Description       = \u0027PSWinReportingV2 is fast and efficient Event Viewing, Event Reporting and Event Collecting tool. It\u0027\u0027s version 2 of known PSWinReporting PowerShell module and can work next to it.\u0027\n    FunctionsToExport = @(\u0027Add-EventsDefinitions\u0027, \u0027Add-WinTaskScheduledForwarder\u0027, \u0027Find-Events\u0027, \u0027New-WinSubscriptionTemplates\u0027, \u0027Remove-WinTaskScheduledForwarder\u0027, \u0027Start-WinNotifications\u0027, \u0027Start-WinReporting\u0027, \u0027Start-WinSubscriptionService\u0027)\n    GUID              = \u0027ea2bd8d2-cca1-4dc3-9e1c-ff80b06e8fbe\u0027\n    ModuleVersion     = \u00272.0.23\u0027\n    PrivateData       = @{\n        PSData = @{\n            Tags       = @(\u0027PSWinReporting\u0027, \u0027ActiveDirectory\u0027, \u0027Events\u0027, \u0027Reporting\u0027, \u0027Windows\u0027, \u0027EventLog\u0027)\n            ProjectUri = \u0027https://github.com/EvotecIT/PSWinReporting\u0027\n            IconUri    = \u0027https://evotec.xyz/wp-content/uploads/2018/10/PSWinReporting.png\u0027\n        }\n    }\n    RequiredModules   = @(@{\n            ModuleVersion = \u00271.0.22\u0027\n            ModuleName    = \u0027PSEventViewer\u0027\n            Guid          = \u00275df72a79-cdf6-4add-b38d-bcacf26fb7bc\u0027\n        }, @{\n            ModuleVersion = \u00270.0.254\u0027\n            ModuleName    = \u0027PSSharedGoods\u0027\n            Guid          = \u0027ee272aa8-baaa-4edf-9f45-b6d6f7d844fe\u0027\n        }, @{\n            ModuleVersion = \u00270.1.15\u0027\n            ModuleName    = \u0027PSWriteExcel\u0027\n            Guid          = \u002782232c6a-27f1-435d-a496-929f7221334b\u0027\n        }, @{\n            ModuleVersion = \u00270.0.180\u0027\n            ModuleName    = \u0027PSWriteHTML\u0027\n            Guid          = \u0027a7bdf640-f5cb-4acf-9de0-365b322d245c\u0027\n        })\n    RootModule        = \u0027PSWinReportingV2.psm1\u0027\n}\n``n\n### PSWinReportingV2.psm1\n\n**Path:** PSWinReportingV2.psm1\n\n``powershell\n#Get public and private function definition files.\n$Public = @( Get-ChildItem -Path $PSScriptRoot\\Public\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Private = @( Get-ChildItem -Path $PSScriptRoot\\Private\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Classes = @( Get-ChildItem -Path $PSScriptRoot\\Classes\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n$Enums = @( Get-ChildItem -Path $PSScriptRoot\\Enums\\*.ps1 -ErrorAction SilentlyContinue -Recurse )\n\n$AssemblyFolders = Get-ChildItem -Path $PSScriptRoot\\Lib -Directory -ErrorAction SilentlyContinue\nif ($AssemblyFolders.BaseName -contains \u0027Standard\u0027) {\n    $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Standard\\*.dll -ErrorAction SilentlyContinue )\n} else {\n    if ($PSEdition -eq \u0027Core\u0027) {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Core\\*.dll -ErrorAction SilentlyContinue )\n    } else {\n        $Assembly = @( Get-ChildItem -Path $PSScriptRoot\\Lib\\Default\\*.dll -ErrorAction SilentlyContinue )\n    }\n}\n$FoundErrors = @(\n    Foreach ($Import in @($Assembly)) {\n        try {\n            Add-Type -Path $Import.Fullname -ErrorAction Stop\n        } catch [System.Reflection.ReflectionTypeLoadException] {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        } catch {\n            Write-Warning \"Processing $($Import.Name) Exception: $($_.Exception.Message)\"\n            $LoaderExceptions = $($_.Exception.LoaderExceptions) | Sort-Object -Unique\n            foreach ($E in $LoaderExceptions) {\n                Write-Warning \"Processing $($Import.Name) LoaderExceptions: $($E.Message)\"\n            }\n            $true\n            #Write-Error -Message \"StackTrace: $($_.Exception.StackTrace)\"\n        }\n    }\n    #Dot source the files\n    Foreach ($Import in @($Private + $Public + $Classes + $Enums)) {\n        Try {\n            . $Import.Fullname\n        } Catch {\n            Write-Error -Message \"Failed to import functions from $($import.Fullname): $_\"\n            $true\n        }\n    }\n)\n\nif ($FoundErrors.Count -gt 0) {\n    $ModuleName = (Get-ChildItem $PSScriptRoot\\*.psd1).BaseName\n    Write-Warning \"Importing module $ModuleName failed. Fix errors before continuing.\"\n    break\n}\n\nExport-ModuleMember -Function \u0027*\u0027 -Alias \u0027*\u0027\n``n\n"
                     }
                 ],
    "SourceType":  "enhanced_github",
    "TotalPatterns":  5
}
