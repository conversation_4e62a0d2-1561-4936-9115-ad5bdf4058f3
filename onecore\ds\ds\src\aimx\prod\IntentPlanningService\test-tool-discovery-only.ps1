# Test script focused only on Tool Discovery endpoints
param(
    [string]$BaseUrl = "http://localhost:8082"
)

Write-Host "Testing IntentPlanningService Tool Discovery" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "=" * 60

# Helper function to make API requests
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Uri,
        [object]$Body = $null
    )
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        $headers = @{ "Content-Type" = "application/json" }
        
        if ($Body) {
            $jsonBody = $Body | ConvertTo-Json -Depth 10
            $response = Invoke-RestMethod -Uri $Uri -Method $Method -Body $jsonBody -Headers $headers -TimeoutSec 30
        } else {
            $response = Invoke-RestMethod -Uri $Uri -Method $Method -Headers $headers -TimeoutSec 30
        }
        
        $stopwatch.Stop()
        
        return @{
            Success = $true
            StatusCode = 200
            Response = $response
            ResponseTimeMs = $stopwatch.ElapsedMilliseconds
            ErrorMessage = ""
        }
    }
    catch {
        $stopwatch.Stop()
        
        $statusCode = 500
        if ($_.Exception.Response) {
            $statusCode = [int]$_.Exception.Response.StatusCode
        }
        
        return @{
            Success = $false
            StatusCode = $statusCode
            Response = $null
            ResponseTimeMs = $stopwatch.ElapsedMilliseconds
            ErrorMessage = $_.Exception.Message
        }
    }
}

# Test 1: Get All Available Tools
Write-Host "`n1. Testing Get All Available Tools..." -ForegroundColor Cyan
$result = Invoke-ApiRequest -Method "GET" -Uri "$BaseUrl/api/IntentPlanning/tools"
if ($result.Success) {
    Write-Host "[PASS] Get All Tools" -ForegroundColor Green
    Write-Host "Tools Found: $($result.Response.Count)" -ForegroundColor White
    Write-Host "Response Time: $($result.ResponseTimeMs)ms" -ForegroundColor Gray
    
    if ($result.Response.Count -gt 0) {
        Write-Host "First 3 Tools:" -ForegroundColor White
        $result.Response | Select-Object -First 3 | ForEach-Object {
            Write-Host "  - $($_.toolName) (Category: $($_.category))" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "[FAIL] Get All Tools: $($result.ErrorMessage)" -ForegroundColor Red
}

# Test 2: Get Tools by Category
Write-Host "`n2. Testing Get Tools by Category (user_management)..." -ForegroundColor Cyan
$result = Invoke-ApiRequest -Method "GET" -Uri "$BaseUrl/api/IntentPlanning/tools/category/user_management"
if ($result.Success) {
    Write-Host "[PASS] Get Tools by Category" -ForegroundColor Green
    Write-Host "User Management Tools: $($result.Response.Count)" -ForegroundColor White
    Write-Host "Response Time: $($result.ResponseTimeMs)ms" -ForegroundColor Gray
    
    if ($result.Response.Count -gt 0) {
        Write-Host "User Management Tools:" -ForegroundColor White
        $result.Response | Select-Object -First 5 | ForEach-Object {
            Write-Host "  - $($_.toolName)" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "[FAIL] Get Tools by Category: $($result.ErrorMessage)" -ForegroundColor Red
}

# Test 3: Get Tools with Capabilities
Write-Host "`n3. Testing Get Tools with Capabilities..." -ForegroundColor Cyan
$capabilitiesRequest = @{
    requiredCapabilities = @("rollback")
}
$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/tools/capabilities" -Body $capabilitiesRequest
if ($result.Success) {
    Write-Host "[PASS] Get Tools with Capabilities" -ForegroundColor Green
    Write-Host "Tools with Rollback: $($result.Response.Count)" -ForegroundColor White
    Write-Host "Response Time: $($result.ResponseTimeMs)ms" -ForegroundColor Gray
} else {
    Write-Host "[FAIL] Get Tools with Capabilities: $($result.ErrorMessage)" -ForegroundColor Red
}

# Test 4: Get Specific Tool by ID (if we have tools)
if ($result.Success -and $result.Response.Count -gt 0) {
    Write-Host "`n4. Testing Get Specific Tool by ID..." -ForegroundColor Cyan
    $firstToolId = $result.Response[0].toolId
    $result = Invoke-ApiRequest -Method "GET" -Uri "$BaseUrl/api/IntentPlanning/tools/$firstToolId"
    if ($result.Success) {
        Write-Host "[PASS] Get Specific Tool" -ForegroundColor Green
        Write-Host "Tool: $($result.Response.toolName)" -ForegroundColor White
        Write-Host "Operations: $($result.Response.operations.Count)" -ForegroundColor White
        Write-Host "Response Time: $($result.ResponseTimeMs)ms" -ForegroundColor Gray
    } else {
        Write-Host "[FAIL] Get Specific Tool: $($result.ErrorMessage)" -ForegroundColor Red
    }
}

# Test 5: Health Check
Write-Host "`n5. Testing Tool Health Status..." -ForegroundColor Cyan
$result = Invoke-ApiRequest -Method "GET" -Uri "$BaseUrl/api/IntentPlanning/tools/health"
if ($result.Success) {
    Write-Host "[PASS] Tool Health Check" -ForegroundColor Green
    Write-Host "Healthy Tools: $($result.Response.healthyTools)" -ForegroundColor White
    Write-Host "Unhealthy Tools: $($result.Response.unhealthyTools)" -ForegroundColor White
    Write-Host "Response Time: $($result.ResponseTimeMs)ms" -ForegroundColor Gray
} else {
    Write-Host "[FAIL] Tool Health Check: $($result.ErrorMessage)" -ForegroundColor Red
}

# Test 6: Refresh Tool Cache
Write-Host "`n6. Testing Refresh Tool Cache..." -ForegroundColor Cyan
$result = Invoke-ApiRequest -Method "POST" -Uri "$BaseUrl/api/IntentPlanning/tools/refresh"
if ($result.Success) {
    Write-Host "[PASS] Refresh Tool Cache" -ForegroundColor Green
    Write-Host "Cache Refreshed: $($result.Response.success)" -ForegroundColor White
    Write-Host "Tools Loaded: $($result.Response.toolsLoaded)" -ForegroundColor White
    Write-Host "Response Time: $($result.ResponseTimeMs)ms" -ForegroundColor Gray
} else {
    Write-Host "[FAIL] Refresh Tool Cache: $($result.ErrorMessage)" -ForegroundColor Red
}

Write-Host "`n" + "=" * 60
Write-Host "Tool Discovery Test Completed!" -ForegroundColor Green
Write-Host "Note: Intent Analysis tests skipped due to LLM service issues" -ForegroundColor Yellow
