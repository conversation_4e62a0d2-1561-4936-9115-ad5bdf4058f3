# Comprehensive Robustness Testing for IntentPlanningService
param(
    [string]$BaseUrl = "http://localhost:8082"
)

Write-Host "🧪 ROBUSTNESS TESTING - IntentPlanningService" -ForegroundColor Cyan
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "=" * 80

# Helper function for API requests
function Invoke-TestRequest {
    param(
        [string]$TestName,
        [string]$UserInput,
        [string]$ExpectedCategory = "",
        [bool]$ShouldSucceed = $true,
        [int]$ExpectedMinSteps = 0
    )
    
    Write-Host "`n🔍 Testing: $TestName" -ForegroundColor Green
    Write-Host "Input: $UserInput" -ForegroundColor White
    
    $requestBody = @{
        userInput = $UserInput
        userId = "robustness-test-$(Get-Random)"
        environment = "production"
        priority = "normal"
        context = @{
            department = "IT"
            role = "administrator"
        }
    }
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        $response = Invoke-RestMethod -Uri "$BaseUrl/api/IntentPlanning/analyze" -Method Post -Body ($requestBody | ConvertTo-Json -Depth 10) -ContentType "application/json" -TimeoutSec 45
        $stopwatch.Stop()
        
        $result = @{
            TestName = $TestName
            Success = $response.success
            Category = $response.userGoal.context.intentCategory
            Goals = $response.extractedGoals.Count
            WorkflowSteps = if ($response.workflow) { $response.workflow.steps.Count } else { 0 }
            ResponseTime = $stopwatch.ElapsedMilliseconds
            ErrorMessage = $response.errorMessage
            Confidence = $response.userGoal.extractionConfidence
        }
        
        # Validation
        $issues = @()
        if ($ShouldSucceed -and -not $response.success) {
            $issues += "Expected success but failed: $($response.errorMessage)"
        }
        if (-not $ShouldSucceed -and $response.success) {
            $issues += "Expected failure but succeeded"
        }
        if ($ExpectedCategory -and $result.Category -ne $ExpectedCategory) {
            $issues += "Expected category '$ExpectedCategory' but got '$($result.Category)'"
        }
        if ($result.WorkflowSteps -lt $ExpectedMinSteps) {
            $issues += "Expected at least $ExpectedMinSteps workflow steps but got $($result.WorkflowSteps)"
        }
        
        # Display results
        if ($issues.Count -eq 0) {
            Write-Host "✅ PASS" -ForegroundColor Green
        } else {
            Write-Host "❌ FAIL" -ForegroundColor Red
            foreach ($issue in $issues) {
                Write-Host "  - $issue" -ForegroundColor Red
            }
        }
        
        Write-Host "  Category: $($result.Category) | Goals: $($result.Goals) | Steps: $($result.WorkflowSteps) | Time: $($result.ResponseTime)ms | Confidence: $($result.Confidence)" -ForegroundColor Gray
        
        return $result
    }
    catch {
        $stopwatch.Stop()
        Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
        return @{
            TestName = $TestName
            Success = $false
            ErrorMessage = $_.Exception.Message
            ResponseTime = $stopwatch.ElapsedMilliseconds
        }
    }
}

# Test scenarios
$testResults = @()

# === CORE AD OPERATIONS ===
Write-Host "`n📁 CORE AD OPERATIONS" -ForegroundColor Magenta

$testResults += Invoke-TestRequest -TestName "User Creation - Basic" -UserInput "Create user account for Sarah Johnson" -ExpectedCategory "user_management" -ShouldSucceed $true -ExpectedMinSteps 1

$testResults += Invoke-TestRequest -TestName "User Creation - Detailed" -UserInput "Create a new user account for Michael Brown in Engineering department <NAME_EMAIL> and temporary password" -ExpectedCategory "user_management" -ShouldSucceed $true -ExpectedMinSteps 1

$testResults += Invoke-TestRequest -TestName "Password Reset" -UserInput "Reset password for user alice.smith and send notification" -ExpectedCategory "user_management" -ShouldSucceed $true -ExpectedMinSteps 1

$testResults += Invoke-TestRequest -TestName "User Modification" -UserInput "Update user profile for john.doe - change department to Finance and add phone number" -ExpectedCategory "user_management" -ShouldSucceed $true

$testResults += Invoke-TestRequest -TestName "Group Management" -UserInput "Add user bob.wilson to Finance security group and remove from Marketing group" -ExpectedCategory "group_management" -ShouldSucceed $true

$testResults += Invoke-TestRequest -TestName "Computer Domain Join" -UserInput "Join computer DESKTOP-001 to the domain and move to Finance OU" -ExpectedCategory "computer_management" -ShouldSucceed $true

# === COMPLEX SCENARIOS ===
Write-Host "`n🔧 COMPLEX SCENARIOS" -ForegroundColor Magenta

$testResults += Invoke-TestRequest -TestName "Multi-Step User Setup" -UserInput "Create new employee account for Lisa Chen in Sales, add to Sales group, set temporary password, and enable account" -ExpectedCategory "user_management" -ShouldSucceed $true -ExpectedMinSteps 1

$testResults += Invoke-TestRequest -TestName "User Offboarding" -UserInput "Disable user account for departing employee tom.anderson, remove from all groups, and backup mailbox" -ExpectedCategory "user_management" -ShouldSucceed $true

$testResults += Invoke-TestRequest -TestName "Bulk Operations" -UserInput "Create 5 new user accounts for new interns in IT department with standard permissions" -ExpectedCategory "user_management" -ShouldSucceed $true

$testResults += Invoke-TestRequest -TestName "Security Audit" -UserInput "Generate report of all users with admin privileges and their last login dates" -ExpectedCategory "reporting" -ShouldSucceed $true

# === EDGE CASES ===
Write-Host "`n⚠️ EDGE CASES" -ForegroundColor Magenta

$testResults += Invoke-TestRequest -TestName "Ambiguous Request" -UserInput "Fix the user issue" -ShouldSucceed $false

$testResults += Invoke-TestRequest -TestName "Non-IT Request" -UserInput "Order pizza for the team meeting" -ShouldSucceed $false

$testResults += Invoke-TestRequest -TestName "Incomplete Information" -UserInput "Create user" -ExpectedCategory "user_management" -ShouldSucceed $true

$testResults += Invoke-TestRequest -TestName "Conflicting Operations" -UserInput "Enable and disable user account for john.smith" -ExpectedCategory "user_management" -ShouldSucceed $true

$testResults += Invoke-TestRequest -TestName "Very Long Request" -UserInput "I need to create a comprehensive user account setup for our new senior software engineer Jennifer Martinez who will be joining the Development team next Monday and needs access to all development tools including Visual Studio Enterprise, Azure DevOps, GitHub Enterprise, Docker Desktop, and SQL Server Management Studio, plus she needs to be added to the Developers security group, Senior Staff distribution list, and have her mailbox configured with a 50GB quota and mobile device access enabled for both iOS and Android devices" -ExpectedCategory "user_management" -ShouldSucceed $true

# === PERFORMANCE TESTS ===
Write-Host "`n⚡ PERFORMANCE TESTS" -ForegroundColor Magenta

$testResults += Invoke-TestRequest -TestName "Quick Response Test" -UserInput "Create user test123" -ExpectedCategory "user_management" -ShouldSucceed $true

$testResults += Invoke-TestRequest -TestName "Complex Analysis Test" -UserInput "Perform comprehensive security audit of all user accounts, groups, and computer objects in the domain, generate detailed reports with recommendations for access optimization and compliance improvements" -ShouldSucceed $true

# === RESULTS SUMMARY ===
Write-Host "`n" + "=" * 80
Write-Host "📊 ROBUSTNESS TEST RESULTS" -ForegroundColor Cyan
Write-Host "=" * 80

$totalTests = $testResults.Count
$passedTests = ($testResults | Where-Object { $_.Success -eq $true }).Count
$failedTests = $totalTests - $passedTests

Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $failedTests" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passedTests / $totalTests) * 100, 1))%" -ForegroundColor Yellow

# Performance statistics
$responseTimes = $testResults | Where-Object { $_.ResponseTime } | ForEach-Object { $_.ResponseTime }
if ($responseTimes.Count -gt 0) {
    $avgResponseTime = ($responseTimes | Measure-Object -Average).Average
    $maxResponseTime = ($responseTimes | Measure-Object -Maximum).Maximum
    $minResponseTime = ($responseTimes | Measure-Object -Minimum).Minimum
    
    Write-Host "`nPerformance Metrics:" -ForegroundColor Yellow
    Write-Host "  Average Response Time: $([math]::Round($avgResponseTime, 0))ms" -ForegroundColor White
    Write-Host "  Min Response Time: $([math]::Round($minResponseTime, 0))ms" -ForegroundColor White
    Write-Host "  Max Response Time: $([math]::Round($maxResponseTime, 0))ms" -ForegroundColor White
}

# Category distribution
$categories = $testResults | Where-Object { $_.Category } | Group-Object Category
if ($categories.Count -gt 0) {
    Write-Host "`nCategory Distribution:" -ForegroundColor Yellow
    foreach ($category in $categories) {
        Write-Host "  $($category.Name): $($category.Count) tests" -ForegroundColor White
    }
}

Write-Host "`n🎯 Robustness Testing Complete!" -ForegroundColor Green
