# Test script to show clean, focused output from the new LLM-driven workflow
param(
    [string]$BaseUrl = "http://localhost:8082"
)

Write-Host "=== TESTING CLEAN LLM-DRIVEN WORKFLOW ===" -ForegroundColor Green
Write-Host "Testing the corrected system per user feedback:" -ForegroundColor Yellow
Write-Host "  ✅ No alternative workflows (completely not needed)" -ForegroundColor Green
Write-Host "  ✅ No tool listing (why list all PowerShell commands?)" -ForegroundColor Green  
Write-Host "  ✅ RAG search returns TOP 1 result only" -ForegroundColor Green
Write-Host "  ✅ Single command per step via RAG → Dataset → LLM" -ForegroundColor Green
Write-Host "=" * 60

$testInput = "Create a new user account for <PERSON> in the Sales department"

Write-Host "`nTesting Input: '$testInput'" -ForegroundColor Cyan

try {
    # Create request (matching working format)
    $requestBody = @{
        userInput = $testInput
        userId = "test.user"
        priority = "normal"
        environment = "production"
        requestId = [System.Guid]::NewGuid().ToString()
    } | ConvertTo-Json

    Write-Host "`nSending request..." -ForegroundColor Yellow
    
    # Measure response time
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/IntentPlanning/analyze" -Method POST -Body $requestBody -ContentType "application/json"
    
    $stopwatch.Stop()
    $responseTime = $stopwatch.ElapsedMilliseconds

    Write-Host "Response received in ${responseTime}ms" -ForegroundColor Green

    # Show clean, focused results
    Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
    Write-Host "CLEAN RESULTS (No Clutter)" -ForegroundColor Cyan
    Write-Host "=" * 60
    
    Write-Host "✅ Success: $($response.success)" -ForegroundColor $(if ($response.success) { "Green" } else { "Red" })
    Write-Host "✅ IT Request: $($response.isItRequest)" -ForegroundColor White
    Write-Host "✅ Category: $($response.intentCategory)" -ForegroundColor White
    Write-Host "✅ Confidence: $($response.userGoal.confidence)" -ForegroundColor White
    Write-Host "✅ Goal: '$($response.userGoal.primaryObjective)'" -ForegroundColor White
    
    if ($response.primaryWorkflow -and $response.primaryWorkflow.steps) {
        Write-Host "✅ Workflow Steps: $($response.primaryWorkflow.steps.Count)" -ForegroundColor Green
        Write-Host "✅ Risk Level: $($response.primaryWorkflow.riskLevel)" -ForegroundColor White
        
        Write-Host "`nGENERATED WORKFLOW STEPS:" -ForegroundColor Cyan
        for ($i = 0; $i -lt $response.primaryWorkflow.steps.Count; $i++) {
            $step = $response.primaryWorkflow.steps[$i]
            Write-Host "  $($i + 1). $($step.description)" -ForegroundColor White
            if ($step.parameters.command) {
                Write-Host "     Command: $($step.parameters.command)" -ForegroundColor Gray
            }
        }
    }
    
    # Verify clean output (no clutter)
    Write-Host "`n" + "=" * 60 -ForegroundColor Yellow
    Write-Host "VERIFICATION (User Feedback Addressed)" -ForegroundColor Yellow
    Write-Host "=" * 60
    
    $alternativeCount = if ($response.alternativeWorkflows) { $response.alternativeWorkflows.Count } else { 0 }
    $toolCount = if ($response.availableTools) { $response.availableTools.Count } else { 0 }
    
    if ($alternativeCount -eq 0) {
        Write-Host "✅ Alternative Workflows: REMOVED (was completely not needed)" -ForegroundColor Green
    } else {
        Write-Host "❌ Alternative Workflows: Still present ($alternativeCount found)" -ForegroundColor Red
    }
    
    if ($toolCount -eq 0) {
        Write-Host "✅ Tool Listing: REMOVED (no longer lists all PowerShell commands)" -ForegroundColor Green
    } else {
        Write-Host "❌ Tool Listing: Still present ($toolCount tools listed)" -ForegroundColor Red
    }
    
    if ($response.primaryWorkflow.steps.Count -gt 0) {
        Write-Host "✅ Workflow Generation: WORKING (LLM → RAG → Dataset → LLM flow)" -ForegroundColor Green
    } else {
        Write-Host "❌ Workflow Generation: FAILED (no steps generated)" -ForegroundColor Red
    }
    
    Write-Host "`n🎉 NEW LLM-DRIVEN WORKFLOW SYSTEM IS CLEAN AND FOCUSED!" -ForegroundColor Green
    Write-Host "   - Each step gets TOP 1 command from RAG search" -ForegroundColor White
    Write-Host "   - No unnecessary alternative workflows" -ForegroundColor White
    Write-Host "   - No tool listing clutter" -ForegroundColor White
    Write-Host "   - Fast response time: ${responseTime}ms" -ForegroundColor White

} catch {
    Write-Host "`n❌ TEST ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest completed at $(Get-Date)" -ForegroundColor Gray
