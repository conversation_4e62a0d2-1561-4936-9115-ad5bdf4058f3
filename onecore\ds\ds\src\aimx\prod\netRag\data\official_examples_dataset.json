﻿{
    "Set-ADAccountPassword":  [
                                  "This command sets the password of the user account with DistinguishedName CN=<PERSON><PERSON>,OU=Accounts,DC=Fabrikam,DC=com to p@ssw0rd.: Set-ADAccountPassword -Identity \u0027CN=<PERSON><PERSON>,OU=Accounts,DC=Fabrikam,DC=com\u0027 -Reset -NewPassword (ConvertTo-SecureString -AsPlainText \"p@ssw0rd\" -Force)",
                                  "This command sets the password of the user account with SamAccountName elisada to qwert@12345. Using -NewPassword with a value, without providing an -OldPassword parameter value, will also reset the password.: Set-ADAccountPassword -Identity elisada -OldPassword (ConvertTo-SecureString -AsPlainText \"p@ssw0rd\" -Force) -NewPassword (ConvertTo-SecureString -AsPlainText \"qwert@12345\" -Force)",
                                  "This command sets the password of the user account with DistinguishedName CN=<PERSON>,CN=Users,DC=Fabrikam,DC=com. The cmdlet prompts you for old and new passwords.: Set-ADAccountPassword -Identity EvanNa",
                                  "This command prompts the user for a new password that is stored in a temporary variable named $NewPassword, then uses it to reset the password for the user account with SamAccountName DavidChe.: Set-ADAccountPassword -Identity DavidChe -NewPassword $NewPassword -Reset",
                                  "Note: Group MSAs cannot set password since they are changed at predetermined intervals.: Set-ADAccountPassword"
                              ],
    "Set-ADCentralAccessPolicy":  [
                                      "This command updates the central access policy named Finance Policy to include the description For the Finance Department.: Set-ADCentralAccessPolicy \"Finance Policy\" -Description \"For the Finance Department.\"",
                                      "This command updates the central access policy named Finance Policy to include the description For the Finance Department.: Set-ADCentralAccessPolicy",
                                      "This command updates the central access policy named Finance Policy to include the description For the Finance Department.: Set-ADCentralAccessPolicy"
                                  ],
    "Remove-ADServiceAccount":  [
                                    "This command removes the managed service account identified as SQL-SRV1.: Remove-ADServiceAccount -Identity SQL-SRV1",
                                    "Note: Removing the service account is a different operation than uninstalling the service account locally.: Remove-ADServiceAccount"
                                ],
    "Set-ADForest":  [
                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Set-ADForest -Identity fabrikam.com -UPNSuffixes @{replace=\"fabrikam.com\",\"fabrikam\",\"corp.fabrikam.com\"}",
                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Set-ADForest -Identity fabrikam.com -SPNSuffixes @{add=\"corp.fabrikam.com\"}",
                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Set-ADForest -Instance $Forest",
                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Set-ADForest"
                     ],
    "New-ADCentralAccessPolicy":  [
                                      "This command creates a central access rule named Finance Documents Rule with a new resource condition. The resource condition scopes the resources to ones containing the value Finance in their Department resource property.: New-ADCentralAccessPolicy"
                                  ],
    "Get-ADComputer":  [
                           "This command gets a specific computer showing all the properties.: Get-ADComputer",
                           "This command gets a specific computer showing all the properties.: Get-ADComputer",
                           "This command gets a specific computer showing all the properties.: Get-ADComputer",
                           "This command gets a specific computer showing all the properties.: Get-ADComputer",
                           "This command gets a specific computer showing all the properties.: Get-ADComputer -Identity \"User01-SRV1\" -Properties *",
                           "This command gets all the computers with a name starting with a particular string and shows the name, DNS hostname, and IPv4 address.: Get-ADComputer -Filter \u0027Name -like \"User01*\"\u0027 -Properties IPv4Address |",
                           "This command gets all the computers that have changed their password in the last 90 days.: Get-ADComputer -Filter \u0027PasswordLastSet -ge $Date\u0027 -Properties PasswordLastSet |",
                           "This command gets all computer accounts.: Get-ADComputer -LDAPFilter \"(name=*laptop*)\" -SearchBase \"CN=Computers,DC= User01,DC=com\"",
                           "This command gets all computer accounts.: Get-ADComputer -Filter *",
                           "This command shows the name, DNS hostname, and IPv4 address.: Get-ADComputer -Filter \u0027Name -like \"Computer01*\" -or Name -like \"Computer02*\"\u0027 -Properties IPv4Address |",
                           "This command shows the name, DNS hostname, and IPv4 address.: Get-ADComputer -Filter \u0027Name -like \"Computer01*\" -and PasswordLastSet -ge $Date\u0027 -Properties IPv4Address |",
                           "To get a list of all the properties of an ADComputer object, use the following command:: Get-ADComputer",
                           "Official code example: Get-ADComputer"
                       ],
    "Remove-ADReplicationSiteLinkBridge":  [
                                               "This command removes the site link bridge named NorthAmerica-Asia.: Remove-ADReplicationSiteLinkBridge -Identity \"NorthAmerica-Asia\"",
                                               "This command removes the site link bridge named NorthAmerica-Asia.: Remove-ADReplicationSiteLinkBridge"
                                           ],
    "Undo-ADServiceAccountMigration":  [
                                           "Specifies the authentication method to use. The acceptable values for this parameter are:: Undo-ADServiceAccountMigration",
                                           "Specifies the authentication method to use. The acceptable values for this parameter are:: Undo-ADServiceAccountMigration",
                                           "Specifies the authentication method to use. The acceptable values for this parameter are:: Undo-ADServiceAccountMigration @params",
                                           "Specifies the authentication method to use. The acceptable values for this parameter are:: Undo-ADServiceAccountMigration @params"
                                       ],
    "Remove-ADDomainControllerPasswordReplicationPolicy":  [
                                                               "This command removes the users with samAccountNames PattiFuller and DavidChew from the Allowed list on the RODC USER01-RODC1.: Remove-ADDomainControllerPasswordReplicationPolicy -Identity \"USER01-RODC1\" -AllowedList \"PattiFuller\", \"DavidChew\"",
                                                               "This command removes the users with samAccountNames Elisa Daugherty and Evan Narvaez from the Denied list on the RODC FABRIKAM-RODC1.: Remove-ADDomainControllerPasswordReplicationPolicy -Identity \"USER01-RODC1\" -DeniedList \"ElisaDaugherty\", \"EvanNarvaez\"",
                                                               "This command removes the users with samAccountNames PattiFuller and DavidChew from the Allowed list on the RODC USER01-RODC1.: Remove-ADDomainControllerPasswordReplicationPolicy",
                                                               "This command removes the users with samAccountNames PattiFuller and DavidChew from the Allowed list on the RODC USER01-RODC1.: Remove-ADDomainControllerPasswordReplicationPolicy"
                                                           ],
    "New-ADObject":  [
                         "This command creates a subnet object in the HQ site with the described attributes.: New-ADObject -Name \"***********/26\" -Type \"subnet\" -Description \"***********/***************\" -OtherAttributes @{location=\"Building A\";siteObject=\"CN=HQ,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\"} -Path \"CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\"",
                         "This example creates a new subnet object, using a different subnet object as a template.: New-ADObject -Instance $SubnetTemplate -Name \"***********/28\" -Type \"subnet\" -Path \"CN=Subnets,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\"",
                         "This command creates a new container object named Apps in an AD LDS instance.: New-ADObject -Name \"SaraDavisContact\" -Type \"contact\" -ProtectedFromAccidentalDeletion $True -OtherAttributes @{\u0027msDS-SourceObjectDN\u0027=\"CN=FabrikamContacts,DC=CONTOSO,DC=COM\"}",
                         "This command creates a new container object named Apps in an AD LDS instance.: New-ADObject -Name \"Apps\" -Type \"container\" -Path \"DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\"",
                         "The following methods explain different ways to create an object by using this cmdlet.: New-ADObject"
                     ],
    "Get-ADDomainControllerPasswordReplicationPolicyUsage":  [
                                                                 "This command gets the list of accounts cached across all RODCs in the domain.: Get-ADDomainControllerPasswordReplicationPolicyUsage -Identity \"USER01-RODC1\" -AuthenticatedAccounts | ft Name,ObjectClass -A",
                                                                 "This command gets the list of accounts cached across all RODCs in the domain.: Get-ADDomainControllerPasswordReplicationPolicyUsage -Identity \"USER01-RODC1\" -RevealedAccounts | ft Name,ObjectClass -A",
                                                                 "This command gets the list of accounts cached across all RODCs in the domain.: Get-ADDomainControllerPasswordReplicationPolicyUsage",
                                                                 "This command gets the list of accounts cached across all RODCs in the domain.: Get-ADDomainControllerPasswordReplicationPolicyUsage"
                                                             ],
    "Rename-ADObject":  [
                            "This command renames the object with the distinguished name OU=ManagedGroups,OU=Managed,DC=Fabrikam,DC=Com to Groups.: Rename-ADObject -Identity \"CN=HQ,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\" -NewName \"UnitedKingdomHQ\"",
                            "This command renames the object with the distinguished name OU=ManagedGroups,OU=Managed,DC=Fabrikam,DC=Com to Groups.: Rename-ADObject -Identity \"4777c8e8-cd29-4699-91e8-c507705a0966\" -NewName \"AmsterdamHQ\" -Partition \"CN=Configuration,DC=FABRIKAM,DC=COM\"",
                            "This command renames the object with the distinguished name OU=ManagedGroups,OU=Managed,DC=Fabrikam,DC=Com to Groups.: Rename-ADObject -Identity \"OU=ManagedGroups,OU=Managed,DC=Fabrikam,DC=Com\" -NewName \"Groups\"",
                            "This command renames the container CN=Apps,DC=AppNC to InternalApps in an LDS instance.: Rename-ADObject -Identity \"4777c8e8-cd29-4699-91e8-c507705a0966\" -NewName \"DavidChew\"",
                            "This command renames the container CN=Apps,DC=AppNC to InternalApps in an LDS instance.: Rename-ADObject -Identity \"CN=Apps,DC=AppNC\" -NewName \"InternalApps\" -Server \"FABRIKAM-SRV1:60000\"",
                            "This command renames the object with the distinguished name OU=ManagedGroups,OU=Managed,DC=Fabrikam,DC=Com to Groups.: Rename-ADObject"
                        ],
    "Set-ADAuthenticationPolicy":  [
                                       "Specifies a list of values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a parameter. To identify an attribute, specify the Lightweight Directory Access Protocol (LDAP) display name defined for it in the Active Directory Domain Services schema.: Set-ADAuthenticationPolicy -Identity AuthenticationPolicy01 -Description \"TestDescription\" -UserTGTLifetimeMins 45",
                                       "Specifies a list of values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a parameter. To identify an attribute, specify the Lightweight Directory Access Protocol (LDAP) display name defined for it in the Active Directory Domain Services schema.: Set-ADAuthenticationPolicy -Instance $AuthPolicy",
                                       "Specifies a list of values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a parameter. To identify an attribute, specify the Lightweight Directory Access Protocol (LDAP) display name defined for it in the Active Directory Domain Services schema.: Set-ADAuthenticationPolicy -Identity AuthenticationPolicy03 -Replace @{description=\"New Description\"}",
                                       "Specifies a list of values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a parameter. To identify an attribute, specify the Lightweight Directory Access Protocol (LDAP) display name defined for it in the Active Directory Domain Services schema.: Set-ADAuthenticationPolicy",
                                       "Specifies a list of values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a parameter. To identify an attribute, specify the Lightweight Directory Access Protocol (LDAP) display name defined for it in the Active Directory Domain Services schema.: Set-ADAuthenticationPolicy"
                                   ],
    "Move-ADDirectoryServerOperationMasterRole":  [
                                                      "This command moves the primary domain controller (PDC) Emulator role to the domain controller USER01-DC1.: Move-ADDirectoryServerOperationMasterRole -Identity \"USER01-DC1\" -OperationMasterRole PDCEmulator",
                                                      "This command moves the PDC Emulator and schema master roles to the domain controller USER02-DC2.: Move-ADDirectoryServerOperationMasterRole -Identity \"USER02-DC2\" -OperationMasterRole PDCEmulator,SchemaMaster",
                                                      "This command moves the schema master flexible single master operations (FSMO) owner to the AD LDS instance instance1 on the server User03-DC.: Move-ADDirectoryServerOperationMasterRole -Identity User03-DC`$instance1 -OperationMasterRole schemaMaster -Server User03-DC:50000",
                                                      "This command seizes the roles RID master, infrastructure master, and domain naming master.: Move-ADDirectoryServerOperationMasterRole -Identity USER04-DC1 -OperationMasterRole RIDMaster,InfrastructureMaster,DomainNamingMaster -Force",
                                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Move-ADDirectoryServerOperationMasterRole -Identity $Server -OperationMasterRole SchemaMaster,DomainNamingMaster,PDCEmulator,RIDMaster,InfrastructureMaster",
                                                      "When you type this value in Windows PowerShell, you must use the backtick (`) as an escape character for the dollar sign ($). Therefore, for this example, you would type the following:: Move-ADDirectoryServerOperationMasterRole"
                                                  ],
    "Add-ADComputerServiceAccount":  [
                                         "Note: Add-ADComputerServiceAccount",
                                         "Note: Add-ADComputerServiceAccount",
                                         "Note: Add-ADComputerServiceAccount",
                                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADComputerServiceAccount -Computer ComputerAcct1 -ServiceAccount SvcAcct1",
                                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADComputerServiceAccount -Computer ComputerAcct1 -ServiceAccount SvcAcct1, SvcAcct2"
                                     ],
    "New-ADAuthenticationPolicySilo":  [
                                           "This command creates an authentication policy silo object and enforces it.: New-ADAuthenticationPolicySilo -Name AuthenticationPolicySilo01 -Enforce",
                                           "This command creates an authentication policy silo object but does not enforce it.: New-ADAuthenticationPolicySilo -Name AuthenticationPolicySilo02",
                                           "This command creates an authentication policy silo object and enforces it.: New-ADAuthenticationPolicySilo"
                                       ],
    "Get-ADReplicationFailure":  [
                                     "This command gets a collection of data that describes an Active Directory replication failure for corp-DC01.: Get-ADReplicationFailure -Target corp-DC01",
                                     "This command gets a collection of data that describes an Active Directory replication failure from corp-DC01.: Get-ADReplicationFailure -Target corp-DC01 -Scope Server",
                                     "This command gets a collection of data describing an Active Directory replication failure from corp-DC01 and corp-DC02.: Get-ADReplicationFailure -Target corp-DC01,corp-DC02",
                                     "This command gets a collection of data describing Active Directory replication failures from all the domain controllers in the site NorthAmerica.: Get-ADReplicationFailure -Target NorthAmerica -Scope Site",
                                     "This command gets a collection of data describing Active Directory replication failures from all the domain controllers in the domain corp.contoso.com.: Get-ADReplicationFailure -Target \"corp.contoso.com\" -Scope Domain",
                                     "This command gets a collection of data describing Active Directory replication failures from all the domain controllers in the forest corp.contoso.com.: Get-ADReplicationFailure -Target \"corp.contoso.com\" -Scope Forest",
                                     "This command gets a collection of data that describes an Active Directory replication failure for corp-DC01.: Get-ADReplicationFailure",
                                     "This command gets a collection of data that describes an Active Directory replication failure for corp-DC01.: Get-ADReplicationFailure"
                                 ],
    "Start-ADServiceAccountMigration":  [
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Start-ADServiceAccountMigration",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Start-ADServiceAccountMigration",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Start-ADServiceAccountMigration @params",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Start-ADServiceAccountMigration @params"
                                        ],
    "New-ADReplicationSite":  [
                                  "This command creates a new site named NorthAmerica.: New-ADReplicationSite -Name \"NorthAmerica\"",
                                  "Specifies the authentication method to use. The acceptable values for this parameter are:: New-ADReplicationSite -Name \"Europe\" -AutomaticInterSiteTopologyGenerationEnabled $FALSE",
                                  "Specifies the authentication method to use. The acceptable values for this parameter are:: New-ADReplicationSite -Name \"Asia\" -ReplicationSchedule $schedule",
                                  "This command creates a new site named NorthAmerica.: New-ADReplicationSite"
                              ],
    "Move-ADObject":  [
                          "This command moves the organizational unit (OU) ManagedGroups to a new location. The OU ManagedGroups must not be protected from accidental deletion for the successful move.: Move-ADObject -Identity \"OU=ManagedGroups,DC=Fabrikam,DC=Com\" -TargetPath \"OU=Managed,DC=Fabrikam,DC=Com\"",
                          "This command moves the object identified by the specified GUID to the new location.: Move-ADObject -Identity \"8d0bcc44-c826-4dd8-af5c-2c69960fbd47\" -TargetPath \"OU=Managed,DC=Fabrikam,DC=Com\"",
                          "This command moves an object to a new location. Both the object and the target path are specified using GUIDs.: Move-ADObject -Identity \"8d0bcc44-c826-4dd8-af5c-2c69960fbd47\" -TargetPath \"1c2ea8a8-c2b7-4a87-8190-0e8a166aee16\"",
                          "This command moves an object with the distinguished name CN=Peter Bankov,OU=Accounting,DC=Fabrikam,DC=com to a different domain.: Move-ADObject -Identity \"CN=Peter Bankov,OU=Accounting,DC=Fabrikam,DC=com\" -TargetPath \"OU=Accounting,DC=Europe,DC=Fabrikam,DC=com\" -TargetServer \"server01.europe.fabrikam.com\"",
                          "This command moves an object to a new location in an AD LDS instance.: Move-ADObject -Identity \"CN=AccountLeads,DC=AppNC\" -TargetPath \"OU=AccountDeptOU,DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\"",
                          "When an object is moved between domains, both the source DC and the target DC need to be the RID Master of their domains. If a different DC is being used, you will receive the following error:: Move-ADObject"
                      ],
    "Add-ADDomainControllerPasswordReplicationPolicy":  [
                                                            "Specifies the users, computers, groups or other accounts to add to the list of accounts allowed to replicate their passwords to this RODC. You can specify more than one value by using a comma-separated list. The acceptable values for this parameter are:: Add-ADDomainControllerPasswordReplicationPolicy",
                                                            "Specifies the users, computers, groups or other accounts to add to the list of accounts allowed to replicate their passwords to this RODC. You can specify more than one value by using a comma-separated list. The acceptable values for this parameter are:: Add-ADDomainControllerPasswordReplicationPolicy",
                                                            "Specifies the users, computers, groups or other accounts to add to the list of accounts allowed to replicate their passwords to this RODC. You can specify more than one value by using a comma-separated list. The acceptable values for this parameter are:: Add-ADDomainControllerPasswordReplicationPolicy",
                                                            "Specifies the users, computers, groups or other accounts to add to the list of accounts allowed to replicate their passwords to this RODC. You can specify more than one value by using a comma-separated list. The acceptable values for this parameter are:: Add-ADDomainControllerPasswordReplicationPolicy",
                                                            "Specifies the users, computers, groups or other accounts to add to the list of accounts allowed to replicate their passwords to this RODC. You can specify more than one value by using a comma-separated list. The acceptable values for this parameter are:: Add-ADDomainControllerPasswordReplicationPolicy @params",
                                                            "Specifies the users, computers, groups or other accounts to add to the list of accounts allowed to replicate their passwords to this RODC. You can specify more than one value by using a comma-separated list. The acceptable values for this parameter are:: Add-ADDomainControllerPasswordReplicationPolicy @params"
                                                        ],
    "Remove-ADFineGrainedPasswordPolicySubject":  [
                                                      "This command removes the fine-grained password policy subject named DlgtdAdminsPSO from the users with SAM account names BobKe and KimAb.: Remove-ADFineGrainedPasswordPolicySubject -Identity DlgtdAdminsPSO -Subjects BobKe,KimAb",
                                                      "This command removes the fine-grained password policy subject named DlgtdAdminsPSO from the users with SAM account names BobKe and KimAb.: Remove-ADFineGrainedPasswordPolicySubject"
                                                  ],
    "Search-ADAccount":  [
                             "This command returns all users, computers, and service accounts that are disabled.: Search-ADAccount -AccountDisabled | FT Name,ObjectClass -A",
                             "This command returns all users that are disabled.: Search-ADAccount -AccountDisabled -UsersOnly | FT Name,ObjectClass -A",
                             "This command returns all users, computers, and service accounts that are expired.: Search-ADAccount -AccountExpired | FT Name,ObjectClass -A",
                             "This command returns all users, computers, and service accounts that will expire in the next 6 days.: Search-ADAccount -AccountExpiring -TimeSpan 6.00:00:00 | FT Name,ObjectClass -A",
                             "This command returns all accounts where the password has expired.: Search-ADAccount -PasswordExpired | FT Name,ObjectClass -A",
                             "This command returns all accounts that have been locked out.: Search-ADAccount -LockedOut | FT Name,ObjectClass -A",
                             "This command returns all users, computers, and service accounts that are disabled.: Search-ADAccount",
                             "This command returns all users, computers, and service accounts that are disabled.: Search-ADAccount",
                             "This command returns all users, computers, and service accounts that are disabled.: Search-ADAccount",
                             "This command returns all users, computers, and service accounts that are disabled.: Search-ADAccount",
                             "This command returns all users, computers, and service accounts that are disabled.: Search-ADAccount",
                             "This command returns all users, computers, and service accounts that are disabled.: Search-ADAccount",
                             "This command returns all users, computers, and service accounts that are disabled.: Search-ADAccount"
                         ],
    "Set-ADClaimTransformLink":  [
                                     "This command applies the claims transformation policy DenyAllPolicy to the trust corp.contoso.com. The rule is applied to where this domain acts as both the trusted and trusting domain in the trust. Effectively, the rule is applied to both claims coming in to this domain from its trust partner, and claims flowing out of this domain towards its trust partner.: Set-ADClaimTransformLink \"corp.contoso.com\" -Policy DenyAllPolicy -TrustRole Trusted",
                                     "This command applies the claims transformation policy DenyAllPolicy to the trust corp.contoso.com. The rule is applied to where this domain acts as both the trusted and trusting domain in the trust. Effectively, the rule is applied to both claims coming in to this domain from its trust partner, and claims flowing out of this domain towards its trust partner.: Set-ADClaimTransformLink \"corp.contoso.com\" -Policy DenyAllPolicy -TrustRole Trusting",
                                     "This command applies the claims transformation policy HumanResourcesToHrPolicy to the trust corp.contoso.com. The rule is applied to where this domain acts as the trusting domain in the trust. Effectively, the rule is applied to claims coming in to this domain from its trust partner.: Set-ADClaimTransformLink -Identity \"corp.contoso.com\" -Policy HumanResourcesToHrPolicy -TrustRole Trusting",
                                     "This command applies the claims transformation policy DenyAllPolicy to the trust corp.contoso.com. The rule is applied to where this domain acts as both the trusted and trusting domain in the trust. Effectively, the rule is applied to both claims coming in to this domain from its trust partner, and claims flowing out of this domain towards its trust partner.: Set-ADClaimTransformLink"
                                 ],
    "Get-ADDefaultDomainPasswordPolicy":  [
                                              "This command gets the default domain password policy from current logged on user domain.: Get-ADDefaultDomainPasswordPolicy -Current LoggedOnUser",
                                              "This command gets the default domain password policy from current local computer.: Get-ADDefaultDomainPasswordPolicy -Current LocalComputer",
                                              "This command gets the default domain password policy objects from all the domains in the forest.: Get-ADDefaultDomainPasswordPolicy -Identity fabrikam.com",
                                              "This command gets the default domain password policy from current logged on user domain.: Get-ADDefaultDomainPasswordPolicy",
                                              "This command gets the default domain password policy from current logged on user domain.: Get-ADDefaultDomainPasswordPolicy",
                                              "This command gets the default domain password policy from current logged on user domain.: Get-ADDefaultDomainPasswordPolicy"
                                          ],
    "Remove-ADCentralAccessRule":  [
                                       "This command removes the specified central access rule, Finance Documents Rule.: Remove-ADCentralAccessRule -Identity \"Finance Documents Rule\"",
                                       "This command removes the specified central access rule, Finance Documents Rule.: Remove-ADCentralAccessRule"
                                   ],
    "Get-ADAuthenticationPolicySilo":  [
                                           "This command gets an authentication policy silo object named AuthenticationPolicySilo01.: Get-ADAuthenticationPolicySilo",
                                           "This command gets an authentication policy silo object named AuthenticationPolicySilo01.: Get-ADAuthenticationPolicySilo",
                                           "This command gets an authentication policy silo object named AuthenticationPolicySilo01.: Get-ADAuthenticationPolicySilo",
                                           "This command gets an authentication policy silo object named AuthenticationPolicySilo01.: Get-ADAuthenticationPolicySilo",
                                           "This command gets an authentication policy silo object named AuthenticationPolicySilo01.: Get-ADAuthenticationPolicySilo -Identity AuthenticationPolicySilo01",
                                           "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADAuthenticationPolicySilo -Filter \u0027Name -like \"*AuthenticationPolicySilo*\"\u0027 |",
                                           "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADAuthenticationPolicySilo -Identity AuthenticationPolicySilo02 -Properties *"
                                       ],
    "Set-ADClaimType":  [
                            "This command sets the suggested values of the user claim type with display name Employee Type to FTE, Intern, and Contractor. Applications using this claim type would allow their users to specify one of the suggested values as this claim type\u0027s value.: Set-ADClaimType -Identity Title -SourceAttribute \"title\"",
                            "This command sets the suggested values of the user claim type with display name Employee Type to FTE, Intern, and Contractor. Applications using this claim type would allow their users to specify one of the suggested values as this claim type\u0027s value.: Set-ADClaimType -Identity \"Employee Type\" -SuggestedValues $FullTime,$Intern,$Contractor",
                            "This command sets the claim type named SourceForest to source from the claims transformation policy engine.: Set-ADClaimType -Identity SourceForest -SourceTransformPolicy",
                            "This command sets the suggested values of the user claim type with display name Employee Type to FTE, Intern, and Contractor. Applications using this claim type would allow their users to specify one of the suggested values as this claim type\u0027s value.: Set-ADClaimType",
                            "This command sets the suggested values of the user claim type with display name Employee Type to FTE, Intern, and Contractor. Applications using this claim type would allow their users to specify one of the suggested values as this claim type\u0027s value.: Set-ADClaimType",
                            "This command sets the suggested values of the user claim type with display name Employee Type to FTE, Intern, and Contractor. Applications using this claim type would allow their users to specify one of the suggested values as this claim type\u0027s value.: Set-ADClaimType",
                            "This command sets the suggested values of the user claim type with display name Employee Type to FTE, Intern, and Contractor. Applications using this claim type would allow their users to specify one of the suggested values as this claim type\u0027s value.: Set-ADClaimType",
                            "This command sets the suggested values of the user claim type with display name Employee Type to FTE, Intern, and Contractor. Applications using this claim type would allow their users to specify one of the suggested values as this claim type\u0027s value.: Set-ADClaimType"
                        ],
    "Move-ADDirectoryServer":  [
                                   "This command moves the domain controller USER01-DC2 to the site Branch-Office-Site.: Move-ADDirectoryServer -Identity \"USER01-DC2\" -Site \"Branch-Office-Site\"",
                                   "This command moves the domain controller USER01-DC2 to the site Branch-Office-Site.: Move-ADDirectoryServer"
                               ],
    "Get-ADUserResultantPasswordPolicy":  [
                                              "This command gets the resultant password policy for the user with SAM account name BobKe.: Get-ADUserResultantPasswordPolicy -Identity BobKe",
                                              "A user can have multiple password policy objects (PSOs) associated with it, but only one PSO is the RSoP. A PSO is associated with a user when the PSO applies directly to the user or when the PSO applies to an Active Directory group that contains the user. When more than one PSO policy is associated with a user or group, the RSoP value defines the PSO to apply.: Get-ADUserResultantPasswordPolicy"
                                          ],
    "Remove-ADClaimType":  [
                               "This command removes the claim type with the name Title.: Remove-ADClaimType -Identity Title",
                               "This command removes the claim type with the name Title.: Remove-ADClaimType"
                           ],
    "Get-ADResourcePropertyValueType":  [
                                            "This command gets the names of all resource property value types.: Get-ADResourcePropertyValueType -Filter * | Format-Table Name",
                                            "This command gets all resource property value types that the resource properties Country and Authors use.: Get-ADResourcePropertyValueType -Filter \"ResourceProperties -eq \u0027Country\u0027 -or ResourceProperties -eq \u0027Authors\u0027\"",
                                            "This command gets a resource property value type named MS-DS-Text.: Get-ADResourcePropertyValueType -Identity \"MS-DS-Text\"",
                                            "This command gets the names of all resource property value types.: Get-ADResourcePropertyValueType",
                                            "This command gets the names of all resource property value types.: Get-ADResourcePropertyValueType",
                                            "This command gets the names of all resource property value types.: Get-ADResourcePropertyValueType"
                                        ],
    "Test-ADServiceAccount":  [
                                  "This command tests the specified service account, MSA1, from the local computer. The test indicates whether the account is ready for use, which means it can be authenticated and that it can access the domain using its current credentials.: Test-ADServiceAccount -Identity MSA1",
                                  "This command tests the specified service account, MSA1, from the local computer. The test indicates whether the account is ready for use, which means it can be authenticated and that it can access the domain using its current credentials.: Test-ADServiceAccount"
                              ],
    "Set-ADReplicationSite":  [
                                  "The command sets the properties of the site with name NorthAmerica to prevent its intersite topology generator (ISTG) at corp-DC02 from generating connections for intersite replication.: Set-ADReplicationSite -Identity NorthAmerica -InterSiteTopologyGenerator corp-DC02 -AutomaticInterSiteTopologyGenerationEnabled $False",
                                  "This example sets the daily replication schedule of the site with name Asia.: Set-ADReplicationSite -Identity \"Asia\" -ReplicationSchedule $Schedule",
                                  "The command sets the properties of the site with name NorthAmerica to prevent its intersite topology generator (ISTG) at corp-DC02 from generating connections for intersite replication.: Set-ADReplicationSite",
                                  "The command sets the properties of the site with name NorthAmerica to prevent its intersite topology generator (ISTG) at corp-DC02 from generating connections for intersite replication.: Set-ADReplicationSite"
                              ],
    "Restore-ADObject":  [
                             "This command finds a deleted user whose SAM account name is pattifuller and restores it.: Restore-ADObject -Identity \"613dc90a-2afd-49fb-8bd8-eac48c6ab59f\" -NewName \"Kim Abercrombie\" -TargetPath \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\"",
                             "This command finds a deleted user whose SAM account name is pattifuller and restores it.: Restore-ADObject -Identity \"CN=Kim Abercrombie\\0ADEL:613dc90a-2afd-49fb-8bd8-eac48c6ab59f,CN=Deleted Objects,DC=FABRIKAM,DC=COM\" -NewName \"Kim Abercrombie\" -TargetPath \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\"",
                             "This command restores an AD LDS object using ObjectGUID.: Restore-ADObject -Identity \u00276bb3bfe9-4355-48ee-b3b6-4fda6917d31d\u0027 -Server server1:50000",
                             "This command finds a deleted user whose SAM account name is pattifuller and restores it.: Restore-ADObject"
                         ],
    "Get-ADReplicationUpToDatenessVectorTable":  [
                                                     "This command gets the highest USN information for the default partition from corp-DC01.: Get-ADReplicationUpToDatenessVectorTable -Target corp-DC01",
                                                     "This command gets the highest USN information for the default partition from corp-DC01.: Get-ADReplicationUpToDatenessVectorTable -Target corp-DC01 -Scope Server",
                                                     "This command gets the highest USN information for the schema partition from corp-DC01 and corp-DC02.: Get-ADReplicationUpToDatenessVectorTable -Target corp-DC01,corp-DC02 -Partition Schema",
                                                     "This command gets the highest USN for all partitions from all the domain controllers in site NorthAmerica.: Get-ADReplicationUpToDatenessVectorTable -Target NorthAmerica -Scope Site -Partition *",
                                                     "This command gets the highest USN for the default partition from all the domain controllers in domain corp.contoso.com.: Get-ADReplicationUpToDatenessVectorTable -Target \"corp.contoso.com\" -Scope Domain -Partition Default",
                                                     "This command gets the highest USN for the configuration partition from all the domain controllers in forest corp.contoso.com.: Get-ADReplicationUpToDatenessVectorTable -Target \"corp.contoso.com\" -Scope Forest -Partition Configuration",
                                                     "This command gets the highest USN information for the default partition from corp-DC01.: Get-ADReplicationUpToDatenessVectorTable",
                                                     "This command gets the highest USN information for the default partition from corp-DC01.: Get-ADReplicationUpToDatenessVectorTable"
                                                 ],
    "Sync-ADObject":  [
                          "This command replicates an object with the distinguished name CN=AccountManagers,OU=AccountDeptOU,DC=corp,DC=contoso,DC=com from corp-DC01 to corp-DC02.: Sync-ADObject -Object \"CN=AccountManagers,OU=AccountDeptOU,DC=corp,DC=contoso,DC=com\" -Source \"corp-DC01\" -Destination \"corp-DC02\"",
                          "This command replicates an object with the distinguished name CN=AccountManagers,OU=AccountDeptOU,DC=corp,DC=contoso,DC=com from corp-DC01 to corp-DC02.: Sync-ADObject"
                      ],
    "Set-ADGroup":  [
                        "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADGroup -Server localhost:60000 -Identity \"CN=AccessControl,DC=AppNC\" -Description \"Access Group\" -Passthru",
                        "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADGroup -Instance $Group -Passthru",
                        "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADGroup",
                        "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADGroup"
                    ],
    "Set-ADCentralAccessRule":  [
                                    "This command sets the central access rule named Finance Documents Rule with a new resource condition. The resource condition scopes the resources to ones containing the value Finance in their Department resource property.: Set-ADCentralAccessRule -Identity \"Finance Documents Rule\" -ResourceCondition $resourceCondition",
                                    "This example sets the central access rule named Finance Documents Rule with a new resource condition and new permissions.: Set-ADCentralAccessRule -Identity \"Finance Documents Rule\" -ResourceCondition $ResourceCondition -CurrentAcl $currentAcl",
                                    "This command sets the central access rule named Finance Documents Rule with a new resource condition. The resource condition scopes the resources to ones containing the value Finance in their Department resource property.: Set-ADCentralAccessRule",
                                    "This command sets the central access rule named Finance Documents Rule with a new resource condition. The resource condition scopes the resources to ones containing the value Finance in their Department resource property.: Set-ADCentralAccessRule"
                                ],
    "Get-ADAuthenticationPolicy":  [
                                       "This command gets an authentication policy object by specifying the object name.: Get-ADAuthenticationPolicy",
                                       "This command gets an authentication policy object by specifying the object name.: Get-ADAuthenticationPolicy",
                                       "This command gets an authentication policy object by specifying the object name.: Get-ADAuthenticationPolicy",
                                       "This command gets an authentication policy object by specifying the object name.: Get-ADAuthenticationPolicy",
                                       "This command gets an authentication policy object by specifying the object name.: Get-ADAuthenticationPolicy -Identity AuthenticationPolicy01",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADAuthenticationPolicy -LDAPFilter \"(name=AuthenticationPolicy*)\" -Server Server01",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADAuthenticationPolicy -Filter \"Name -like \u0027AuthenticationPolicy*\u0027\" -Server Server02",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADAuthenticationPolicy -Filter * | Format-Table Name, Enforce -AutoSize",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADAuthenticationPolicy -Identity \"AuthenticationPolicy01\" -Properties \"*\""
                                   ],
    "New-ADReplicationSiteLink":  [
                                      "This command creates a new site link named NorthAmerica-Europe linking the two sites NorthAmerica and Europe.: New-ADReplicationSiteLink -Name \"NorthAmerica-Europe\" -SitesIncluded NorthAmerica,Europe",
                                      "This command creates a new site link named Europe-Asia to link two sites, Europe and Asia. The command also enables change notification on the new object.: New-ADReplicationSiteLink -Name \"Europe-Asia\" -SitesIncluded Europe,Asia -Cost 100 -ReplicationFrequencyInMinutes 15 -InterSiteTransportProtocol IP",
                                      "This command creates a new site link named Europe-Asia to link two sites, Europe and Asia. The command also enables change notification on the new object.: New-ADReplicationSiteLink -Name \"NorthAmerica-SouthAmerica\" -SitesIncluded NorthAmerica,SouthAmerica -ReplicationSchedule $Schedule",
                                      "This command creates a new site link named Europe-Asia to link two sites, Europe and Asia. The command also enables change notification on the new object.: New-ADReplicationSiteLink -Name \"Europe-Asia\" -SitesIncluded Europe,Asia -OtherAttributes @{\u0027options\u0027=1}",
                                      "This command creates a new site link named NorthAmerica-Europe linking the two sites NorthAmerica and Europe.: New-ADReplicationSiteLink"
                                  ],
    "Disable-ADOptionalFeature":  [
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADOptionalFeature",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADOptionalFeature",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADOptionalFeature",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADOptionalFeature @params",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADOptionalFeature @params",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADOptionalFeature @params",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADOptionalFeature @params"
                                  ],
    "Remove-ADFineGrainedPasswordPolicy":  [
                                               "This command removes the fine-grained password policy object named MyPolicy.: Remove-ADFineGrainedPasswordPolicy -Identity MyPolicy",
                                               "This command removes the fine-grained password policy object with DistinguishedName CN=MyPolicy,CN=Password Settings Container,CN=System,DC=USER01,DC=COM.: Remove-ADFineGrainedPasswordPolicy -Identity \u0027CN=MyPolicy,CN=Password Settings Container,CN=System,DC=USER01,DC=COM\u0027",
                                               "This command removes the fine-grained password policy object named MyPolicy.: Remove-ADFineGrainedPasswordPolicy"
                                           ],
    "New-ADComputer":  [
                           "This command creates a new computer account in the OU OU=ApplicationServers,OU=ComputerAccounts,OU=Managed,DC=USER02,DC=COM.: New-ADComputer -Name \"USER02-SRV2\" -SamAccountName \"USER02-SRV2\" -Path \"OU=ApplicationServers,OU=ComputerAccounts,OU=Managed,DC=USER02,DC=COM\"",
                           "This command creates a new computer account under a particular OU, which is enabled and located in Redmond, WA.: New-ADComputer -Name \"USER01-SRV3\" -SamAccountName \"USER01-SRV3\" -Path \"OU=ApplicationServers,OU=ComputerAccounts,OU=Managed,DC=USER01,DC=COM\" -Enabled $True -Location \"Redmond,WA\"",
                           "This example creates a new computer account from a template object.: New-ADComputer -Instance $TemplateComp -Name \"LabServer-01\"",
                           "You can use this cmdlet to provision a computer account before the computer is added to the domain. These pre-created computer objects can be used with offline domain join, unsecure domain join, and RODC domain join scenarios.: New-ADComputer"
                       ],
    "Get-ADDomainControllerPasswordReplicationPolicy":  [
                                                            "This command gets from an RODC domain controller password replication policy the allowed accounts showing the name and object class of each.: Get-ADDomainControllerPasswordReplicationPolicy -Identity \"USER01-RODC1\" -Allowed | ft Name,ObjectClass",
                                                            "If you specify a writeable domain controller for this cmdlet, the cmdlet returns a non-terminating error.: Get-ADDomainControllerPasswordReplicationPolicy",
                                                            "If you specify a writeable domain controller for this cmdlet, the cmdlet returns a non-terminating error.: Get-ADDomainControllerPasswordReplicationPolicy"
                                                        ],
    "Get-ADTrust":  [
                        "This command gets all of the trusted domain objects in the forest.: Get-ADTrust -Filter *",
                        "This command gets all the trusted domain objects with corp.contoso.com as the trust partner.: Get-ADTrust -Filter \"Target -eq \u0027corp.contoso.com\u0027\"",
                        "This command gets the trusted domain object with name corp.contoso.com.: Get-ADTrust -Identity \"corp.contoso.com\"",
                        "This command gets all of the trusted domain objects in the forest.: Get-ADTrust",
                        "This command gets all of the trusted domain objects in the forest.: Get-ADTrust",
                        "This command gets all of the trusted domain objects in the forest.: Get-ADTrust",
                        "This command gets all of the trusted domain objects in the forest.: Get-ADTrust"
                    ],
    "Remove-ADOrganizationalUnit":  [
                                        "This command removes an OU and all of its children. If the OU is protected from deletion, then the OU and its children are not deleted. If the OU is not protected but any of the children are, then the OU and its children are deleted.: Remove-ADOrganizationalUnit -Identity \"OU=Accounting,DC=FABRIKAM,DC=COM\" -Recursive",
                                        "This command removes an OU that is specified by its objectGUID and suppresses the confirmation prompt.: Remove-ADOrganizationalUnit -Identity \"1b228aa5-2c14-48b8-ad8a-2685dc22e055\" -Confirm:$False",
                                        "This command removes the Accounting OU.: Remove-ADOrganizationalUnit -Identity \"OU=Accounting,DC=FABRIKAM,DC=COM\"",
                                        "This command removes an OU from an AD LDS instance.: Remove-ADOrganizationalUnit -Identity \"OU=Managed,DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\" -Confirm:$False",
                                        "This command removes an OU and all of its children. If the OU is protected from deletion, then the OU and its children are not deleted. If the OU is not protected but any of the children are, then the OU and its children are deleted.: Remove-ADOrganizationalUnit"
                                    ],
    "Remove-ADUser":  [
                          "This command removes the user with SAM account name GlenJohn.: Remove-ADUser -Identity GlenJohn",
                          "This command removes the user with the distinguished name CN=Glen John,OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.: Remove-ADUser -Identity \"CN=Glen John,OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\"",
                          "This command removes the user with SAM account name GlenJohn.: Remove-ADUser"
                      ],
    "Get-ADReplicationConnection":  [
                                        "This command gets all of the replication connections.: Get-ADReplicationConnection -Filter *",
                                        "This command gets all replication connections that replicate from corp-DC01.: Get-ADReplicationConnection -Filter \"ReplicateFromDirectoryServer -eq \u0027corp-DC01\u0027\"",
                                        "This command gets the replication connection with the GUID 5f98e288-19e0-47a0-9677-57f05ed54f6b.: Get-ADReplicationConnection -Identity \"5f98e288-19e0-47a0-9677-57f05ed54f6b\"",
                                        "This command gets all the properties of the replication connection with the GUID 5f98e288-19e0-47a0-9677-57f05ed54f6b.: Get-ADReplicationConnection -Identity \"5f98e288-19e0-47a0-9677-57f05ed54f6b\" -Properties *",
                                        "This command gets all of the replication connections.: Get-ADReplicationConnection",
                                        "This command gets all of the replication connections.: Get-ADReplicationConnection"
                                    ],
    "New-ADGroup":  [
                        "This command creates a new group using the property values from a current group.: New-ADGroup -Name \"RODC Admins\" -SamAccountName RODCAdmins -GroupCategory Security -GroupScope Global -DisplayName \"RODC Administrators\" -Path \"CN=Users,DC=Fabrikam,DC=Com\" -Description \"Members of this group are RODC Administrators\"",
                        "This command creates a group named AccountLeads on an AD LDS instance.: New-ADGroup -Server localhost:60000 -Path \"OU=AccountDeptOU,DC=AppNC\" -Name \"AccountLeads\" -GroupScope DomainLocal -GroupCategory Distribution",
                        "The following methods explain different ways to create an object by using this cmdlet.: New-ADGroup"
                    ],
    "Get-ADUser":  [
                       "This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.: Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\"",
                       "This command gets all users that have a name that ends with SvcAccount.: Get-ADUser -Filter \u0027Name -like \"*SvcAccount\"\u0027 | Format-Table Name,SamAccountName -A",
                       "This command gets all of the properties of the user with the SAM account name ChewDavid.: Get-ADUser -Identity ChewDavid -Properties *",
                       "This command gets the user with the name ChewDavid in the Active Directory Lightweight Directory Services (AD LDS) instance.: Get-ADUser -Filter \"Name -eq \u0027ChewDavid\u0027\" -SearchBase \"DC=AppNC\" -Properties \"mail\" -Server lds.Fabrikam.com:50000",
                       "This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.: Get-ADUser",
                       "This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.: Get-ADUser",
                       "This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.: Get-ADUser",
                       "To get a list of the most commonly used properties of an ADUser object, use the following command:: Get-ADUser",
                       "Official code example: Get-ADUser",
                       "Official code example: Get-ADUser"
                   ],
    "Get-ADAccountResultantPasswordReplicationPolicy":  [
                                                            "The policy is one of the following values:: Get-ADAccountResultantPasswordReplicationPolicy",
                                                            "The policy is one of the following values:: Get-ADAccountResultantPasswordReplicationPolicy",
                                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADAccountResultantPasswordReplicationPolicy",
                                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADAccountResultantPasswordReplicationPolicy -Identity DavidChe -DomainController DC1",
                                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADAccountResultantPasswordReplicationPolicy @params"
                                                        ],
    "Get-ADCentralAccessPolicy":  [
                                      "This command retrieves a list of all central access policies.: Get-ADCentralAccessPolicy",
                                      "This command retrieves a list of all central access policies.: Get-ADCentralAccessPolicy",
                                      "This command retrieves a list of all central access policies.: Get-ADCentralAccessPolicy",
                                      "This command retrieves a list of all central access policies.: Get-ADCentralAccessPolicy",
                                      "This command retrieves a list of all central access policies.: Get-ADCentralAccessPolicy -Filter *",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADCentralAccessPolicy -Filter \"Members -eq \u0027Finance Documents Rule\u0027\"",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADCentralAccessPolicy -Identity \"Finance Policy\""
                                  ],
    "Set-ADServiceAccount":  [
                                 "This command sets the description of the MSA identified as Service1 to Secretive Data Server.: Set-ADServiceAccount -Identity Service1 -Description \"Secretive Data Server\"",
                                 "This command sets the principals allowed to retrieve the password for this MSA to be limited to members of the specified Active Directory group account.: Set-ADServiceAccount -Identity Mongol01ADAM -ServicePrincipalNames @{replace=\"ADAMwdb/a.contoso.com\", \"ADAMbdb/a.contoso.com\"}",
                                 "This command sets the principals allowed to retrieve the password for this MSA to be limited to members of the specified Active Directory group account.: Set-ADServiceAccount -Identity Service1 -PrincipalsAllowedToRetrieveManagedPassword \"MsaAdmins.corp.contoso.com\"",
                                 "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADServiceAccount -Identity AccessTSQA -ServicePrincipalNames @{Add=ACCESSAPP/TSQA.contoso.com}",
                                 "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADServiceAccount -Instance $ServiceAccount",
                                 "This command sets the description of the MSA identified as Service1 to Secretive Data Server.: Set-ADServiceAccount",
                                 "This command sets the description of the MSA identified as Service1 to Secretive Data Server.: Set-ADServiceAccount"
                             ],
    "Remove-ADGroupMember":  [
                                 "This command removes the user with the SAM account name DavidChew from the group DocumentReaders.: Remove-ADGroupMember -Identity DocumentReaders -Members DavidChew",
                                 "This command removes the users with SAM account name administrator and DavidChew from the group DocumentReaders.: Remove-ADGroupMember -Identity \"DocumentReaders\" -Members administrator,DavidChew",
                                 "This command removes the user with the SAM account name DavidChew from the group DocumentReaders.: Remove-ADGroupMember"
                             ],
    "Set-ADReplicationSiteLinkBridge":  [
                                            "This command updates the site link bridge NorthAmerica-Asia to use Europe2 instead of Europe.: Set-ADReplicationSiteLinkBridge -Identity \"NorthAmerica-Asia\" -SiteLinksIncluded @{Add=\u0027NorthAmerica-Europe2\u0027,\u0027Europe2-Asia\u0027;Remove=\u0027NorthAmerica-Europe\u0027,\u0027Europe-Asia\u0027}",
                                            "This command updates the site link bridge NorthAmerica-Asia to use Europe2 instead of Europe.: Set-ADReplicationSiteLinkBridge",
                                            "This command updates the site link bridge NorthAmerica-Asia to use Europe2 instead of Europe.: Set-ADReplicationSiteLinkBridge"
                                        ],
    "Uninstall-ADServiceAccount":  [
                                       "This command uninstalls the MSA identified as SQL-SRV1 from the local machine.: Uninstall-ADServiceAccount -Identity SQL-SRV1",
                                       "This command uninstalls the specified standalone MSA from a server located in a read-only domain controller site such as a perimeter network.: Uninstall-ADServiceAccount -Identity sql-hr-01 -ForceRemoveLocal",
                                       "This command uninstalls the MSA identified as SQL-SRV1 from the local machine.: Uninstall-ADServiceAccount"
                                   ],
    "Remove-ADResourcePropertyListMember":  [
                                                "This command removes the resource property specified as a list member, Country, from the specified resource property list, Global Resource Property List.: Remove-ADResourcePropertyListMember -Identity \"Global Resource Property List\" -Members Country",
                                                "This command removes the resource properties named Department and Country from the resource property list, Corporate Resource Property List.: Remove-ADResourcePropertyListMember -Identity \"Corporate Resource Property List\" -Members Department,Country",
                                                "This command removes the resource property specified as a list member, Country, from the specified resource property list, Global Resource Property List.: Remove-ADResourcePropertyListMember"
                                            ],
    "Get-ADReplicationQueueOperation":  [
                                            "This command gets the pending operations in the replication queue for the domain controller corp-DC01 as specified by its fully qualified domain name (FQDN).: Get-ADReplicationQueueOperation -Server \"corp-DC01.corp.contoso.com\"",
                                            "The replication queue operates in the following manner: suppose a domain controller has five inbound replication connections. As the domain controller formulates change requests, either by a schedule being reached or from a notification, it adds a work item for each request to the end of the queue of pending synchronization requests. Each pending synchronization request represents one \u0026lt;source domain controller, directory partition\u0026gt; pair, such as synchronize the schema directory partition from DC1 or delete the ApplicationX directory partition.: Get-ADReplicationQueueOperation"
                                        ],
    "Set-ADOrganizationalUnit":  [
                                     "This command sets the description of the OU with the distinguished name OU=UserAccounts,DC=FABRIKAM,DC=COM.: Set-ADOrganizationalUnit -Identity \"OU=UserAccounts,DC=FABRIKAM,DC=COM\" -Description \"This Organizational Unit holds all of the users accounts of FABRIKAM.COM\"",
                                     "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADOrganizationalUnit -Identity \"OU=UserAccounts,DC=FABRIKAM,DC=COM\" -ProtectedFromAccidentalDeletion $false",
                                     "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADOrganizationalUnit -Identity \"OU=AsiaPacific,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM\" -Country \"AU\" -StreetAddress \"45 Martens Place\" -City Balmoral -State QLD -PostalCode 4171 -Replace @{co=\"Australia\"}",
                                     "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADOrganizationalUnit -Identity \"OU=Managed,DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\" -Country \"UK\"",
                                     "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADOrganizationalUnit -Instance $OrganizationalUnit",
                                     "This command sets the description of the OU with the distinguished name OU=UserAccounts,DC=FABRIKAM,DC=COM.: Set-ADOrganizationalUnit",
                                     "This command sets the description of the OU with the distinguished name OU=UserAccounts,DC=FABRIKAM,DC=COM.: Set-ADOrganizationalUnit"
                                 ],
    "New-ADOrganizationalUnit":  [
                                     "This command creates an OU named UserAccounts that is protected from accidental deletion. Note that accidental protection is implicit.: New-ADOrganizationalUnit -Name \"UserAccounts\" -Path \"DC=FABRIKAM,DC=COM\"",
                                     "This command creates an OU named UserAccounts that is not protected from accidental deletion.: New-ADOrganizationalUnit -Name \"UserAccounts\" -Path \"DC=FABRIKAM,DC=COM\" -ProtectedFromAccidentalDeletion $False",
                                     "This command uses the data from the OU OU=UserAccounts,DC=Fabrikam,DC=com as a template for another OU.: New-ADOrganizationalUnit -Name \"UserAccounts\" -Path \"DC=FABRIKAM,DC=COM\" -OtherAttributes @{seeAlso=\"CN=HumanResourceManagers,OU=Groups,OU=Managed,DC=Fabrikam,DC=com\";managedBy=\"CN=TomC,DC=FABRIKAM,DC=COM\"}",
                                     "This command uses the data from the OU OU=UserAccounts,DC=Fabrikam,DC=com as a template for another OU.: New-ADOrganizationalUnit -Name \"TomCReports\" -Instance $OuTemplate",
                                     "This command creates an OU named Managed in an AD LDS instance.: New-ADOrganizationalUnit -Name \"Managed\" -Path \"DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\"",
                                     "The following methods describe how to create an object by using this cmdlet.: New-ADOrganizationalUnit"
                                 ],
    "Reset-ADServiceAccountPassword":  [
                                           "This command resets the password on the standalone managed service account ServiceAccount1.: Reset-ADServiceAccountPassword -Identity ServiceAccount1",
                                           "Note: When you reset the password for a computer, you also reset all of the standalone MSA passwords for that computer.: Reset-ADServiceAccountPassword"
                                       ],
    "Get-ADGroupMember":  [
                              "This command gets all the members of the Administrators group.: Get-ADGroupMember",
                              "This command gets all the group members of the Administrators group.: Get-ADGroupMember -Identity Administrators",
                              "This command gets all the members of the Enterprise Admins group including the members of any child groups.: Get-ADGroupMember -Identity \"Enterprise Admins\" -Recursive",
                              "This command gets all the members of the Administrators group.: Get-ADGroupMember"
                          ],
    "New-ADFineGrainedPasswordPolicy":  [
                                            "This example creates two new fine-grained password policy objects using a template object.: New-ADFineGrainedPasswordPolicy -Name \"DomainUsersPSO\" -Precedence 500 -ComplexityEnabled $true -Description \"The Domain Users Password Policy\" -DisplayName \"Domain Users PSO\" -LockoutDuration \"0.12:00:00\" -LockoutObservationWindow \"0.00:15:00\" -LockoutThreshold 10",
                                            "This example creates two new fine-grained password policy objects using a template object.: New-ADFineGrainedPasswordPolicy -Instance $TemplatePSO -Name \"SvcAccPSO\" -Precedence 100 -Description \"The Service Accounts Password Policy\" -DisplayName \"Service Accounts PSO\" -MaxPasswordAge \"30.00:00:00\" -MinPasswordLength 20",
                                            "This example creates two new fine-grained password policy objects using a template object.: New-ADFineGrainedPasswordPolicy -Instance $TemplatePSO -Name \"AdminsPSO\" -Precedence 200 -Description \"The Domain Administrators Password Policy\" -DisplayName \"Domain Administrators PSO\" -MaxPasswordAge \"15.00:00:00\" -MinPasswordLength 10",
                                            "The following methods explain different ways to create an object by using this cmdlet.: New-ADFineGrainedPasswordPolicy"
                                        ],
    "Install-ADServiceAccount":  [
                                     "Specifies the authentication method to use. Possible values for this parameter include: The acceptable values for this parameter are:: Install-ADServiceAccount -Identity \u0027SQL-HR-svc-01\u0027",
                                     "Specifies the authentication method to use. Possible values for this parameter include: The acceptable values for this parameter are:: Install-ADServiceAccount $Account",
                                     "Specifies the authentication method to use. Possible values for this parameter include: The acceptable values for this parameter are:: Install-ADServiceAccount -Identity \u0027SQL-HR-svc-01\u0027 -PromptForPassword",
                                     "Specifies the authentication method to use. Possible values for this parameter include: The acceptable values for this parameter are:: Install-ADServiceAccount -Identity \u0027SQL-HR-svc-01\u0027 -AccountPassword (ConvertTo-SecureString -AsPlainText \"p@ssw0rd\" -Force)",
                                     "Specifies the authentication method to use. Possible values for this parameter include: The acceptable values for this parameter are:: Install-ADServiceAccount"
                                 ],
    "New-ADClaimTransformPolicy":  [
                                       "This command creates a new claims transformation policy named DenyAllPolicy that denies all claims, both those that are sent as well as those that are received.: New-ADClaimTransformPolicy -Name \"DenyAllPolicy\" -DenyAll",
                                       "This command creates a new claims transformation policy named AllowAllExceptCompanyAndDepartmentPolicy that allows all claims to be sent or received except for the claims Company and Department.: New-ADClaimTransformPolicy -Name \"AllowAllExceptCompanyAndDepartmentPolicy\" -AllowAllExcept Company,Department",
                                       "This command creates a new claims transformation policy named HumanResourcesToHrPolicy that transforms the value Human Resources to HR in the claim Department.: New-ADClaimTransformPolicy -Name \"HumanResourcesToHrPolicy\" -Rule \u0027C1:[Type==\"ad://ext/Department:88ceb0fe88a125db\", Value==\"Human Resources\", ValueType==\"string\"] =\u003e issue(Type=C1.Type, Value=\"HR\", ValueType=C1.ValueType);\u0027",
                                       "This example creates a claims transformation policy named MyRule with the rule specified in C:\\rule.txt.: New-ADClaimTransformPolicy -Name \"MyRule\" -Rule $Rule",
                                       "This command creates a new claims transformation policy named DenyAllPolicy that denies all claims, both those that are sent as well as those that are received.: New-ADClaimTransformPolicy",
                                       "This command creates a new claims transformation policy named DenyAllPolicy that denies all claims, both those that are sent as well as those that are received.: New-ADClaimTransformPolicy",
                                       "This command creates a new claims transformation policy named DenyAllPolicy that denies all claims, both those that are sent as well as those that are received.: New-ADClaimTransformPolicy",
                                       "This command creates a new claims transformation policy named DenyAllPolicy that denies all claims, both those that are sent as well as those that are received.: New-ADClaimTransformPolicy",
                                       "This command creates a new claims transformation policy named DenyAllPolicy that denies all claims, both those that are sent as well as those that are received.: New-ADClaimTransformPolicy",
                                       "Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory snapshot instance.: New-ADClaimTransformPolicy MyRule -Rule $Rule"
                                   ],
    "Remove-ADGroup":  [
                           "This command removes the group that has SAM account name SanjaysReports.: Remove-ADGroup -Identity SanjaysReports",
                           "This command removes the group that has SAM account name SanjaysReports.: Remove-ADGroup"
                       ],
    "Get-ADDomain":  [
                         "This command gets the domain information for the domain user.com.: Get-ADDomain -Identity user.com",
                         "This command gets the domain information of the current local computer domain.: Get-ADDomain -Current LocalComputer",
                         "This command gets the domain information for the domain of the currently logged on user.: Get-ADDomain -Current LoggedOnUser",
                         "This command gets the domain information for the domain of the currently logged on user.: Get-ADDomain",
                         "This command gets the domain information for the domain user.com.: Get-ADDomain",
                         "This command gets the domain information for the domain user.com.: Get-ADDomain",
                         "Official code example: Get-ADDomain"
                     ],
    "Clear-ADClaimTransformLink":  [
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Clear-ADClaimTransformLink",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Clear-ADClaimTransformLink",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Clear-ADClaimTransformLink -Identity \u0027corp.contoso.com\u0027 -Policy DenyAllPolicy",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Clear-ADClaimTransformLink -Identity \u0027corp.contoso.com\u0027 -TrustRole Trusted",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Clear-ADClaimTransformLink @params"
                                   ],
    "Remove-ADComputer":  [
                              "This command removes a specified computer from Active Directory.: Remove-ADComputer -Identity \"USER04-SRV4\"",
                              "This command removes a specified computer from Active Directory.: Remove-ADComputer"
                          ],
    "New-ADDCCloneConfigFile":  [
                                    "This command creates a clone domain controller named VirtualDC2 with a static IPv4 address.: New-ADDCCloneConfigFile -Static -IPv4Address \"********\" -IPv4DNSResolver \"********\" -IPv4SubnetMask \"*************\" -CloneComputerName \"VirtualDC2\" -IPv4DefaultGateway \"********\" -PreferredWINSServer \"********\" -SiteName \"REDMOND\"",
                                    "This command creates a clone domain controller named Clone1 with a static IPv6 setting.: New-ADDCCloneConfigFile -Static -CloneComputerName \"Clone1\" -IPv6DNSResolver \"FEC0:0:0:FFFF::1\"",
                                    "This command creates a clone domain controller named Clone2 with dynamic IPv4 settings.: New-ADDCCloneConfigFile -AlternateWINSServer \"********\" -CloneComputerName \"Clone2\"-IPv4DNSResolver \"********\" -PreferredWINSServer \"********\"",
                                    "This command creates a clone domain controller with dynamic IPv6 settings.: New-ADDCCloneConfigFile -IPv6DNSResolver \"FEC0:0:0:FFFF::1\" -SiteName \"REDMOND\"",
                                    "This command creates a clone domain controller named Clone2 with static IPv4 and static IPv6 settings.: New-ADDCCloneConfigFile -Static -IPv4Address \"********\" -IPv4DNSResolver \"********\" -IPv4SubnetMask \"*************\" -Static -IPv6DNSResolver \"FEC0:0:0:FFFF::1\" -CloneComputerName \"Clone2\" -PreferredWINSServer \"********\"",
                                    "This command creates a clone domain controller named Clone2 with static IPv4 and dynamic IPv6 settings.: New-ADDCCloneConfigFile -IPv4Address \"********\" -IPv4DNSResolver \"********\" -IPv4SubnetMask \"*************\" -IPv4DefaultGateway \"********\" -IPv6DNSResolver \"FEC0:0:0:FFFF::1\"",
                                    "This command creates a clone domain controller named Clone1 with dynamic IPv4 and static IPv6 settings.: New-ADDCCloneConfigFile -Static -IPv6DNSResolver \"FEC0:0:0:FFFF::1\" -CloneComputerName \"Clone1\" -PreferredWINSServer \"********\" -SiteName \"REDMOND\"",
                                    "There are two modes of operation for this cmdlet, depending on where it is executed. When run on the domain controller that is being prepared for cloning, it will run the following pre-requisite checks to make sure this domain controller is adequately prepared for cloning:: New-ADDCCloneConfigFile",
                                    "There are two modes of operation for this cmdlet, depending on where it is executed. When run on the domain controller that is being prepared for cloning, it will run the following pre-requisite checks to make sure this domain controller is adequately prepared for cloning:: New-ADDCCloneConfigFile",
                                    "There are two modes of operation for this cmdlet, depending on where it is executed. When run on the domain controller that is being prepared for cloning, it will run the following pre-requisite checks to make sure this domain controller is adequately prepared for cloning:: New-ADDCCloneConfigFile",
                                    "There are two modes of operation for this cmdlet, depending on where it is executed. When run on the domain controller that is being prepared for cloning, it will run the following pre-requisite checks to make sure this domain controller is adequately prepared for cloning:: New-ADDCCloneConfigFile",
                                    "There are two modes of operation for this cmdlet, depending on where it is executed. When run on the domain controller that is being prepared for cloning, it will run the following pre-requisite checks to make sure this domain controller is adequately prepared for cloning:: New-ADDCCloneConfigFile"
                                ],
    "Get-ADCentralAccessRule":  [
                                    "This command retrieves a list of all central access rules.: Get-ADCentralAccessRule",
                                    "This command retrieves a list of all central access rules.: Get-ADCentralAccessRule",
                                    "This command retrieves a list of all central access rules.: Get-ADCentralAccessRule",
                                    "This command retrieves a list of all central access rules.: Get-ADCentralAccessRule",
                                    "This command retrieves a list of all central access rules.: Get-ADCentralAccessRule -Filter *",
                                    "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADCentralAccessRule -Filter \"ResourceCondition -like \u0027*Department*\u0027\"",
                                    "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADCentralAccessRule -Identity \"Financial Documents Rule\""
                                ],
    "New-ADServiceAccount":  [
                                 "The following methods explain different ways to create an object by using this cmdlet.: New-ADServiceAccount",
                                 "The following methods explain different ways to create an object by using this cmdlet.: New-ADServiceAccount",
                                 "The following methods explain different ways to create an object by using this cmdlet.: New-ADServiceAccount",
                                 "The following methods explain different ways to create an object by using this cmdlet.: New-ADServiceAccount",
                                 "This command creates an enabled managed service account in Active Directory Domain Services (AD DS).: New-ADServiceAccount",
                                 "This command creates an enabled managed service account in Active Directory Domain Services (AD DS).: New-ADServiceAccount",
                                 "This command creates an enabled managed service account in Active Directory Domain Services (AD DS).: New-ADServiceAccount",
                                 "This command creates an enabled managed service account in Active Directory Domain Services (AD DS).: New-ADServiceAccount -Name \"Service01\" -DNSHostName \"Service01.contoso.com\" -Enabled $True",
                                 "This command creates a managed service account and registers its service principal name.: New-ADServiceAccount @params",
                                 "This command creates a managed service account and restricts its use to a single computer.: New-ADServiceAccount -Name \"Service01\" -RestrictToSingleComputer",
                                 "This command creates a managed service account and restricts its use to outbound authentication.: New-ADServiceAccount -Name \"Service01\" -RestrictToOutboundAuthenticationOnly",
                                 "This command creates a delegated managed service account.: New-ADServiceAccount @params",
                                 "Specifies a new password value for the service account. This value is stored as an encrypted string.: New-ADServiceAccount @params"
                             ],
    "Get-ADServiceAccount":  [
                                 "This command gets a managed service account with SAM account name service1.: Get-ADServiceAccount -Identity service1",
                                 "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADServiceAccount -Identity S-1-5-21-*********-**********-**********-29770",
                                 "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADServiceAccount -Filter \"HostComputers -eq \u0027CN=SQL-Server-1, DC=contoso,DC=com\u0027\"",
                                 "This command gets a managed service account with SAM account name service1.: Get-ADServiceAccount",
                                 "This command gets a managed service account with SAM account name service1.: Get-ADServiceAccount",
                                 "This command gets a managed service account with SAM account name service1.: Get-ADServiceAccount"
                             ],
    "Get-ADReplicationSiteLink":  [
                                      "This command gets all the site links.: Get-ADReplicationSiteLink -Filter *",
                                      "This command gets all site links that include NorthAmerica.: Get-ADReplicationSiteLink -Filter \"SitesIncluded -eq \u0027NorthAmerica\u0027\" | Format-Table Name,SitesIncluded -A",
                                      "This command gets all site links that have a cost greater than 100 and a replication frequency less than 15 minutes.: Get-ADReplicationSiteLink -Filter \"Cost -gt 100 -and ReplicationFrequencyInMinutes -lt 15\"",
                                      "This command gets the site link with name Europe-Asia.: Get-ADReplicationSiteLink -Identity \"Europe-Asia\"",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADReplicationSiteLink -Identity \"Europe-Asia\" -Properties ReplicationSchedule",
                                      "This command gets all the site links.: Get-ADReplicationSiteLink",
                                      "This command gets all the site links.: Get-ADReplicationSiteLink"
                                  ],
    "Add-ADGroupMember":  [
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADGroupMember",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADGroupMember",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADGroupMember",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADGroupMember -Identity SvcAccPSOGroup -Members SQL01, SQL02",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADGroupMember -Members \u0027CN=PattiFuller,OU=AccountDeptOU,DC=AppNC\u0027",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADGroupMember",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADGroupMember -Identity $Group -Members $User -Server \"europe.fabrikam.com\""
                          ],
    "Remove-ADCentralAccessPolicyMember":  [
                                               "This command removes the resource property named Finance Documents Rule from the central access policy named Finance Policy.: Remove-ADCentralAccessPolicyMember -Identity \"Finance Policy\" -Members \"Finance Documents Rule\"",
                                               "This command removes the central access rules named Finance Documents Rule and Corporate Documents Rule from the central access policy Finance Policy.: Remove-ADCentralAccessPolicyMember -Identity \"Finance Policy\" -Members \"Finance Documents Rule\",\"Corporate Documents Rule\"",
                                               "This command removes the resource property named Finance Documents Rule from the central access policy named Finance Policy.: Remove-ADCentralAccessPolicyMember"
                                           ],
    "New-ADClaimType":  [
                            "This command creates a new device claim type with display name Bitlocker Enabled with the source OID 1.3.6.1.4.1.311.67.1.1. The claim type set to disabled.: New-ADClaimType -DisplayName \"Title\" -SourceAttribute \"title\"",
                            "This command creates a new device claim type with display name Bitlocker Enabled with the source OID 1.3.6.1.4.1.311.67.1.1. The claim type set to disabled.: New-ADClaimType -DisplayName \"Employee Type\" -SourceAttribute \"employeeType\" -SuggestedValues $FullTime,$Intern,$Contractor",
                            "This command creates a new device claim type with display name Bitlocker Enabled with the source OID 1.3.6.1.4.1.311.67.1.1. The claim type set to disabled.: New-ADClaimType -DisplayName \"Bitlocker Enabled\" -SourceOID \"1.3.6.1.4.1.311.67.1.1\" -Enabled $False",
                            "The ID should only be set manually in a multi-forest environment where the same claim type needs to work across forests. By default, New-ADClaimType generates the ID automatically. For claim types to be considered identical across forests, their ID must be the same.: New-ADClaimType -DisplayName \"Title\" -SourceAttribute \"title\" -ID \"ad://ext/title\"",
                            "This command creates a new device claim type with display name Bitlocker Enabled with the source OID 1.3.6.1.4.1.311.67.1.1. The claim type set to disabled.: New-ADClaimType",
                            "This command creates a new device claim type with display name Bitlocker Enabled with the source OID 1.3.6.1.4.1.311.67.1.1. The claim type set to disabled.: New-ADClaimType",
                            "This command creates a new device claim type with display name Bitlocker Enabled with the source OID 1.3.6.1.4.1.311.67.1.1. The claim type set to disabled.: New-ADClaimType"
                        ],
    "Set-ADDomainMode":  [
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Set-ADDomainMode",
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Set-ADDomainMode",
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Set-ADDomainMode",
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Set-ADDomainMode",
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Set-ADDomainMode -Identity user01.com -DomainMode Windows2003Domain",
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Set-ADDomainMode -Identity $PDC.Domain -Server $PDC.HostName[0] -DomainMode Windows2003Domain"
                         ],
    "Remove-ADPrincipalGroupMembership":  [
                                              "This command removes the user David Chew from the Administrators group.: Remove-ADPrincipalGroupMembership -Identity \"David Chew\" -MemberOf \"Administrators\"",
                                              "This cmdlet collects all of the user, computer, service account, and group objects from the pipeline, and then removes these objects from the specified group by using one Active Directory operation.: Remove-ADPrincipalGroupMembership"
                                          ],
    "Set-ADAccountExpiration":  [
                                    "This command sets the account with SamAccountName PattiFu to expire on the 18th of October, 2008.: Set-ADAccountExpiration -Identity PattiFu -DateTime \"10/18/2008\"",
                                    "This command sets the account with SamAccountName PattiFu to expire on the 18th of October, 2008.: Set-ADAccountExpiration"
                                ],
    "Remove-ADCentralAccessPolicy":  [
                                         "This command removes the central access policy named Finance Policy.: Remove-ADCentralAccessPolicy -Identity \"Finance Policy\"",
                                         "This command removes the central access policy named Finance Policy.: Remove-ADCentralAccessPolicy"
                                     ],
    "Show-ADAuthenticationPolicyExpression":  [
                                                  "Specifies the authentication method to use. The acceptable values for this parameter are:: Show-ADAuthenticationPolicyExpression -AllowedToAuthenticateFrom \u003e someFile.txt",
                                                  "Specifies the authentication method to use. The acceptable values for this parameter are:: Show-ADAuthenticationPolicyExpression",
                                                  "Specifies the authentication method to use. The acceptable values for this parameter are:: Show-ADAuthenticationPolicyExpression"
                                              ],
    "Get-ADOptionalFeature":  [
                                  "This command gets all of the available optional features in the current forest.: Get-ADOptionalFeature -Filter *",
                                  "This command gets the optional feature with the name Recycle Bin Feature.: Get-ADOptionalFeature -Identity \u0027Recycle Bin Feature\u0027",
                                  "This command gets the optional feature with the feature GUID 766ddcd8-acd0-445e-f3b9-a7f9b6744f2a.: Get-ADOptionalFeature -Identity 766ddcd8-acd0-445e-f3b9-a7f9b6744f2a",
                                  "This command gets the optional feature Recycle Bin Feature in an AD LDS instance.: Get-ADOptionalFeature -Identity \u0027Recycle Bin Feature\u0027 -Server server1:50000",
                                  "This command gets all of the available optional features in the current forest.: Get-ADOptionalFeature",
                                  "This command gets all of the available optional features in the current forest.: Get-ADOptionalFeature",
                                  "This command gets all of the available optional features in the current forest.: Get-ADOptionalFeature",
                                  "Official code example: Get-ADOptionalFeature",
                                  "Official code example: Get-ADOptionalFeature"
                              ],
    "Remove-ADReplicationSite":  [
                                     "This command removes the site with name Europe.: Remove-ADReplicationSite -Identity \"Europe\"",
                                     "This command removes the site with name Europe.: Remove-ADReplicationSite"
                                 ],
    "Get-ADForest":  [
                         "This command gets information for the Fabrikam.com forest.: Get-ADForest -Identity Fabrikam.com",
                         "This command gets the information for the current local computer\u0027s forest.: Get-ADForest -Current LocalComputer",
                         "This command gets the forest information of the currently logged on user.: Get-ADForest -Current LoggedOnUser",
                         "This command gets the forest information of the currently logged on user.: Get-ADForest",
                         "The domain is set to the domain of the LocalComputer or LoggedOnUser and a server is located in this domain. The credentials of the current logged on user are used to get the domain.: Get-ADForest",
                         "The domain is set to the domain of the LocalComputer or LoggedOnUser and a server is located in this domain. The credentials of the current logged on user are used to get the domain.: Get-ADForest",
                         "Official code example: Get-ADForest"
                     ],
    "Get-ADAccountAuthorizationGroup":  [
                                            "Note: Get-ADAccountAuthorizationGroup",
                                            "Note: Get-ADAccountAuthorizationGroup",
                                            "Note: Get-ADAccountAuthorizationGroup",
                                            "Note: Get-ADAccountAuthorizationGroup -Identity DavidCh",
                                            "Note: Get-ADAccountAuthorizationGroup -Identity \"CN=DavidCh,DC=AppNC\" -Server \"\u003cServer\u003e:50000\"",
                                            "Note: Get-ADAccountAuthorizationGroup -Server \"\u003cServer\u003e:50000\" -Identity Administrator |"
                                        ],
    "New-ADReplicationSiteLinkBridge":  [
                                            "This command creates a site link bridge named NorthAmerica-Asia that bridges the site links NorthAmerica-Europe and Europe-Asia.: New-ADReplicationSiteLinkBridge -Name \"NorthAmerica-Asia\" -SiteLinksIncluded \"NorthAmerica-Europe\",\"Europe-Asia\"",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: New-ADReplicationSiteLinkBridge -Name \"NorthAmerica-Asia\" -SiteLinksIncluded \"NorthAmerica-Europe\",\"Europe-Asia\" -InterSiteTransportProtocol IP",
                                            "This command creates a site link bridge named NorthAmerica-Asia that bridges the site links NorthAmerica-Europe and Europe-Asia.: New-ADReplicationSiteLinkBridge"
                                        ],
    "Enable-ADAccount":  [
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Enable-ADAccount",
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Enable-ADAccount",
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Enable-ADAccount",
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Enable-ADAccount -Identity \u0027PattiFul\u0027",
                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Enable-ADAccount -Identity \u0027CN=Patti Fuller,OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027"
                         ],
    "Clear-ADAccountExpiration":  [
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Clear-ADAccountExpiration",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Clear-ADAccountExpiration",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Clear-ADAccountExpiration",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Clear-ADAccountExpiration -Identity PattiFuller",
                                      "Specifies the authentication method to use. The acceptable values for this parameter are:: Clear-ADAccountExpiration -Identity \u0027CN=PattiFuller,DC=AppNC\u0027 -Server \u0027PATTIFU-SVR1:60000\u0027"
                                  ],
    "Get-ADGroup":  [
                        "This command gets the group with the SAM account name Administrators.: Get-ADGroup -Identity Administrators",
                        "This command gets the group with SID S-1-5-32-544 and the property member.: Get-ADGroup -Identity S-1-5-32-544 -Properties member",
                        "This command gets all groups that have a GroupCategory of Security but do not have a GroupScope of DomainLocal.: Get-ADGroup -Filter \u0027GroupCategory -eq \"Security\" -and GroupScope -ne \"DomainLocal\"\u0027",
                        "This command gets all the DomainLocal groups from the AppNC partition of the AD LDS instance.: Get-ADGroup -Server localhost:60000 -Filter \"GroupScope -eq \u0027DomainLocal\u0027\" -SearchBase \"DC=AppNC\"",
                        "This command gets the group with the SAM account name Administrators.: Get-ADGroup",
                        "This command gets the group with the SAM account name Administrators.: Get-ADGroup",
                        "This command gets the group with the SAM account name Administrators.: Get-ADGroup",
                        "Official code example: Get-ADGroup",
                        "Official code example: Get-ADGroup"
                    ],
    "Get-ADRootDSE":  [
                          "This command gets the root of the directory server information tree of the directory server from the default domain controller.: Get-ADRootDSE",
                          "This command gets the root of the directory server information tree of FABRIKAM-ADLDS1 using the FABRIKAM\\user1 credentials.: Get-ADRootDSE -Server Fabrikam-RODC1 -Properties supportedExtension",
                          "This command gets the root of the directory server information tree of FABRIKAM-ADLDS1 using the FABRIKAM\\user1 credentials.: Get-ADRootDSE -Server \"FABRIKAM-ADLDS1.Fabrikam.com:60000\" -Credential \"FABRIKAM\\User1\"",
                          "This command gets the root of the directory server information tree of the directory server from the default domain controller.: Get-ADRootDSE"
                      ],
    "Add-ADCentralAccessPolicyMember":  [
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADCentralAccessPolicyMember",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADCentralAccessPolicyMember",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADCentralAccessPolicyMember @params",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADCentralAccessPolicyMember -Members \u0027Corporate Documents Rule\u0027",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADCentralAccessPolicyMember",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADCentralAccessPolicyMember"
                                        ],
    "Get-ADReplicationPartnerMetadata":  [
                                             "This command gets the replication metadata between corp-DC01 and its inbound partners for the default partition only.: Get-ADReplicationPartnerMetadata -Target corp-DC01",
                                             "This command gets the replication metadata between corp-DC01 and its inbound partners for the default partition only (same as above).: Get-ADReplicationPartnerMetadata -Target corp-DC01 -PartnerType Inbound",
                                             "This command gets the replication metadata between corp-DC01, corp-DC02 and their respective partners only (both inbound and outbound) for the schema partition.: Get-ADReplicationPartnerMetadata -Target corp-DC01,corp-DC02 -PartnerType Both -Partition Schema",
                                             "This command gets the replication metadata for all the inbound partners of all the domain controllers within the NorthAmerica site for all hosted partitions.: Get-ADReplicationPartnerMetadata -Target NorthAmerica -Scope Site -Partition *",
                                             "This command gets the replication metadata for all the domain controllers that are inbound partners for the default partition in the domain corp.contoso.com.: Get-ADReplicationPartnerMetadata -Target \"corp.contoso.com\" -Scope Domain",
                                             "This command gets the replication metadata for all the domain controllers that are inbound partners for the configuration partition in the forest corp.contoso.com.: Get-ADReplicationPartnerMetadata -Target \"corp.contoso.com\" -Scope Forest -Partition Configuration",
                                             "This command gets the replication metadata between corp-DC01 and its inbound partners for the default partition only.: Get-ADReplicationPartnerMetadata",
                                             "This command gets the replication metadata between corp-DC01 and its inbound partners for the default partition only.: Get-ADReplicationPartnerMetadata"
                                         ],
    "Get-ADClaimType":  [
                            "This command retrieves a list of all claim types.: Get-ADClaimType -Filter *",
                            "This command gets all the claim types that are sourced from the attribute title.: Get-ADClaimType -Filter \"SourceAttribute -eq \u0027title\u0027\"",
                            "This command gets the claim type with display name Employee Type.: Get-ADClaimType -Identity \"Employee Type\"",
                            "This command gets all properties of the claim type with display name Employee Type.: Get-ADClaimType -Identity \"Employee Type\" -Properties *",
                            "This command retrieves a list of all claim types.: Get-ADClaimType",
                            "This command retrieves a list of all claim types.: Get-ADClaimType",
                            "This command retrieves a list of all claim types.: Get-ADClaimType"
                        ],
    "Remove-ADAuthenticationPolicy":  [
                                          "Specifies the authentication method to use. The acceptable values for this parameter are:: Remove-ADAuthenticationPolicy -Identity AuthenticationPolicy01",
                                          "Specifies the authentication method to use. The acceptable values for this parameter are:: Remove-ADAuthenticationPolicy"
                                      ],
    "Add-ADResourcePropertyListMember":  [
                                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADResourcePropertyListMember",
                                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADResourcePropertyListMember",
                                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADResourcePropertyListMember @params",
                                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADResourcePropertyListMember -Members Country, Authors",
                                             "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADResourcePropertyListMember"
                                         ],
    "New-ADUser":  [
                       "The following methods explain different ways to create an object by using this cmdlet.: New-ADUser",
                       "The following methods explain different ways to create an object by using this cmdlet.: New-ADUser",
                       "The following methods explain different ways to create an object by using this cmdlet.: New-ADUser",
                       "This command creates a new user named ChewDavid and sets the account password.: New-ADUser",
                       "This command creates a new user named ChewDavid and sets the account password.: New-ADUser",
                       "This command creates a new user named ChewDavid and sets the account password.: New-ADUser",
                       "This command creates a new user named ChewDavid and sets the account password.: New-ADUser @splat",
                       "This command creates a new user named ChewDavid and sets the account password.: New-ADUser -Name \u0027ChewDavid\u0027 -OtherAttributes @{",
                       "This command creates a new user named ChewDavid and sets the account password.: New-ADUser -Name \u0027ChewDavid\u0027 -Type iNetOrgPerson -Path \u0027DC=AppNC\u0027 -Server lds.Fabrikam.com:50000",
                       "This command creates a new user named ChewDavid and sets the account password.: New-ADUser @splat",
                       "Note: New-ADUser",
                       "Note: New-ADUser",
                       "Note: New-ADUser"
                   ],
    "Complete-ADServiceAccountMigration":  [
                                               "Specifies the authentication method to use. The acceptable values for this parameter are:: Complete-ADServiceAccountMigration",
                                               "Specifies the authentication method to use. The acceptable values for this parameter are:: Complete-ADServiceAccountMigration",
                                               "Specifies the authentication method to use. The acceptable values for this parameter are:: Complete-ADServiceAccountMigration @params",
                                               "Specifies the authentication method to use. The acceptable values for this parameter are:: Complete-ADServiceAccountMigration @params"
                                           ],
    "Grant-ADAuthenticationPolicySiloAccess":  [
                                                   "This command grants access to the authentication policy silo named AuthenticationPolicySilo01 to the user account named User01.: Grant-ADAuthenticationPolicySiloAccess -Identity AuthenticationPolicySilo01 -Account User01",
                                                   "This command grants access to the authentication policy silo named AuthenticationPolicySilo01 to the user account named User01.: Grant-ADAuthenticationPolicySiloAccess"
                                               ],
    "Get-ADComputerServiceAccount":  [
                                         "This command gets the service accounts hosted on a computer account ComputerAcct1.: Get-ADComputerServiceAccount -Identity ComputerAcct1",
                                         "This command gets the service accounts hosted on a computer account ComputerAcct1.: Get-ADComputerServiceAccount"
                                     ],
    "Remove-ADObject":  [
                            "This command removes the object identified by the distinguished name CN=AmyAl-LPTOP,CN=Computers,DC=FABRIKAM,DC=COM.: Remove-ADObject -Identity \u0027CN=AmyAl-LPTOP,CN=Computers,DC=FABRIKAM,DC=COM\u0027",
                            "This command deletes the container with the distinguished name OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM. All the children of the container, including the ones that are protected from accidental deletion, are also deleted.: Remove-ADObject -Identity \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\" -Recursive",
                            "This command removes the object with the GUID 65511e76-ea80-45e1-bc93-08a78d8c4853 without prompting for confirmation.: Remove-ADObject -Identity \"65511e76-ea80-45e1-bc93-08a78d8c4853\" -Confirm:$False",
                            "This command removes the object with distinguished name CN=InternalApps,DC=AppNC from an LDS instance.: Remove-ADObject -Identity \"CN=InternalApps,DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\"",
                            "This command removes the object identified by the distinguished name CN=AmyAl-LPTOP,CN=Computers,DC=FABRIKAM,DC=COM.: Remove-ADObject"
                        ],
    "Add-ADFineGrainedPasswordPolicySubject":  [
                                                   "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADFineGrainedPasswordPolicySubject",
                                                   "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADFineGrainedPasswordPolicySubject",
                                                   "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADFineGrainedPasswordPolicySubject",
                                                   "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADFineGrainedPasswordPolicySubject",
                                                   "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADFineGrainedPasswordPolicySubject -Identity DomainUsersPSO -Subjects \u0027Domain Users\u0027",
                                                   "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADFineGrainedPasswordPolicySubject -Identity DlgtdAdminsPSO -Subjects BobKe, KimAb",
                                                   "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADFineGrainedPasswordPolicySubject -Identity DlgtdAdminsPSO -Subjects DlgtdAdminGroup",
                                                   "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADFineGrainedPasswordPolicySubject -Identity DlgtdAdminsPSO"
                                               ],
    "Set-ADClaimTransformPolicy":  [
                                       "This command sets the transformation rule on the claims transformation policy named DenyAllPolicy to deny all claims, both those that are sent as well as those that are received.: Set-ADClaimTransformPolicy -Identity DenyAllPolicy -DenyAll",
                                       "This command sets the transformation rule on the claims transformation policy named AllowAllExceptCompanyAndDepartmentPolicy to allow all claims to be sent or received except for the claims Company and Department.: Set-ADClaimTransformPolicy -Identity AllowAllExceptCompanyAndDepartmentPolicy -AllowAllExcept Company,Department",
                                       "This command sets the transformation rule on the claims transformation policy named HumanResourcesToHrPolicy to transform the value Human Resources to HR in the claim ad://ext/Department:88ce6e1cc00e9524.: Set-ADClaimTransformPolicy -Identity HumanResourcesToHrPolicy -Rule \u0027C1:[Type==\"ad://ext/Department:88ce6e1cc00e9524\", Value==\"Human Resources\", ValueType==\"string\"] =\u003e issue(Type=C1.Type, Value=\"HR\", ValueType=C1.ValueType);\u0027",
                                       "This command sets the transformation rule on the claims transformation policy named MyRule with the rule specified in C:\\rule.txt.: Set-ADClaimTransformPolicy MyRule -Rule $Rule",
                                       "This command sets the transformation rule on the claims transformation policy named DenyAllPolicy to deny all claims, both those that are sent as well as those that are received.: Set-ADClaimTransformPolicy",
                                       "This command sets the transformation rule on the claims transformation policy named DenyAllPolicy to deny all claims, both those that are sent as well as those that are received.: Set-ADClaimTransformPolicy",
                                       "This command sets the transformation rule on the claims transformation policy named DenyAllPolicy to deny all claims, both those that are sent as well as those that are received.: Set-ADClaimTransformPolicy",
                                       "This command sets the transformation rule on the claims transformation policy named DenyAllPolicy to deny all claims, both those that are sent as well as those that are received.: Set-ADClaimTransformPolicy",
                                       "This command sets the transformation rule on the claims transformation policy named DenyAllPolicy to deny all claims, both those that are sent as well as those that are received.: Set-ADClaimTransformPolicy",
                                       "This command sets the transformation rule on the claims transformation policy named DenyAllPolicy to deny all claims, both those that are sent as well as those that are received.: Set-ADClaimTransformPolicy"
                                   ],
    "Set-ADAccountControl":  [
                                 "This command sets the flag on userAccountControl to make sure that a password is required for logon.: Set-ADAccountControl -Identity ElisaD -PasswordNotRequired $False",
                                 "This command sets the security descriptor of the user to make sure they cannot change their own password.: Set-ADAccountControl -Identity \u0027CN=Elisa Daugherty,OU=HumanResources,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 -CannotChangePassword $True",
                                 "This command sets the flag on userAccountControl to make sure that the account cannot be delegated.: Set-ADAccountControl -Identity SQLAdmin1 -AccountNotDelegated $True",
                                 "This command sets the flag on userAccountControl to make sure that the account is now trusted to authenticate for delegation.: Set-ADAccountControl -Identity \u0027CN=IIS01 SvcAccount,OU=ServiceAccounts,OU=Managed,DC=FABRIKAM,DC=COM\u0027 -TrustedToAuthForDelegation $True",
                                 "This command sets specified computer to be trusted for delegation.: Set-ADAccountControl -Identity \"FABRIKAM-SRV1\" -TrustedForDelegation $True",
                                 "This command sets the password of the user to never expire.: Set-ADAccountControl -Identity EvanNa -PasswordNeverExpires $True",
                                 "This command sets the user account to require a Home Directory.: Set-ADAccountControl -Identity \u0027CN=Evan Narvaez,OU=HumanResources,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 -HomedirRequired $True",
                                 "This command sets the flag on userAccountControl to make sure that a password is required for logon.: Set-ADAccountControl"
                             ],
    "Enable-ADOptionalFeature":  [
                                     "Specifies the authentication method to use. The acceptable values for this parameter are:: Enable-ADOptionalFeature",
                                     "Specifies the authentication method to use. The acceptable values for this parameter are:: Enable-ADOptionalFeature",
                                     "Specifies the authentication method to use. The acceptable values for this parameter are:: Enable-ADOptionalFeature",
                                     "Specifies the authentication method to use. The acceptable values for this parameter are:: Enable-ADOptionalFeature @params",
                                     "Specifies the authentication method to use. The acceptable values for this parameter are:: Enable-ADOptionalFeature @params"
                                 ],
    "Set-ADDefaultDomainPasswordPolicy":  [
                                              "This command sets the default domain password policy for the current logged on user domain.: Set-ADDefaultDomainPasswordPolicy -Identity fabrikam.com -LockoutDuration 00:40:00 -LockoutObservationWindow 00:20:00 -ComplexityEnabled $True -ReversibleEncryptionEnabled $False -MaxPasswordAge 10.00:00:00",
                                              "This command sets the default domain password policy for the current logged on user domain.: Set-ADDefaultDomainPasswordPolicy"
                                          ],
    "Set-ADAccountAuthenticationPolicySilo":  [
                                                  "This example assigns the authentication policy silo named AuthenticationPolicySilo01 and the authentication policy named AuthenticationPolicy01 to the user account named User01.: Set-ADAccountAuthenticationPolicySilo -Identity User01 -AuthenticationPolicySilo AuthenticationPolicySilo01 -AuthenticationPolicy AuthenticationPolicy01",
                                                  "This example assigns the authentication policy silo named AuthenticationPolicySilo01 and the authentication policy named AuthenticationPolicy01 to the user account named User01.: Set-ADAccountAuthenticationPolicySilo"
                                              ],
    "Get-ADDCCloningExcludedApplicationList":  [
                                                   "This command displays the excluded application list to the console. If there is already a CustomDCCloneAllowList.xml file, this cmdlet displays the delta of that list compared to the operating system, which may be nothing if the lists match.: Get-ADDCCloningExcludedApplicationList",
                                                   "This command generates the excluded application list as a file named CustomDCCloneAllowList.xml at the specified folder path, C:\\Windows\\NTDS, and forces overwrite if a file by that name is found to already exist at that path location.: Get-ADDCCloningExcludedApplicationList -GenerateXml -Path C:\\Windows\\NTDS -Force",
                                                   "This command displays the excluded application list to the console. If there is already a CustomDCCloneAllowList.xml file, this cmdlet displays the delta of that list compared to the operating system, which may be nothing if the lists match.: Get-ADDCCloningExcludedApplicationList",
                                                   "This command displays the excluded application list to the console. If there is already a CustomDCCloneAllowList.xml file, this cmdlet displays the delta of that list compared to the operating system, which may be nothing if the lists match.: Get-ADDCCloningExcludedApplicationList"
                                               ],
    "New-ADCentralAccessRule":  [
                                    "This command creates a new central access rule named Finance Documents Rule.: New-ADCentralAccessRule -Name \"Finance Documents Rule\"",
                                    "This command creates a new central access rule named Finance Documents Rule.: New-ADCentralAccessRule"
                                ],
    "Set-ADAuthenticationPolicySilo":  [
                                           "This command modifies the user authentication policy for the authentication policy silo named AuthenticationPolicySilo01.: Set-ADAuthenticationPolicySilo -Identity AuthenticationPolicySilo01 -UserAuthenticationPolicy \u0027AuthenticationPolicy1\u0027",
                                           "This example first gets all authentication policy silos that match the filter specified by the Filter parameter for Get-ADAuthenticationPolicySilo. The results of the filter are then passed to Set-ADAuthenticationPolicySilo by using the pipeline operator.: Set-ADAuthenticationPolicySilo -Instance $AuthPolicySilo",
                                           "This command replaces the description for the authentication policy silo object named AuthenticationPolicySilo03.: Set-ADAuthenticationPolicySilo -Identity AuthenticationPolicySilo03 -Replace @{description=\"New Description\"}",
                                           "This command modifies the user authentication policy for the authentication policy silo named AuthenticationPolicySilo01.: Set-ADAuthenticationPolicySilo",
                                           "This command modifies the user authentication policy for the authentication policy silo named AuthenticationPolicySilo01.: Set-ADAuthenticationPolicySilo"
                                       ],
    "Reset-ADServiceAccountMigration":  [
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Reset-ADServiceAccountMigration",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Reset-ADServiceAccountMigration",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Reset-ADServiceAccountMigration @params",
                                            "Specifies the authentication method to use. The acceptable values for this parameter are:: Reset-ADServiceAccountMigration @params"
                                        ],
    "Get-ADOrganizationalUnit":  [
                                     "This command gets all of the OUs in a domain.: Get-ADOrganizationalUnit -Filter \u0027Name -like \"*\"\u0027 | Format-Table Name, DistinguishedName -A",
                                     "This command gets the OU with the distinguished name OU=AsiaPacific,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM.: Get-ADOrganizationalUnit -Identity \u0027OU=AsiaPacific,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 | Format-Table Name,Country,PostalCode,City,StreetAddress,State -A",
                                     "This command gets OUs underneath the Sales OU using an LDAP filter.: Get-ADOrganizationalUnit -LDAPFilter \u0027(name=*)\u0027 -SearchBase \u0027OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 -SearchScope OneLevel | Format-Table Name,Country,PostalCode,City,StreetAddress,State",
                                     "This command gets all of the OUs in a domain.: Get-ADOrganizationalUnit",
                                     "This command gets all of the OUs in a domain.: Get-ADOrganizationalUnit",
                                     "This command gets all of the OUs in a domain.: Get-ADOrganizationalUnit",
                                     "Official code example: Get-ADOrganizationalUnit",
                                     "Official code example: Get-ADOrganizationalUnit"
                                 ],
    "Unlock-ADAccount":  [
                             "This command unlocks the account with the SAM account name PattiFu.: Unlock-ADAccount -Identity PattiFu",
                             "This command unlocks the account with the distinguished name CN=Patti Fuller,OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.: Unlock-ADAccount -Identity \"CN=Patti Fuller,OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\"",
                             "This command unlocks the account with the SAM account name PattiFu.: Unlock-ADAccount"
                         ],
    "Get-ADReplicationSiteLinkBridge":  [
                                            "This command gets all of the site link bridges.: Get-ADReplicationSiteLinkBridge -Filter *",
                                            "This command gets all site link bridges that include the site link NorthAmerica-Europe.: Get-ADReplicationSiteLinkBridge -Filter \"SiteLinksIncluded -eq \u0027NorthAmerica-Europe\u0027\" | FT Name,SiteLinksIncluded -A",
                                            "This command gets the site link bridge with the name NorthAmerica-Europe.: Get-ADReplicationSiteLinkBridge -Identity \"NorthAmerica-Asia\"",
                                            "This command gets all of the properties of the site link bridge with the name NorthAmerica-Europe.: Get-ADReplicationSiteLinkBridge -Identity \"NorthAmerica-Asia\" -Properties *",
                                            "This command gets all of the site link bridges.: Get-ADReplicationSiteLinkBridge",
                                            "This command gets all of the site link bridges.: Get-ADReplicationSiteLinkBridge"
                                        ],
    "Get-ADReplicationSite":  [
                                  "This command gets all Active Directory Replication sites.: Get-ADReplicationSite -Filter *",
                                  "This command gets the site with name NorthAmerica.: Get-ADReplicationSite -Properties * -Filter \"WindowsServer2003KCCSiteLinkBridgingEnabled -eq `$TRUE\"",
                                  "This command gets the site with name NorthAmerica.: Get-ADReplicationSite -Identity NorthAmerica",
                                  "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADReplicationSite -Identity NorthAmerica -Properties AutomaticInterSiteTopologyGenerationEnabled",
                                  "This command gets all Active Directory Replication sites.: Get-ADReplicationSite",
                                  "This command gets all Active Directory Replication sites.: Get-ADReplicationSite"
                              ],
    "Set-ADReplicationSiteLink":  [
                                      "This command adds the site Asia2 to the replication site link Europe-Asia, and removes the site Asia.: Set-ADReplicationSiteLink -Identity \"Europe-Asia\" -SitesIncluded @{Add=\"Asia2\";Remove=\"Asia\"}",
                                      "This command sets the daily replication schedule of the site link with name NorthAmerica-SouthAmerica.: Set-ADReplicationSiteLink -Identity \"NorthAmerica-SouthAmerica\" -ReplicationSchedule $Schedule",
                                      "This command enables change notification on the site link with the name Europe-Asia.: Set-ADReplicationSiteLink -Identity \"Europe-Asia\" -Replace @{\u0027options\u0027=1}",
                                      "This command adds the site Asia2 to the replication site link Europe-Asia, and removes the site Asia.: Set-ADReplicationSiteLink",
                                      "This command adds the site Asia2 to the replication site link Europe-Asia, and removes the site Asia.: Set-ADReplicationSiteLink"
                                  ],
    "Revoke-ADAuthenticationPolicySiloAccess":  [
                                                    "Specifies the account to remove from the authentication policy silo. Specify the account in one of the following formats:: Revoke-ADAuthenticationPolicySiloAccess -Identity AuthenticationPolicySilo01 -Account User01 -Confirm:$False",
                                                    "Specifies the account to remove from the authentication policy silo. Specify the account in one of the following formats:: Revoke-ADAuthenticationPolicySiloAccess"
                                                ],
    "Set-ADFineGrainedPasswordPolicy":  [
                                            "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADFineGrainedPasswordPolicy -Identity MyPolicy -Precedence 100 -LockoutDuration 00:40:00 -LockoutObservationWindow 00:20:00 -ComplexityEnabled $True -ReversibleEncryptionEnabled $false -MinPasswordLength 12",
                                            "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADFineGrainedPasswordPolicy -Identity \u0027CN=MyPolicy,CN=Password Settings Container,CN=System,DC=FABRIKAM,DC=COM\u0027 -MinPasswordLength 12",
                                            "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADFineGrainedPasswordPolicy -Instance $FGPP",
                                            "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADFineGrainedPasswordPolicy",
                                            "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is:: Set-ADFineGrainedPasswordPolicy"
                                        ],
    "Set-ADObject":  [
                         "This command sets container CN=InternalApps,DC=AppNC in an AD LDS instance to be protected from accidental deletion.: Set-ADObject -Identity \u0027CN=PattiFu Direct Reports,OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\u0027 -Description \"Distribution List of Patti Fuller Direct Reports\"",
                         "This command sets container CN=InternalApps,DC=AppNC in an AD LDS instance to be protected from accidental deletion.: Set-ADObject -Identity \u0027CN=DEFAULTIPSITELINK,CN=IP,CN=Inter-Site Transports,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\u0027 -Add @{siteList=\u0027CN=BO3,CN=Sites,CN=Configuration,DC=FABRIKAM,DC=COM\u0027} -Partition \u0027CN=Configuration,DC=FABRIKAM,DC=COM\u0027",
                         "This command sets container CN=InternalApps,DC=AppNC in an AD LDS instance to be protected from accidental deletion.: Set-ADObject -Identity \"cdadd380-d3a8-4fd1-9d30-5cf72d94a056\" -Add @{url=$UrlValues}",
                         "This command sets container CN=InternalApps,DC=AppNC in an AD LDS instance to be protected from accidental deletion.: Set-ADObject -Identity \"cdadd380-d3a8-4fd1-9d30-5cf72d94a056\" -Replace @{url=$UrlValues;description=\"Patti Fuller\"}",
                         "This command sets container CN=InternalApps,DC=AppNC in an AD LDS instance to be protected from accidental deletion.: Set-ADObject -Identity \"cdadd380-d3a8-4fd1-9d30-5cf72d94a056\" -Remove @{url=\"www.contoso.com\"} -Replace @{description=\"Patti Fuller (European Manager)\"}",
                         "This command sets container CN=InternalApps,DC=AppNC in an AD LDS instance to be protected from accidental deletion.: Set-ADObject -Instance $MyComp",
                         "This command sets container CN=InternalApps,DC=AppNC in an AD LDS instance to be protected from accidental deletion.: Set-ADObject -Identity \"CN=InternalApps,DC=AppNC\" -protectedFromAccidentalDeletion $True -Server \"FABRIKAM-SRV1:60000\"",
                         "This command sets container CN=InternalApps,DC=AppNC in an AD LDS instance to be protected from accidental deletion.: Set-ADObject",
                         "This command sets container CN=InternalApps,DC=AppNC in an AD LDS instance to be protected from accidental deletion.: Set-ADObject",
                         "The following two examples show how to specify a value for this parameter.: Set-ADObject -Instance $ObjectInstance"
                     ],
    "Set-ADUser":  [
                       "Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires.: Set-ADUser -Identity GlenJohn -Replace @{title=\"director\";mail=\"<EMAIL>\"}",
                       "Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires.: Set-ADUser -Identity GlenJohn -Remove @{otherMailbox=\"glen.john\"} -Add @{url=\"fabrikam.com\"} -Replace @{title=\"manager\"} -Clear description",
                       "Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires.: Set-ADUser -Instance $User",
                       "Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires.: Set-ADUser -Identity \"SarahDavis\" -Replace $ReplaceHashTable",
                       "Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires.: Set-ADUser -Identity ChewDavid -Manager $Manager -Server Branch-DC02",
                       "Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires.: Set-ADUser",
                       "Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires.: Set-ADUser",
                       "Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires.: Set-ADUser",
                       "Specifies the expiration date for an account. This parameter sets the AccountExpirationDate property of an account object. The LDAP display name (ldapDisplayName) for this property is accountExpires.: Set-ADUser @params"
                   ],
    "Remove-ADComputerServiceAccount":  [
                                            "This command removes a service account SvcAcct1 from a Computer Account ComputerAcct1.: Remove-ADComputerServiceAccount -Identity ComputerAcct1 -ServiceAccount SvcAcct1",
                                            "This command removes service accounts SvcAcct1 and SvcAcct2 from a Computer Account ComputerAcct1.: Remove-ADComputerServiceAccount -Identity ComputerAcct1 -ServiceAccount SvcAcct1,SvcAcct2",
                                            "This command removes a service account SvcAcct1 from a Computer Account ComputerAcct1.: Remove-ADComputerServiceAccount"
                                        ],
    "Remove-ADAuthenticationPolicySilo":  [
                                              "This command removes the authentication policy silo object named AuthenticationPolicySilo01.: Remove-ADAuthenticationPolicySilo -Identity AuthenticationPolicySilo01",
                                              "This command removes the authentication policy silo object named AuthenticationPolicySilo01.: Remove-ADAuthenticationPolicySilo"
                                          ],
    "Set-ADReplicationConnection":  [
                                        "This command sets the replication connection with GUID 5f98e288-19e0-47a0-9677-57f05ed54f6b to replicate from corp-DC01.: Set-ADReplicationConnection -Identity \"5f98e288-19e0-47a0-9677-57f05ed54f6b\" -ReplicateFromDirectoryServer corp-DC01",
                                        "This command sets the replication connection with GUID 5f98e288-19e0-47a0-9677-57f05ed54f6b to replicate from corp-DC01.: Set-ADReplicationConnection",
                                        "This command sets the replication connection with GUID 5f98e288-19e0-47a0-9677-57f05ed54f6b to replicate from corp-DC01.: Set-ADReplicationConnection",
                                        "Specifies the Active Directory Domain Services (AD DS) instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Directory Services (AD LDS), AD DS, or Active Directory snapshot instance.: Set-ADReplicationConnection \"5f98e288-19e0-47a0-9677-57f05ed54f6b\" -ReplicationSchedule $Schedule."
                                    ],
    "Get-ADObject":  [
                         "This command displays a list of sites for Fabrikam using the LDAP filter syntax.: Get-ADObject -LDAPFilter \"(objectClass=site)\" -SearchBase \u0027CN=Configuration,DC=Fabrikam,DC=Com\u0027 -Properties CanonicalName | FT Name,CanonicalName -A",
                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADObject -Filter \u0027ObjectClass -eq \"site\"\u0027 -SearchBase \u0027CN=Configuration,DC=Fabrikam,DC=Com\u0027 -Properties siteObjectBL | foreach {$_.siteObjectBL}",
                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADObject -Filter \u0027whenChanged -gt $ChangeDate\u0027 -IncludeDeletedObjects",
                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADObject -Filter \u0027whenChanged -gt $ChangeDate -and isDeleted -eq $True -and -not (isRecycled -eq $True) -and name -ne \"Deleted Objects\"\u0027 -IncludeDeletedObjects",
                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADObject -Filter \u0027whenChanged -gt $ChangeDate -and isDeleted -eq $True -and -not (isRecycled -eq $True) -and lastKnownParent -eq \"OU=Accounting,DC=Fabrikam,DC=com\"\u0027 -IncludeDeletedObjects",
                         "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADObject -Identity \"DC=AppNC\" -Server \"FABRIKAM-SRV1:60000\"",
                         "This command displays a list of sites for Fabrikam using the LDAP filter syntax.: Get-ADObject",
                         "This command displays a list of sites for Fabrikam using the LDAP filter syntax.: Get-ADObject",
                         "This command displays a list of sites for Fabrikam using the LDAP filter syntax.: Get-ADObject",
                         "Official code example: Get-ADObject",
                         "Official code example: Get-ADObject"
                     ],
    "Get-ADReplicationAttributeMetadata":  [
                                               "This command gets the replication metadata for the attributes of an object with the GUID 1A7BFEC6-C92C-4804-94B0-D407E51F1B64, including the deleted objects and the deactivated forward and backward links.: Get-ADReplicationAttributeMetadata -Object \"CN=Domain Admins,CN=Users,DC=corp,DC=contoso,DC=com\" -Server corp-DC01 -ShowAllLinkedValues",
                                               "This command gets the replication metadata for the attributes of an object with the GUID 1A7BFEC6-C92C-4804-94B0-D407E51F1B64, including the deleted objects and the deactivated forward and backward links.: Get-ADReplicationAttributeMetadata -Object \"1A7BFEC6-C92C-4804-94B0-D407E51F1B64\" -Server corp-DC01 -IncludeDeletedObjects",
                                               "The cmdlet parses the byte array(s) and returns the data in a readable format.: Get-ADReplicationAttributeMetadata"
                                           ],
    "Set-ADComputer":  [
                           "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon.. The format for this parameter is:: Set-ADComputer -Identity \"USER01-SRV1\" -ServicePrincipalName @{Replace=\"MSSQLSVC/USER01-SRV1.USER01.COM:1456\",\"MSOLAPSVC.3/USER01-SRV1.USER01.COM:analyze\"}",
                           "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon.. The format for this parameter is:: Set-ADComputer -Identity \"USER02-SRV1\" -Location \"NA/HQ/Building A\"",
                           "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon.. The format for this parameter is:: Set-ADComputer -Identity \"USER03-SRV1\" -ManagedBy \"CN=SQL Administrator 01,OU=UserAccounts,OU=Managed,DC=USER03,DC=COM\"",
                           "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon.. The format for this parameter is:: Set-ADComputer -Instance $Comp",
                           "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon.. The format for this parameter is:: Set-ADComputer",
                           "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the LDAP display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon.. The format for this parameter is:: Set-ADComputer"
                       ],
    "New-ADAuthenticationPolicy":  [
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: New-ADAuthenticationPolicy -Name \"AuthenticationPolicy01\" -UserTGTLifetimeMins 60",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: New-ADAuthenticationPolicy -Name \"AuthenticationPolicy02\" -Enforce",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: New-ADAuthenticationPolicy -Name \"TestAuthenticationPolicy\" -UserAllowedToAuthenticateFrom (Get-Acl .\\someFile.txt).sddl",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: New-ADAuthenticationPolicy"
                                   ],
    "Get-ADFineGrainedPasswordPolicy":  [
                                            "This command gets the fine-grained password policy named AdminsPSO.: Get-ADFineGrainedPasswordPolicy -Identity AdminsPSO",
                                            "This command gets all the properties for the fine-grained password policy with DistinguishedName CN=DlgtdAdminsPSO,CN=Password Settings Container,CN=System,DC=USER01,DC=COM.: Get-ADFineGrainedPasswordPolicy -Identity \u0027CN=DlgtdAdminsPSO,CN=Password Settings Container,CN=System,DC=USER01,DC=COM\u0027 -Properties *",
                                            "This command gets all the fine-grained password policy objects that have a name that begins with admin.: Get-ADFineGrainedPasswordPolicy -Filter \"name -like \u0027*admin*\u0027\"",
                                            "This command gets the fine-grained password policy named AdminsPSO.: Get-ADFineGrainedPasswordPolicy",
                                            "This command gets the fine-grained password policy named AdminsPSO.: Get-ADFineGrainedPasswordPolicy",
                                            "This command gets the fine-grained password policy named AdminsPSO.: Get-ADFineGrainedPasswordPolicy",
                                            "Official code example: Get-ADFineGrainedPasswordPolicy",
                                            "Official code example: Get-ADFineGrainedPasswordPolicy"
                                        ],
    "Get-ADFineGrainedPasswordPolicySubject":  [
                                                   "This command gets the fine-grained password policy subject of the password policy named DomainUsersPSO.: Get-ADFineGrainedPasswordPolicySubject -Identity DomainUsersPSO | FT Name,ObjectClass,DistinguishedName -AutoSize",
                                                   "This command gets the fine-grained password policy subject of the password policy named DomainUsersPSO.: Get-ADFineGrainedPasswordPolicySubject"
                                               ],
    "Disable-ADAccount":  [
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADAccount",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADAccount",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADAccount",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADAccount -Identity PattiFul",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADAccount -Identity \u0027CN=Patti Fuller,OU=Finance,OU=Users,DC=FABRIKAM,DC=COM\u0027",
                              "Specifies the authentication method to use. The acceptable values for this parameter are:: Disable-ADAccount"
                          ],
    "Get-ADDomainController":  [
                                   "This command gets a global catalog in the current forest using Discovery.: Get-ADDomainController -Discover -Site \"Default-First-Site-Name\"",
                                   "This command gets a global catalog in the current forest using Discovery.: Get-ADDomainController -Discover -Site \"Default-First-Site-Name\" -ForceDiscover",
                                   "This command gets a global catalog in the current forest using Discovery.: Get-ADDomainController -Discover -Service \"GlobalCatalog\"",
                                   "This command gets one available domain controller in the current domain using Discovery.: Get-ADDomainController -Discover",
                                   "This command gets one available domain controller in a given domain using Discovery.: Get-ADDomainController -Discover -Domain \"user01.com\"",
                                   "This command gets the primary domain controller using Discovery and make sure that is advertising as a time server.: Get-ADDomainController -Discover -Domain \"corp.contoso.com\" -Service \"PrimaryDC\",\"TimeService\"",
                                   "This command gets a domain controller using its NetBIOS name.: Get-ADDomainController -Identity \"PDC-01\"",
                                   "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADDomainController -Identity \"TK5-CORP-DC-10.user01.com\" -Server \"user01.com\" -Credential \"corp\\administrator\"",
                                   "This command gets a global catalog in the current forest using Discovery.: Get-ADDomainController",
                                   "This command gets a global catalog in the current forest using Discovery.: Get-ADDomainController",
                                   "This command gets a global catalog in the current forest using Discovery.: Get-ADDomainController",
                                   "This cmdlet does not work with Active Directory Lightweight Directory Services (AD LDS).: Get-ADDomainController"
                               ],
    "Set-ADDomain":  [
                         "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is: Set-ADDomain -Identity USER01 -AllowedDNSSuffixes @{Replace=\"USER01.com\",\"corp.USER01.com\"}",
                         "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is: Set-ADDomain -Identity USER01 -AllowedDNSSuffixes @{Add=\"corp.USER01.com\"}",
                         "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is: Set-ADDomain -Identity USER01 -ManagedBy \u0027CN=Domain Admins,CN=Users,DC=USER01,DC=COM\u0027",
                         "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is: Set-ADDomain -Instance $Domain",
                         "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is: Set-ADDomain -Identity FABRIKAM -PublicKeyRequiredPasswordRolling $True",
                         "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is: Set-ADDomain",
                         "Specifies values to add to an object property. Use this parameter to add one or more values to a property that cannot be modified using a cmdlet parameter. To modify an object property, you must use the Lightweight Directory Access Protocol (LDAP) display name. You can specify multiple values to a property by specifying a comma-separated list of values and more than one property by separating them using a semicolon. The format for this parameter is: Set-ADDomain"
                     ],
    "Get-ADClaimTransformPolicy":  [
                                       "This command retrieves a list of all claims transformation policies.: Get-ADClaimTransformPolicy",
                                       "This command retrieves a list of all claims transformation policies.: Get-ADClaimTransformPolicy",
                                       "This command retrieves a list of all claims transformation policies.: Get-ADClaimTransformPolicy",
                                       "This command retrieves a list of all claims transformation policies.: Get-ADClaimTransformPolicy",
                                       "This command retrieves a list of all claims transformation policies.: Get-ADClaimTransformPolicy -Filter *",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADClaimTransformPolicy -Filter $filter",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADClaimTransformPolicy -Identity DenyAllPolicy",
                                       "Specifies the authentication method to use. The acceptable values for this parameter are:: Get-ADClaimTransformPolicy -LDAPFilter \"(name=DenyAll*)\""
                                   ],
    "Remove-ADClaimTransformPolicy":  [
                                          "This command removes the claims transformation policy with the name DenyAllPolicy.: Remove-ADClaimTransformPolicy -Identity DenyAllPolicy",
                                          "This command removes the claims transformation policy with the name DenyAllPolicy.: Remove-ADClaimTransformPolicy"
                                      ],
    "Add-ADPrincipalGroupMembership":  [
                                           "This cmdlet collects all of the user, computer and group objects from the pipeline, and then adds these objects to the specified group by using one Active Directory operation.: Add-ADPrincipalGroupMembership",
                                           "This cmdlet collects all of the user, computer and group objects from the pipeline, and then adds these objects to the specified group by using one Active Directory operation.: Add-ADPrincipalGroupMembership",
                                           "This cmdlet collects all of the user, computer and group objects from the pipeline, and then adds these objects to the specified group by using one Active Directory operation.: Add-ADPrincipalGroupMembership",
                                           "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADPrincipalGroupMembership -Identity SQLAdmin1 -MemberOf DlgtdAdminsPSOGroup",
                                           "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADPrincipalGroupMembership -MemberOf SvcAccPSOGroup",
                                           "Specifies the authentication method to use. The acceptable values for this parameter are:: Add-ADPrincipalGroupMembership -MemberOf \"CN=AccountLeads,OU=AccountDeptOU,DC=AppNC\""
                                       ],
    "Remove-ADReplicationSiteLink":  [
                                         "This command removes the site link with the name Europe-Asia.: Remove-ADReplicationSiteLink -Identity \"Europe-Asia\"",
                                         "This command removes the site link with the name Europe-Asia.: Remove-ADReplicationSiteLink"
                                     ],
    "Get-ADPrincipalGroupMembership":  [
                                           "This command gets all of the group memberships for the user CN=DavidChew,DC=AppNC in an AD LDS instance.: Get-ADPrincipalGroupMembership -Server localhost:60000 -Identity \"CN=DavidChew,DC=AppNC\" -Partition \"DC=AppNC\"",
                                           "This command gets all the group memberships for the Administrator.: Get-ADPrincipalGroupMembership -Identity Administrator",
                                           "This command gets all of the group memberships for the Administrator account in the local domain in the resource domain ChildDomain.Fabrikam.Com.: Get-ADPrincipalGroupMembership -Identity Administrator -ResourceContextServer ChildDomain.Fabrikam.Com -ResourceContextPartition \"DC=Fabrikam,DC=com\"",
                                           "This command gets all of the group memberships for the user CN=DavidChew,DC=AppNC in an AD LDS instance.: Get-ADPrincipalGroupMembership"
                                       ],
    "Set-ADForestMode":  [
                             "This example sets the forest mode of the current user\u0027s forest. The set operation targets the schema master flexible single master operation (FSMO) role to apply the update.: Set-ADForestMode",
                             "This example sets the forest mode of the current user\u0027s forest. The set operation targets the schema master flexible single master operation (FSMO) role to apply the update.: Set-ADForestMode",
                             "This example sets the forest mode of the current user\u0027s forest. The set operation targets the schema master flexible single master operation (FSMO) role to apply the update.: Set-ADForestMode",
                             "This example sets the forest mode of the current user\u0027s forest. The set operation targets the schema master flexible single master operation (FSMO) role to apply the update.: Set-ADForestMode",
                             "This example sets the forest mode of the current user\u0027s forest. The set operation targets the schema master flexible single master operation (FSMO) role to apply the update.: Set-ADForestMode -Identity fabrikam.com -ForestMode Windows2003Forest",
                             "This example sets the forest mode of the current user\u0027s forest. The set operation targets the schema master flexible single master operation (FSMO) role to apply the update.: Set-ADForestMode @params"
                         ]
}
