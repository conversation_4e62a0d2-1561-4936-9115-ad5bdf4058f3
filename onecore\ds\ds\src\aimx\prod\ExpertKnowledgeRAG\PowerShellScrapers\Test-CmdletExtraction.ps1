# Test cmdlet extraction logic
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Testing Cmdlet Extraction Logic" -ForegroundColor Cyan

try {
    # Import core module
    $coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"
    Import-Module $coreModulePath -Force
    
    # Initialize framework
    $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath

    # Load the scraper functions first
    . (Join-Path $PSScriptRoot "Scrapers/MicrosoftDocsScraper.ps1")

    # Get the content
    $baseUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/"
    Write-Host "Fetching content from: $baseUrl" -ForegroundColor Yellow
    
    $response = Invoke-WebRequestWithRetry -Uri $baseUrl
    $content = $response.Content
    
    Write-Host "✅ Content fetched: $($content.Length) characters" -ForegroundColor Green
    
    # Test the regex pattern
    Write-Host "`nTesting cmdlet extraction pattern..." -ForegroundColor Yellow
    $cmdletPattern = '>([A-Za-z]+-AD[A-Za-z]+)<'
    
    Write-Host "Pattern: $cmdletPattern" -ForegroundColor Gray
    
    $matches = [regex]::Matches($content, $cmdletPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    Write-Host "✅ Found $($matches.Count) matches" -ForegroundColor Green
    
    # Process matches like the scraper does
    Write-Host "`nProcessing matches..." -ForegroundColor Yellow
    $cmdlets = @()
    $count = 0
    
    foreach ($match in $matches) {
        $count++
        if ($count -gt 10) { break }  # Limit for testing
        
        Write-Host "  Match $count : $($match.Value)" -ForegroundColor Gray
        Write-Host "    Groups: $($match.Groups.Count)" -ForegroundColor DarkGray
        
        if ($match.Groups.Count -gt 1) {
            $cmdletName = $match.Groups[1].Value
            Write-Host "    Cmdlet name: $cmdletName" -ForegroundColor Green
            
            # Test the domain/operation functions
            try {
                $domain = Get-CmdletDomain -CmdletName $cmdletName
                $operation = Get-CmdletOperation -CmdletName $cmdletName
                Write-Host "    Domain: $domain, Operation: $operation" -ForegroundColor Green
                
                # Construct URL
                $cmdletUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($cmdletName.ToLower())"
                Write-Host "    URL: $cmdletUrl" -ForegroundColor DarkGray
                
                $cmdlets += @{
                    Name = $cmdletName
                    Url = $cmdletUrl
                    Domain = $domain
                    Operation = $operation
                }
            }
            catch {
                Write-Host "    ❌ Error processing cmdlet: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "    ⚠️  No capture groups found" -ForegroundColor Yellow
        }
    }
    
    Write-Host "`n✅ Successfully processed $($cmdlets.Count) cmdlets" -ForegroundColor Green
    
    # Test the domain/operation functions separately
    Write-Host "`nTesting domain/operation functions..." -ForegroundColor Yellow
    
    $testCmdlets = @("Get-ADUser", "Set-ADUser", "New-ADGroup", "Remove-ADComputer")
    
    foreach ($testCmdlet in $testCmdlets) {
        try {
            $domain = Get-CmdletDomain -CmdletName $testCmdlet
            $operation = Get-CmdletOperation -CmdletName $testCmdlet
            Write-Host "  $testCmdlet -> $domain / $operation" -ForegroundColor Green
        }
        catch {
            Write-Host "  ❌ Error with $testCmdlet : $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "`n✅ Cmdlet extraction test completed successfully!" -ForegroundColor Green
}
catch {
    Write-Host "`n❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
    Write-Host "Stack trace:" -ForegroundColor Red
    Write-Host $_.ScriptStackTrace -ForegroundColor Red
}
