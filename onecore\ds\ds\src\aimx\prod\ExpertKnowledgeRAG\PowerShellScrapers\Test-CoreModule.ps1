# Test the core module import and functions
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath = "./ScrapingConfig.json"
)

Write-Host "Core Module Test" -ForegroundColor Cyan

# Test core module import
Write-Host "`nTesting core module import..." -ForegroundColor Yellow
$coreModulePath = Join-Path $PSScriptRoot "Core/ScrapingFramework.psm1"

try {
    Import-Module $coreModulePath -Force
    Write-Host "   Core module imported successfully" -ForegroundColor Green
    
    # List exported functions
    $exportedFunctions = Get-Command -Module ScrapingFramework
    Write-Host "   Exported functions: $($exportedFunctions.Count)" -ForegroundColor Green
    foreach ($func in $exportedFunctions) {
        Write-Host "     - $($func.Name)" -ForegroundColor Gray
    }
    
    # Test framework initialization
    Write-Host "`nTesting framework initialization..." -ForegroundColor Yellow
    try {
        $result = Initialize-ScrapingFramework -ConfigPath $ConfigPath
        if ($result) {
            Write-Host "   Framework initialization: PASSED" -ForegroundColor Green
        } else {
            Write-Host "   Framework initialization: FAILED" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   Framework initialization error: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Test content extraction functions
    Write-Host "`nTesting content extraction..." -ForegroundColor Yellow
    $testContent = @'
Here is some PowerShell code:
```powershell
Get-ADUser -Filter "Name -eq 'John Smith'"
Set-ADUser -Identity jsmith -Title "Manager"
```
And some more text with Get-ADGroup and New-ADUser commands.
'@

    try {
        $codeBlocks = Extract-CodeBlocks -Content $testContent
        Write-Host "   Code extraction: Found $($codeBlocks.Count) code blocks" -ForegroundColor Green
        
        $commands = Extract-PowerShellCommands -Content $testContent
        Write-Host "   Command extraction: Found $($commands.Count) commands" -ForegroundColor Green
        
        $qualityResult = Test-ContentQuality -Content $testContent -Title "Test Content"
        Write-Host "   Quality test: Score $([math]::Round($qualityResult.Score, 3))" -ForegroundColor Green
    }
    catch {
        Write-Host "   Content extraction error: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Test pattern creation
    Write-Host "`nTesting pattern creation..." -ForegroundColor Yellow
    try {
        $pattern = New-KnowledgePattern -Title "Test Pattern" -Content $testContent -SourceUrl "https://test.com" -SourceType "test" -Domain "user_management" -Operation "read"
        if ($pattern -and $pattern.Id) {
            Write-Host "   Pattern creation: PASSED (ID: $($pattern.Id))" -ForegroundColor Green
        } else {
            Write-Host "   Pattern creation: FAILED - No pattern returned" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   Pattern creation error: $($_.Exception.Message)" -ForegroundColor Red
    }
    
}
catch {
    Write-Host "   Core module import failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Error details: $($_.Exception.GetType().FullName)" -ForegroundColor Red
}

Write-Host "`nCore module test completed!" -ForegroundColor Cyan
