# Multi-Tier PowerShell Knowledge Scraping Orchestrator
# Coordinates comprehensive scraping across all tiers for maximum knowledge coverage
param(
    [Parameter(Mandatory = $false)]
    [string]$OutputDir = "./ScrapedData",
    [Parameter(Mandatory = $false)]
    [switch]$RunTier1 = $true,
    [Parameter(Mandatory = $false)]
    [switch]$RunTier2 = $true,
    [Parameter(Mandatory = $false)]
    [switch]$RunTier3 = $true,
    [Parameter(Mandatory = $false)]
    [switch]$ConsolidateOnly = $false
)

Write-Host "Multi-Tier PowerShell Knowledge Scraping Orchestrator" -ForegroundColor Cyan
Write-Host "Building comprehensive RAG-optimized knowledge base" -ForegroundColor Gray
Write-Host "=" * 70 -ForegroundColor Gray

$timestamp = Get-Date -Format 'yyyyMMdd_HHmmss'
$consolidatedOutputPath = "$OutputDir/consolidated_multitier_knowledge_$timestamp.json"

# Tier configuration
$tierConfig = @{
    tier1 = @{
        name = "Microsoft Learn Cmdlet Reference"
        description = "High-quality structured baseline from official documentation"
        script = ".\Tier1-EnhancedMicrosoftScraper.ps1"
        weight = 1.0
        priority = 1
    }
    tier2 = @{
        name = "GitHub Repository Scripts & Modules"
        description = "Real-world PowerShell scripts and modules from community"
        script = ".\Tier2-GitHubScraper.ps1"
        weight = 0.8
        priority = 2
    }
    tier3 = @{
        name = "Community Expert Blogs & Articles"
        description = "Expert insights and tutorials from PowerShell community"
        script = ".\Tier3-CommunityScraper.ps1"
        weight = 0.7
        priority = 3
    }
}

function Invoke-TierScraping {
    param(
        [string]$TierName,
        [hashtable]$TierInfo,
        [string]$OutputDirectory
    )
    
    Write-Host "`n🎯 Starting $($TierInfo.name)" -ForegroundColor Yellow
    Write-Host "   $($TierInfo.description)" -ForegroundColor Gray
    Write-Host "   Priority: $($TierInfo.priority) | Weight: $($TierInfo.weight)" -ForegroundColor Gray
    
    try {
        $scriptPath = $TierInfo.script
        if (-not (Test-Path $scriptPath)) {
            Write-Host "   ❌ Script not found: $scriptPath" -ForegroundColor Red
            return $null
        }
        
        # Execute the tier scraping script
        $outputPath = "$OutputDirectory/$($TierName)_$timestamp.json"
        $result = & $scriptPath -OutputPath $outputPath
        
        if (Test-Path $outputPath) {
            Write-Host "   ✅ $TierName completed successfully" -ForegroundColor Green
            Write-Host "   📁 Output: $outputPath" -ForegroundColor Gray
            return $outputPath
        } else {
            Write-Host "   ❌ $TierName failed - no output file generated" -ForegroundColor Red
            return $null
        }
    }
    catch {
        Write-Host "   ❌ $TierName failed: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Consolidate-MultiTierData {
    param(
        [array]$TierFiles,
        [string]$OutputPath
    )
    
    Write-Host "`n🔄 Consolidating multi-tier knowledge base..." -ForegroundColor Cyan
    
    $consolidatedKnowledgeBase = @{
        schema_version = "2.0"
        consolidated_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        description = "Multi-tier PowerShell knowledge base optimized for RAG"
        tiers = @()
        total_entries = 0
        entries = @()
        statistics = @{
            tier1_entries = 0
            tier2_entries = 0
            tier3_entries = 0
            total_cmdlets = 0
            total_code_examples = 0
            total_sources = 0
            avg_content_length = 0
            quality_distribution = @{
                high = 0      # Tier 1
                medium = 0    # Tier 2
                community = 0 # Tier 3
            }
        }
    }
    
    $allEntries = @()
    $allCmdlets = @()
    $allCodeExamples = @()
    $totalContentLength = 0
    $processedFiles = 0
    
    foreach ($tierFile in $TierFiles) {
        if (-not $tierFile -or -not (Test-Path $tierFile)) {
            Write-Host "  ⚠️  Skipping missing tier file: $tierFile" -ForegroundColor Yellow
            continue
        }
        
        try {
            Write-Host "  📖 Processing: $(Split-Path $tierFile -Leaf)" -ForegroundColor Gray
            
            $tierData = Get-Content $tierFile -Raw | ConvertFrom-Json
            
            if ($tierData.entries) {
                $validEntries = $tierData.entries | Where-Object { 
                    $_.content -and $_.content.Length -gt 100 
                }
                
                Write-Host "    Loaded $($validEntries.Count) valid entries" -ForegroundColor Green
                
                # Add tier information to each entry
                foreach ($entry in $validEntries) {
                    # Add tier metadata
                    $entry | Add-Member -NotePropertyName "tier" -NotePropertyValue $tierData.tier -Force
                    $entry | Add-Member -NotePropertyName "tier_weight" -NotePropertyValue $tierConfig["tier$($tierData.tier)"].weight -Force
                    
                    # Enhance credibility based on tier
                    if ($entry.source -and $entry.source.credibility) {
                        $tierMultiplier = switch ($tierData.tier) {
                            1 { 1.0 }    # Microsoft Learn - highest credibility
                            2 { 0.9 }    # GitHub - high credibility
                            3 { 0.8 }    # Community - good credibility
                            default { 0.7 }
                        }
                        $entry.source.credibility = [math]::Min(1.0, $entry.source.credibility * $tierMultiplier)
                    }
                }
                
                # Add tier info to consolidated data
                $consolidatedKnowledgeBase.tiers += @{
                    tier = $tierData.tier
                    name = $tierConfig["tier$($tierData.tier)"].name
                    source_file = $(Split-Path $tierFile -Leaf)
                    entries_count = $validEntries.Count
                    scraped_at = $tierData.scraped_at
                    weight = $tierConfig["tier$($tierData.tier)"].weight
                }
                
                $allEntries += $validEntries
                $processedFiles++
                
                # Update tier-specific statistics
                switch ($tierData.tier) {
                    1 { $consolidatedKnowledgeBase.statistics.tier1_entries = $validEntries.Count }
                    2 { $consolidatedKnowledgeBase.statistics.tier2_entries = $validEntries.Count }
                    3 { $consolidatedKnowledgeBase.statistics.tier3_entries = $validEntries.Count }
                }
                
                # Collect cmdlets and code examples
                foreach ($entry in $validEntries) {
                    if ($entry.cmdlets) {
                        $allCmdlets += $entry.cmdlets
                    }
                    if ($entry.code_examples) {
                        $allCodeExamples += $entry.code_examples
                    }
                    if ($entry.content) {
                        $totalContentLength += $entry.content.Length
                    }
                }
            }
        }
        catch {
            Write-Host "    ❌ Failed to process $tierFile`: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "  📊 Total entries loaded: $($allEntries.Count)" -ForegroundColor Green
    
    # Advanced deduplication with tier priority
    Write-Host "  🔍 Performing intelligent deduplication..." -ForegroundColor Gray
    
    $uniqueEntries = @{}
    $duplicatesRemoved = 0
    
    # Sort by tier priority (lower tier number = higher priority)
    $sortedEntries = $allEntries | Sort-Object tier, { $_.source.credibility } -Descending
    
    foreach ($entry in $sortedEntries) {
        $key = $entry.id
        
        if ($uniqueEntries.ContainsKey($key)) {
            # Keep the higher priority entry (lower tier number or higher credibility)
            $existing = $uniqueEntries[$key]
            if ($entry.tier -lt $existing.tier -or 
                ($entry.tier -eq $existing.tier -and $entry.source.credibility -gt $existing.source.credibility)) {
                $uniqueEntries[$key] = $entry
                Write-Host "    🔄 Replaced duplicate: $key (tier $($existing.tier) -> tier $($entry.tier))" -ForegroundColor Yellow
            } else {
                $duplicatesRemoved++
                Write-Host "    🗑️  Removed duplicate: $key (keeping tier $($existing.tier))" -ForegroundColor Yellow
            }
        } else {
            $uniqueEntries[$key] = $entry
        }
    }
    
    $finalEntries = $uniqueEntries.Values
    Write-Host "  ✅ Removed $duplicatesRemoved duplicates, final count: $($finalEntries.Count)" -ForegroundColor Green
    
    # Calculate final statistics
    $uniqueCmdlets = $allCmdlets | Sort-Object -Unique
    $avgContentLength = if ($finalEntries.Count -gt 0) { [math]::Round($totalContentLength / $finalEntries.Count) } else { 0 }
    
    # Quality distribution
    $tier1Count = ($finalEntries | Where-Object { $_.tier -eq 1 }).Count
    $tier2Count = ($finalEntries | Where-Object { $_.tier -eq 2 }).Count
    $tier3Count = ($finalEntries | Where-Object { $_.tier -eq 3 }).Count
    
    # Update consolidated knowledge base
    $consolidatedKnowledgeBase.entries = $finalEntries
    $consolidatedKnowledgeBase.total_entries = $finalEntries.Count
    $consolidatedKnowledgeBase.statistics.total_cmdlets = $uniqueCmdlets.Count
    $consolidatedKnowledgeBase.statistics.total_code_examples = $allCodeExamples.Count
    $consolidatedKnowledgeBase.statistics.total_sources = $processedFiles
    $consolidatedKnowledgeBase.statistics.avg_content_length = $avgContentLength
    $consolidatedKnowledgeBase.statistics.quality_distribution.high = $tier1Count
    $consolidatedKnowledgeBase.statistics.quality_distribution.medium = $tier2Count
    $consolidatedKnowledgeBase.statistics.quality_distribution.community = $tier3Count
    
    # Save consolidated knowledge base
    $consolidatedKnowledgeBase | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "`n🎉 Multi-tier consolidation completed!" -ForegroundColor Green
    Write-Host "=" * 70 -ForegroundColor Gray
    Write-Host "📊 Final Knowledge Base Statistics:" -ForegroundColor Cyan
    Write-Host "  Total entries: $($consolidatedKnowledgeBase.total_entries)" -ForegroundColor White
    Write-Host "  Tier 1 (Microsoft Learn): $tier1Count entries" -ForegroundColor White
    Write-Host "  Tier 2 (GitHub): $tier2Count entries" -ForegroundColor White
    Write-Host "  Tier 3 (Community): $tier3Count entries" -ForegroundColor White
    Write-Host "  Unique cmdlets: $($consolidatedKnowledgeBase.statistics.total_cmdlets)" -ForegroundColor White
    Write-Host "  Code examples: $($consolidatedKnowledgeBase.statistics.total_code_examples)" -ForegroundColor White
    Write-Host "  Average content length: $($consolidatedKnowledgeBase.statistics.avg_content_length) characters" -ForegroundColor White
    Write-Host "  Output saved to: $OutputPath" -ForegroundColor Green
    
    return $consolidatedKnowledgeBase
}

# Main orchestration logic
try {
    if (-not (Test-Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    }
    
    $tierFiles = @()
    
    if (-not $ConsolidateOnly) {
        Write-Host "🚀 Starting Multi-Tier PowerShell Knowledge Scraping" -ForegroundColor Green
        Write-Host "   Target: Comprehensive RAG-optimized knowledge base" -ForegroundColor Gray
        
        # Execute tiers in priority order
        if ($RunTier1) {
            $tier1File = Invoke-TierScraping -TierName "tier1" -TierInfo $tierConfig.tier1 -OutputDirectory $OutputDir
            if ($tier1File) { $tierFiles += $tier1File }
        }
        
        if ($RunTier2) {
            $tier2File = Invoke-TierScraping -TierName "tier2" -TierInfo $tierConfig.tier2 -OutputDirectory $OutputDir
            if ($tier2File) { $tierFiles += $tier2File }
        }
        
        if ($RunTier3) {
            $tier3File = Invoke-TierScraping -TierName "tier3" -TierInfo $tierConfig.tier3 -OutputDirectory $OutputDir
            if ($tier3File) { $tierFiles += $tier3File }
        }
    } else {
        Write-Host "🔄 Consolidation-only mode - looking for existing tier files..." -ForegroundColor Yellow
        
        # Look for the most recent tier files
        $tier1Files = Get-ChildItem "$OutputDir/tier1_*.json" | Sort-Object LastWriteTime -Descending
        $tier2Files = Get-ChildItem "$OutputDir/tier2_*.json" | Sort-Object LastWriteTime -Descending
        $tier3Files = Get-ChildItem "$OutputDir/tier3_*.json" | Sort-Object LastWriteTime -Descending
        
        if ($tier1Files) { $tierFiles += $tier1Files[0].FullName }
        if ($tier2Files) { $tierFiles += $tier2Files[0].FullName }
        if ($tier3Files) { $tierFiles += $tier3Files[0].FullName }
    }
    
    # Consolidate all tier data
    if ($tierFiles.Count -gt 0) {
        $finalKnowledgeBase = Consolidate-MultiTierData -TierFiles $tierFiles -OutputPath $consolidatedOutputPath
        
        Write-Host "`n🎯 Multi-Tier Scraping Mission Accomplished!" -ForegroundColor Green
        Write-Host "   📚 Knowledge base ready for RAG implementation" -ForegroundColor Gray
        Write-Host "   🔗 All content is offline-ready with tier-based quality scoring" -ForegroundColor Gray
    } else {
        Write-Host "`n❌ No tier files available for consolidation" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "`n❌ Multi-tier scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Full error: $($_.Exception)" -ForegroundColor Red
    exit 1
}
