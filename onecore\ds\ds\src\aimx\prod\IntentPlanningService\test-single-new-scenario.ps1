# Test a single new scenario with detailed error handling
$baseUrl = "http://localhost:8082"

Write-Host "=== TESTING USER CREATION SCENARIO ===" -ForegroundColor Cyan
Write-Host ""

$testInput = "Create a new user account for <PERSON> in the Sales department"
Write-Host "Input: $testInput" -ForegroundColor White

try {
    $requestBody = @{
        requestId = [System.Guid]::NewGuid().ToString()
        userInput = $testInput
        priority = "normal"
        environment = "production"
        userId = "test.user"
    } | ConvertTo-Json
    
    Write-Host "Request Body:" -ForegroundColor Gray
    Write-Host $requestBody -ForegroundColor Gray
    Write-Host ""
    
    $startTime = Get-Date
    $response = Invoke-RestMethod -Uri "$baseUrl/api/IntentPlanning/analyze" -Method Post -Body $requestBody -ContentType "application/json"
    $endTime = Get-Date
    $responseTime = ($endTime - $startTime).TotalMilliseconds
    
    Write-Host "✅ SUCCESS" -ForegroundColor Green
    Write-Host "Response Time: $([math]::Round($responseTime))ms" -ForegroundColor Gray
    Write-Host "Success: $($response.success)" -ForegroundColor Gray
    Write-Host "Category: $($response.category)" -ForegroundColor Gray
    Write-Host "Confidence: $($response.confidence)" -ForegroundColor Gray
    Write-Host "Steps Generated: $($response.primaryWorkflow.steps.Count)" -ForegroundColor Gray
    
    if ($response.primaryWorkflow.steps.Count -gt 0) {
        Write-Host "`nGenerated Steps:" -ForegroundColor White
        for ($i = 0; $i -lt $response.primaryWorkflow.steps.Count; $i++) {
            $step = $response.primaryWorkflow.steps[$i]
            Write-Host "  $($i + 1). $($step.description)" -ForegroundColor White
            Write-Host "     Command: $($step.operation)" -ForegroundColor Cyan
        }
    }
    
    Write-Host "`nFull Response:" -ForegroundColor Gray
    Write-Host ($response | ConvertTo-Json -Depth 10) -ForegroundColor Gray
}
catch {
    Write-Host "❌ FAILED" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        Write-Host "Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor Red
        
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error Body: $errorBody" -ForegroundColor Red
        }
        catch {
            Write-Host "Could not read error response body" -ForegroundColor Red
        }
    }
}

Write-Host "`n=== TEST COMPLETE ===" -ForegroundColor Cyan
