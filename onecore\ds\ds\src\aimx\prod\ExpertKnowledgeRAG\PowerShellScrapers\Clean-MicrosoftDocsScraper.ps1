# Clean Microsoft Docs Scraper - Full Offline Content for RAG
# Gets complete cmdlet documentation without unnecessary metadata
param(
    [Parameter(Mandatory = $false)]
    [int]$MaxCmdlets = 100,
    
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "./ScrapedData/clean_microsoft_docs_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
)

Write-Host "Clean Microsoft Docs Scraper - Full Offline Content" -ForegroundColor Cyan
Write-Host "Getting complete cmdlet documentation for RAG usage" -ForegroundColor Gray
Write-Host "=" * 60 -ForegroundColor Gray

# Helper function to clean HTML content
function Clean-HtmlContent {
    param([string]$HtmlContent)
    
    if (-not $HtmlContent) { return "" }
    
    # Remove HTML tags but preserve structure
    $cleaned = $HtmlContent -replace '<pre><code[^>]*>', '```powershell' -replace '</code></pre>', '```'
    $cleaned = $cleaned -replace '<code[^>]*>', '`' -replace '</code>', '`'
    $cleaned = $cleaned -replace '<h[1-6][^>]*>', "`n## " -replace '</h[1-6]>', "`n"
    $cleaned = $cleaned -replace '<p[^>]*>', "`n" -replace '</p>', "`n"
    $cleaned = $cleaned -replace '<li[^>]*>', "`n- " -replace '</li>', ""
    $cleaned = $cleaned -replace '<ul[^>]*>|</ul>', ""
    $cleaned = $cleaned -replace '<ol[^>]*>|</ol>', ""
    $cleaned = $cleaned -replace '<br[^>]*>', "`n"
    $cleaned = $cleaned -replace '<[^>]+>', ''
    $cleaned = $cleaned -replace '&lt;', '<' -replace '&gt;', '>' -replace '&amp;', '&' -replace '&quot;', '"' -replace '&nbsp;', ' '
    $cleaned = $cleaned -replace '\s+', ' ' -replace '^\s+|\s+$', ''
    $cleaned = $cleaned -replace '\n\s*\n', "`n`n"
    
    return $cleaned.Trim()
}

# Helper function to extract PowerShell syntax and examples
function Extract-PowerShellExamples {
    param([string]$Content)
    
    $examples = @()
    
    # Extract code blocks
    $codePattern = '```powershell\s*(.*?)```'
    $matches = [regex]::Matches($Content, $codePattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    foreach ($match in $matches) {
        $code = $match.Groups[1].Value.Trim()
        if ($code.Length -gt 5) {
            $examples += @{
                description = "PowerShell example"
                code = $code
                language = "powershell"
            }
        }
    }
    
    # Extract inline code with cmdlets
    $inlinePattern = '`([^`]*(?:Get-|Set-|New-|Remove-|Add-|Enable-|Disable-)[^`]*)`'
    $inlineMatches = [regex]::Matches($Content, $inlinePattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    foreach ($match in $inlineMatches) {
        $code = $match.Groups[1].Value.Trim()
        if ($code.Length -gt 5 -and $code -notmatch '^\w+$') {
            $examples += @{
                description = "Inline PowerShell example"
                code = $code
                language = "powershell"
            }
        }
    }
    
    return $examples | Select-Object -Unique
}

# List of Active Directory cmdlets to scrape
$adCmdlets = @(
    "Get-ADUser", "Set-ADUser", "New-ADUser", "Remove-ADUser", "Enable-ADAccount", "Disable-ADAccount",
    "Get-ADGroup", "Set-ADGroup", "New-ADGroup", "Remove-ADGroup", "Add-ADGroupMember", "Remove-ADGroupMember",
    "Get-ADComputer", "Set-ADComputer", "New-ADComputer", "Remove-ADComputer",
    "Get-ADOrganizationalUnit", "Set-ADOrganizationalUnit", "New-ADOrganizationalUnit", "Remove-ADOrganizationalUnit",
    "Get-ADDomain", "Set-ADDomain", "Get-ADForest", "Set-ADForest",
    "Get-ADDomainController", "Get-ADReplicationSite", "Set-ADReplicationSite",
    "Search-ADAccount", "Unlock-ADAccount", "Reset-ADAccountPassword",
    "Get-ADGroupMember", "Get-ADPrincipalGroupMembership", "Move-ADObject",
    "Get-ADObject", "Set-ADObject", "New-ADObject", "Remove-ADObject"
)

try {
    $knowledgeBase = @{
        schema_version = "1.0"
        scraped_at = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        source = "microsoft_docs_clean"
        total_entries = 0
        entries = @()
    }
    
    $processedCount = 0
    $maxToProcess = [Math]::Min($MaxCmdlets, $adCmdlets.Count)
    
    foreach ($cmdlet in $adCmdlets | Select-Object -First $maxToProcess) {
        $processedCount++
        Write-Host "Processing cmdlet $processedCount/$maxToProcess : $cmdlet" -ForegroundColor White
        
        try {
            # Try multiple URL patterns for Microsoft Docs
            $urls = @(
                "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($cmdlet.ToLower())",
                "https://docs.microsoft.com/en-us/powershell/module/activedirectory/$($cmdlet.ToLower())"
            )
            
            $content = $null
            $sourceUrl = $null
            
            foreach ($url in $urls) {
                try {
                    Write-Host "  Trying: $url" -ForegroundColor Gray
                    $response = Invoke-WebRequest -Uri $url -TimeoutSec 30 -ErrorAction Stop
                    $content = $response.Content
                    $sourceUrl = $url
                    break
                }
                catch {
                    Write-Host "    Failed: $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
            
            if (-not $content) {
                Write-Host "  Could not retrieve content for $cmdlet" -ForegroundColor Red
                continue
            }
            
            # Extract main content area
            $mainContentPattern = '<main[^>]*>(.*?)</main>'
            $mainMatch = [regex]::Match($content, $mainContentPattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            
            if ($mainMatch.Success) {
                $mainContent = $mainMatch.Groups[1].Value
            } else {
                # Fallback to article content
                $articlePattern = '<article[^>]*>(.*?)</article>'
                $articleMatch = [regex]::Match($content, $articlePattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
                if ($articleMatch.Success) {
                    $mainContent = $articleMatch.Groups[1].Value
                } else {
                    $mainContent = $content
                }
            }
            
            # Clean the content
            $cleanContent = Clean-HtmlContent $mainContent
            
            # Extract title
            $titlePattern = '<title[^>]*>([^<]+)</title>'
            $titleMatch = [regex]::Match($content, $titlePattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            $title = if ($titleMatch.Success) { 
                $titleMatch.Groups[1].Value -replace ' \| Microsoft Learn', '' -replace ' \| Microsoft Docs', ''
            } else { 
                "$cmdlet Documentation" 
            }
            
            # Build structured content
            $structuredContent = "# $title`n`n"
            
            # Extract synopsis/description
            $synopsisPattern = '(?i)<h2[^>]*>.*?synopsis.*?</h2>\s*<p[^>]*>(.*?)</p>'
            $synopsisMatch = [regex]::Match($content, $synopsisPattern, [System.Text.RegularExpressions.RegexOptions]::Singleline -bor [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            if ($synopsisMatch.Success) {
                $synopsis = Clean-HtmlContent $synopsisMatch.Groups[1].Value
                $structuredContent += "## Synopsis`n`n$synopsis`n`n"
            }
            
            # Add the main cleaned content
            $structuredContent += "## Documentation`n`n$cleanContent"
            
            # Extract code examples
            $codeExamples = Extract-PowerShellExamples $content
            
            # Extract parameters from content
            $parameterPattern = '(?i)-(\w+)\s+<([^>]+)>'
            $paramMatches = [regex]::Matches($cleanContent, $parameterPattern)
            $parameters = $paramMatches | ForEach-Object { $_.Groups[1].Value } | Select-Object -Unique
            
            # Create clean knowledge entry
            $entry = @{
                id = "microsoft_docs_$($cmdlet.ToLower())"
                title = $title
                content = $structuredContent
                code_examples = $codeExamples
                source = @{
                    type = "microsoft_docs"
                    url = $sourceUrl
                    credibility = 0.95
                }
                tags = @("powershell", "active-directory", "microsoft-docs", $cmdlet.ToLower())
                cmdlets = @($cmdlet)
                last_updated = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
                metadata = @{
                    cmdlet_name = $cmdlet
                    parameters = $parameters
                    module = "ActiveDirectory"
                }
            }
            
            $knowledgeBase.entries += $entry
            Write-Host "  Successfully processed $cmdlet" -ForegroundColor Green
            
            # Rate limiting
            Start-Sleep -Milliseconds 500
        }
        catch {
            Write-Host "  Failed to process $cmdlet : $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    $knowledgeBase.total_entries = $knowledgeBase.entries.Count
    
    # Save results
    $knowledgeBase | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "`nScraping completed successfully!" -ForegroundColor Green
    Write-Host "Total entries: $($knowledgeBase.total_entries)" -ForegroundColor Green
    Write-Host "Output saved to: $OutputPath" -ForegroundColor Green
    
    # Show sample of what we got
    if ($knowledgeBase.entries.Count -gt 0) {
        $sample = $knowledgeBase.entries[0]
        Write-Host "`nSample entry:" -ForegroundColor Yellow
        Write-Host "Title: $($sample.title)" -ForegroundColor White
        Write-Host "Content length: $($sample.content.Length) characters" -ForegroundColor White
        Write-Host "Code examples: $($sample.code_examples.Count)" -ForegroundColor White
        Write-Host "Cmdlet: $($sample.cmdlets -join ', ')" -ForegroundColor White
    }
}
catch {
    Write-Host "Scraping failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
