using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using System.Runtime.Versioning;

namespace netRag;

/// <summary>
/// Windows service host for the NetRag MCP Tools service
/// </summary>
public class NetRagWindowsService : BackgroundService
{
    private readonly ILogger<NetRagWindowsService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly RagConfiguration _configuration;
    private WebApplication? _webApp;

    public NetRagWindowsService(
        ILogger<NetRagWindowsService> logger,
        IServiceProvider serviceProvider,
        RagConfiguration configuration)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _configuration = configuration;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("NetRag Windows Service starting...");

            // Create and configure the web application
            var builder = WebApplication.CreateBuilder();

            // Configure the web application with the same configuration as the console app
            await ConfigureWebApplication(builder);

            _webApp = builder.Build();

            // Configure the HTTP request pipeline
            ConfigureRequestPipeline(_webApp);

            // Initialize services
            await InitializeServices(_webApp);

            _logger.LogInformation("NetRag service configured successfully");
            _logger.LogInformation("Service will be available at: http://{Host}:{Port}", 
                _configuration.McpService.Host, _configuration.McpService.Port);

            // Start the web application
            await _webApp.RunAsync(stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("NetRag Windows Service is stopping due to cancellation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in NetRag Windows Service");
            throw;
        }
    }

    private Task ConfigureWebApplication(WebApplicationBuilder builder)
    {
        // Configure registry-only configuration
        builder.Configuration.Sources.Clear();
        builder.Configuration.AddAimxServiceRegistry();

        // Register the same services
        builder.Services.AddSingleton(_configuration);
        builder.Services.AddSingleton(_configuration.FoundryLocal);
        builder.Services.AddSingleton(_configuration.Embedding);
        builder.Services.AddSingleton(_configuration.DocumentIngestion);
        builder.Services.AddSingleton(_configuration.InMemoryVectorStore);
        builder.Services.AddSingleton(_configuration.McpService);

        // Configure Semantic Kernel (same as Program.cs)
        var kernelBuilder = Microsoft.SemanticKernel.Kernel.CreateBuilder();

        // Add BERT ONNX embedding generator
        kernelBuilder.AddBertOnnxEmbeddingGenerator(
            _configuration.Embedding.ModelPath,
            _configuration.Embedding.VocabPath);

        // Add OpenAI chat completion (Foundry Local)
        kernelBuilder.AddOpenAIChatCompletion(
            _configuration.FoundryLocal.ModelName,
            new Uri(_configuration.FoundryLocal.BaseUrl),
            apiKey: "",
            serviceId: _configuration.FoundryLocal.ServiceId);

        var kernel = kernelBuilder.Build();
        builder.Services.AddSingleton(kernel);

        // Get services from kernel
        var embeddingService = kernel.GetRequiredService<Microsoft.Extensions.AI.IEmbeddingGenerator<string, Microsoft.Extensions.AI.Embedding<float>>>();
        var chatService = kernel.GetRequiredService<Microsoft.SemanticKernel.ChatCompletion.IChatCompletionService>(serviceKey: _configuration.FoundryLocal.ServiceId);

        builder.Services.AddSingleton(embeddingService);
        builder.Services.AddSingleton(chatService);

        // Register application services
        builder.Services.AddSingleton<VectorStoreService>();
        builder.Services.AddSingleton<DocumentIngestionService>();
        builder.Services.AddSingleton<RagQueryService>();
        builder.Services.AddSingleton<McpToolService>();

        // Add controllers and API services
        builder.Services.AddControllers();

        // Configure CORS
        builder.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(policy =>
            {
                policy.AllowAnyOrigin()
                      .AllowAnyMethod()
                      .AllowAnyHeader();
            });
        });

        // Configure logging for Windows service
        builder.Logging.ClearProviders();
        if (OperatingSystem.IsWindows())
        {
            builder.Logging.AddEventLog(); // Log to Windows Event Log
        }
        builder.Logging.SetMinimumLevel(LogLevel.Information);

        // Configure web host
        builder.WebHost.UseUrls($"http://{_configuration.McpService.Host}:{_configuration.McpService.Port}");
        
        return Task.CompletedTask;
    }

    private void ConfigureRequestPipeline(WebApplication app)
    {
        // Configure the HTTP request pipeline
        if (_configuration.McpService.EnableCors)
        {
            app.UseCors();
        }

        app.UseRouting();
        app.MapControllers();
    }

    private async Task InitializeServices(WebApplication app)
    {
        // Initialize vector store
        var vectorStoreService = app.Services.GetRequiredService<VectorStoreService>();
        await vectorStoreService.InitializeAsync(_configuration.Embedding.VectorSize);

        _logger.LogInformation("Vector store initialized with vector size: {VectorSize}", 
            _configuration.Embedding.VectorSize);
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("NetRag Windows Service is stopping...");

        if (_webApp != null)
        {
            await _webApp.StopAsync(cancellationToken);
            await _webApp.DisposeAsync();
        }

        await base.StopAsync(cancellationToken);
        _logger.LogInformation("NetRag Windows Service stopped");
    }
}
