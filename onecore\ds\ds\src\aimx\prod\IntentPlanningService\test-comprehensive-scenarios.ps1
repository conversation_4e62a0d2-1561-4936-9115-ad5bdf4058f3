# Comprehensive test suite for PowerShell reasoning framework
$baseUrl = "http://localhost:8082"

$testScenarios = @(
    @{
        Name = "User Property Update"
        Input = "Update the title for employee <PERSON> to Senior Manager"
        ExpectedPattern = "Get-ADUser + Set-ADUser with -Title parameter"
        ExpectedSteps = 1
    },
    @{
        Name = "Remove User from Group"
        Input = "Remove user <PERSON> from the IT Admins group"
        ExpectedPattern = "Get-ADUser + Get-ADGroup + Remove-ADGroupMember"
        ExpectedSteps = 1
    },
    @{
        Name = "Disable Multiple Users"
        Input = "Disable user accounts for <PERSON> and <PERSON>"
        ExpectedPattern = "Get-ADUser for each + Disable-ADAccount"
        ExpectedSteps = 2
    },
    @{
        Name = "Create OU Structure"
        Input = "Create a new organizational unit called Marketing under the Users OU"
        ExpectedPattern = "New-ADOrganizationalUnit with proper path"
        ExpectedSteps = 1
    },
    @{
        Name = "Computer Account Management"
        Input = "Reset the computer account password for LAPTOP-ABC123"
        ExpectedPattern = "Get-ADComputer + Reset-ComputerMachinePassword or Set-ADAccountPassword"
        ExpectedSteps = 1
    },
    @{
        Name = "Bulk User Creation"
        Input = "Create user accounts for Tom Brown and Lisa Green in the Sales department"
        ExpectedPattern = "New-ADUser for each + Add-ADGroupMember for Sales group"
        ExpectedSteps = 4
    },
    @{
        Name = "Group Property Update"
        Input = "Change the description of the Finance group to Financial Operations Team"
        ExpectedPattern = "Get-ADGroup + Set-ADGroup with -Description"
        ExpectedSteps = 1
    },
    @{
        Name = "User Account Unlock"
        Input = "Unlock the user <NAME_EMAIL>"
        ExpectedPattern = "Get-ADUser with UPN filter + Unlock-ADAccount"
        ExpectedSteps = 1
    }
)

Write-Host "=== COMPREHENSIVE POWERSHELL REASONING TEST ===" -ForegroundColor Cyan
Write-Host "Testing $($testScenarios.Count) different scenarios" -ForegroundColor Yellow
Write-Host ""

$successCount = 0
$totalTests = $testScenarios.Count

foreach ($scenario in $testScenarios) {
    Write-Host "TEST: $($scenario.Name)" -ForegroundColor White
    Write-Host "Input: $($scenario.Input)" -ForegroundColor Gray
    Write-Host "Expected: $($scenario.ExpectedPattern)" -ForegroundColor Gray
    
    try {
        $requestBody = @{
            requestId = [System.Guid]::NewGuid().ToString()
            userInput = $scenario.Input
            priority = "normal"
            environment = "production"
            userId = "test.user"
        } | ConvertTo-Json
        
        $startTime = Get-Date
        $response = Invoke-RestMethod -Uri "$baseUrl/api/IntentPlanning/analyze" -Method Post -Body $requestBody -ContentType "application/json"
        $endTime = Get-Date
        $responseTime = ($endTime - $startTime).TotalMilliseconds
        
        if ($response.success -and $response.primaryWorkflow.steps.Count -gt 0) {
            Write-Host "✅ SUCCESS" -ForegroundColor Green
            Write-Host "Response Time: $([math]::Round($responseTime))ms | Steps: $($response.primaryWorkflow.steps.Count)" -ForegroundColor Gray
            
            # Show generated commands
            for ($i = 0; $i -lt $response.primaryWorkflow.steps.Count; $i++) {
                $step = $response.primaryWorkflow.steps[$i]
                Write-Host "  $($i + 1). $($step.operation)" -ForegroundColor Cyan
            }
            
            # Quick analysis
            $allCommands = ($response.primaryWorkflow.steps | ForEach-Object { $_.operation }) -join " "
            $analysisResults = @()
            
            # Check for correct cmdlet usage patterns
            if ($allCommands -match 'Get-ADUser.*Set-ADUser' -and $scenario.Name -like "*Property Update*") {
                $analysisResults += "✅ Correct user property pattern"
            }
            if ($allCommands -match 'Remove-ADGroupMember' -and $scenario.Name -like "*Remove*") {
                $analysisResults += "✅ Correct removal pattern"
            }
            if ($allCommands -match 'Get-ADComputer' -and $scenario.Name -like "*Computer*") {
                $analysisResults += "✅ Correct computer cmdlet"
            }
            if ($allCommands -match 'New-ADOrganizationalUnit' -and $scenario.Name -like "*OU*") {
                $analysisResults += "✅ Correct OU creation"
            }
            if ($allCommands -match 'Unlock-ADAccount' -and $scenario.Name -like "*Unlock*") {
                $analysisResults += "✅ Correct unlock pattern"
            }
            if ($allCommands -match '\$\w+.*=' -and $scenario.ExpectedSteps -gt 1) {
                $analysisResults += "✅ Uses variables appropriately"
            }
            
            if ($analysisResults.Count -gt 0) {
                Write-Host "Analysis: $($analysisResults -join ', ')" -ForegroundColor Green
                $successCount++
            } else {
                Write-Host "⚠️  Generated commands but pattern unclear" -ForegroundColor Yellow
            }
            
        } else {
            Write-Host "❌ FAILED: No workflow generated" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
    Start-Sleep -Milliseconds 500
}

Write-Host "=== COMPREHENSIVE TEST RESULTS ===" -ForegroundColor Cyan
Write-Host "Successful Tests: $successCount / $totalTests" -ForegroundColor $(if ($successCount -eq $totalTests) { "Green" } else { "Yellow" })
Write-Host "Success Rate: $([math]::Round(($successCount / $totalTests) * 100, 1))%" -ForegroundColor $(if ($successCount -eq $totalTests) { "Green" } else { "Yellow" })

if ($successCount -eq $totalTests) {
    Write-Host "🎉 EXCELLENT! PowerShell reasoning framework is working across all scenarios!" -ForegroundColor Green
} elseif ($successCount -gt ($totalTests * 0.7)) {
    Write-Host "✅ GOOD! Most scenarios working well. Framework is solid." -ForegroundColor Yellow
} else {
    Write-Host "⚠️  NEEDS WORK! Framework needs refinement for better coverage." -ForegroundColor Red
}

Write-Host "`nTest completed at $(Get-Date)" -ForegroundColor Gray
